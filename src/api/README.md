# 两阶段文章生成API文档

本文档介绍了修改后的两阶段文章生成API的使用方法。新的设计将内容提取、分析和选择方向的过程与文章生成过程分离，使流程更加灵活和交互式。

## 优势

1. **交互式方向选择**: 用户可以查看并选择最合适的内容方向，而不是系统自动决定
2. **简化流程**: 去除了复杂的中断和恢复机制，工作流更加稳定
3. **灵活性**: 支持从URL和直接输入两种模式，满足不同场景需求

## 文章生成流程

两阶段文章生成流程如下：

### 第一阶段：内容分析

1. 系统从URL提取内容或使用用户提供的标题/关键词
2. 使用LLM分析内容，生成多个可能的内容方向
3. 返回分析结果给前端，包括推荐标题、内容方向和关键词
4. 用户选择最合适的方向

### 第二阶段：文章生成

1. 系统使用选定的方向、标题和关键词启动文章生成流程
2. 执行规划、搜索、写作、图片生成和最终化步骤
3. 生成完整的文章内容，包括标题、正文和图片

## 接口说明

### 1. 内容分析接口

```
POST /api/article/analyze
```

#### 请求参数

| 参数 | 类型 | 必需 | 说明 |
|-----|------|-----|------|
| url | string | 否 | 要分析的文章URL |
| title | string | 否 | 文章标题（当URL不提供时） |
| keywords | array | 否 | 关键词列表 |
| content_intro | string | 否 | 内容简介 |

**注意**: 必须提供`url`或`title`中的至少一个

#### 响应

```json
{
  "success": true,
  "analysis_id": "f8e7d6c5-b4a3-42e1-9f8e-7d6c5b4a3e2f",
  "directions": [
    {
      "index": 0,
      "title": "人工智能在医疗领域的应用",
      "selected_direction": "探讨AI如何改变现代医疗服务，从诊断到治疗的全方位应用",
      "keywords": ["人工智能", "医疗", "诊断", "辅助决策", "医学影像"]
    },
    {
      "index": 1,
      "title": "医疗AI的伦理挑战",
      "selected_direction": "分析人工智能在医疗应用中面临的伦理问题和隐私保护挑战",
      "keywords": ["医疗AI", "伦理", "隐私", "医患关系", "决策透明度"]
    }
  ],
  "extraction_time": 2.35,
  "message": "成功分析内容并找到2个可能的方向"
}
```

### 2. 文章生成接口

```
POST /api/article/generate
```

#### 请求参数

| 参数 | 类型 | 必需 | 说明 |
|-----|------|-----|------|
| analysis_id | string | 否* | 分析ID |
| direction_index | integer | 否* | 选择的方向索引 |
| title | string | 否* | 文章标题 (后端将此参数映射为ArticleProcessor.process_article的topic参数) |
| selected_direction | string | 否 | 选择的内容方向 |
| keywords | array | 否 | 关键词列表 |
| tone | string | 否 | 语调：informative, professional, casual，默认informative |
| target_length | integer | 否 | 目标长度(字数)，默认3500 |
| language | string | 否 | 语言，默认zh-CN |

**注意**: 
- 必须提供`(analysis_id和direction_index)`或`title`中的至少一组
- 如果同时提供两组，将优先使用`analysis_id`和`direction_index`

#### 响应

```json
{
  "success": true,
  "article_id": "a1b2c3d4-e5f6-7890-abcd-ef1234567890",
  "status": "processing",
  "message": "文章生成已开始，可使用article_id查询进度"
}
```

### 3. 文章状态查询接口

```
GET /api/article/{article_id}
```

#### 响应

```json
{
  "id": "a1b2c3d4-e5f6-7890-abcd-ef1234567890",
  "created_at": 1621234567,
  "updated_at": 1621234789,
  "status": "completed",
  "progress": 100,
  "topic": "人工智能在医疗领域的应用",
  "keywords": ["人工智能", "医疗", "诊断", "辅助决策", "医学影像"],
  "final_content": {
    "title": "人工智能在医疗领域的应用",
    "introduction": "近年来，人工智能技术在医疗领域的应用日益广泛...",
    "sections": [
      {
        "title": "医学影像诊断",
        "content": "在医学影像诊断领域，AI技术已经展现出卓越的性能..."
      },
      // ... 更多章节
    ],
    "conclusion": "人工智能技术正在深刻改变医疗行业..."
  },
  "generated_images": [
    {
      "url": "https://example.com/images/123.jpg",
      "description": "AI辅助医学影像诊断示例"
    }
    // ... 更多图片
  ],
  "total_execution_time": 45.8
}
```


### JavaScript 示例

参考 `hotpot_gpt/src/api/article_client.js` 文件，提供了前端API客户端封装。示例用法：

```javascript
import { analyzeContent, generateArticle, getArticleStatus, pollArticleStatus } from './article_client';

// 从URL生成文章（自动化流程）
async function generateFromUrl() {
  try {
    // 1. 分析URL
    const analysisResult = await analyzeContent({ url: 'https://example.com/article' });
    
    // 2. 生成文章（使用第一个方向）
    const generateResult = await generateArticle({
      analysis_id: analysisResult.analysis_id,
      direction_index: 0
    });
    
    // 3. 轮询获取结果
    const finalResult = await pollArticleStatus(generateResult.article_id, {
      onProgress: (progress) => console.log(`生成进度: ${progress.progress}%`)
    });
    
    console.log('文章生成完成:', finalResult.final_content.title);
  } catch (error) {
    console.error('生成失败:', error);
  }
}

// 交互式从URL生成文章
async function interactiveGenerate() {
  try {
    // 1. 分析URL
    const analysisResult = await analyzeContent({ url: 'https://example.com/article' });
    
    // 2. 显示可选方向
    console.log('请选择内容方向:');
    analysisResult.directions.forEach((dir, idx) => {
      console.log(`${idx}: ${dir.title} - ${dir.selected_direction}`);
    });
    
    // 3. 获取用户选择（这里假设用户选择了索引0）
    const selectedIndex = 0;
    
    // 4. 生成文章
    const generateResult = await generateArticle({
      analysis_id: analysisResult.analysis_id,
      direction_index: selectedIndex
    });
    
    // 5. 轮询获取结果
    const finalResult = await pollArticleStatus(generateResult.article_id);
    console.log('文章生成完成:', finalResult.final_content.title);
  } catch (error) {
    console.error('生成失败:', error);
  }
}
```

## 状态码说明

| 状态码 | 说明 |
|-------|------|
| 200 | 请求成功 |
| 400 | 参数错误，请检查请求参数 |
| 404 | 未找到文章或分析结果 |
| 500 | 服务器错误，请检查日志 | 