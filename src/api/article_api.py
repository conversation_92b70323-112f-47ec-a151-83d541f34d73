import os
import json
import asyncio
import logging
import uuid
from typing import Dict, List, Any, Optional, Union
from fastapi import FastAPI, HTTPException, BackgroundTasks, Request, Depends
from fastapi.responses import JSONResponse
from pydantic import BaseModel, Field

# 导入文章处理器
from src.services.generator.article_processor import ArticleProcessor
from src.services.generator.state import ArticleState, ArticleStatus

# 导入统一内容提取和分析接口
from src.services.extractor import (
    extract_and_analyze,
    extract_only,
    analyze_from_extraction_result,
    ContentType
)

# 从配置模块获取API密钥
from src.config.api_config import get_deepseek_api_key, get_qianwen_api_key

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 获取API密钥
DEEPSEEK_API_KEY = get_deepseek_api_key()
QIANWEN_API_KEY = get_qianwen_api_key()

# 创建文章处理器单例
ARTICLE_PROCESSOR = ArticleProcessor(config={
    "api_keys": {
        "deepseek_api_key": DEEPSEEK_API_KEY,
        "qianwen_api_key": QIANWEN_API_KEY,
    },
    "content_extractor_config": {
        "article_api_key": DEEPSEEK_API_KEY,
    }
})

# 分析结果缓存
ANALYSIS_CACHE = {}

# 请求模型
class AnalyzeRequest(BaseModel):
    url: Optional[str] = Field(None, description="要分析的文章URL")
    title: Optional[str] = Field(None, description="文章标题（当URL不提供时）")
    keywords: Optional[List[str]] = Field(None, description="关键词列表")
    content_intro: Optional[str] = Field(None, description="内容简介")

class GenerateRequest(BaseModel):
    analysis_id: Optional[str] = Field(None, description="分析ID，与direction_index一起使用")
    direction_index: Optional[int] = Field(None, description="选择的方向索引")
    
    # 或者直接提供内容详情
    title: Optional[str] = Field(None, description="文章标题")
    selected_direction: Optional[str] = Field(None, description="选择的内容方向")
    keywords: Optional[List[str]] = Field(None, description="关键词列表")
    
    # 其他可选参数
    tone: Optional[str] = Field("informative", description="语调：informative, professional, casual")
    target_length: Optional[int] = Field(3500, description="目标长度(字数)")
    language: Optional[str] = Field("zh-CN", description="语言")

# API函数
async def analyze_content(request: AnalyzeRequest) -> Dict[str, Any]:
    """
    分析内容并返回可能的内容方向
    
    Args:
        request: 分析请求参数
    
    Returns:
        包含分析结果的字典
    """
    logger.info(f"收到内容分析请求: url={request.url}, title={request.title}")
    
    try:
        # 验证参数
        if not request.url and not request.title:
            raise HTTPException(status_code=400, detail="必须提供URL或标题")
        
        # 使用统一接口进行提取和分析 - 仅对URL模式
        if request.url:
            logger.info(f"从URL提取和分析内容: {request.url}")
            result = await extract_and_analyze(request.url)
            
            if "error" in result:
                logger.error(f"分析内容时出错: {result.get('error')}")
                raise HTTPException(status_code=500, detail=result.get("error"))
            
            # 提取分析结果
            analysis_result = result.get('analysis_result', [])
        else:
            # 非URL模式 - 直接创建分析结果，无需调用extract_and_analyze
            logger.info(f"非URL模式，直接使用标题和关键词: {request.title}, {request.keywords}")
            # 直接构建一个分析结果
            analysis_result = [{
                "recommended_title": request.title,
                "content_direction": request.content_intro or f"关于{request.title}的分析",
                "keywords": request.keywords or []
            }]
        
        # 处理分析结果
        if not analysis_result:
            logger.warning("没有找到有效的分析结果")
            
            # 如果分析结果为空，但有标题和关键词，则使用这些
            if request.title:
                # 创建一个默认分析结果
                analysis_result = [{
                    "recommended_title": request.title,
                    "content_direction": f"关于{request.title}的分析",
                    "keywords": request.keywords or []
                }]
        
        # 生成分析ID
        analysis_id = str(uuid.uuid4())
        
        # 准备方向列表
        directions = []
        for i, item in enumerate(analysis_result):
            if not isinstance(item, dict):
                continue
                
            direction = {
                "index": i,
                "title": item.get("recommended_title", request.title or "内容分析"),
                "selected_direction": item.get("content_direction", f"关于{item.get('recommended_title', request.title)}的分析"),
                "keywords": item.get("keywords", [])
            }
            directions.append(direction)
        
        # 缓存分析结果
        ANALYSIS_CACHE[analysis_id] = {
            "analysis_result": analysis_result,
            "request": request.dict(),
            "directions": directions
        }
        
        # 返回响应
        return {
            "success": True,
            "analysis_id": analysis_id,
            "directions": directions,
            "extraction_time": result.get("extraction_time", 0),
            "message": f"成功分析内容并找到 {len(directions)} 个可能的方向"
        }
    except Exception as e:
        logger.exception(f"分析内容时发生异常: {str(e)}")
        raise HTTPException(status_code=500, detail=f"分析内容失败: {str(e)}")

async def generate_article(request: GenerateRequest) -> Dict[str, Any]:
    """
    根据选择的内容方向生成文章
    
    Args:
        request: 生成请求参数
    
    Returns:
        包含文章ID和状态的字典
    """
    logger.info(f"收到文章生成请求: analysis_id={request.analysis_id}, direction_index={request.direction_index}")
    
    try:
        # 准备参数
        title = request.title
        selected_direction = request.selected_direction or request.content_intro
        keywords = request.keywords
        
        # 如果提供了analysis_id，从缓存获取分析结果
        if request.analysis_id:
            if request.analysis_id not in ANALYSIS_CACHE:
                raise HTTPException(status_code=404, detail=f"找不到分析ID: {request.analysis_id}")
            
            cached_analysis = ANALYSIS_CACHE[request.analysis_id]
            directions = cached_analysis.get("directions", [])
            
            # 验证direction_index
            if request.direction_index is None:
                raise HTTPException(status_code=400, detail="必须提供direction_index")
                
            if request.direction_index < 0 or request.direction_index >= len(directions):
                raise HTTPException(status_code=400, detail=f"无效的direction_index: {request.direction_index}")
            
            # 获取选择的方向
            selected = directions[request.direction_index]
            title = selected.get("title")
            selected_direction = selected.get("selected_direction")
            keywords = selected.get("keywords")
        
        # 验证必要参数
        if not title:
            raise HTTPException(status_code=400, detail="必须提供文章标题")
        
        # 调用process_article方法处理文章 - 注意这里将title参数映射为topic参数
        logger.info(f"启动文章生成流程: 标题={title}, 方向={selected_direction}")
        result = await ARTICLE_PROCESSOR.process_article(
            topic=title,  # 这里做了参数映射：title -> topic
            keywords=keywords or [],
            selected_direction=selected_direction or f"关于{title}的分析",
            tone=request.tone,
            target_length=request.target_length,
            language=request.language
        )
        
        if "error" in result:
            logger.error(f"启动文章生成流程失败: {result.get('error')}")
            raise HTTPException(status_code=500, detail=result.get("error"))
        
        # 返回响应
        return {
            "success": True,
            "article_id": result.get("article_id"),
            "status": result.get("status", "processing"),
            "message": result.get("message", "文章生成已开始，可使用article_id查询进度")
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.exception(f"生成文章时发生异常: {str(e)}")
        raise HTTPException(status_code=500, detail=f"生成文章失败: {str(e)}")

async def get_article_status(article_id: str) -> Dict[str, Any]:
    """
    获取文章生成状态和结果
    
    Args:
        article_id: 文章ID
    
    Returns:
        包含文章状态和内容的字典
    """
    logger.info(f"收到文章状态查询请求: article_id={article_id}")
    
    try:
        # 调用generate_article方法获取状态
        result = await ARTICLE_PROCESSOR.generate_article(article_id)
        
        if "error" in result:
            logger.error(f"获取文章状态失败: {result.get('error')}")
            raise HTTPException(status_code=404, detail=result.get("error"))
        
        # 返回响应
        return result
    except Exception as e:
        logger.exception(f"获取文章状态时发生异常: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取文章状态失败: {str(e)}")

# 添加FastAPI路由
def setup_routes(app: FastAPI):
    @app.post("/api/article/analyze")
    async def api_analyze_content(request: AnalyzeRequest):
        return await analyze_content(request)
    
    @app.post("/api/article/generate")
    async def api_generate_article(request: GenerateRequest):
        return await generate_article(request)
    
    @app.get("/api/article/{article_id}")
    async def api_get_article_status(article_id: str):
        return await get_article_status(article_id) 