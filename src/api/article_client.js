/**
 * 文章生成API客户端
 * 
 * 封装了两阶段文章生成流程：
 * 1. 内容分析，获取多个可能的内容方向
 * 2. 选择一个方向生成完整文章
 */

// API基础URL，可以通过环境变量或配置来设置
const API_BASE_URL = process.env.API_BASE_URL || 'http://localhost:8000/api';

/**
 * 从URL或主题分析内容，获取可能的内容方向
 * 
 * @param {Object} params 请求参数
 * @param {string} [params.url] 文章URL
 * @param {string} [params.title] 文章标题（当URL不提供时）
 * @param {string[]} [params.keywords] 关键词列表
 * @param {string} [params.content_intro] 内容简介
 * @returns {Promise<Object>} 包含分析ID和内容方向列表的响应
 */
export async function analyzeContent(params) {
  try {
    const response = await fetch(`${API_BASE_URL}/article/analyze`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(params),
    });
    
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.detail || '内容分析失败');
    }
    
    return await response.json();
  } catch (error) {
    console.error('分析内容时出错:', error);
    throw error;
  }
}

/**
 * 根据选择的内容方向生成文章
 * 
 * @param {Object} params 请求参数
 * @param {string} [params.analysis_id] 分析ID，与direction_index一起使用
 * @param {number} [params.direction_index] 选择的方向索引
 * @param {string} [params.title] 文章标题 (注意:后端API会将此参数映射为topic参数)
 * @param {string} [params.selected_direction] 选择的内容方向
 * @param {string[]} [params.keywords] 关键词列表
 * @param {string} [params.tone] 语调
 * @param {number} [params.target_length] 目标长度
 * @param {string} [params.language] 语言
 * @returns {Promise<Object>} 包含文章ID和状态的响应
 */
export async function generateArticle(params) {
  try {
    const response = await fetch(`${API_BASE_URL}/article/generate`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(params),
    });
    
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.detail || '文章生成失败');
    }
    
    return await response.json();
  } catch (error) {
    console.error('生成文章时出错:', error);
    throw error;
  }
}

/**
 * 获取文章生成状态和结果
 * 
 * @param {string} articleId 文章ID
 * @returns {Promise<Object>} 包含文章状态和内容的响应
 */
export async function getArticleStatus(articleId) {
  try {
    const response = await fetch(`${API_BASE_URL}/article/${articleId}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });
    
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.detail || '获取文章状态失败');
    }
    
    return await response.json();
  } catch (error) {
    console.error('获取文章状态时出错:', error);
    throw error;
  }
}

/**
 * 轮询获取文章生成状态，直到完成或失败
 * 
 * @param {string} articleId 文章ID
 * @param {Object} options 轮询选项
 * @param {number} [options.interval=5000] 轮询间隔（毫秒）
 * @param {number} [options.timeout=1800000] 超时时间（毫秒，默认30分钟）
 * @param {Function} [options.onProgress] 进度回调函数
 * @returns {Promise<Object>} 最终的文章状态和内容
 */
export async function pollArticleStatus(articleId, options = {}) {
  const interval = options.interval || 5000;
  const timeout = options.timeout || 1800000;
  const onProgress = options.onProgress || (() => {});
  
  const startTime = Date.now();
  
  return new Promise((resolve, reject) => {
    const poll = async () => {
      try {
        const result = await getArticleStatus(articleId);
        
        // 回调进度
        onProgress(result);
        
        // 检查是否完成
        if (result.status === 'completed') {
          return resolve(result);
        }
        
        // 检查是否失败
        if (result.status === 'failed' || result.status === 'error') {
          return reject(new Error(result.errors ? JSON.stringify(result.errors) : '文章生成失败'));
        }
        
        // 检查是否超时
        if (Date.now() - startTime > timeout) {
          return reject(new Error('文章生成超时'));
        }
        
        // 继续轮询
        setTimeout(poll, interval);
      } catch (error) {
        reject(error);
      }
    };
    
    poll();
  });
}

/**
 * 直接生成文章，跳过分析阶段
 * 
 * @param {Object} params 请求参数
 * @param {string} params.title 文章标题
 * @param {string[]} [params.keywords] 关键词列表
 * @param {string} [params.selected_direction] 内容简介/方向
 * @param {string} [params.tone] 语调
 * @param {number} [params.target_length] 目标长度
 * @param {string} [params.language] 语言
 * @returns {Promise<Object>} 包含文章ID和状态的响应
 */
export async function directlyGenerateArticle(params) {
  try {
    const response = await fetch(`${API_BASE_URL}/article/generate`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        title: params.title,
        keywords: params.keywords || [],
        selected_direction: params.selected_direction || params.content_intro,
        tone: params.tone || 'informative',
        target_length: params.target_length || 3500,
        language: params.language || 'zh-CN'
      }),
    });
    
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.detail || '文章生成失败');
    }
    
    return await response.json();
  } catch (error) {
    console.error('生成文章时出错:', error);
    throw error;
  }
}

/**
 * 使用示例 - 从URL生成文章
 */
export async function generateArticleFromUrl(url, options = {}) {
  // 1. 分析URL
  const analysisResult = await analyzeContent({ url });
  
  if (!analysisResult.success || !analysisResult.directions.length) {
    throw new Error('URL分析失败或没有找到可用的内容方向');
  }
  
  // 2. 选择第一个内容方向（自动模式）
  const generateResult = await generateArticle({
    analysis_id: analysisResult.analysis_id,
    direction_index: 0,
    tone: options.tone || 'informative',
    target_length: options.target_length || 3500,
    language: options.language || 'zh-CN'
  });
  
  if (!generateResult.success) {
    throw new Error('文章生成请求失败');
  }
  
  // 3. 轮询获取结果
  return await pollArticleStatus(generateResult.article_id, options);
}

/**
 * 使用示例 - 从关键词和主题生成文章
 */
export async function generateArticleFromTopic(title, keywords, options = {}) {
  // 如果有content_intro，直接生成文章而不进行分析
  if (options.content_intro) {
    // 直接生成文章，跳过分析阶段
    const generateResult = await directlyGenerateArticle({
      title, 
      keywords,
      selected_direction: options.content_intro,
      tone: options.tone || 'informative',
      target_length: options.target_length || 3500,
      language: options.language || 'zh-CN'
    });
    
    if (!generateResult.success) {
      throw new Error('文章生成请求失败');
    }
    
    // 轮询获取结果
    return await pollArticleStatus(generateResult.article_id, options);
  }
  
  // 否则采用原有的两阶段流程
  // 1. 分析主题和关键词
  const analysisResult = await analyzeContent({ 
    title, 
    keywords,
    content_intro: options.content_intro
  });
  
  if (!analysisResult.success || !analysisResult.directions.length) {
    throw new Error('主题分析失败或没有找到可用的内容方向');
  }
  
  // 2. 选择第一个内容方向（自动模式）
  const generateResult = await generateArticle({
    analysis_id: analysisResult.analysis_id,
    direction_index: 0,
    tone: options.tone || 'informative',
    target_length: options.target_length || 3500,
    language: options.language || 'zh-CN'
  });
  
  if (!generateResult.success) {
    throw new Error('文章生成请求失败');
  }
  
  // 3. 轮询获取结果
  return await pollArticleStatus(generateResult.article_id, options);
} 