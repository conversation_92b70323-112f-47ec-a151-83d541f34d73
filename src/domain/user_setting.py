"""用户设置模型"""
from sqlalchemy import <PERSON>umn, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Foreign<PERSON>ey
from sqlalchemy.orm import relationship
from pydantic import BaseModel

from src.domain.base import Base

class UserSetting(Base):
    """用户设置表"""
    __tablename__ = 'user_settings'
    
    user_id = Column(String(36), ForeignKey('users.id', ondelete='CASCADE'), primary_key=True)
    language = Column(String(10), default='zh-CN')
    timezone = Column(String(50), default='Asia/Shanghai')
    notification_enabled = Column(Boolean, default=True)
    theme = Column(String(20), default='light')
    privacy_level = Column(Integer, default=0)
    
    # 关联关系
    user = relationship("User", back_populates="settings")
    
    def __repr__(self):
        return f"<UserSetting {self.user_id}>"


class UserSettingCreate(BaseModel):
    """用户设置创建模型"""
    language: str = 'zh-CN'
    timezone: str = 'Asia/Shanghai'
    notification_enabled: bool = True
    theme: str = 'light'
    privacy_level: int = 0 