"""文章相关数据模型"""
from datetime import datetime
from typing import List, Optional, Dict, Any
from sqlalchemy import Column, Integer, String, Text, DateTime, ForeignKey, JSON
from sqlalchemy.orm import relationship, Session
import json

from .base import BaseDBModel, TimestampMixin, BaseRepository, BaseSchema
from pydantic import BaseModel, Field

class ArticleAnalysis(BaseDBModel, TimestampMixin):
    """文章分析表"""
    __tablename__ = "article_analysis"
    
    url = Column(String(500), unique=True, index=True, nullable=False, comment="文章URL")
    keywords = Column(JSON, nullable=True, comment="关键词")
    recommended_titles = Column(JSON, nullable=True, comment="推荐标题")
    content_directions = Column(JSON, nullable=True, comment="内容方向")
    wordcloud = Column(JSON, nullable=True, comment="词云数据")
    raw_data = Column(JSON, nullable=True, comment="原始分析数据")

class ArticleExtraction(BaseDBModel, TimestampMixin):
    """文章提取表"""
    __tablename__ = "article_extraction"
    
    url = Column(String(500), unique=True, index=True, nullable=False, comment="文章URL")
    title = Column(String(255), nullable=True, comment="标题")
    content = Column(Text, nullable=True, comment="内容")
    author = Column(String(100), nullable=True, comment="作者")
    account = Column(String(100), nullable=True, comment="账号")
    publish_time = Column(String(50), nullable=True, comment="发布时间")
    images = Column(JSON, nullable=True, comment="图片列表")
    raw_data = Column(JSON, nullable=True, comment="原始提取数据")

# Pydantic模型
class ArticleAnalysisCreate(BaseSchema):
    """创建文章分析"""
    url: str
    keywords: Optional[List[str]] = None
    recommended_titles: Optional[List[str]] = None
    content_directions: Optional[List[str]] = None
    wordcloud: Optional[List[Dict[str, Any]]] = None
    raw_data: Optional[Dict[str, Any]] = None

class ArticleAnalysisResponse(BaseSchema):
    """文章分析响应"""
    id: int
    url: str
    keywords: Optional[List[str]] = None
    recommended_titles: Optional[List[str]] = None
    content_directions: Optional[List[str]] = None
    wordcloud: Optional[List[Dict[str, Any]]] = None
    created_at: datetime
    updated_at: datetime

class ArticleExtractionCreate(BaseSchema):
    """创建文章提取"""
    url: str
    title: Optional[str] = None
    content: Optional[str] = None
    author: Optional[str] = None
    account: Optional[str] = None
    publish_time: Optional[str] = None
    images: Optional[List[str]] = None
    raw_data: Optional[Dict[str, Any]] = None

class ArticleExtractionResponse(BaseSchema):
    """文章提取响应"""
    id: int
    url: str
    title: Optional[str] = None
    content: Optional[str] = None
    author: Optional[str] = None
    account: Optional[str] = None
    publish_time: Optional[str] = None
    images: Optional[List[str]] = None
    created_at: datetime
    updated_at: datetime

class ArticleAnalysisRepository(BaseRepository[ArticleAnalysis, ArticleAnalysisCreate, ArticleAnalysisCreate]):
    """文章分析仓储"""

    def __init__(self, db: Session):
        super().__init__(ArticleAnalysis, db)

    def get_by_url(self, url: str) -> Optional[ArticleAnalysis]:
        """根据URL获取文章分析"""
        return self.db.query(ArticleAnalysis).filter(ArticleAnalysis.url == url).first()

    def create_from_dict(self, url: str, data: Dict[str, Any]) -> ArticleAnalysis:
        """从字典创建文章分析"""
        analysis_data = {
            "url": url,
            "raw_data": data
        }
        
        # 提取关键字段
        if "analysis_result" in data:
            keywords = []
            recommended_titles = []
            content_directions = []
            
            for item in data.get("analysis_result", []):
                if isinstance(item, dict):
                    if "keywords" in item:
                        keywords.extend(item.get("keywords", []))
                    if "recommended_titles" in item:
                        if isinstance(item["recommended_titles"], list):
                            recommended_titles.extend(item["recommended_titles"])
                        else:
                            recommended_titles.append(item["recommended_titles"])
                    if "content_directions" in item:
                        if isinstance(item["content_directions"], list):
                            content_directions.extend(item["content_directions"])
                        else:
                            content_directions.append(item["content_directions"])
            
            analysis_data["keywords"] = keywords
            analysis_data["recommended_titles"] = recommended_titles
            analysis_data["content_directions"] = content_directions
        
        # 转换为Pydantic模型，然后创建
        create_schema = ArticleAnalysisCreate(**analysis_data)
        return self.create(create_schema)

class ArticleExtractionRepository(BaseRepository[ArticleExtraction, ArticleExtractionCreate, ArticleExtractionCreate]):
    """文章提取仓储"""

    def __init__(self, db: Session):
        super().__init__(ArticleExtraction, db)

    def get_by_url(self, url: str) -> Optional[ArticleExtraction]:
        """根据URL获取文章提取"""
        return self.db.query(ArticleExtraction).filter(ArticleExtraction.url == url).first()

    def create_from_dict(self, url: str, data: Dict[str, Any]) -> ArticleExtraction:
        """从字典创建文章提取"""
        extraction_data = {
            "url": url,
            "title": data.get("title", ""),
            "content": data.get("content", ""),
            "author": data.get("author", ""),
            "account": data.get("account", ""),
            "publish_time": data.get("publish_time", ""),
            "images": data.get("images", []),
            "raw_data": data
        }
        
        # 转换为Pydantic模型，然后创建
        create_schema = ArticleExtractionCreate(**extraction_data)
        return self.create(create_schema) 