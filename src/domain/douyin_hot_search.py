"""
抖音热搜数据模型和仓储层
"""
from datetime import datetime
from typing import Any, Dict, List, Optional, Tuple, Union
from sqlalchemy import Column, Integer, String, DateTime, Boolean, JSON, and_, desc, UniqueConstraint, BigInteger
from sqlalchemy.orm import Session
from pydantic import Field, validator

from .base import (
    BaseDBModel,
    TimestampMixin,
    BaseSchema,
    BaseRepository
)

# 字段配置
FIELD_CONFIG = {
    # 字段最大长度
    "lengths": {
        "ds": 10,
        "board": 100,
        "aweme_infos": 100,
        "drift_info": 100,
        "group_id": 50,
        "hotlist_param": 255,
        "label_url": 100,
        "related_words": 100,
        "sentence_id": 50,
        "word": 255,
        "word_sub_board": 100,
        "url": 255
    },
    # 字段描述
    "descriptions": {
        "ds": "数据日期，格式：YYYY-MM-DD",
        "board": "板块",
        "article_detail_count": "文章详情数",
        "aweme_infos": "视频信息",
        "can_extend_detail": "是否可扩展详情",
        "discuss_video_count": "讨论视频数",
        "display_style": "展示样式",
        "drift_info": "漂流信息",
        "event_time": "事件时间",
        "group_id": "分组ID",
        "hot_value": "热度值",
        "hotlist_param": "热榜参数",
        "label": "标签",
        "label_url": "标签链接",
        "position": "排名",
        "related_words": "相关词",
        "sentence_id": "句子ID",
        "sentence_tag": "句子标签",
        "video_count": "视频数",
        "view_count": "观看数",
        "word": "热搜词",
        "word_cover": "词封面",
        "word_sub_board": "词子板块",
        "word_type": "词类型",
        "url": "链接"
    }
}

# 数据库模型
class DouyinHotSearch(BaseDBModel, TimestampMixin):
    """热榜数据表模型"""
    __tablename__ = "douyin_hot_search"
    
    # 热点基本信息
    ds = Column(String(FIELD_CONFIG["lengths"]["ds"]), nullable=False, index=True, 
                comment=FIELD_CONFIG["descriptions"]["ds"])
    board = Column(String(FIELD_CONFIG["lengths"]["board"]), nullable=False,
                   comment=FIELD_CONFIG["descriptions"]["board"])
    article_detail_count = Column(Integer, default=0,
                                comment=FIELD_CONFIG["descriptions"]["article_detail_count"])
    aweme_infos = Column(String(FIELD_CONFIG["lengths"]["aweme_infos"]), default=0,
                        comment=FIELD_CONFIG["descriptions"]["aweme_infos"])
    can_extend_detail = Column(Boolean, default=False,
                             comment=FIELD_CONFIG["descriptions"]["can_extend_detail"])
    discuss_video_count = Column(Integer, default=0,
                               comment=FIELD_CONFIG["descriptions"]["discuss_video_count"])
    display_style = Column(Integer, default=0,
                          comment=FIELD_CONFIG["descriptions"]["display_style"])
    drift_info = Column(String(FIELD_CONFIG["lengths"]["drift_info"]), default="",
                        comment=FIELD_CONFIG["descriptions"]["drift_info"])
    event_time = Column(BigInteger, nullable=True,
                       comment=FIELD_CONFIG["descriptions"]["event_time"])
    group_id = Column(String(FIELD_CONFIG["lengths"]["group_id"]), nullable=False,
                     comment=FIELD_CONFIG["descriptions"]["group_id"])
    hot_value = Column(BigInteger, nullable=False,
                      comment=FIELD_CONFIG["descriptions"]["hot_value"])
    hotlist_param = Column(String(FIELD_CONFIG["lengths"]["hotlist_param"]), nullable=False,
                          comment=FIELD_CONFIG["descriptions"]["hotlist_param"])
    label = Column(Integer, default="",
                  comment=FIELD_CONFIG["descriptions"]["label"])
    label_url = Column(String(FIELD_CONFIG["lengths"]["label_url"]), default="",
                      comment=FIELD_CONFIG["descriptions"]["label_url"])
    position = Column(Integer, nullable=False,
                     comment=FIELD_CONFIG["descriptions"]["position"])
    related_words = Column(String(FIELD_CONFIG["lengths"]["related_words"]), default="",
                        comment=FIELD_CONFIG["descriptions"]["related_words"])
    sentence_id = Column(String(FIELD_CONFIG["lengths"]["sentence_id"]), nullable=False,
                        comment=FIELD_CONFIG["descriptions"]["sentence_id"])
    sentence_tag = Column(Integer, default=0,
                         comment=FIELD_CONFIG["descriptions"]["sentence_tag"])
    video_count = Column(Integer, default=0,
                        comment=FIELD_CONFIG["descriptions"]["video_count"])
    view_count = Column(Integer, default=0,
                        comment=FIELD_CONFIG["descriptions"]["view_count"])
    word = Column(String(FIELD_CONFIG["lengths"]["word"]), nullable=False,
                 comment=FIELD_CONFIG["descriptions"]["word"])
    word_cover = Column(JSON, default=dict,
                       comment=FIELD_CONFIG["descriptions"]["word_cover"])
    word_sub_board = Column(String(FIELD_CONFIG["lengths"]["word_sub_board"]), default="",
                        comment=FIELD_CONFIG["descriptions"]["word_sub_board"])
    word_type = Column(Integer, default=0,
                      comment=FIELD_CONFIG["descriptions"]["word_type"])
    url = Column(String(FIELD_CONFIG["lengths"]["url"]), nullable=False,
                comment=FIELD_CONFIG["descriptions"]["url"])
    
    # 定义联合唯一索引，确保分区内数据唯一性
    __table_args__ = (
        # 联合唯一约束：同一天内的数据按这些字段唯一
        UniqueConstraint('ds', 'board', 'position', 
                        name='uk_daily_video_unique'),
    )

# API模型
class DouyinHotSearchBase(BaseSchema):
    """热榜数据基础模型"""
    ds: str = Field(..., description=FIELD_CONFIG["descriptions"]["ds"])
    board: str = Field(..., description=FIELD_CONFIG["descriptions"]["board"])
    article_detail_count: int = Field(..., description=FIELD_CONFIG["descriptions"]["article_detail_count"])
    aweme_infos: str = Field(..., description=FIELD_CONFIG["descriptions"]["aweme_infos"])  
    can_extend_detail: bool = Field(..., description=FIELD_CONFIG["descriptions"]["can_extend_detail"])
    discuss_video_count: int = Field(..., description=FIELD_CONFIG["descriptions"]["discuss_video_count"])
    display_style: int = Field(..., description=FIELD_CONFIG["descriptions"]["display_style"])
    drift_info: str = Field(..., description=FIELD_CONFIG["descriptions"]["drift_info"])
    event_time: datetime = Field(..., description=FIELD_CONFIG["descriptions"]["event_time"])
    group_id: str = Field(..., description=FIELD_CONFIG["descriptions"]["group_id"])
    hot_value: int = Field(..., description=FIELD_CONFIG["descriptions"]["hot_value"])
    hotlist_param: str = Field(..., description=FIELD_CONFIG["descriptions"]["hotlist_param"])
    label: int = Field(..., description=FIELD_CONFIG["descriptions"]["label"])
    label_url: str = Field(..., description=FIELD_CONFIG["descriptions"]["label_url"])
    position: int = Field(..., description=FIELD_CONFIG["descriptions"]["position"])
    related_words: str = Field(..., description=FIELD_CONFIG["descriptions"]["related_words"])
    sentence_id: str = Field(..., description=FIELD_CONFIG["descriptions"]["sentence_id"])
    sentence_tag: int = Field(..., description=FIELD_CONFIG["descriptions"]["sentence_tag"])
    video_count: int = Field(..., description=FIELD_CONFIG["descriptions"]["video_count"])
    view_count: int = Field(..., description=FIELD_CONFIG["descriptions"]["view_count"])
    word: str = Field(..., description=FIELD_CONFIG["descriptions"]["word"])
    word_cover: Dict = Field(..., description=FIELD_CONFIG["descriptions"]["word_cover"])
    word_sub_board: str = Field(..., description=FIELD_CONFIG["descriptions"]["word_sub_board"])
    word_type: int = Field(..., description=FIELD_CONFIG["descriptions"]["word_type"])
    url: str = Field(..., description=FIELD_CONFIG["descriptions"]["url"])        

    class Config:
        orm_mode = True

class DouyinHotSearchCreate(DouyinHotSearchBase):
    """创建热榜数据模型"""
    pass

class DouyinHotSearchUpdate(DouyinHotSearchBase):
    """更新热榜数据模型"""
    pass

class DouyinHotSearchInDB(DouyinHotSearchBase):
    """数据库中的热榜数据模型"""
    id: int
    created_at: datetime
    updated_at: datetime

# 仓储层
class DouyinHotSearchRepository(BaseRepository[DouyinHotSearch, DouyinHotSearchCreate, DouyinHotSearchUpdate]):
    """热榜数据仓储"""
    
    def __init__(self, db: Session):
        super().__init__(DouyinHotSearch, db)
    
    def get_by_partition_key(self, ds: str, board: str, position: int) -> Optional[DouyinHotSearch]:
        """根据分区键获取数据"""
        return (self.db.query(self.model)
                .filter(self.model.ds == ds,
                       self.model.board == board,
                       self.model.position == position)
                .first())
    
    async def upsert(self, datas: Dict[str, Any]) -> DouyinHotSearch:
        """更新或插入视频数据"""
        # 确保有ds字段，如果没有则使用当前日期
        if "ds" not in datas:
            datas["ds"] = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0).strftime("%Y-%m-%d")
            
        # 查找是否存在相同分区的记录
        existing = self.get_by_partition_key(
            ds=datas["ds"],
            board=datas["board"],
            position=datas["position"]
        )
        
        if existing:
            # 更新现有记录
            for key, value in datas.items():
                if key not in ["ds", "board", "position"]:
                    setattr(existing, key, value)
            existing.updated_at = datetime.now()
            self.db.merge(existing)
            return existing
        else:
            # 创建新记录
            data = DouyinHotSearch(**datas)
            self.db.add(data)
            return data
    
    def get_by_date_range(self, start_date: str, end_date: str, 
                         board: Optional[str] = None) -> List[DouyinHotSearch]:
        """获取指定日期范围的数据"""
        query = (self.db.query(self.model)
                .filter(self.model.ds >= start_date)
                .filter(self.model.ds <= end_date))
        
        if board is not None:
            query = query.filter(self.model.board == board)
            
        return query.order_by(self.model.ds.desc(), 
                            self.model.score.desc()).all()
    
    def get_latest_by_ds(self, board: Optional[str] = None, limit: int = 50) -> List[DouyinHotSearch]:
        """获取最新日期的热搜数据
        
        Args:
            board: 可选，指定板块
            limit: 返回的数据条数限制，默认50条
            
        Returns:
            List[DouyinHotSearch]: 热搜数据列表，按position排序
        """
        # 先获取最新的日期
        latest_ds = (self.db.query(self.model.ds)
                    .order_by(self.model.ds.desc())
                    .first())
        
        if not latest_ds:
            return []
            
        # 构建查询
        query = (self.db.query(self.model)
                .filter(self.model.ds == latest_ds[0]))
        
        # 如果指定了board，添加board过滤
        if board:
            query = query.filter(self.model.board == board)
            
        # 按position排序并限制返回数量
        return (query.order_by(self.model.position.asc())
                .limit(limit)
                .all())
    
    def cleanup_old_partitions(self, keep_days: int = 7) -> None:
        """清理旧的分区数据，默认保留7天"""
        cutoff_date = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0) - timedelta(days=keep_days)
        
        # 使用 SQL 直接删除分区
        sql = f"""
            ALTER TABLE {self.model.__tablename__}
            DROP PARTITION p_{cutoff_date.strftime('%Y%m%d')}
        """
        try:
            self.db.execute(sql)
            self.db.commit()
        except Exception as e:
            self.db.rollback()
            # log_error("douhot_video", f"清理旧分区失败: {str(e)}")
