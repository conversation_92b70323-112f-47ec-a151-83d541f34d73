"""抖音热榜话题领域模型"""
from datetime import datetime, timedelta
from typing import Dict, Optional, List, Any
from sqlalchemy import Column, String, Integer, BigInteger, Boolean, JSON, UniqueConstraint, func, DateTime
from sqlalchemy.orm import Session
from pydantic import Field, validator

from .base import (
    BaseDBModel,
    TimestampMixin,
    BaseSchema,
    BaseRepository
)

# 字段配置
FIELD_CONFIG = {
    # 字段长度
    "lengths": {
        "ds": 10,
        "challenge_id": 50,
        "challenge_name": 200,
        "cover_url": 500,
        "origin_trend_str": 1000,
        "item_list": 1000,
    },
    # 字段描述
    "descriptions": {
        "ds": "数据日期，格式：YYYY-MM-DD",
        "topic_tag1": "一级分类",
        "topic_tag2": "二级分类",
        "topic_tag3": "三级分类",
        "billboard_type": "榜单类型(2001:话题总榜,2002:热度飙升的话题榜)",
        "date_window": "时间窗口(小时)",
        "challenge_id": "话题ID",
        "challenge_name": "话题名称",
        "play_cnt": "播放总数",
        "publish_cnt": "发布总数",
        "cover_url": "封面URL",
        "score": "热度分数",
        "avg_play_cnt": "平均播放数",
        "create_time": "创建时间",
        "origin_trend_str": "原始趋势数据字符串",
        "item_list": "item列表",
        "trends": "趋势数据，格式：[{date: YYYYMMDD, value: 热度值}]",
        "challenge_type": "话题类型",
        "is_favorite": "是否收藏",
        "is_recommend": "是否推荐",
        "related_event": "相关事件",
        "show_rank": "展示排名",
        "real_rank": "实际排名",
        "origin_rank": "原始排名",
        "record_create_time": "db记录创建时间",
        "record_update_time": "db记录更新时间"
    }
}

class DouhotBillboardTopic(BaseDBModel, TimestampMixin):
    """抖音热榜话题数据表模型"""
    __tablename__ = "douhot_billboard_topics"

    # 分区字段
    ds = Column(String(FIELD_CONFIG["lengths"]["ds"]), nullable=False, index=True, 
                comment=FIELD_CONFIG["descriptions"]["ds"])
    
    # 标签字段
    topic_tag1 = Column(Integer, nullable=False, default=0,
                       comment=FIELD_CONFIG["descriptions"]["topic_tag1"])
    topic_tag2 = Column(Integer, nullable=False, default=0,
                       comment=FIELD_CONFIG["descriptions"]["topic_tag2"])
    topic_tag3 = Column(Integer, nullable=False, default=0,
                       comment=FIELD_CONFIG["descriptions"]["topic_tag3"])
    billboard_type = Column(Integer, nullable=False, default=0,
                          comment=FIELD_CONFIG["descriptions"]["billboard_type"])
    date_window = Column(Integer, nullable=False,
                        comment=FIELD_CONFIG["descriptions"]["date_window"])

    # 基础信息
    challenge_id = Column(String(FIELD_CONFIG["lengths"]["challenge_id"]), nullable=False,
                         comment=FIELD_CONFIG["descriptions"]["challenge_id"])
    challenge_name = Column(String(FIELD_CONFIG["lengths"]["challenge_name"]), nullable=False,
                          comment=FIELD_CONFIG["descriptions"]["challenge_name"])
    play_cnt = Column(BigInteger, nullable=False,
                     comment=FIELD_CONFIG["descriptions"]["play_cnt"])
    publish_cnt = Column(BigInteger, nullable=False,
                        comment=FIELD_CONFIG["descriptions"]["publish_cnt"])
    cover_url = Column(String(FIELD_CONFIG["lengths"]["cover_url"]), nullable=True,
                      comment=FIELD_CONFIG["descriptions"]["cover_url"])
    score = Column(BigInteger, nullable=False,
                  comment=FIELD_CONFIG["descriptions"]["score"])
    avg_play_cnt = Column(BigInteger, nullable=False,
                         comment=FIELD_CONFIG["descriptions"]["avg_play_cnt"])
    create_time = Column(BigInteger, nullable=False,
                        comment=FIELD_CONFIG["descriptions"]["create_time"])
    origin_trend_str = Column(String(FIELD_CONFIG["lengths"]["origin_trend_str"]), nullable=True,
                             comment=FIELD_CONFIG["descriptions"]["origin_trend_str"])
    item_list = Column(String(FIELD_CONFIG["lengths"]["item_list"]), nullable=True,
                             comment=FIELD_CONFIG["descriptions"]["item_list"])

    # 趋势数据
    trends = Column(JSON, nullable=False,
                   comment=FIELD_CONFIG["descriptions"]["trends"])

    # 扩展信息
    challenge_type = Column(Integer, default=0,
                          comment=FIELD_CONFIG["descriptions"]["challenge_type"])
    is_favorite = Column(Boolean, default=False,
                        comment=FIELD_CONFIG["descriptions"]["is_favorite"])
    is_recommend = Column(Boolean, default=False,
                        comment=FIELD_CONFIG["descriptions"]["is_recommend"])
    show_rank = Column(Integer, nullable=False, default=0,
                      comment=FIELD_CONFIG["descriptions"]["show_rank"])
    real_rank = Column(Integer, nullable=False, default=0,
                      comment=FIELD_CONFIG["descriptions"]["real_rank"])
    origin_rank = Column(Integer, nullable=False, default=0,
                      comment=FIELD_CONFIG["descriptions"]["origin_rank"])
    related_event = Column(JSON, nullable=True,
                          comment=FIELD_CONFIG["descriptions"]["related_event"])
    
    # db记录时间
    record_create_time = Column(DateTime, nullable=False, default=func.now(),
                               comment=FIELD_CONFIG["descriptions"]["record_create_time"])
    record_update_time = Column(DateTime, nullable=False, default=func.now(), onupdate=func.now(),
                               comment=FIELD_CONFIG["descriptions"]["record_update_time"])

    __table_args__ = (
        # 联合唯一索引：日期分区+话题ID+标签+时间窗口+榜单类型
        UniqueConstraint('ds', 'challenge_id', 'topic_tag1', 'topic_tag2', 'date_window', 'billboard_type', 
                        name='uk_topic_partition'),
    )

    def update_trend_stats(self):
        """更新趋势数据统计"""
        if not self.trends:
            return
        
        trend_values = [t.get('value', 0) for t in self.trends]
        trend_dates = [t.get('date', '') for t in self.trends]
        
        if trend_values:
            self.max_trend_value = max(trend_values)
            self.min_trend_value = min(trend_values)
        
        if trend_dates:
            self.trend_start_date = min(trend_dates)
            self.trend_end_date = max(trend_dates)

class TopicTrend(BaseSchema):
    """话题趋势数据模型"""
    date: str  # YYYYMMDD格式
    value: int

    @validator('date')
    def validate_date_format(cls, v):
        """验证日期格式"""
        try:
            datetime.strptime(v, '%Y-%m-%d %H')
            return v
        except ValueError:
            raise ValueError('日期格式必须为YYYY-MM-DD HH')

class DouhotBillboardTopicBase(BaseSchema):
    """抖音热榜话题基础模型"""
    ds: str = Field(..., description=FIELD_CONFIG["descriptions"]["ds"])
    topic_tag1: int = Field(default=0, description=FIELD_CONFIG["descriptions"]["topic_tag1"])
    topic_tag2: int = Field(default=0, description=FIELD_CONFIG["descriptions"]["topic_tag2"])
    topic_tag3: int = Field(default=0, description=FIELD_CONFIG["descriptions"]["topic_tag3"])
    billboard_type: int = Field(..., description=FIELD_CONFIG["descriptions"]["billboard_type"])
    date_window: int = Field(..., description=FIELD_CONFIG["descriptions"]["date_window"])
    challenge_id: str = Field(..., description=FIELD_CONFIG["descriptions"]["challenge_id"],
                            max_length=FIELD_CONFIG["lengths"]["challenge_id"])
    challenge_name: str = Field(..., description=FIELD_CONFIG["descriptions"]["challenge_name"],
                              max_length=FIELD_CONFIG["lengths"]["challenge_name"])
    play_cnt: int = Field(..., description=FIELD_CONFIG["descriptions"]["play_cnt"])
    publish_cnt: int = Field(..., description=FIELD_CONFIG["descriptions"]["publish_cnt"])
    cover_url: Optional[str] = Field(None, description=FIELD_CONFIG["descriptions"]["cover_url"],
                                   max_length=FIELD_CONFIG["lengths"]["cover_url"])
    score: int = Field(..., description=FIELD_CONFIG["descriptions"]["score"])
    avg_play_cnt: int = Field(..., description=FIELD_CONFIG["descriptions"]["avg_play_cnt"])
    create_time: int = Field(..., description=FIELD_CONFIG["descriptions"]["create_time"])
    origin_trend_str: Optional[str] = Field(None, description=FIELD_CONFIG["descriptions"]["origin_trend_str"],
                                          max_length=FIELD_CONFIG["lengths"]["origin_trend_str"])
    item_list: Optional[str] = Field(default="null", description=FIELD_CONFIG["descriptions"]["item_list"])
    trends: Optional[List[TopicTrend]] = Field(None, description=FIELD_CONFIG["descriptions"]["trends"])
    challenge_type: int = Field(default=0, description=FIELD_CONFIG["descriptions"]["challenge_type"])
    is_favorite: bool = Field(default=False, description=FIELD_CONFIG["descriptions"]["is_favorite"])
    is_recommend: bool = Field(default=False, description=FIELD_CONFIG["descriptions"]["is_recommend"])
    show_rank: int = Field(default=0, description=FIELD_CONFIG["descriptions"]["show_rank"])
    real_rank: int = Field(default=0, description=FIELD_CONFIG["descriptions"]["real_rank"])
    origin_rank: int = Field(default=0, description=FIELD_CONFIG["descriptions"]["origin_rank"])
    related_event: Optional[Dict] = Field(None, description=FIELD_CONFIG["descriptions"]["related_event"])
    record_create_time: datetime = Field(default_factory=datetime.now, description=FIELD_CONFIG["descriptions"]["record_create_time"])
    record_update_time: datetime = Field(default_factory=datetime.now, description=FIELD_CONFIG["descriptions"]["record_update_time"])

    class Config:
        orm_mode = True

class DouhotBillboardTopicCreate(DouhotBillboardTopicBase):
    """创建抖音热榜话题模型"""
    pass

class DouhotBillboardTopicUpdate(DouhotBillboardTopicBase):
    """更新抖音热榜话题模型"""
    pass

class DouhotBillboardTopicInDB(DouhotBillboardTopicBase):
    """数据库中的抖音热榜话题模型"""
    # id: int
    # created_at: datetime
    # updated_at: datetime

    # class Config:
    #     orm_mode = True
    pass

class DouhotBillboardTopicRepository(BaseRepository):
    """抖音热榜话题数据仓储"""
    def __init__(self, db: Session):
        super().__init__(DouhotBillboardTopic, db)

    def get_by_partition_key(self, ds: datetime, challenge_id: str, topic_tag1: int, 
                           topic_tag2: int, date_window: int, billboard_type: int) -> Optional[DouhotBillboardTopic]:
        """根据分区键获取数据"""
        return (self.db.query(self.model)
                .filter(self.model.ds == ds,
                       self.model.challenge_id == challenge_id,
                       self.model.topic_tag1 == topic_tag1,
                       self.model.topic_tag2 == topic_tag2,
                       self.model.date_window == date_window,
                       self.model.billboard_type == billboard_type)
                .first())
    
    async def upsert_topic(self, topic_data: Dict[str, Any]) -> DouhotBillboardTopic:
        """更新或插入话题数据"""
        # 确保有ds字段，如果没有则使用当前日期
        if "ds" not in topic_data:
            topic_data["ds"] = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0).strftime("%Y-%m-%d")
            
        # 查找是否存在相同分区的记录
        existing = self.get_by_partition_key(
            ds=topic_data["ds"],
            challenge_id=topic_data["challenge_id"],
            topic_tag1=topic_data["topic_tag1"],
            topic_tag2=topic_data["topic_tag2"],
            date_window=topic_data["date_window"],
            billboard_type=topic_data["billboard_type"]
        )
        
        if existing:
            # 更新现有记录
            for key, value in topic_data.items():
                if key not in ["id", "created_at", "ds"]:  # 保留原始 id、创建时间和分区日期
                    setattr(existing, key, value)
            existing.updated_at = datetime.now()
            self.db.merge(existing)
            return existing
        else:
            # 创建新记录
            topic = DouhotBillboardTopic(**topic_data)
            self.db.add(topic)
            return topic
    
    def get_by_date_range(self, start_date: datetime, end_date: datetime, 
                         billboard_type: Optional[int] = None) -> List[DouhotBillboardTopic]:
        """获取指定日期范围的数据"""
        query = (self.db.query(self.model)
                .filter(self.model.ds >= start_date)
                .filter(self.model.ds <= end_date))
        
        if billboard_type is not None:
            query = query.filter(self.model.billboard_type == billboard_type)
            
        return query.order_by(self.model.ds.desc(), 
                            self.model.score.desc()).all()

    def get_latest_by_ds(self, topic_tag1: Optional[int] = None, 
                        topic_tag2: Optional[int] = None, date_window: Optional[int] = None, 
                        billboard_type: Optional[int] = None, limit: int = 50
                        ) -> List[DouhotBillboardTopic]:
        """获取最新日期的视频数据
        
        """
        # 先获取最新的日期
        latest_ds = (self.db.query(self.model.ds)
                    .order_by(self.model.ds.desc())
                    .first())
        if not latest_ds:
            return []
            
        # 构建查询
        query = (self.db.query(self.model)
                .filter(self.model.ds == latest_ds[0]))
        
        if topic_tag1:
            query = query.filter(self.model.topic_tag1 == topic_tag1)
            
        if topic_tag2:
            query = query.filter(self.model.topic_tag2 == topic_tag2)
            
        if date_window:
            query = query.filter(self.model.date_window == date_window)
            
        if billboard_type:
            query = query.filter(self.model.billboard_type == billboard_type)
            
        # 按score排序并限制返回数量
        # data = query.order_by(self.model.score.desc()).limit(limit).all()
        print("query: ", query)
        return (query.order_by(self.model.score.desc())
                .limit(limit)
                .all())
    
    def cleanup_old_partitions(self, keep_days: int = 7) -> None:
        """清理旧的分区数据，默认保留7天"""
        cutoff_date = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0) - timedelta(days=keep_days)
        
        # 使用 SQL 直接删除分区
        sql = f"""
        ALTER TABLE {self.model.__tablename__}
        DROP PARTITION p_{cutoff_date.strftime('%Y%m%d')}
        """
        try:
            self.db.execute(sql)
            self.db.commit()
        except Exception as e:
            self.db.rollback()
            logging.error(f"清理旧分区失败: {str(e)}")

    def get_by_challenge_id(self, challenge_id: str, date_window: int, ds: datetime) -> Optional[DouhotBillboardTopic]:
        """根据话题ID获取数据"""
        return self.db.query(self.model).filter(
            self.model.challenge_id == challenge_id,
            self.model.date_window == date_window,
            self.model.ds == ds
        ).first()

    def get_top_topics(self, date_window: int, limit: int = 50) -> List[DouhotBillboardTopic]:
        """获取指定时间窗口的热门话题"""
        return self.db.query(self.model).filter(
            self.model.date_window == date_window
        ).order_by(
            self.model.score.desc()
        ).limit(limit).all()

    def get_topic_trends(self, challenge_id: str, date_window: int, days: int = 14) -> List[DouhotBillboardTopic]:
        """获取指定话题的趋势数据"""
        cutoff_date = datetime.now() - timedelta(days=days)
        return self.db.query(self.model).filter(
            self.model.challenge_id == challenge_id,
            self.model.date_window == date_window,
            self.model.ds >= cutoff_date
        ).order_by(
            self.model.ds.asc()
        ).all()

    def get_trending_topics(self, date_window: int, days: int = 7, min_score: int = 1000000) -> List[DouhotBillboardTopic]:
        """获取持续上升的热门话题"""
        cutoff_date = datetime.now() - timedelta(days=days)
        return self.db.query(self.model).filter(
            self.model.date_window == date_window,
            self.model.ds >= cutoff_date,
            self.model.score >= min_score
        ).order_by(
            self.model.score.desc()
        ).all()
