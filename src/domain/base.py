"""领域模型基础设施"""
from datetime import datetime
from typing import TypeVar, Generic, Optional, List, Any, Dict
from pydantic import BaseModel, ConfigDict
from sqlalchemy import Column, Integer, DateTime
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import Session

from src.utils.logger import log_info, log_error, log_execution_time
from src.config.logging_config import LoggerName

# SQLAlchemy 基础模型
Base = declarative_base()

class TimestampMixin:
    """时间戳混入类"""
    created_at = Column(DateTime, default=datetime.utcnow, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment="更新时间")

class BaseDBModel(Base):
    """数据库模型基类"""
    __abstract__ = True
    id = Column(Integer, primary_key=True, autoincrement=True)

# Pydantic 模型
class BaseSchema(BaseModel):
    """API模型基类"""
    model_config = ConfigDict(from_attributes=True)

# 仓储泛型类型
ModelType = TypeVar("ModelType")
CreateSchemaType = TypeVar("CreateSchemaType")
UpdateSchemaType = TypeVar("UpdateSchemaType")

class BaseRepository(Generic[ModelType, CreateSchemaType, UpdateSchemaType]):
    """通用仓储基类"""
    
    def __init__(self, model: type[ModelType], db: Session):
        self.model = model
        self.db = db
    
    @log_execution_time(LoggerName.DOMAIN)
    def get(self, id: int) -> Optional[ModelType]:
        """获取单个实体"""
        try:
            result = self.db.query(self.model).filter(self.model.id == id).first()
            log_info(LoggerName.DOMAIN,
                    f"获取{self.model.__name__}实体",
                    id=id,
                    found=result is not None
            )
            return result
        except Exception as e:
            log_error(LoggerName.DOMAIN,
                     f"获取{self.model.__name__}实体失败",
                     id=id,
                     error_type=type(e).__name__,
                     error_message=str(e)
            )
            raise
    
    @log_execution_time(LoggerName.DOMAIN)
    def list(self, skip: int = 0, limit: int = 100) -> List[ModelType]:
        """获取实体列表"""
        try:
            results = self.db.query(self.model).offset(skip).limit(limit).all()
            log_info(LoggerName.DOMAIN,
                    f"获取{self.model.__name__}实体列表",
                    skip=skip,
                    limit=limit,
                    count=len(results)
            )
            return results
        except Exception as e:
            log_error(LoggerName.DOMAIN,
                     f"获取{self.model.__name__}实体列表失败",
                     skip=skip,
                     limit=limit,
                     error_type=type(e).__name__,
                     error_message=str(e)
            )
            raise
    
    @log_execution_time(LoggerName.DOMAIN)
    def create(self, schema: CreateSchemaType) -> ModelType:
        """创建实体"""
        try:
            db_obj = self.model(**schema.model_dump())
            self.db.add(db_obj)
            self.db.commit()
            self.db.refresh(db_obj)
            log_info(LoggerName.DOMAIN,
                    f"创建{self.model.__name__}实体成功",
                    id=db_obj.id
            )
            return db_obj
        except Exception as e:
            log_error(LoggerName.DOMAIN,
                     f"创建{self.model.__name__}实体失败",
                     schema=schema.model_dump(),
                     error_type=type(e).__name__,
                     error_message=str(e)
            )
            self.db.rollback()
            raise
    
    @log_execution_time(LoggerName.DOMAIN)
    def update(self, id: int, schema: UpdateSchemaType) -> Optional[ModelType]:
        """更新实体"""
        try:
            db_obj = self.get(id)
            if not db_obj:
                log_info(LoggerName.DOMAIN,
                        f"更新{self.model.__name__}实体失败：实体不存在",
                        id=id
                )
                return None
            
            for key, value in schema.model_dump(exclude_unset=True).items():
                setattr(db_obj, key, value)
            
            self.db.commit()
            self.db.refresh(db_obj)
            log_info(LoggerName.DOMAIN,
                    f"更新{self.model.__name__}实体成功",
                    id=id
            )
            return db_obj
        except Exception as e:
            log_error(LoggerName.DOMAIN,
                     f"更新{self.model.__name__}实体失败",
                     id=id,
                     schema=schema.model_dump(),
                     error_type=type(e).__name__,
                     error_message=str(e)
            )
            self.db.rollback()
            raise
    
    @log_execution_time(LoggerName.DOMAIN)
    def delete(self, id: int) -> bool:
        """删除实体"""
        try:
            db_obj = self.get(id)
            if not db_obj:
                log_info(LoggerName.DOMAIN,
                        f"删除{self.model.__name__}实体失败：实体不存在",
                        id=id
                )
                return False
            
            self.db.delete(db_obj)
            self.db.commit()
            log_info(LoggerName.DOMAIN,
                    f"删除{self.model.__name__}实体成功",
                    id=id
            )
            return True
        except Exception as e:
            log_error(LoggerName.DOMAIN,
                     f"删除{self.model.__name__}实体失败",
                     id=id,
                     error_type=type(e).__name__,
                     error_message=str(e)
            )
            self.db.rollback()
            raise
