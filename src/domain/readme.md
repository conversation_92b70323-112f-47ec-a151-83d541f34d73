# 用户账号体系设计文档

## 设计概述

这个账号体系设计基于"关注点分离"原则,将用户核心信息与身份认证方式解耦,实现了一个灵活可扩展的用户系统。主要特点包括:

- **一个用户可以拥有多种登录方式** (邮箱、手机号、第三方账号等)
- **身份认证和用户信息分离存储**,提高安全性
- **支持基于角色的权限管理**
- **用户配置和偏好独立存储**,便于个性化
- **易于扩展**,无需修改核心用户表结构即可添加新的认证方式

## 数据库表结构

### 1. 用户核心表 (users)

存储用户的基本信息,是整个账号体系的核心表。

| 字段名 | 类型 | 说明 | 特别说明 |
|--------|------|------|----------|
| id | String(36) | 主键,UUID | 系统内部使用的唯一标识 |
| username | String(50) | 用户名 | 唯一,可选,用于登录 |
| nickname | String(50) | 昵称 | 必填,用于显示 |
| avatar | String(255) | 头像URL | 可选 |
| status | Integer | 用户状态 | 1=正常,0=禁用 |
| created_at | DateTime | 创建时间 | 自动设置 |
| updated_at | DateTime | 更新时间 | 自动更新 |
| last_login_at | DateTime | 最后登录时间 | 登录时更新 |
| last_login_ip | String(45) | 最后登录IP | 登录时更新 |

### 2. 用户身份凭证表 (user_identities)

存储用户的认证信息,支持多种登录方式。一个用户可以有多个身份凭证。

| 字段名 | 类型 | 说明 | 特别说明 |
|--------|------|------|----------|
| id | String(36) | 主键,UUID | 系统内部使用的唯一标识 |
| user_id | String(36) | 用户ID | 关联到users表的id |
| identity_type | String(20) | 身份类型 | email, phone, wechat, qq等 |
| identifier | String(100) | 标识符 | 如邮箱地址,手机号等 |
| credential | String(255) | 凭证 | 如密码哈希,令牌等 |
| verified | Boolean | 是否已验证 | 默认false |
| created_at | DateTime | 创建时间 | 自动设置 |
| updated_at | DateTime | 更新时间 | 自动更新 |

> **特别说明**: `identity_type`和`identifier`组合必须唯一,即同一种类型的标识符不能重复。

### 3. 用户设置表 (user_settings)

存储用户的个性化设置和偏好。

| 字段名 | 类型 | 说明 | 特别说明 |
|--------|------|------|----------|
| user_id | String(36) | 主键,用户ID | 关联到users表的id |
| language | String(10) | 语言偏好 | 默认'zh-CN' |
| timezone | String(50) | 时区 | 默认'Asia/Shanghai' |
| notification_enabled | Boolean | 是否启用通知 | 默认true |
| theme | String(20) | 界面主题 | 默认'light' |
| privacy_level | Integer | 隐私级别 | 默认0 |

### 4. 角色表 (roles)

定义系统角色,用于权限管理。

| 字段名 | 类型 | 说明 | 特别说明 |
|--------|------|------|----------|
| id | String(36) | 主键,UUID | 系统内部使用的唯一标识 |
| name | String(50) | 角色名称 | 唯一,如'admin','user' |
| description | Text | 角色描述 | 可选 |
| created_at | DateTime | 创建时间 | 自动设置 |
| updated_at | DateTime | 更新时间 | 自动更新 |

### 5. 用户角色关联表 (user_roles)

用户和角色的多对多关系表。

| 字段名 | 类型 | 说明 | 特别说明 |
|--------|------|------|----------|
| user_id | String(36) | 用户ID | 联合主键,关联users表 |
| role_id | String(36) | 角色ID | 联合主键,关联roles表 |
| created_at | DateTime | 创建时间 | 自动设置 |

## 表关系图

```
+---------+       +----------------+
|  users  |<------| user_settings  |
+---------+       +----------------+
    |  |
    |  |
    |  +-----------+
    |              |
    v              v
+--------------+  +------------+
|user_identities|  | user_roles |
+--------------+  +------------+
                     |
                     |
                     v
                  +-------+
                  | roles |
                  +-------+
```

## 特别说明的点

1. **多身份认证**:
   - 一个用户可以绑定多种登录方式
   - 用户可以使用任何一种认证方式登录系统
   - 添加新的认证方式不需要修改用户表结构

2. **密码安全**:
   - 密码经过bcrypt算法哈希后存储
   - UserIdentity类提供静态方法hash_password和verify_password进行密码管理
   - 身份凭证与用户基本信息分离存储,提高安全性

3. **用户状态管理**:
   - 用户状态字段(status)用于控制账号是否可用
   - 支持用户账号禁用/启用功能

4. **扩展性**:
   - 可以轻松添加其他第三方登录方式(如GitHub、Apple ID等)
   - UserSetting表可以根据需要扩展,添加更多用户偏好设置

5. **唯一性约束**:
   - username是唯一的,但是可选的
   - 同一类型的身份标识符(如邮箱)是唯一的
   - 不同类型的标识符(如邮箱和手机号)可以指向同一个用户

6. **主键策略**:
   - 使用UUID作为主键,避免顺序ID暴露用户数量信息
   - UUID还便于在分布式系统中生成唯一ID

## 使用示例

### 1. 创建新用户(邮箱注册)

```python
# 使用UserRepository创建用户
user_repo = UserRepository(db)

# 用户基本信息
user_data = {
    "nickname": "张三",
    "username": "zhangsan"  # 可选
}

# 邮箱身份信息
identity_data = {
    "identity_type": "email",
    "identifier": "<EMAIL>",
    "password": "secure_password"  # 会自动进行哈希处理
}

# 创建用户
user = user_repo.create_user(user_data, identity_data)
```

### 2. 用户登录验证

```python
# 验证用户凭证
user = user_repo.verify_credential(
    identity_type="email",
    identifier="<EMAIL>",
    credential="secure_password"
)

if user:
    # 登录成功,更新登录信息
    user_repo.update_login_info(user.id, client_ip)
else:
    # 登录失败
    pass
```

### 3. 添加其他登录方式

```python
# 为已有用户添加手机号登录
identity = UserIdentity(
    id=str(uuid.uuid4()),
    user_id=user.id,
    identity_type="phone",
    identifier="13800138000",
    credential=UserIdentity.hash_password("phone_password"),
    verified=True
)
db.add(identity)
db.commit()
```