"""用户相关的存储库"""
from datetime import datetime
from typing import List, Optional, Dict, Any
import uuid
from sqlalchemy.orm import Session
from sqlalchemy import or_

from src.domain.user import User, UserCreate
from src.domain.user_identity import UserIdentity, UserIdentityCreate
from src.domain.user_setting import UserSetting, UserSettingCreate
from src.domain.role import Role, RoleCreate

class UserRepository:
    """用户相关操作的存储库"""
    
    def __init__(self, db: Session):
        self.db = db
    
    def get_by_id(self, user_id: str) -> Optional[User]:
        """根据ID获取用户"""
        return self.db.query(User).filter(User.id == user_id).first()
    
    def get_by_identity(self, identity_type: str, identifier: str) -> Optional[User]:
        """根据身份标识获取用户"""
        identity = self.db.query(UserIdentity).filter(
            UserIdentity.identity_type == identity_type,
            UserIdentity.identifier == identifier
        ).first()
        
        if identity:
            return self.get_by_id(identity.user_id)
        return None
    
    def create_user(self, user_data: Dict[str, Any], identity_data: Dict[str, Any] = None) -> User:
        """创建新用户
        
        Args:
            user_data: 用户基本信息
            identity_data: 身份认证信息（可选）
            
        Returns:
            新创建的用户对象
        """
        # 创建用户
        user_id = str(uuid.uuid4())
        user_create = UserCreate(**user_data)
        user = User(
            id=user_id,
            username=user_create.username,
            nickname=user_create.nickname,
            avatar=user_create.avatar,
            created_at=datetime.now()
        )
        self.db.add(user)
        
        # 如果提供了身份信息，创建身份
        if identity_data:
            identity_data['user_id'] = user_id
            if 'password' in identity_data:
                # 如果是密码类型的凭证，进行哈希处理
                password = identity_data.pop('password')
                identity_data['credential'] = UserIdentity.hash_password(password)
            
            identity_create = UserIdentityCreate(**identity_data)
            identity = UserIdentity(
                id=str(uuid.uuid4()),
                user_id=user_id,
                identity_type=identity_create.identity_type,
                identifier=identity_create.identifier,
                credential=identity_create.credential,
                created_at=datetime.now()
            )
            self.db.add(identity)
        
        # 创建用户设置
        setting = UserSetting(user_id=user_id)
        self.db.add(setting)
        
        # 分配默认用户角色
        user_role = self.db.query(Role).filter(Role.name == "user").first()
        if not user_role:
            # 如果用户角色不存在，创建它
            user_role = Role(
                name="user",
                description="普通用户"
            )
            self.db.add(user_role)
            self.db.flush()
        
        # 将角色分配给用户
        user.roles.append(user_role)
        
        # 提交事务
        self.db.commit()
        self.db.refresh(user)
        
        return user
    
    def verify_credential(self, identity_type: str, identifier: str, credential: str) -> Optional[User]:
        """验证用户凭证
        
        Args:
            identity_type: 身份类型（如email）
            identifier: 标识符（如邮箱地址）
            credential: 凭证（如密码）
            
        Returns:
            验证成功返回用户对象，否则返回None
        """
        identity = self.db.query(UserIdentity).filter(
            UserIdentity.identity_type == identity_type,
            UserIdentity.identifier == identifier
        ).first()
        
        if not identity:
            return None
        
        # 验证凭证
        if UserIdentity.verify_password(credential, identity.credential):
            return self.get_by_id(identity.user_id)
        
        return None
    
    def update_login_info(self, user_id: str, ip: str) -> None:
        """更新用户登录信息"""
        user = self.get_by_id(user_id)
        if user:
            user.last_login_at = datetime.now()
            user.last_login_ip = ip
            self.db.commit() 