"""角色与权限模型"""
from datetime import datetime
import uuid
from typing import List, Optional
from sqlalchemy import Column, String, DateTime, Text
from sqlalchemy.orm import relationship
from pydantic import BaseModel

from src.domain.base import Base
from src.domain.user import user_roles

class Role(Base):
    """角色表"""
    __tablename__ = 'roles'
    
    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    name = Column(String(50), unique=True, nullable=False)
    description = Column(Text, nullable=True)
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)
    
    # 关联关系
    users = relationship("User", secondary=user_roles, back_populates="roles")
    
    def __repr__(self):
        return f"<Role {self.name}>"


class RoleCreate(BaseModel):
    """角色创建模型"""
    name: str
    description: Optional[str] = None 