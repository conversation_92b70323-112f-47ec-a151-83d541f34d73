"""用户身份凭证模型"""
from datetime import datetime
import uuid
from typing import Optional, List
from sqlalchemy import Column, String, DateTime, <PERSON><PERSON><PERSON>, Integer, ForeignKey, UniqueConstraint
from sqlalchemy.orm import relationship, Session
from pydantic import BaseModel, Field, validator, EmailStr
from passlib.context import CryptContext

from src.domain.base import Base
from src.domain.user import User

# 密码哈希工具
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

class UserIdentity(Base):
    """用户身份凭证表"""
    __tablename__ = 'user_identities'
    
    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    user_id = Column(String(36), ForeignKey('users.id', ondelete='CASCADE'), nullable=False)
    identity_type = Column(String(20), nullable=False)  # email, phone, wechat, qq等
    identifier = Column(String(100), nullable=False)  # 标识符（如邮箱，手机号）
    credential = Column(String(255), nullable=True)  # 凭证（如密码哈希，token）
    verified = Column(Boolean, default=False)
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)
    
    # 关联关系
    user = relationship("User", back_populates="identities")
    
    __table_args__ = (
        # 同一类型的标识符必须唯一
        UniqueConstraint('identity_type', 'identifier'),
    )
    
    def __repr__(self):
        return f"<UserIdentity {self.identity_type}:{self.identifier}>"
    
    @staticmethod
    def hash_password(password: str) -> str:
        """对密码进行哈希处理"""
        return pwd_context.hash(password)
    
    @staticmethod
    def verify_password(plain_password: str, hashed_password: str) -> bool:
        """验证密码是否正确"""
        return pwd_context.verify(plain_password, hashed_password)


class UserIdentityCreate(BaseModel):
    """身份凭证创建模型"""
    user_id: str
    identity_type: str
    identifier: str
    credential: Optional[str] = None
    
    @validator('identity_type')
    def validate_identity_type(cls, v):
        allowed_types = ['email', 'phone', 'wechat', 'qq', 'weibo']
        if v not in allowed_types:
            raise ValueError(f'身份类型必须是以下之一: {", ".join(allowed_types)}')
        return v 