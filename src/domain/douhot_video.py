"""抖音热榜视频领域模型"""
from datetime import datetime, timedelta
from typing import Dict, Optional, List, Union, Any
from sqlalchemy import Column, String, Integer, BigInteger, Boolean, Float, DateTime, JSON, UniqueConstraint, func
from sqlalchemy.orm import Session
from pydantic import Field, validator

from .base import (
    BaseDBModel,
    TimestampMixin,
    BaseSchema,
    BaseRepository
)

# 字段配置
FIELD_CONFIG = {
    # 字段最大长度
    "lengths": {
        "ds": 10,
        "item_id": 50,
        "item_title": 2000,
        "item_cover_url": 1000,
        "nick_name": 200,
        "avatar_url": 500,
        "item_url": 1000,
    },
    # 字段描述
    "descriptions": {
        "ds": "数据日期，格式：YYYY-MM-DD",
        "video_tag1": "一级分类",
        "video_tag2": "二级分类",
        "video_tag3": "三级分类",
        "billboard_type": "榜单类型(1001:视频总榜,1002:低粉爆款,1003:高完播,1004:高涨粉,1005:高点赞)",
        "date_window": "时间窗口(小时)",
        "item_id": "视频ID",
        "item_title": "视频标题",
        "item_cover_url": "视频封面URL",
        "item_duration": "视频时长(毫秒)",
        "nick_name": "作者昵称",
        "avatar_url": "作者头像URL",
        "fans_cnt": "粉丝数",
        "play_cnt": "播放数",
        "publish_time": "发布时间",
        "score": "热度分数",
        "item_url": "视频URL",
        "like_cnt": "点赞数",
        "follow_cnt": "关注数",
        "follow_rate": "关注率",
        "like_rate": "点赞率",
        "media_type": "媒体类型",
        "favorite_id": "收藏ID",
        "is_favorite": "是否收藏",
        "image_cnt": "图片数量",
        "is_recommend": "是否推荐",
        "related_event": "相关事件",
        "record_create_time": "记录创建时间",
        "record_update_time": "记录更新时间"
    }
}

class DouhotBillboardVideo(BaseDBModel, TimestampMixin):
    """抖音热榜视频数据表模型"""
    __tablename__ = "douhot_billboard_videos"

    # 分区字段
    ds = Column(String(FIELD_CONFIG["lengths"]["ds"]), nullable=False, index=True, 
                comment=FIELD_CONFIG["descriptions"]["ds"])
    
    # 标签字段
    video_tag1 = Column(Integer, nullable=False,
                      comment=FIELD_CONFIG["descriptions"]["video_tag1"])
    video_tag2 = Column(Integer, nullable=False,
                       comment=FIELD_CONFIG["descriptions"]["video_tag2"])
    video_tag3 = Column(Integer, nullable=False,
                       comment=FIELD_CONFIG["descriptions"]["video_tag3"])
    billboard_type = Column(Integer, nullable=False,
                     comment=FIELD_CONFIG["descriptions"]["billboard_type"])
    date_window = Column(Integer, nullable=False,
                        comment=FIELD_CONFIG["descriptions"]["date_window"])

    item_id = Column(String(FIELD_CONFIG["lengths"]["item_id"]), index=True,
                    nullable=False, comment=FIELD_CONFIG["descriptions"]["item_id"])
    item_title = Column(String(FIELD_CONFIG["lengths"]["item_title"]), nullable=False,
                       comment=FIELD_CONFIG["descriptions"]["item_title"])
    item_cover_url = Column(String(FIELD_CONFIG["lengths"]["item_cover_url"]), nullable=False,
                           comment=FIELD_CONFIG["descriptions"]["item_cover_url"])
    item_duration = Column(Integer, nullable=False,
                          comment=FIELD_CONFIG["descriptions"]["item_duration"])
    nick_name = Column(String(FIELD_CONFIG["lengths"]["nick_name"]), nullable=False,
                      comment=FIELD_CONFIG["descriptions"]["nick_name"])
    avatar_url = Column(String(FIELD_CONFIG["lengths"]["avatar_url"]), nullable=False,
                       comment=FIELD_CONFIG["descriptions"]["avatar_url"])
    fans_cnt = Column(BigInteger, default=0,
                     comment=FIELD_CONFIG["descriptions"]["fans_cnt"])
    play_cnt = Column(BigInteger, default=0,
                     comment=FIELD_CONFIG["descriptions"]["play_cnt"])
    publish_time = Column(DateTime, nullable=False,
                         comment=FIELD_CONFIG["descriptions"]["publish_time"])
    score = Column(BigInteger, default=0,
                  comment=FIELD_CONFIG["descriptions"]["score"])
    item_url = Column(String(FIELD_CONFIG["lengths"]["item_url"]), nullable=False,
                     comment=FIELD_CONFIG["descriptions"]["item_url"])
    like_cnt = Column(BigInteger, default=0,
                     comment=FIELD_CONFIG["descriptions"]["like_cnt"])
    follow_cnt = Column(BigInteger, default=0,
                       comment=FIELD_CONFIG["descriptions"]["follow_cnt"])
    follow_rate = Column(Float, default=0.0,
                        comment=FIELD_CONFIG["descriptions"]["follow_rate"])
    like_rate = Column(Float, default=0.0,
                      comment=FIELD_CONFIG["descriptions"]["like_rate"])
    media_type = Column(Integer, default=0,
                       comment=FIELD_CONFIG["descriptions"]["media_type"])
    favorite_id = Column(BigInteger, default=0,
                        comment=FIELD_CONFIG["descriptions"]["favorite_id"])
    is_favorite = Column(Boolean, default=False,
                        comment=FIELD_CONFIG["descriptions"]["is_favorite"])
    image_cnt = Column(Integer, default=0,
                      comment=FIELD_CONFIG["descriptions"]["image_cnt"])
    is_recommend = Column(Boolean, default=False,
                        comment=FIELD_CONFIG["descriptions"]["is_recommend"])
    related_event = Column(JSON, nullable=True,
                          comment=FIELD_CONFIG["descriptions"]["related_event"])
    
    # 记录时间
    record_create_time = Column(DateTime, nullable=False, default=func.now(),
                               comment=FIELD_CONFIG["descriptions"]["record_create_time"])
    record_update_time = Column(DateTime, nullable=False, default=func.now(), onupdate=func.now(),
                               comment=FIELD_CONFIG["descriptions"]["record_update_time"])

    # 定义联合唯一索引，确保分区内数据唯一性
    __table_args__ = (
        # 联合唯一约束：同一天内的数据按这些字段唯一
        UniqueConstraint('ds', 'item_id', 'video_tag1', 'video_tag2', 'date_window', 'billboard_type', 
                        name='uk_daily_video_unique'),
    )

# API模型
class DouhotBillboardVideoBase(BaseSchema):
    """抖音热榜视频基础模型"""
    item_id: str = Field(..., description=FIELD_CONFIG["descriptions"]["item_id"],
                        max_length=FIELD_CONFIG["lengths"]["item_id"])
    item_title: str = Field(..., description=FIELD_CONFIG["descriptions"]["item_title"],
                           max_length=FIELD_CONFIG["lengths"]["item_title"])
    item_cover_url: str = Field(..., description=FIELD_CONFIG["descriptions"]["item_cover_url"],
                               max_length=FIELD_CONFIG["lengths"]["item_cover_url"])
    item_duration: int = Field(..., description=FIELD_CONFIG["descriptions"]["item_duration"])
    nick_name: str = Field(..., description=FIELD_CONFIG["descriptions"]["nick_name"],
                          max_length=FIELD_CONFIG["lengths"]["nick_name"])
    avatar_url: str = Field(..., description=FIELD_CONFIG["descriptions"]["avatar_url"],
                           max_length=FIELD_CONFIG["lengths"]["avatar_url"])
    fans_cnt: int = Field(default=0, description=FIELD_CONFIG["descriptions"]["fans_cnt"])
    play_cnt: int = Field(default=0, description=FIELD_CONFIG["descriptions"]["play_cnt"])
    publish_time: datetime = Field(..., description=FIELD_CONFIG["descriptions"]["publish_time"])
    score: int = Field(default=0, description=FIELD_CONFIG["descriptions"]["score"])
    item_url: str = Field(..., description=FIELD_CONFIG["descriptions"]["item_url"],
                         max_length=FIELD_CONFIG["lengths"]["item_url"])
    like_cnt: int = Field(default=0, description=FIELD_CONFIG["descriptions"]["like_cnt"])
    follow_cnt: int = Field(default=0, description=FIELD_CONFIG["descriptions"]["follow_cnt"])
    follow_rate: float = Field(default=0.0, description=FIELD_CONFIG["descriptions"]["follow_rate"])
    like_rate: float = Field(default=0.0, description=FIELD_CONFIG["descriptions"]["like_rate"])
    media_type: int = Field(default=0, description=FIELD_CONFIG["descriptions"]["media_type"])
    favorite_id: int = Field(default=0, description=FIELD_CONFIG["descriptions"]["favorite_id"])
    is_favorite: bool = Field(default=False, description=FIELD_CONFIG["descriptions"]["is_favorite"])
    image_cnt: int = Field(default=0, description=FIELD_CONFIG["descriptions"]["image_cnt"])
    is_recommend: bool = Field(default=False, description=FIELD_CONFIG["descriptions"]["is_recommend"])
    related_event: Optional[Dict] = Field(None, description=FIELD_CONFIG["descriptions"]["related_event"])
    record_create_time: datetime = Field(default_factory=datetime.now, description=FIELD_CONFIG["descriptions"]["record_create_time"])
    record_update_time: datetime = Field(default_factory=datetime.now, description=FIELD_CONFIG["descriptions"]["record_update_time"])
    video_tag1: Optional[int] = Field(None, description=FIELD_CONFIG["descriptions"]["video_tag1"])
    video_tag2: Optional[int] = Field(None, description=FIELD_CONFIG["descriptions"]["video_tag2"])
    video_tag3: Optional[int] = Field(None, description=FIELD_CONFIG["descriptions"]["video_tag3"])
    billboard_type: int = Field(..., description=FIELD_CONFIG["descriptions"]["billboard_type"])
    date_window: int = Field(..., description=FIELD_CONFIG["descriptions"]["date_window"])
    ds: str = Field(..., description=FIELD_CONFIG["descriptions"]["ds"])

    class Config:
        orm_mode = True

class DouhotBillboardVideoCreate(DouhotBillboardVideoBase):
    """创建抖音热榜视频模型"""
    pass

class DouhotBillboardVideoUpdate(DouhotBillboardVideoBase):
    """更新抖音热榜视频模型"""
    pass

class DouhotBillboardVideoInDB(DouhotBillboardVideoBase):
    """数据库中的抖音热榜视频模型"""
    id: int
    created_at: datetime
    updated_at: datetime

# 仓储层
class DouhotBillboardVideoRepository(BaseRepository[DouhotBillboardVideo, DouhotBillboardVideoCreate, DouhotBillboardVideoUpdate]):
    """抖音热榜视频数据仓储"""
    
    def __init__(self, db: Session):
        super().__init__(DouhotBillboardVideo, db)
    
    def get_by_partition_key(self, ds: str, item_id: str, video_tag1: int, 
                           video_tag2: int, date_window: int, billboard_type: int) -> Optional[DouhotBillboardVideo]:
        """根据分区键获取数据"""
        return (self.db.query(self.model)
                .filter(self.model.ds == ds,
                       self.model.item_id == item_id,
                       self.model.video_tag1 == video_tag1,
                       self.model.video_tag2 == video_tag2,
                       self.model.date_window == date_window,
                       self.model.billboard_type == billboard_type)
                .first())
    
    async def upsert_video(self, video_data: Dict[str, Any]) -> DouhotBillboardVideo:
        """更新或插入视频数据"""
        # 确保有ds字段，如果没有则使用当前日期
        if "ds" not in video_data:
            video_data["ds"] = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0).strftime("%Y-%m-%d")
            
        # 查找是否存在相同分区的记录
        existing = self.get_by_partition_key(
            ds=video_data["ds"],
            item_id=video_data["item_id"],
            video_tag1=video_data["video_tag1"],
            video_tag2=video_data["video_tag2"],
            date_window=video_data["date_window"],
            billboard_type=video_data["billboard_type"]
        )
        
        if existing:
            # 更新现有记录
            for key, value in video_data.items():
                if key not in ["id", "created_at", "ds"]:  # 保留原始 id、创建时间和分区日期
                    setattr(existing, key, value)
            existing.updated_at = datetime.now()
            self.db.merge(existing)
            return existing
        else:
            # 创建新记录
            video = DouhotBillboardVideo(**video_data)
            self.db.add(video)
            return video
    
    def get_by_date_range(self, start_date: str, end_date: str, 
                         billboard_type: Optional[int] = None) -> List[DouhotBillboardVideo]:
        """获取指定日期范围的数据"""
        query = (self.db.query(self.model)
                .filter(self.model.ds >= start_date)
                .filter(self.model.ds <= end_date))
        
        if billboard_type is not None:
            query = query.filter(self.model.billboard_type == billboard_type)
            
        return query.order_by(self.model.ds.desc(), 
                            self.model.score.desc()).all()

    def get_latest_by_ds(self, video_tag1: int, 
                        video_tag2: int, date_window: int, 
                        billboard_type: int, limit: int = 50
                        ) -> List[DouhotBillboardVideo]:
        """获取最新日期的视频数据
        
        """
        # 先获取最新的日期
        latest_ds = (self.db.query(self.model.ds)
                    .order_by(self.model.ds.desc())
                    .first())
        
        if not latest_ds:
            return []
            
        # 构建查询
        query = (self.db.query(self.model)
                .filter(self.model.ds == latest_ds[0]))
        
        if video_tag1:
            query = query.filter(self.model.video_tag1 == video_tag1)
            
        if video_tag2:
            query = query.filter(self.model.video_tag2 == video_tag2)
            
        if date_window:
            query = query.filter(self.model.date_window == date_window)
            
        if billboard_type:
            query = query.filter(self.model.billboard_type == billboard_type)
            
        # 按score排序并限制返回数量
        return (query.order_by(self.model.score.desc())
                .limit(limit)
                .all())
    
    def cleanup_old_partitions(self, keep_days: int = 7) -> None:
        """清理旧的分区数据，默认保留7天"""
        cutoff_date = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0) - timedelta(days=keep_days)
        
        # 使用 SQL 直接删除分区
        sql = f"""
        ALTER TABLE {self.model.__tablename__}
        DROP PARTITION p_{cutoff_date.strftime('%Y%m%d')}
        """
        try:
            self.db.execute(sql)
            self.db.commit()
        except Exception as e:
            self.db.rollback()
            log_error("douhot_video", f"清理旧分区失败: {str(e)}")
