"""抖音热榜搜索领域模型"""
from datetime import datetime, timedelta
from typing import Optional, List, Dict, Any
from sqlalchemy import Column, String, Integer, BigInteger, Float, DateTime, JSON, UniqueConstraint, func
from sqlalchemy.orm import Session
from pydantic import Field, validator
from datetime import datetime

from .base import (
    BaseDBModel,
    TimestampMixin,
    BaseSchema,
    BaseRepository
)

# 字段配置
FIELD_CONFIG = {
    "lengths": {
        "ds": 10,
        "keyword": 200
    },
    "descriptions": {
        "ds": "数据分区日期",
        "billboard_type": "榜单类型",
        "keyword": "搜索关键词",
        "search_score": "搜索热度分数",
        "trends": "搜索趋势数据，格式：[{date: YYYYMMDD, value: 热度值}]",
        "date_window": "时间窗口(小时)",
        "total_count": "总数量",
        "page_num": "页码",
        "page_size": "每页数量",
        "max_trend_value": "趋势数据中的最大值",
        "min_trend_value": "趋势数据中的最小值",
        "trend_start_date": "趋势数据起始日期",
        "trend_end_date": "趋势数据结束日期",
        "record_create_time": "记录创建时间",
        "record_update_time": "记录更新时间"
    }
}

class DouhotBillboardSearch(BaseDBModel, TimestampMixin):
    """抖音热榜搜索数据表模型"""
    __tablename__ = "douhot_billboard_searches"
    # 分区字段
    ds = Column(String(FIELD_CONFIG["lengths"]["ds"]), nullable=False, index=True,
               comment=FIELD_CONFIG["descriptions"]["ds"])
    billboard_type = Column(Integer, nullable=False, default=0,
                          comment=FIELD_CONFIG["descriptions"]["billboard_type"])

    keyword = Column(String(FIELD_CONFIG["lengths"]["keyword"]), nullable=False,
                    comment=FIELD_CONFIG["descriptions"]["keyword"])
    search_score = Column(BigInteger, default=0,
                         comment=FIELD_CONFIG["descriptions"]["search_score"])
    trends = Column(JSON, nullable=True,
                   comment=FIELD_CONFIG["descriptions"]["trends"])
    date_window = Column(Integer, nullable=False,
                        comment=FIELD_CONFIG["descriptions"]["date_window"])
    total_count = Column(Integer, default=0,
                        comment=FIELD_CONFIG["descriptions"]["total_count"])
    page_num = Column(Integer, default=1,
                     comment=FIELD_CONFIG["descriptions"]["page_num"])
    page_size = Column(Integer, default=50,
                      comment=FIELD_CONFIG["descriptions"]["page_size"])
    # 趋势数据统计
    max_trend_value = Column(BigInteger, default=0,
                           comment=FIELD_CONFIG["descriptions"]["max_trend_value"])
    min_trend_value = Column(BigInteger, default=0,
                           comment=FIELD_CONFIG["descriptions"]["min_trend_value"])
    trend_start_date = Column(String(8), nullable=True,
                             comment=FIELD_CONFIG["descriptions"]["trend_start_date"])
    trend_end_date = Column(String(8), nullable=True,
                           comment=FIELD_CONFIG["descriptions"]["trend_end_date"])
    # 记录时间
    record_create_time = Column(DateTime, nullable=False, default=func.now(),
                               comment=FIELD_CONFIG["descriptions"]["record_create_time"])
    record_update_time = Column(DateTime, nullable=False, default=func.now(), onupdate=func.now(),
                               comment=FIELD_CONFIG["descriptions"]["record_update_time"])

    __table_args__ = (
        # 联合唯一索引：分区日期+榜单类型+时间窗口+关键词
        UniqueConstraint('ds', 'billboard_type', 'date_window', 'keyword', 
                        name='uk_ds_type_window_keyword'),
    )

    def update_trend_stats(self):
        """更新趋势数据统计"""
        if not self.trends:
            return
        
        trend_values = [t.get('value', 0) for t in self.trends]
        trend_dates = [t.get('date', '') for t in self.trends]
        
        if trend_values:
            self.max_trend_value = max(trend_values)
            self.min_trend_value = min(trend_values)
        
        if trend_dates:
            self.trend_start_date = min(trend_dates)
            self.trend_end_date = max(trend_dates)

class SearchTrend(BaseSchema):
    """搜索趋势数据模型"""
    date: str  # YYYYMMDD格式
    value: int

    @validator('date')
    def validate_date_format(cls, v):
        """验证日期格式"""
        for fmt in ['%Y-%m-%d %H:%M:%S', '%Y%m%d']:
            try:
                datetime.strptime(v, fmt)
                return v
            except ValueError:
                continue
        raise ValueError('日期格式必须为YYYY-MM-DD HH:mm:ss或YYYYMMDD')

class DouhotBillboardSearchBase(BaseSchema):
    """抖音热榜搜索基础模型"""
    ds: str = Field(..., description=FIELD_CONFIG["descriptions"]["ds"])
    billboard_type: int = Field(..., description=FIELD_CONFIG["descriptions"]["billboard_type"])
    keyword: str = Field(..., description=FIELD_CONFIG["descriptions"]["keyword"],
                        max_length=FIELD_CONFIG["lengths"]["keyword"])
    search_score: int = Field(default=0, description=FIELD_CONFIG["descriptions"]["search_score"])
    trends: Optional[List[SearchTrend]] = Field(None, description=FIELD_CONFIG["descriptions"]["trends"])
    date_window: int = Field(..., description=FIELD_CONFIG["descriptions"]["date_window"])
    total_count: int = Field(default=0, description=FIELD_CONFIG["descriptions"]["total_count"])
    page_num: int = Field(default=1, description=FIELD_CONFIG["descriptions"]["page_num"])
    page_size: int = Field(default=50, description=FIELD_CONFIG["descriptions"]["page_size"])
    max_trend_value: int = Field(default=0, description=FIELD_CONFIG["descriptions"]["max_trend_value"])
    min_trend_value: int = Field(default=0, description=FIELD_CONFIG["descriptions"]["min_trend_value"])
    trend_start_date: Optional[str] = Field(None, description=FIELD_CONFIG["descriptions"]["trend_start_date"])
    trend_end_date: Optional[str] = Field(None, description=FIELD_CONFIG["descriptions"]["trend_end_date"])
    record_create_time: datetime = Field(default_factory=datetime.now, description=FIELD_CONFIG["descriptions"]["record_create_time"])
    record_update_time: datetime = Field(default_factory=datetime.now, description=FIELD_CONFIG["descriptions"]["record_update_time"])

    class Config:
        orm_mode = True

class DouhotBillboardSearchCreate(DouhotBillboardSearchBase):
    """创建抖音热榜搜索模型"""
    pass

class DouhotBillboardSearchUpdate(DouhotBillboardSearchBase):
    """更新抖音热榜搜索模型"""
    pass

class DouhotBillboardSearchInDB(DouhotBillboardSearchBase):
    """数据库中的抖音热榜搜索模型"""
    id: int
    created_at: datetime
    updated_at: datetime

    class Config:
        orm_mode = True

class DouhotBillboardSearchRepository(BaseRepository):
    """抖音热榜搜索数据仓储"""
    def __init__(self, db: Session):
        super().__init__(DouhotBillboardSearch, db)

    def get_by_partition_key(self, ds: str, billboard_type: int, 
                           date_window: int, keyword: str) -> Optional[DouhotBillboardSearch]:
        """根据分区键获取数据"""
        return (self.db.query(self.model)
                .filter(self.model.ds == ds,
                       self.model.billboard_type == billboard_type,
                       self.model.date_window == date_window,
                       self.model.keyword == keyword)
                .first())
    
    async def upsert_search(self, search_data: Dict[str, Any]) -> DouhotBillboardSearch:
        """更新或插入搜索数据"""
        # 确保有ds字段，如果没有则使用当前日期
        if "ds" not in search_data:
            search_data["ds"] = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0).strftime("%Y-%m-%d")
            
        # 查找是否存在相同分区的记录
        existing = self.get_by_partition_key(
            ds=search_data["ds"],
            billboard_type=search_data["billboard_type"],
            date_window=search_data["date_window"],
            keyword=search_data["keyword"]
        )
        
        if existing:
            # 更新现有记录
            for key, value in search_data.items():
                if key not in ["id", "created_at", "ds"]:  # 保留原始 id、创建时间和分区日期
                    setattr(existing, key, value)
            existing.updated_at = datetime.now()
            self.db.merge(existing)
            return existing
        else:
            # 创建新记录
            search = DouhotBillboardSearch(**search_data)
            self.db.add(search)
            return search
    
    def get_by_date_range(self, start_date: str, end_date: str, 
                         billboard_type: Optional[int] = None) -> List[DouhotBillboardSearch]:
        """获取指定日期范围的数据"""
        query = (self.db.query(self.model)
                .filter(self.model.ds >= start_date)
                .filter(self.model.ds <= end_date))
        
        if billboard_type is not None:
            query = query.filter(self.model.billboard_type == billboard_type)
            
        return query.order_by(self.model.ds.desc(), 
                            self.model.search_score.desc()).all()

    def get_latest_by_ds(self, date_window: int, 
                        billboard_type: int, limit: int = 50
                        ) -> List[DouhotBillboardSearch]:
        """获取最新日期的视频数据
        
        """
        # 先获取最新的日期
        latest_ds = (self.db.query(self.model.ds)
                    .order_by(self.model.ds.desc())
                    .first())
        print("latest_ds:", latest_ds)
        if not latest_ds:
            return []
            
        # 构建查询
        query = (self.db.query(self.model)
                .filter(self.model.ds == latest_ds[0]))
            
        if date_window:
            query = query.filter(self.model.date_window == date_window)
            
        if billboard_type:
            query = query.filter(self.model.billboard_type == billboard_type)
        print("query:", query)
        # 按score排序并限制返回数量
        return (query.order_by(self.model.search_score.desc())
                .limit(limit)
                .all())
    
    def cleanup_old_partitions(self, keep_days: int = 7) -> None:
        """清理旧的分区数据，默认保留7天"""
        cutoff_date = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0) - timedelta(days=keep_days)
        
        # 使用 SQL 直接删除分区
        sql = f"""
        ALTER TABLE {self.model.__tablename__}
        DROP PARTITION p_{cutoff_date.strftime('%Y%m%d')}
        """
        try:
            self.db.execute(sql)
            self.db.commit()
        except Exception as e:
            self.db.rollback()
            logging.error(f"清理旧分区失败: {str(e)}")

    def get_trending_keywords(self, date_window: int, days: int = 7, min_score: int = 1000000) -> List[str]:
        """获取持续上升的热搜关键词"""
        end_date = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
        start_date = end_date - timedelta(days=days)

        # 子查询：计算每个关键词的平均分数和最新分数
        subq = (self.db.query(
            self.model.keyword,
            func.avg(self.model.search_score).label('avg_score'),
            func.max(self.model.search_score).label('latest_score')
        ).filter(
            self.model.date_window == date_window,
            self.model.ds.between(start_date, end_date)
        ).group_by(self.model.keyword)
        ).subquery()

        # 找出分数持续上升的关键词
        return [row.keyword for row in self.db.query(subq).filter(
            subq.c.latest_score >= min_score,
            subq.c.latest_score > subq.c.avg_score
        ).all()]
