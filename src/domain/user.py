"""用户核心信息模型"""
from datetime import datetime
import uuid
from typing import Optional, List
from sqlalchemy import Column, String, DateTime, Boolean, Integer, ForeignKey, Table
from sqlalchemy.orm import relationship, Session
from pydantic import BaseModel, Field, validator, EmailStr

from src.domain.base import Base

# 用户角色关联表（多对多）
user_roles = Table(
    'user_roles',
    Base.metadata,
    Column('user_id', String(36), ForeignKey('users.id', ondelete='CASCADE'), primary_key=True),
    Column('role_id', String(36), ForeignKey('roles.id', ondelete='CASCADE'), primary_key=True),
    Column('created_at', DateTime, default=datetime.now)
)

class User(Base):
    """用户核心信息表"""
    __tablename__ = 'users'
    
    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    username = Column(String(50), unique=True, nullable=True, index=True)
    nickname = Column(String(50), nullable=False)
    avatar = Column(String(255), nullable=True)
    status = Column(Integer, default=1)  # 1-正常, 0-禁用
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)
    last_login_at = Column(DateTime, nullable=True)
    last_login_ip = Column(String(45), nullable=True)
    
    # 关联关系
    identities = relationship("UserIdentity", back_populates="user", cascade="all, delete-orphan")
    settings = relationship("UserSetting", back_populates="user", uselist=False, cascade="all, delete-orphan")
    roles = relationship("Role", secondary=user_roles, back_populates="users")
    
    def __repr__(self):
        return f"<User {self.nickname}>"


class UserCreate(BaseModel):
    """用户创建模型"""
    nickname: str
    username: Optional[str] = None
    avatar: Optional[str] = None
    
    @validator('username')
    def username_must_be_valid(cls, v):
        if v is not None and (len(v) < 3 or len(v) > 30):
            raise ValueError('用户名长度必须在3-30个字符之间')
        return v 