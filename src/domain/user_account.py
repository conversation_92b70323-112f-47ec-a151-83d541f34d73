"""
用户账号数据模型和仓储层
"""
from datetime import datetime
from typing import Any, Dict, List, Optional, Tuple, Union
from sqlalchemy import Column, Integer, String, DateTime, Boolean, JSON, and_, desc, UniqueConstraint, Text
from sqlalchemy.orm import Session
from pydantic import Field, validator, EmailStr
from passlib.context import CryptContext

from .base import (
    BaseDBModel,
    TimestampMixin,
    BaseSchema,
    BaseRepository
)

# 密码哈希上下文
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# 字段配置
FIELD_CONFIG = {
    # 字段最大长度
    "lengths": {
        "username": 50,
        "accountid": 64,
        "nickname": 50,
        "email": 100,
        "password": 128,
        "source": 30,
        "avatar": 255,
        "last_login_ip": 50
    },
    # 字段描述
    "descriptions": {
        "username": "用户名/账号名",
        "accountid": "账号唯一ID",
        "nickname": "用户昵称",
        "email": "电子邮箱",
        "password": "密码(已哈希)",
        "source": "注册来源",
        "is_active": "是否激活",
        "avatar": "头像URL",
        "last_login_time": "最后登录时间",
        "last_login_ip": "最后登录IP",
        "login_count": "登录次数"
    }
}

# 数据库模型
class UserAccount(BaseDBModel, TimestampMixin):
    """用户账号表模型"""
    __tablename__ = "user_account"
    
    # 用户基本信息
    username = Column(String(FIELD_CONFIG["lengths"]["username"]), nullable=False, index=True, 
                     comment=FIELD_CONFIG["descriptions"]["username"])
    accountid = Column(String(FIELD_CONFIG["lengths"]["accountid"]), nullable=False, unique=True, index=True,
                      comment=FIELD_CONFIG["descriptions"]["accountid"])
    nickname = Column(String(FIELD_CONFIG["lengths"]["nickname"]), nullable=False,
                     comment=FIELD_CONFIG["descriptions"]["nickname"])
    email = Column(String(FIELD_CONFIG["lengths"]["email"]), nullable=False, unique=True, index=True,
                  comment=FIELD_CONFIG["descriptions"]["email"])
    password = Column(String(FIELD_CONFIG["lengths"]["password"]), nullable=False,
                     comment=FIELD_CONFIG["descriptions"]["password"])
    source = Column(String(FIELD_CONFIG["lengths"]["source"]), nullable=False, default="web",
                   comment=FIELD_CONFIG["descriptions"]["source"])
    is_active = Column(Boolean, default=True,
                      comment=FIELD_CONFIG["descriptions"]["is_active"])
    avatar = Column(String(FIELD_CONFIG["lengths"]["avatar"]), default="",
                   comment=FIELD_CONFIG["descriptions"]["avatar"])
    last_login_time = Column(DateTime, nullable=True,
                            comment=FIELD_CONFIG["descriptions"]["last_login_time"])
    last_login_ip = Column(String(FIELD_CONFIG["lengths"]["last_login_ip"]), default="",
                          comment=FIELD_CONFIG["descriptions"]["last_login_ip"])
    login_count = Column(Integer, default=0,
                        comment=FIELD_CONFIG["descriptions"]["login_count"])
    
    # 定义联合唯一索引
    __table_args__ = (
        # 确保用户名唯一
        UniqueConstraint('username', 'accountid', name='uk_username_unique'),
    )
    
    @staticmethod
    def get_password_hash(password: str) -> str:
        """获取密码哈希值"""
        return pwd_context.hash(password)
    
    @staticmethod
    def verify_password(plain_password: str, hashed_password: str) -> bool:
        """验证密码是否正确"""
        return pwd_context.verify(plain_password, hashed_password)

# API模型
class UserAccountBase(BaseSchema):
    """用户账号基础模型"""
    username: str = Field(..., description=FIELD_CONFIG["descriptions"]["username"])
    nickname: str = Field(..., description=FIELD_CONFIG["descriptions"]["nickname"])
    email: EmailStr = Field(..., description=FIELD_CONFIG["descriptions"]["email"])
    source: str = Field("web", description=FIELD_CONFIG["descriptions"]["source"])

class UserAccountCreate(UserAccountBase):
    """创建用户账号模型"""
    password: str = Field(..., min_length=6, description="用户密码")
    confirm_password: str = Field(..., description="确认密码")
    
    @validator('confirm_password')
    def passwords_match(cls, v, values, **kwargs):
        if 'password' in values and v != values['password']:
            raise ValueError('两次输入的密码不匹配')
        return v

class UserLogin(BaseSchema):
    """用户登录模型"""
    email: EmailStr = Field(..., description="用户邮箱")
    password: str = Field(..., description="用户密码")

class UserAccountUpdate(BaseSchema):
    """更新用户账号模型"""
    nickname: Optional[str] = None
    avatar: Optional[str] = None
    email: Optional[EmailStr] = None
    is_active: Optional[bool] = None

class UserAccountInDB(UserAccountBase):
    """数据库中的用户账号模型"""
    id: int
    accountid: str
    is_active: bool
    avatar: str
    created_at: datetime
    updated_at: datetime
    last_login_time: Optional[datetime] = None
    login_count: int

class UserAccountFull(UserAccountInDB):
    """包含所有字段的用户账号模型(仅用于管理)"""
    password: str

# 仓储层
class UserAccountRepository(BaseRepository[UserAccount, UserAccountCreate, UserAccountUpdate]):
    """用户账号仓储"""
    
    def __init__(self, db: Session):
        super().__init__(UserAccount, db)
    
    def get_by_email(self, email: str) -> Optional[UserAccount]:
        """根据邮箱获取用户"""
        return self.db.query(self.model).filter(self.model.email == email).first()
    
    def get_by_username(self, username: str) -> Optional[UserAccount]:
        """根据用户名获取用户"""
        return self.db.query(self.model).filter(self.model.username == username).first()
    
    def get_by_accountid(self, accountid: str) -> Optional[UserAccount]:
        """根据账号ID获取用户"""
        return self.db.query(self.model).filter(self.model.accountid == accountid).first()
    
    def create_user(self, user_data: UserAccountCreate) -> UserAccount:
        """创建新用户"""
        import uuid
        
        # 生成账号ID
        accountid = str(uuid.uuid4())
        
        # 创建用户对象
        db_user = UserAccount(
            username=user_data.username,
            accountid=accountid,
            nickname=user_data.nickname,
            email=user_data.email,
            password=UserAccount.get_password_hash(user_data.password),
            source=user_data.source
        )
        
        self.db.add(db_user)
        self.db.commit()
        self.db.refresh(db_user)
        return db_user
    
    def authenticate(self, email: str, password: str) -> Optional[UserAccount]:
        """用户认证"""
        user = self.get_by_email(email)
        if not user:
            return None
        if not UserAccount.verify_password(password, user.password):
            return None
        return user
    
    def update_login_info(self, user_id: int, ip: str) -> Optional[UserAccount]:
        """更新用户登录信息"""
        user = self.get(user_id)
        if not user:
            return None
        
        user.last_login_time = datetime.utcnow()
        user.last_login_ip = ip
        user.login_count += 1
        
        self.db.commit()
        self.db.refresh(user)
        return user
