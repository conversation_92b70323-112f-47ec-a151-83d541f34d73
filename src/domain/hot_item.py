"""热榜领域模型"""
from datetime import datetime
from typing import Dict, Optional, List, Union
from sqlalchemy import Column, String, Integer, DateTime, JSON, Boolean, desc
from sqlalchemy.orm import Session
from pydantic import Field, validator

from .base import (
    BaseDBModel,
    TimestampMixin,
    BaseSchema,
    BaseRepository
)

# 字段配置
FIELD_CONFIG = {
    # 字段最大长度
    "lengths": {
        "title": 255,
        "sentence_id": 50,
        "group_id": 50,
        "hotlist_param": 255,
        "label": 50,
        "label_url": 255,
        "sentence_tag": 50,
        "word": 255,
        "url": 255
    },
    # 字段描述
    "descriptions": {
        "title": "热点词",
        "sentence_id": "话题ID",
        "hot_value": "热度值",
        "article_detail_count": "文章详情数",
        "discuss_video_count": "讨论视频数",
        "video_count": "视频数量",
        "can_extend_detail": "是否可展开",
        "display_style": "展示样式",
        "position": "位置",
        "max_rank": "最高排名",
        "group_id": "分组ID",
        "label": "标签",
        "label_url": "标签链接",
        "sentence_tag": "话题标签",
        "event_time": "事件时间",
        "hotlist_param": "热榜参数",
        "word": "热点词",
        "word_cover": "封面图片",
        "word_type": "词类型",
        "url": "话题链接"
    }
}

# 数据库模型
class HotItem(BaseDBModel, TimestampMixin):
    """热榜数据表模型"""
    __tablename__ = "hot_items"
    
    # 热点基本信息
    title = Column(String(FIELD_CONFIG["lengths"]["title"]), nullable=False, 
                  comment=FIELD_CONFIG["descriptions"]["title"])
    sentence_id = Column(String(FIELD_CONFIG["lengths"]["sentence_id"]), nullable=False,
                        comment=FIELD_CONFIG["descriptions"]["sentence_id"])
    hot_value = Column(Integer, nullable=False,
                      comment=FIELD_CONFIG["descriptions"]["hot_value"])
    
    # 统计信息
    article_detail_count = Column(Integer, default=0,
                                comment=FIELD_CONFIG["descriptions"]["article_detail_count"])
    discuss_video_count = Column(Integer, default=0,
                               comment=FIELD_CONFIG["descriptions"]["discuss_video_count"])
    video_count = Column(Integer, default=0,
                        comment=FIELD_CONFIG["descriptions"]["video_count"])
    
    # 展示相关
    can_extend_detail = Column(Boolean, default=False,
                             comment=FIELD_CONFIG["descriptions"]["can_extend_detail"])
    display_style = Column(Integer, default=0,
                          comment=FIELD_CONFIG["descriptions"]["display_style"])
    position = Column(Integer, default=0,
                     comment=FIELD_CONFIG["descriptions"]["position"])
    max_rank = Column(Integer, default=0,
                     comment=FIELD_CONFIG["descriptions"]["max_rank"])
    
    # 分类和标签
    group_id = Column(String(FIELD_CONFIG["lengths"]["group_id"]), nullable=False,
                     comment=FIELD_CONFIG["descriptions"]["group_id"])
    label = Column(String(FIELD_CONFIG["lengths"]["label"]), default="",
                  comment=FIELD_CONFIG["descriptions"]["label"])
    label_url = Column(String(FIELD_CONFIG["lengths"]["label_url"]), default="",
                      comment=FIELD_CONFIG["descriptions"]["label_url"])
    sentence_tag = Column(String(FIELD_CONFIG["lengths"]["sentence_tag"]), default="",
                         comment=FIELD_CONFIG["descriptions"]["sentence_tag"])
    
    # 其他信息
    event_time = Column(DateTime, nullable=True,
                       comment=FIELD_CONFIG["descriptions"]["event_time"])
    hotlist_param = Column(String(FIELD_CONFIG["lengths"]["hotlist_param"]), nullable=False,
                          comment=FIELD_CONFIG["descriptions"]["hotlist_param"])
    word = Column(String(FIELD_CONFIG["lengths"]["word"]), nullable=False,
                 comment=FIELD_CONFIG["descriptions"]["word"])
    word_cover = Column(JSON, default=dict,
                       comment=FIELD_CONFIG["descriptions"]["word_cover"])
    word_type = Column(Integer, default=0,
                      comment=FIELD_CONFIG["descriptions"]["word_type"])
    url = Column(String(FIELD_CONFIG["lengths"]["url"]), nullable=False,
                comment=FIELD_CONFIG["descriptions"]["url"])

# API模型
class HotItemBase(BaseSchema):
    """热榜数据基础模型"""
    title: str = Field(..., description=FIELD_CONFIG["descriptions"]["title"],
                      max_length=FIELD_CONFIG["lengths"]["title"])
    sentence_id: str = Field(..., description=FIELD_CONFIG["descriptions"]["sentence_id"],
                           max_length=FIELD_CONFIG["lengths"]["sentence_id"])
    hot_value: int = Field(..., description=FIELD_CONFIG["descriptions"]["hot_value"], gt=0)
    article_detail_count: int = Field(default=0, description=FIELD_CONFIG["descriptions"]["article_detail_count"])
    discuss_video_count: int = Field(default=0, description=FIELD_CONFIG["descriptions"]["discuss_video_count"])
    video_count: int = Field(default=0, description=FIELD_CONFIG["descriptions"]["video_count"])
    can_extend_detail: bool = Field(default=False, description=FIELD_CONFIG["descriptions"]["can_extend_detail"])
    display_style: int = Field(default=0, description=FIELD_CONFIG["descriptions"]["display_style"])
    position: int = Field(default=0, description=FIELD_CONFIG["descriptions"]["position"])
    max_rank: int = Field(default=0, description=FIELD_CONFIG["descriptions"]["max_rank"])
    group_id: str = Field(..., description=FIELD_CONFIG["descriptions"]["group_id"],
                         max_length=FIELD_CONFIG["lengths"]["group_id"])
    label: Union[int, str] = Field("", description=FIELD_CONFIG["descriptions"]["label"])
    label_url: str = Field("", description=FIELD_CONFIG["descriptions"]["label_url"],
                          max_length=FIELD_CONFIG["lengths"]["label_url"])
    sentence_tag: Union[int, str] = Field("", description=FIELD_CONFIG["descriptions"]["sentence_tag"])
    event_time: Optional[datetime] = Field(None, description=FIELD_CONFIG["descriptions"]["event_time"])
    hotlist_param: str = Field(..., description=FIELD_CONFIG["descriptions"]["hotlist_param"],
                             max_length=FIELD_CONFIG["lengths"]["hotlist_param"])
    word: str = Field(..., description=FIELD_CONFIG["descriptions"]["word"],
                     max_length=FIELD_CONFIG["lengths"]["word"])
    word_cover: Dict = Field(default_factory=dict, description=FIELD_CONFIG["descriptions"]["word_cover"])
    word_type: int = Field(default=0, description=FIELD_CONFIG["descriptions"]["word_type"])
    url: str = Field(..., description=FIELD_CONFIG["descriptions"]["url"],
                    max_length=FIELD_CONFIG["lengths"]["url"])

    @validator('label', 'sentence_tag', pre=True)
    def convert_to_str(cls, v):
        return str(v) if v is not None else ""

class HotItemCreate(HotItemBase):
    """创建热榜数据模型"""
    pass

class HotItemUpdate(HotItemBase):
    """更新热榜数据模型"""
    pass

class HotItemInDB(HotItemBase):
    """数据库中的热榜数据模型"""
    id: int
    created_at: datetime
    updated_at: datetime

# 仓储层
class HotItemRepository(BaseRepository[HotItem, HotItemCreate, HotItemUpdate]):
    """热榜数据仓储"""
    
    def __init__(self, db: Session):
        super().__init__(HotItem, db)
    
    def get_by_sentence_id(self, sentence_id: str) -> Optional[HotItem]:
        """根据话题ID获取热榜数据"""
        return self.db.query(self.model).filter(self.model.sentence_id == sentence_id).first()
    
    def get_latest(self, limit: int = 50) -> List[HotItem]:
        """获取最新热榜数据"""
        return self.db.query(self.model)\
            .order_by(desc(self.model.created_at))\
            .limit(limit)\
            .all()
    
    def get_by_time_range(self, start_time: datetime, end_time: datetime) -> List[HotItem]:
        """获取指定时间范围内的热榜数据"""
        return self.db.query(self.model)\
            .filter(self.model.created_at.between(start_time, end_time))\
            .order_by(desc(self.model.created_at))\
            .all()
    
    def get_top_by_hot_value(self, limit: int = 10) -> List[HotItem]:
        """获取热度最高的数据"""
        return self.db.query(self.model)\
            .order_by(desc(self.model.hot_value))\
            .limit(limit)\
            .all()
    
    def cleanup_old_data(self, days: int = 30) -> int:
        """清理指定天数之前的数据"""
        cutoff_date = datetime.utcnow() - timedelta(days=days)
        result = self.db.query(self.model)\
            .filter(self.model.created_at < cutoff_date)\
            .delete()
        self.db.commit()
        return result
