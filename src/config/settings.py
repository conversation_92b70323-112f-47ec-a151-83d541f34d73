"""配置模块"""
import os
from pathlib import Path
# from pydantic_settings import BaseSettings
from typing import Optional, List
from dotenv import load_dotenv

load_dotenv()

class Settings():
    """应用配置类"""
    
    # 项目根目录
    BASE_DIR: Path = Path(__file__).parent.parent.parent
    
    # API配置
    API_V1_PREFIX: str = "/api/v1"
    PROJECT_NAME: str = "抖音热榜API"
    VERSION: str = "1.0.0"
    DESCRIPTION: str = "抖音热榜数据采集和API服务"

    # API密钥配置
    DEEPSEEK_API_KEY: str = os.getenv("DEEPSEEK_API_KEY", "***********************************")
    QIANWEN_API_KEY: str = os.getenv("OPENAI_API_KEY", "sk-2ec73c1fb47d48b9b8b470d6618c678f")

    STABILITY_API_KEY: str = os.getenv("STABILITY_API_KEY", "")  # Stability API密钥
    
    # CORS配置
    ALLOWED_ORIGINS: List[str] = ["*"]
    
    # MySQL配置
    MYSQL_HOST: str = os.getenv("MYSQL_HOST", "localhost")
    MYSQL_PORT: int = int(os.getenv("MYSQL_PORT", "3306"))
    MYSQL_USER: str = os.getenv("MYSQL_USER", "root")
    MYSQL_PASSWORD: str = os.getenv("MYSQL_PASSWORD", "")
    MYSQL_DATABASE: str = os.getenv("MYSQL_DATABASE", "douyin_hot")
    
    # 数据库配置
    # DATABASE_URL: Optional[str] = None

    SQL_ECHO: bool = False  # 是否打印SQL语句
    
    # 日志配置
    LOG_ROOT: Path = BASE_DIR / "logs"
    LOG_LEVEL: str = "INFO"
    LOG_FORMAT: str = "json"  # 'json' or 'text'
    LOG_RETENTION_DAYS: int = 7
    LOG_FILE_MAX_SIZE: str = "10MB"
    
    # 爬虫配置
    CRAWLER_INTERVAL: int = int(os.getenv("CRAWLER_INTERVAL", "300"))
    DOUYIN_API_URL: str = "https://api.douyin.com"
    REQUEST_TIMEOUT: int = 10
    MAX_RETRIES: int = 3
    
    
    # 抖音应用配置
    DOUYIN_APP_KEY: Optional[str] = None
    DOUYIN_APP_SECRET: Optional[str] = None
    DOUSHOT_COOKIE: Optional[str] = os.getenv("DOUSHOT_COOKIE", "douhot_cookie")
    
    class Config:
        """配置类设置"""
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False
        
    @property
    def MYSQL_URL(self) -> str:
        """MySQL连接URL"""
        return f"mysql+pymysql://{self.MYSQL_USER}:{self.MYSQL_PASSWORD}@{self.MYSQL_HOST}:{self.MYSQL_PORT}/{self.MYSQL_DATABASE}"

# 创建全局配置实例
settings = Settings()

# 确保日志目录存在
settings.LOG_ROOT.mkdir(parents=True, exist_ok=True)
