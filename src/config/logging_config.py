"""日志配置模块"""
from enum import Enum
from pathlib import Path
import os
import sys
import json
import logging
from logging.handlers import RotatingFileHandler
import datetime
from typing import Dict, Any, Optional
from src.config.settings import settings

class LoggerName(str, Enum):
    """日志记录器名称枚举"""
    MAIN = "main"
    CRAWLER = "crawler"
    DATABASE = "database"
    DOMAIN = "domain"
    API = "api"
    TOOLS = "tools"

def get_log_path(name: str, log_type: str) -> Path:
    """获取日志文件路径
    
    Args:
        name: 日志记录器名称
        log_type: 日志类型 (info/error)
        
    Returns:
        Path: 日志文件路径
    """
    log_dir = Path(settings.LOG_ROOT)
    log_dir.mkdir(parents=True, exist_ok=True)
    return log_dir / f"{name}_{log_type}.log"

# 自定义JSON格式化器
class JsonFormatter(logging.Formatter):
    """JSON格式的日志格式化器"""
    
    def format(self, record):
        """格式化日志记录为JSON"""
        log_record = {
            "timestamp": datetime.datetime.fromtimestamp(record.created).isoformat(),
            "level": record.levelname,
            "logger": record.name,
            "message": record.getMessage(),
            "module": record.module,
            "function": record.funcName,
            "line": record.lineno,
        }
        
        # 添加异常信息（如果有）
        if record.exc_info:
            log_record["exception"] = {
                "type": record.exc_info[0].__name__,
                "message": str(record.exc_info[1]),
                "traceback": self.formatException(record.exc_info)
            }
            
        # 添加额外字段
        for key, value in record.__dict__.items():
            if key not in ["args", "exc_info", "exc_text", "msg", "message", 
                          "levelname", "levelno", "pathname", "filename", 
                          "module", "lineno", "funcName", "created", 
                          "msecs", "relativeCreated", "asctime", "name", 
                          "thread", "threadName", "processName", "process"]:
                log_record[key] = value
                
        return json.dumps(log_record, ensure_ascii=False)

# 日志配置字典
LOGGER_CONFIGS: Dict[str, Dict[str, Any]] = {
    LoggerName.MAIN: {
        'level': settings.LOG_LEVEL,
        'format': settings.LOG_FORMAT,
        'file_path': {
            'info': get_log_path(LoggerName.MAIN, 'info'),
            'error': get_log_path(LoggerName.MAIN, 'error')
        },
        'rotation': settings.LOG_FILE_MAX_SIZE,
        'retention': f"{settings.LOG_RETENTION_DAYS} days"
    },
    LoggerName.CRAWLER: {
        'level': 'INFO',
        'format': 'json',
        'file_path': {
            'info': get_log_path(LoggerName.CRAWLER, 'info'),
            'error': get_log_path(LoggerName.CRAWLER, 'error')
        },
        'rotation': '50MB',
        'retention': '7 days'
    },
    LoggerName.DATABASE: {
        'level': 'INFO',
        'format': 'json',
        'file_path': {
            'info': get_log_path(LoggerName.DATABASE, 'info'),
            'error': get_log_path(LoggerName.DATABASE, 'error')
        },
        'rotation': '20MB',
        'retention': '7 days'
    },
    LoggerName.DOMAIN: {
        'level': 'INFO',
        'format': 'json',
        'file_path': {
            'info': get_log_path(LoggerName.DOMAIN, 'info'),
            'error': get_log_path(LoggerName.DOMAIN, 'error')
        },
        'rotation': '20MB',
        'retention': '7 days'
    },
    LoggerName.API: {
        'level': 'INFO',
        'format': 'json',
        'file_path': {
            'info': get_log_path(LoggerName.API, 'info'),
            'error': get_log_path(LoggerName.API, 'error')
        },
        'rotation': '20MB',
        'retention': '7 days'
    },
    LoggerName.TOOLS: {
        'level': 'INFO',
        'format': 'json',
        'file_path': {
            'info': get_log_path(LoggerName.TOOLS, 'info'),
            'error': get_log_path(LoggerName.TOOLS, 'error')
        },
        'rotation': '20MB',
        'retention': '7 days'
    }
}

def configure_logging(
    logger_name: Optional[str] = None, 
    console_level: str = "INFO",
    file_level: str = "DEBUG"
) -> None:
    """配置日志系统
    
    Args:
        logger_name: 指定要配置的日志记录器名称，如果为None则配置所有日志记录器
        console_level: 控制台日志级别
        file_level: 文件日志级别
    """
    # 设置根日志级别
    logging.basicConfig(level=logging.WARNING)
    
    # 确定要配置的日志记录器列表
    if logger_name:
        logger_names = [logger_name]
    else:
        logger_names = list(LOGGER_CONFIGS.keys())
        
    # 配置指定的日志记录器
    for name in logger_names:
        if name not in LOGGER_CONFIGS:
            print(f"警告: 未找到名为 '{name}' 的日志配置")
            continue
            
        config = LOGGER_CONFIGS[name]
        logger = logging.getLogger(name)
        
        # 设置日志级别
        log_level = getattr(logging, config.get('level', 'INFO'))
        logger.setLevel(log_level)
        
        # 如果logger已经有处理器，跳过配置
        if logger.handlers:
            continue
            
        # 设置控制台处理器
        if os.environ.get("LOG_TO_CONSOLE", "true").lower() == "true":
            console_handler = logging.StreamHandler(sys.stdout)
            console_handler.setLevel(getattr(logging, console_level))
            
            # 设置格式化器
            if config.get('format') == 'json':
                formatter = JsonFormatter()
            else:
                formatter = logging.Formatter(
                    '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
                )
                
            console_handler.setFormatter(formatter)
            logger.addHandler(console_handler)
            
        # 设置文件处理器
        if os.environ.get("LOG_TO_FILE", "true").lower() == "true":
            # 信息日志文件
            info_file = str(config['file_path']['info'])
            info_handler = RotatingFileHandler(
                info_file,
                maxBytes=int(config.get('rotation', '10MB').replace('MB', '')) * 1024 * 1024,
                backupCount=int(config.get('retention', '7 days').split()[0])
            )
            info_handler.setLevel(getattr(logging, file_level))
            
            # 错误日志文件
            error_file = str(config['file_path']['error'])
            error_handler = RotatingFileHandler(
                error_file,
                maxBytes=int(config.get('rotation', '10MB').replace('MB', '')) * 1024 * 1024,
                backupCount=int(config.get('retention', '7 days').split()[0])
            )
            error_handler.setLevel(logging.ERROR)
            
            # 设置格式化器
            if config.get('format') == 'json':
                info_formatter = JsonFormatter()
                error_formatter = JsonFormatter()
            else:
                log_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
                info_formatter = logging.Formatter(log_format)
                error_formatter = logging.Formatter(log_format)
                
            info_handler.setFormatter(info_formatter)
            error_handler.setFormatter(error_formatter)
            
            logger.addHandler(info_handler)
            logger.addHandler(error_handler)
            
    # 配置一些通用的日志设置
    # 禁用某些第三方库的过多日志
    logging.getLogger('urllib3').setLevel(logging.WARNING)
    logging.getLogger('httpx').setLevel(logging.WARNING)
    logging.getLogger('asyncio').setLevel(logging.WARNING)
    
    # 设置日志传播
    logging.getLogger('api').propagate = False
    
    # 打印日志配置信息
    if os.environ.get("ENV", "development").lower() == "development":
        print(f"日志配置完成。日志将输出到: {settings.LOG_ROOT}")
        for name in logger_names:
            if name in LOGGER_CONFIGS:
                config = LOGGER_CONFIGS[name]
                print(f"  - {name}: {config['level']} -> {config['file_path']['info']}")
