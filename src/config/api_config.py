"""
API配置模块 - 提供统一的API密钥访问接口
"""
from typing import Optional
from src.config.settings import settings

def get_deepseek_api_key() -> str:
    """
    获取DeepSeek API密钥
    
    Returns:
        DeepSeek API密钥
    """
    return settings.DEEPSEEK_API_KEY

def get_openai_api_key() -> str:
    """
    获取OpenAI API密钥 (已废弃)
    
    Returns:
        OpenAI API密钥 (总是返回空字符串)
    """
    # 不再使用OpenAI API，返回空字符串
    return ""

def get_qianwen_api_key() -> str:
    """
    获取通义万象(Qianwen) API密钥
    
    Returns:
        通义万象 API密钥
    """
    return settings.QIANWEN_API_KEY

def get_stability_api_key() -> str:
    """
    获取Stability API密钥
    
    Returns:
        Stability API密钥，如果未配置则返回空字符串
    """
    return getattr(settings, 'STABILITY_API_KEY', '')

def get_api_key(api_type: str) -> Optional[str]:
    """
    根据API类型获取对应的API密钥
    
    Args:
        api_type: API类型，支持 'deepseek', 'qianwen', 'stability'
        
    Returns:
        对应的API密钥，如果类型不支持则返回None
    """
    api_type = api_type.lower()
    
    if api_type == 'deepseek':
        return get_deepseek_api_key()
    elif api_type == 'openai' or api_type == 'qianwen':
        # OpenAI API已废弃，使用通义万象API替代
        return get_qianwen_api_key()
    elif api_type == 'stability':
        return get_stability_api_key()
    else:
        return None 