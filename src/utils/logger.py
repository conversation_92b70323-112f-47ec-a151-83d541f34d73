"""日志工具模块"""
import logging
import sys
from datetime import datetime
from pathlib import Path
from logging.handlers import RotatingFileHandler, TimedRotatingFileHandler
import json
from typing import Dict, Any, Optional
import functools
import time
import traceback
from src.config.settings import settings
from src.config.logging_config import LoggerName, LOGGER_CONFIGS

class CustomJsonFormatter(logging.Formatter):
    """自定义JSON格式的日志格式化器"""
    
    def __init__(self):
        super().__init__()
        self.default_fields = {
            'timestamp': '',
            'level': '',
            'message': '',
            'logger_name': '',
            'line_number': '',
            'function': '',
            'path': '',
            'process_id': '',
            'thread_id': ''
        }

    def format(self, record: logging.LogRecord) -> str:
        """格式化日志记录"""
        message = {
            'timestamp': datetime.fromtimestamp(record.created).isoformat(),
            'level': record.levelname,
            'message': record.getMessage(),
            'logger_name': record.name,
            'line_number': record.lineno,
            'function': record.funcName,
            'path': record.pathname,
            'process_id': record.process,
            'thread_id': record.thread
        }
        
        # 添加异常信息
        if record.exc_info:
            try:
                message['exception'] = {
                    'type': record.exc_info[0].__name__ if record.exc_info[0] else 'Unknown',
                    'message': str(record.exc_info[1]) if record.exc_info[1] else '',
                    'stack_trace': traceback.format_exception(*record.exc_info) if all(record.exc_info) else []
                }
            except (AttributeError, TypeError):
                message['exception'] = {
                    'type': 'Unknown',
                    'message': 'Error formatting exception information',
                    'stack_trace': []
                }
            
        # 添加额外字段
        if hasattr(record, 'extra_fields'):
            message.update(record.extra_fields)
            
        return json.dumps(message, ensure_ascii=False)

class CustomTextFormatter(logging.Formatter):
    """自定义文本格式的日志格式化器"""
    
    def __init__(self):
        super().__init__()
        self.fmt = (
            "%(asctime)s [%(levelname)s] %(name)s:%(funcName)s:%(lineno)d - %(message)s"
        )

    def format(self, record: logging.LogRecord) -> str:
        """格式化日志记录"""
        formatter = logging.Formatter(self.fmt)
        output = formatter.format(record)
        
        # 添加异常信息
        if record.exc_info:
            try:
                if not record.exc_text:
                    record.exc_text = self.formatException(record.exc_info)
                output += f"\n{record.exc_text}"
            except (AttributeError, TypeError):
                output += "\nError formatting exception information"
            
        # 添加额外字段
        if hasattr(record, 'extra_fields'):
            extra = json.dumps(record.extra_fields, ensure_ascii=False, indent=2)
            output += f"\nExtra Fields:\n{extra}"
            
        return output

class CrawlerLogger:
    """爬虫日志管理器"""
    
    _instance = None
    _loggers: Dict[str, logging.Logger] = {}
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    def get_logger(self, name: str, level: Optional[str] = None) -> logging.Logger:
        """获取或创建logger实例

        Args:
            name: logger名称
            level: 日志级别，如果未指定则使用配置文件中的级别

        Returns:
            logging.Logger: logger实例
        """
        if name in self._loggers:
            return self._loggers[name]
            
        # 获取日志配置
        config = LOGGER_CONFIGS.get(name, LOGGER_CONFIGS[LoggerName.MAIN])
        log_level = level or config['level']
            
        # 创建logger
        logger = logging.getLogger(name)
        logger.setLevel(getattr(logging, log_level.upper()))
        
        # 清除已存在的处理器
        if logger.handlers:
            logger.handlers.clear()
            
        # 创建格式化器
        if config['format'].lower() == 'json':
            formatter = CustomJsonFormatter()
        else:
            formatter = CustomTextFormatter()
            
        # 控制台处理器
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setFormatter(formatter)
        logger.addHandler(console_handler)
        
        # 常规日志文件处理器
        info_handler = RotatingFileHandler(
            filename=config['file_path']['info'],
            maxBytes=self._parse_size(config['rotation']),
            backupCount=5,
            encoding='utf-8'
        )
        info_handler.setFormatter(formatter)
        logger.addHandler(info_handler)
        
        # 错误日志文件处理器
        error_handler = TimedRotatingFileHandler(
            filename=config['file_path']['error'],
            when='midnight',
            interval=1,
            backupCount=int(config['retention'].split()[0]),
            encoding='utf-8'
        )
        error_handler.setLevel(logging.ERROR)
        error_handler.setFormatter(formatter)
        logger.addHandler(error_handler)
        
        self._loggers[name] = logger
        return logger
    
    @staticmethod
    def _parse_size(size_str: str) -> int:
        """解析文件大小字符串

        Args:
            size_str: 大小字符串，如 "10MB"

        Returns:
            int: 字节数
        """
        units = {
            'B': 1,
            'KB': 1024,
            'MB': 1024 * 1024,
            'GB': 1024 * 1024 * 1024
        }
        
        size = size_str.strip().upper()
        for unit, multiplier in units.items():
            if size.endswith(unit):
                try:
                    number = float(size[:-len(unit)])
                    return int(number * multiplier)
                except ValueError:
                    return 10 * 1024 * 1024  # 默认10MB
        return 10 * 1024 * 1024  # 默认10MB

def log_execution_time(logger_name: str):
    """函数执行时间装饰器"""
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            logger = CrawlerLogger().get_logger(logger_name)
            start_time = time.time()
            try:
                result = func(*args, **kwargs)
                execution_time = time.time() - start_time
                logger.info(
                    f"Function '{func.__name__}' completed",
                    extra={
                        'extra_fields': {
                            'execution_time': f"{execution_time:.2f}s",
                            'status': 'success',
                            'args': str(args),
                            'kwargs': str(kwargs)
                        }
                    }
                )
                return result
            except Exception as e:
                execution_time = time.time() - start_time
                logger.error(
                    f"Function '{func.__name__}' failed",
                    exc_info=True,
                    extra={
                        'extra_fields': {
                            'execution_time': f"{execution_time:.2f}s",
                            'status': 'failed',
                            'error_type': type(e).__name__,
                            'error_message': str(e),
                            'args': str(args),
                            'kwargs': str(kwargs)
                        }
                    }
                )
                raise
        return wrapper
    return decorator

# 创建便捷的日志记录函数
def log_info(logger_name: str, message: str, **extra):
    """记录INFO级别日志"""
    logger = CrawlerLogger().get_logger(logger_name)
    logger.info(message, extra={'extra_fields': extra})

def log_error(logger_name: str, message: str, exc_info=True, **extra):
    """记录ERROR级别日志"""
    logger = CrawlerLogger().get_logger(logger_name)
    logger.error(message, exc_info=exc_info, extra={'extra_fields': extra})

def log_warning(logger_name: str, message: str, **extra):
    """记录WARNING级别日志"""
    logger = CrawlerLogger().get_logger(logger_name)
    logger.warning(message, extra={'extra_fields': extra})

def log_debug(logger_name: str, message: str, **extra):
    """记录DEBUG级别日志"""
    logger = CrawlerLogger().get_logger(logger_name)
    logger.debug(message, extra={'extra_fields': extra})
