# 文章分析器 (Article Analyzer)

一个用于从公众号和其他网页提取文章内容、分析关键词并推荐相似文章标题的Python模块。

## 功能特点

- **内容提取**：支持从微信公众号文章和通用网页提取标题、正文、作者、发布时间等信息
- **动态页面支持**：使用 DrissionPage 支持JavaScript渲染的动态网页内容提取
- **关键词分析**：提取文章中的关键词和主题
- **标题推荐**：基于文章内容推荐相似的文章标题
- **多种接口**：提供命令行工具、HTTP API和Python模块接口

## 安装

```bash
# 克隆仓库
git clone https://github.com/yourusername/article-analyzer.git
cd article-analyzer

# 安装依赖
pip install -r requirements.txt
```

## 环境变量配置

对于关键词分析和标题推荐功能，需要配置AI API：

```bash
# 设置AI API URL和密钥
export AI_API_URL="https://api.anthropic.com/v1/messages"
export AI_API_KEY="your_api_key"
```

## 使用方法

### 命令行工具

```bash
# 提取文章内容（静态方式）
python -m article_analyzer.cli extract "https://example.com/article"

# 提取文章内容（动态加载方式）
python -m article_analyzer.cli extract "https://example.com/article" --dynamic

# 分析文章
python -m article_analyzer.cli analyze --url "https://example.com/article"

# 分析动态加载的文章
python -m article_analyzer.cli analyze --url "https://example.com/article" --dynamic

# 分析本地文本文件
python -m article_analyzer.cli analyze --file "article.txt"
```

### Python模块

```python
from article_analyzer.extractor import extract_article
from article_analyzer.analyzer import ArticleAnalyzer

# 提取文章内容（静态方式）
article_data = await extract_article("https://example.com/article")

# 提取文章内容（动态加载方式）
article_data = await extract_article("https://example.com/article", use_dynamic=True)

# 创建分析器实例
analyzer = ArticleAnalyzer(
    ai_api_url="https://api.anthropic.com/v1/messages",
    api_key="your_api_key"
)

# 从URL分析文章
result = await analyzer.analyze_from_url("https://example.com/article")

# 从动态加载URL分析文章
result = await analyzer.analyze_from_url("https://example.com/article", use_dynamic=True)

# 直接分析文本内容
result = await analyzer.analyze_from_text("文章标题", "文章正文内容...")
```

### HTTP API

启动API服务：

```bash
uvicorn article_analyzer.api:app --host 0.0.0.0 --port 8000
```

API端点：

- `GET /`: API信息
- `POST /extract`: 提取文章内容
- `POST /analyze/url`: 分析URL中的文章
- `POST /analyze/content`: 分析提供的文本内容

示例请求：

```bash
# 提取文章内容
curl -X POST "http://localhost:8000/extract" \
  -H "Content-Type: application/json" \
  -d '{"url": "https://example.com/article", "use_dynamic": true}'

# 分析文章
curl -X POST "http://localhost:8000/analyze/url" \
  -H "Content-Type: application/json" \
  -d '{"url": "https://example.com/article", "use_dynamic": true}'
```

## 示例

查看 `article_analyzer/example.py` 文件获取完整示例：

```bash
# 运行示例
python -m article_analyzer.example
```

## 依赖项

- Python 3.8+
- BeautifulSoup4
- Requests
- DrissionPage (用于动态页面提取)
- FastAPI (用于HTTP API)
- Uvicorn (用于运行HTTP服务)

## 许可证

MIT

# HotpotGPT 文章生成系统

HotpotGPT 是一个自动化文章生成系统，使用多代理协作架构生成高质量的文章内容。

## 最近更新

### 2023-07-15: FinalizingAgent优化

我们对文章完成流程进行了重大优化：

1. **合并了EditingAgent和FinalizingAgent功能**：将原本需要两次LLM调用的流程优化为一次，提高效率
2. **一体化文章编辑和完成**：通过一次性提交完整提示，让LLM同时处理编辑和最终处理
3. **改进解析逻辑**：增强了对多种格式响应（JSON和Markdown）的解析能力
4. **新增单元测试**：添加了`test_finalizing_agent.py`测试文件，全面验证新功能

这一优化显著提高了生成效率，减少了API调用次数，并保持了文章质量。

## 系统架构

HotpotGPT使用多代理架构，每个代理负责特定的任务：

1. **PlanningAgent**: 规划文章结构和内容
2. **ResearchAgent**: 收集相关信息和资料
3. **WritingAgent**: 生成文章草稿
4. **EditingAgent**: (现已集成到FinalizingAgent) 编辑和优化内容
5. **SEOAgent**: 优化文章的搜索引擎表现
6. **ImageGenerationAgent**: 生成相关图片
7. **FinalizingAgent**: 集成所有内容，完成最终文章
