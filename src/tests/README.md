# 文章生成功能测试

本目录包含用于测试HotpotGPT文章生成功能的测试文件。这些测试文件可用于验证文章生成系统的各个功能点，包括内容提取、分析、搜索和生成。

## 测试文件

- `test_article_generator_e2e.py`: 端到端测试文章生成过程，包括从主题和关键词生成文章
- `test_url_article_generator.py`: 测试从URL(微信文章和视频)生成文章的功能
- `test_extractor.py`: 测试统一内容提取和分析功能，支持微信文章和视频
- `test_douyin.py`: 专门测试抖音视频内容提取功能

## 运行测试

### 前置要求

1. 安装所需依赖
```bash
pip install -r requirements.txt
```

2. 设置环境变量
```bash
export DEEPSEEK_API_KEY=your_api_key  # 设置DeepSeek API密钥（用于文章分析）
export OPENAI_API_KEY=your_api_key    # 设置OpenAI API密钥（用于视频分析）
export QIANWEN_API_KEY=your_api_key   # 设置千问API密钥（用于文章生成）
```

### 关于API密钥

测试文件中已将API密钥设置为全局变量，从环境变量中获取：

```python
# 从环境变量获取API密钥
DEEPSEEK_API_KEY = os.environ.get("DEEPSEEK_API_KEY", "")
OPENAI_API_KEY = os.environ.get("OPENAI_API_KEY", "")
QIANWEN_API_KEY = os.environ.get("QIANWEN_API_KEY", "")
```

测试运行时会检查所需的API密钥是否已设置，如果缺少某些密钥，相关测试可能会被跳过。各测试使用的API密钥：

| 功能 | 所需API密钥 |
|------|------------|
| 文章内容分析 | DEEPSEEK_API_KEY |
| 视频内容分析 | OPENAI_API_KEY |
| 文章生成 | QIANWEN_API_KEY, DEEPSEEK_API_KEY |

### 运行所有测试

```bash
cd /root/workspace/hotpot_gpt
pytest src/tests/ -v
```

### 运行特定测试

```bash
# 测试URL文章生成功能
pytest src/tests/test_url_article_generator.py -v

# 测试内容提取功能
pytest src/tests/test_extractor.py -v

# 测试微信文章提取
pytest src/tests/test_url_article_generator.py::test_wechat_article_extraction -v

# 测试视频提取
pytest src/tests/test_url_article_generator.py::test_video_extraction -v

# 测试内容分析
pytest src/tests/test_url_article_generator.py::test_url_content_analysis -v
```

## 测试URL

如果您想测试自己的URL，可以修改测试文件中的URL列表：

1. 打开 `test_url_article_generator.py`
2. 修改 `TEST_WECHAT_URLS` 和 `TEST_VIDEO_URLS` 中的URL
3. 重新运行测试

## 统一内容提取和分析接口

项目实现了一套统一的内容提取和分析接口，支持多种内容类型：

### 主要功能

- 支持从微信文章、B站视频、抖音视频等多种来源提取内容
- 提供统一的内容分析功能，包括关键词提取、推荐标题生成和内容方向分析
- 支持一站式提取和分析，或分步骤处理
- 提供直接文本分析功能，无需提取步骤

### 使用示例

```python
from src.services.extractor import (
    extract_and_analyze,  # 一站式提取和分析
    extract_only,         # 仅提取，不分析
    analyze_from_extraction_result,  # 从提取结果分析
    analyze_text_content,  # 直接分析文本内容
    ContentType
)

# 一站式提取和分析（使用环境变量中的API密钥）
result = await extract_and_analyze("https://mp.weixin.qq.com/s/your_article_id")

# 指定API密钥
result = await extract_and_analyze("https://mp.weixin.qq.com/s/your_article_id", api_key=DEEPSEEK_API_KEY)

# 分步提取和分析
extraction_result = await extract_only("https://www.bilibili.com/video/your_video_id")
analysis_result = await analyze_from_extraction_result(extraction_result, api_key=DEEPSEEK_API_KEY)

# 直接分析文本内容
text_result = await analyze_text_content("文章标题", "文章内容", api_key=DEEPSEEK_API_KEY)
```

## 文章生成功能使用说明

### 从主题生成文章

调用API端点：`POST /aitool/generate`

```json
{
  "topic": "人工智能在教育领域的应用",
  "keywords": ["AI教育", "智能学习", "个性化教学"],
  "tone": "professional",
  "language": "zh-CN",
  "target_length": 1500
}
```

### 从URL生成文章

调用API端点：`POST /aitool/generate`

```json
{
  "source_url": "https://mp.weixin.qq.com/s/your_article_id",
  "tone": "professional",
  "language": "zh-CN",
  "target_length": 1500
}
```

### 从视频生成文章

现在可以使用统一接口处理视频和文章：

调用API端点：`POST /aitool/generate`

```json
{
  "source_url": "https://www.bilibili.com/video/your_video_id",
  "tone": "professional",
  "language": "zh-CN",
  "target_length": 1500
}
```

### 查询文章状态和结果

调用API端点：`GET /aitool/article/{article_id}`

返回文章的生成状态和内容。

## 文章生成流程

文章生成的完整流程包括以下步骤：

1. **初始化** - 创建文章状态并分配唯一ID
2. **内容提取** - 从URL(若提供)提取内容，支持文章和视频
3. **内容分析** - 分析提取的内容，获取关键词、推荐标题和内容方向
4. **搜索** - 在网上搜索相关信息
5. **研究** - 整理研究材料
6. **规划** - 创建文章结构和大纲
7. **写作** - 生成文章内容
8. **编辑** - 编辑和改进内容
9. **图片生成** - 生成相关图片
10. **SEO优化** - 进行搜索引擎优化
11. **完成** - 最终处理并生成文章

## 更新说明

最近的主要更新：
- 统一了文章和视频的内容提取和分析接口
- 支持多种平台内容提取（微信文章、B站、抖音等）
- 优化了内容分析流程，提供更丰富的分析结果
- 添加了直接文本分析功能，无需提取步骤
- 将API密钥设置为全局变量，统一管理

## 故障排除

如果测试失败，请检查以下几点：

1. 确保所有必需的API密钥都已设置
2. 检查网络连接，确保可以访问外部资源
3. 查看日志文件获取详细的错误信息
4. 确保URL是有效的并且可以访问
5. 检查是否有足够的配额/积分来调用外部API

如果仍有问题，请提交issue获取帮助。 