"""
URL文章生成器测试

此测试文件用于验证从URL(文章和视频)生成文章的功能。
测试包括：
1. 从微信文章URL生成文章
2. 从视频URL(B站、抖音等)生成文章
3. 验证统一内容提取和分析是否正常工作
4. 验证生成的图文内容质量
"""

import os
import sys
import asyncio
import time
import json
import re
import logging
import pytest
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
import urllib.parse

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 从配置模块获取API密钥
from src.config.api_config import get_deepseek_api_key, get_qianwen_api_key

DEEPSEEK_API_KEY = get_deepseek_api_key()
QIANWEN_API_KEY = get_qianwen_api_key()

# 导入文章处理器
from src.services.generator.article_processor import ArticleProcessor
from src.services.generator.state import ArticleState, ArticleStatus

# 导入统一内容提取和分析接口
from src.services.extractor import (
    extract_and_analyze,
    extract_only,
    analyze_from_extraction_result,
    ContentType
)

# 测试URL列表
TEST_WECHAT_URLS = [
    #"https://mp.weixin.qq.com/s/OGdxVy6jry7jQZwIP88eN",  
    #"https://mp.weixin.qq.com/s/hbmAnD7SwY2FUwOVHwBpiA",
    "https://mp.weixin.qq.com/s/mZwzUlpD25eqFChastpuQQ",
    #"https://mp.weixin.qq.com/s/PjN5bjAQZ-4ZU2HpPcp58Q",
    #"https://mp.weixin.qq.com/s/akXK3yfyQkxbQI6m4eymAA",
    #"https://mp.weixin.qq.com/s/cTZwOqfTX7yZKyToWPaIfA",
    #"https://mp.weixin.qq.com/s/a5sB5iOAdYFyOcAZ735qKg",
]

TEST_VIDEO_URLS = [
    "https://www.bilibili.com/video/BV1GJ411x7h7",  # 随机B站视频，需要替换为实际可用的URL
]


def generate_markdown_output(article_data: Dict[str, Any]) -> str:
    """将文章内容转换为Markdown格式，并添加生成的图片
    
    Args:
        article_data: 文章数据
        
    Returns:
        Markdown格式的文章内容，包含图片
    """
    # 获取最终内容
    content = article_data.get("final_content", {})
    
    # 处理字符串类型的JSON内容 - 这种情况通常是JSON字符串
    if isinstance(content, str):
        logger.info("检测到字符串类型的内容，尝试解析为JSON")
        try:
            # 处理可能存在的无效控制字符
            control_chars_pattern = r'[\x00-\x08\x0B\x0C\x0E-\x1F]'
            if re.search(control_chars_pattern, content):
                content = re.sub(control_chars_pattern, '', content)
                logger.info("已移除JSON字符串中的无效控制字符")
                
            parsed_content = json.loads(content)
            if isinstance(parsed_content, dict):
                content = parsed_content
                logger.info("成功将字符串内容解析为JSON对象")
            else:
                logger.warning(f"解析的内容不是字典: {type(parsed_content)}")
        except Exception as e:
            logger.error(f"解析内容字符串失败: {str(e)}")
            # 如果解析失败，直接使用原始内容作为正文
            return f"# 生成文章\n\n{content}"
    
    # 处理非字典类型
    if not isinstance(content, dict):
        logger.warning(f"文章内容格式不正确: {type(content)}")
        # 如果内容是字符串，直接作为正文输出
        if isinstance(content, str):
            return f"# 生成文章\n\n{content}"
        else:
            return f"# 生成文章\n\n{str(content)}"
    
    # 提取标题和内容
    title = content.get("title", "无标题")
    
    
    # 判断文章结构类型并直接返回Markdown
    if "content" in content and content["content"]:
        logger.info("检测到流畅型结构文章，直接输出标题和内容")
        article_content = content["content"]
        
        if isinstance(article_content, str):
            # 替换转义的换行符和引号
            article_content = article_content.replace('\\n', '\n')
            article_content = article_content.replace("\\'", "'")
            article_content = article_content.replace('\\"', '"')
            
            # 关键修复：检查content字段是否是JSON字符串并处理它
            if article_content.strip().startswith('{') and article_content.strip().endswith('}'):
                try:
                    # 使用local_json命名以避免变量引用错误
                    import json as local_json
                    json_content = local_json.loads(article_content)
                    
                    # 如果是包含完整文章结构的JSON
                    if isinstance(json_content, dict) and "title" in json_content:
                        logger.info("检测到content字段是完整的JSON文章结构，提取纯文本内容")
                        
                        # 创建纯文本内容
                        markdown_parts = []
                        
                        # 添加标题 (如果和外层标题不同才添加)
                        inner_title = json_content.get("title", "")
                        if inner_title and inner_title != title:
                            title = inner_title  # 使用内部的标题
                        
                        # 添加引言
                        if "introduction" in json_content and json_content["introduction"]:
                            markdown_parts.append(json_content["introduction"])
                        
                        # 添加章节
                        if "sections" in json_content and isinstance(json_content["sections"], list):
                            for section in json_content["sections"]:
                                if isinstance(section, dict):
                                    if "title" in section and section["title"]:
                                        markdown_parts.append(f"## {section['title']}")
                                    if "content" in section and section["content"]:
                                        markdown_parts.append(section["content"])
                        
                        # 添加结论
                        if "conclusion" in json_content and json_content["conclusion"]:
                            markdown_parts.append(f"## 结论\n{json_content['conclusion']}")
                        
                        # 使用提取的纯文本内容
                        if markdown_parts:
                            article_content = "\n\n".join(markdown_parts)
                            logger.info("成功从JSON内容中提取了纯文本")
                        else:
                            # 如果JSON内部有content字段
                            if "content" in json_content and json_content["content"]:
                                article_content = json_content["content"]
                                logger.info("从内层JSON的content字段提取内容")
                    
                    # 如果只是简单的包含content字段的JSON
                    elif isinstance(json_content, dict) and "content" in json_content:
                        article_content = json_content["content"]
                        logger.info("从嵌套JSON中提取了content字段内容")
                        
                except Exception as e:
                    logger.warning(f"尝试解析嵌套JSON失败: {str(e)}")
                    logger.info("使用原始content内容")
            
            # 构建Markdown内容
            markdown_content = f"# {title}\n\n"
           
            # 添加文章内容
            markdown_content += article_content

            return markdown_content.strip()
          
    else:
        logger.info("检测到章节型结构文章")
        
        # 创建Markdown内容字符串
        markdown_content = f"# {title}\n\n"
        
   
        introduction = content.get("introduction", "")
        if introduction:
            markdown_content += introduction + "\n\n"
        
        # 添加章节和图片
        sections = content.get("sections", [])
        for i, section in enumerate(sections):
            if not isinstance(section, dict):
                continue
            
            # 添加章节标题
            section_title = section.get("title", f"章节 {i+1}")
            markdown_content += f"## {section_title}\n\n"
            
            # 添加章节内容
            section_content = section.get("content", "")
            if section_content:
                markdown_content += section_content + "\n\n"
            
            # 处理子章节（可能包含图片）
            subsections = section.get("subsections", [])
            for subsection in subsections:
                if not isinstance(subsection, dict):
                    continue
                
             
                elif "title" in subsection:
                    subsection_title = subsection.get("title", "")
                    markdown_content += f"### {subsection_title}\n\n"
                    
                    if "content" in subsection:
                        markdown_content += subsection.get("content", "") + "\n\n"
        
        # 添加结论
        conclusion = content.get("conclusion", "")
        if conclusion:
            markdown_content += f"## 结论\n\n{conclusion}\n\n"
     
        logger.info(f"生成的Markdown内容长度: {len(markdown_content)} 字符")
        return markdown_content.strip()


async def extract_and_analyze_content(url: str = "", title: str = "", 
                                      keywords: List[str] = None, 
                                      content_intro: str = "") -> Dict[str, Any]:
    """从URL或主题提取内容并进行分析，然后让用户选择合适的内容方向
    
    Args:
        url: 内容URL
        title: 文章标题（如果没有URL）
        keywords: 关键词列表（如果没有URL）
        content_intro: 内容简介（如果没有URL）
        
    Returns:
        包含用户选择的内容方向、标题和关键词的字典
    """
    logger.info(f"开始内容提取和分析: URL={url}, 标题={title}")
    start_time = time.time()
    
    try:
        # 对于URL模式，需要提取和分析
        if url:
            logger.info(f"从URL提取和分析内容: {url}")
            
            # 调用统一接口
            result = await extract_and_analyze(url)
            
            if "error" in result:
                logger.error(f"提取和分析URL出错: {result.get('error')}")
                return {
                    "success": False,
                    "error": result.get("error"),
                    "url": url
                }
                
            # 提取分析结果
            analysis_result = result.get('analysis_result', [])
        else:
            # 非URL模式，直接使用提供的标题、关键词和内容简介
            logger.info(f"非URL模式，直接使用标题和关键词: {title}, {keywords}")
            
            # 准备关键词列表
            keywords_list = keywords if keywords else []
            
            # 创建一个简单的分析结果（不调用API）
            analysis_result = [{
                "recommended_title": title,
                "content_direction": content_intro or f"关于{title}的分析",
                "keywords": keywords_list
            }]
        
        # 展示分析结果并获取用户选择
        if analysis_result and len(analysis_result) > 0:
            # 对于非URL模式（只有一个分析结果），直接使用而不显示选项
            if not url and len(analysis_result) == 1:
                selected_index = 0
                selected_item = analysis_result[0]
                
                # 提取选择的内容
                selected_title = selected_item.get("recommended_title", title)
                selected_keywords = selected_item.get("keywords", []) if isinstance(selected_item.get("keywords", []), list) else []
                selected_direction = selected_item.get("content_direction", content_intro or f"关于{selected_title}的分析")
                
                logger.info("非URL模式，直接使用提供的内容:")
                logger.info(f"标题: {selected_title}")
                logger.info(f"方向: {selected_direction}")
                logger.info(f"关键词: {selected_keywords[:5] if len(selected_keywords) > 5 else selected_keywords}")
            else:
                # 对于URL模式，提取并展示所有选项
                content_directions = []
                recommended_titles = []
                keywords_list = []
                
                for i, item in enumerate(analysis_result):
                    if not isinstance(item, dict):
                        continue
                        
                    # 提取内容方向
                    if "content_direction" in item and item["content_direction"]:
                        content_directions.append({
                            "index": i,
                            "direction": item["content_direction"]
                        })
                        
                    # 提取推荐标题
                    if "recommended_title" in item and item["recommended_title"]:
                        recommended_titles.append({
                            "index": i,
                            "title": item["recommended_title"]
                        })
                        
                    # 提取关键词
                    if "keywords" in item and isinstance(item["keywords"], list) and item["keywords"]:
                        keywords_list.append({
                            "index": i,
                            "keywords": item["keywords"]
                        })
                
                # 打印分析结果供用户选择
                print("\n============ 内容分析结果 ============")
                
                if content_directions:
                    print("\n--- 可能的内容方向 ---")
                    for direction in content_directions:
                        print(f"{direction['index']}. {direction['direction']}")
                
                if recommended_titles:
                    print("\n--- 推荐标题 ---")
                    for title_item in recommended_titles:
                        print(f"{title_item['index']}. {title_item['title']}")
                
                if keywords_list:
                    print("\n--- 关键词列表 ---")
                    for kw_item in keywords_list:
                        print(f"{kw_item['index']}. {', '.join(kw_item['keywords'][:5])}...")
                        
                print("\n====================================")
                
                # 获取用户选择 (在自动化测试中使用第一个选项)
                selected_index = 0
                try:
                    user_input = input("\n请选择一个内容方向的索引(直接按Enter使用默认0): ")
                    if user_input.strip():
                        selected_index = int(user_input)
                except ValueError:
                    print("输入无效，使用默认值0")
                    
                # 确保索引有效
                selected_index = max(0, min(selected_index, len(analysis_result) - 1))
                
                # 获取选择的分析项
                selected_item = analysis_result[selected_index]
                
                # 提取选择的内容
                selected_title = selected_item.get("recommended_title", title or "内容分析")
                selected_keywords = selected_item.get("keywords", []) if isinstance(selected_item.get("keywords", []), list) else []
                selected_direction = selected_item.get("content_direction", f"关于{selected_title}的分析")
                
                logger.info(f"用户选择了索引 {selected_index}:")
                logger.info(f"标题: {selected_title}")
                logger.info(f"方向: {selected_direction}")
                logger.info(f"关键词: {selected_keywords[:5]}...")
            
            # 准备返回结果
            return {
                "success": True,
                "selected_index": selected_index,
                "title": selected_title,
                "selected_direction": selected_direction,
                "keywords": selected_keywords,
                "extraction_time": time.time() - start_time,
                "source_url": url,
                "analysis_result": analysis_result
            }
        else:
            logger.warning("没有找到有效的分析结果")
            # 如果分析结果为空，但有标题和关键词，则使用这些
            if title:
                return {
                    "success": True,
                    "title": title,
                    "selected_direction": f"关于{title}的分析",
                    "keywords": keywords or [],
                    "extraction_time": time.time() - start_time,
                    "source_url": url
                }
            else:
                return {
                    "success": False,
                    "error": "未找到有效的分析结果，且未提供标题",
                    "url": url
                }
                
    except Exception as e:
        logger.exception(f"内容提取和分析过程中发生异常: {str(e)}")
        return {
            "success": False,
            "error": f"内容提取和分析失败: {str(e)}",
            "url": url,
            "title": title
        }

async def test_full_url_article_generation(url: str) -> Dict[str, Any]:
    """测试完整的URL文章生成流程
    
    现在分为两个阶段：
    1. 内容提取和分析，获取用户选择
    2. 使用选定内容生成文章
    
    Args:
        url: 要测试的URL
        
    Returns:
        测试结果
    """
    logger.info(f"开始测试完整的URL文章生成流程: {url}")
    start_time = time.time()
    
    try:
        # 第一阶段：内容提取和分析
        logger.info("阶段1: 内容提取和分析，获取用户选择")
        analysis_result = await extract_and_analyze_content(url=url)
        
        if not analysis_result.get("success", False):
            logger.error(f"内容提取和分析失败: {analysis_result.get('error')}")
            return {
                "url": url,
                "success": False,
                "error": analysis_result.get("error", "内容提取和分析失败")
            }
        
        # 提取选择的内容
        selected_title = analysis_result.get("title", "")
        selected_keywords = analysis_result.get("keywords", [])
        selected_direction = analysis_result.get("selected_direction", "")
        
        logger.info(f"内容分析完成，选择的标题: {selected_title}")
        logger.info(f"选择的方向: {selected_direction}")
        logger.info(f"选择的关键词数量: {len(selected_keywords)}")
        
        # 第二阶段：基于选择的内容生成文章
        logger.info("阶段2: 基于选择的内容生成文章")
        
        # 初始化处理器
        processor = ArticleProcessor(config={
            "api_keys": {
                "deepseek_api_key": DEEPSEEK_API_KEY,
                "qianwen_api_key": QIANWEN_API_KEY,
            },
            "content_extractor_config": {
                "article_api_key": DEEPSEEK_API_KEY,
            }
        })
        
        # 使用process_article方法处理文章，直接传入已分析的内容
        logger.info("调用process_article开始处理文章...")
        result = await processor.process_article(
            topic=selected_title,
            keywords=selected_keywords,
            selected_direction=selected_direction,
            tone="informative",
            target_length=3500,
            language="zh-CN"
        )
        
        article_id = result.get("article_id")
        
        if not article_id:
            logger.error(f"文章生成请求失败: {result}")
            return {
                "url": url,
                "success": False,
                "error": "无法获取article_id"
            }
        
        logger.info(f"文章生成请求已提交，article_id: {article_id}")
        
        # 等待文章处理完成
        max_wait_time = 780  # 最多等待13分钟
        wait_interval = 10   # 每10秒检查一次
        wait_time = 0
        
        article_data = None
        while wait_time < max_wait_time:
            article_data = await processor.generate_article(article_id)
            
            # 记录详细状态信息
            current_status = article_data.get("status", "unknown")
            current_progress = article_data.get("progress", 0)
            logger.info(f"当前状态: {current_status}, 进度: {current_progress}, 已等待: {wait_time}秒")
            
            if current_status in ["completed", "failed"]:
                logger.info(f"文章处理{current_status}，停止等待")
                break
            
            # 检查是否有错误
            if "errors" in article_data and article_data["errors"]:
                errors = article_data["errors"]
                logger.warning(f"文章处理中存在错误: {errors}")
            
            await asyncio.sleep(wait_interval)
            wait_time += wait_interval
        
        if not article_data:
            logger.error(f"无法获取文章数据: {article_id}")
            return {
                "url": url,
                "article_id": article_id,
                "success": False,
                "error": "无法获取文章数据"
            }
        
        success = article_data.get("status") == "completed"
        
        if success:
            logger.info(f"文章生成成功: {article_id}, 总耗时: {wait_time}秒")
            
            # 检查final_content是否存在
            if "final_content" not in article_data:
                logger.error("文章缺少final_content字段")
                article_data["final_content"] = {"title": "内容缺失", "content": "文章生成过程中出现问题，无法获取最终内容。"}
            
            # 检查final_content的数据类型
            if not isinstance(article_data["final_content"], dict):
                logger.error(f"final_content格式不正确，类型: {type(article_data['final_content'])}")
                if isinstance(article_data["final_content"], str):
                    # 尝试解析JSON字符串
                    try:
                        article_data["final_content"] = json.loads(article_data["final_content"])
                    except json.JSONDecodeError:
                        article_data["final_content"] = {"title": "内容格式错误", "content": article_data["final_content"]}
            
            # 输出final_content的结构
            if isinstance(article_data["final_content"], dict):
                logger.info(f"final_content包含以下字段: {', '.join(article_data['final_content'].keys())}")
                
                # 检查文章结构类型
                if "content" in article_data["final_content"]:
                    logger.info("检测到流畅型结构的文章")
                else:
                    logger.info("检测到章节型结构的文章")
                    
                    # 检查章节数
                    sections = article_data["final_content"].get("sections", [])
                    logger.info(f"文章包含 {len(sections)} 个章节")
            
            # 生成Markdown格式输出
            markdown_output = generate_markdown_output(article_data)
            logger.info(f"生成的Markdown内容长度: {len(markdown_output)} 字符")
            
            # 将Markdown输出保存到文件
            timestamp = int(time.time())
            markdown_filename = f"./output/article_output_busseness.md"
            with open(markdown_filename, "w", encoding="utf-8") as f:
                f.write(markdown_output)
            
            logger.info(f"文章内容已保存为Markdown格式: {markdown_filename}")
            
            # 简单的质量检查
            has_content = "final_content" in article_data and article_data["final_content"]
        
            
            # 计算内容长度
            content_length = 0
            if isinstance(article_data["final_content"], dict):
                content = article_data["final_content"]
                content_text = ""
                
                # 处理直接的final_content结构
                if content.get('title'):
                    content_text += content.get('title', '') + " "
                
                if content.get('introduction'):
                    content_text += content.get('introduction', '') + " "
                
                # 处理不同的内容结构
                if "content" in content and content["content"]:
                    # 流畅型结构
                    content_text += content.get('content', '')
                elif isinstance(content.get('sections'), list):
                    # 章节型结构
                    for section in content.get('sections', []):
                        if isinstance(section, dict):
                            if section.get('title'):
                                content_text += section.get('title', '') + " "
                            
                            if section.get('content'):
                                content_text += section.get('content', '') + " "
                    
                if content.get('conclusion'):
                    content_text += content.get('conclusion', '')
                
                content_length = len(content_text)
                # 估算字数（中文）
                word_count = len(content_text) // 2
                logger.info(f"文章内容长度: {content_length} 字符, 约 {word_count} 字")
            else:
                content_length = len(str(article_data["final_content"]))
                logger.info(f"文章内容长度(非结构化): {content_length} 字符")
            
         
            # 计算质量评分
            quality_score = 0
            if has_content:
                quality_score += 60
                logger.info("文章包含内容 (+50分)")
            
            if content_length > 2000:
                quality_score += 30
                logger.info("文章内容丰富 (+30分)")
            elif content_length > 1000:
                quality_score += 20
                logger.info("文章内容适中 (+20分)")
            else:
                quality_score += 10
                logger.info("文章内容较短 (+10分)")
            
            # 记录总的处理时间
            end_time = time.time()
            total_time = end_time - start_time
            logger.info(f"文章处理总耗时: {total_time:.2f}秒")
            
            return {
                "url": url,
                "article_id": article_id,
                "success": success,
                "has_content": has_content,
                "content_length": content_length,
                "quality_score": quality_score,
                "processing_time": wait_time,
                "total_time": total_time,
                "markdown_output": markdown_output,
                "markdown_filename": markdown_filename,
                "final_content": article_data.get("final_content", {})
            }
        else:
            logger.error(f"文章生成失败: {article_id}, 状态: {article_data.get('status')}")
            # 输出具体错误信息
            if "errors" in article_data and article_data["errors"]:
                logger.error(f"错误详情: {article_data['errors']}")
            return {
                "url": url,
                "article_id": article_id,
                "success": False,
                "status": article_data.get("status"),
                "errors": article_data.get("errors", [])
            }
    except Exception as e:
        logger.exception(f"测试过程中发生异常: {str(e)}")
        return {
            "url": url,
            "success": False,
            "error": str(e)
        }

@pytest.mark.asyncio
async def test_url_full_generation():
    """测试完整的URL文章生成流程"""
    # 检查是否设置了API密钥
    if not DEEPSEEK_API_KEY or not QIANWEN_API_KEY:
        pytest.skip("未设置所需API密钥(DEEPSEEK_API_KEY, QIANWEN_API_KEY)，无法执行完整生成流程")
    
    # 优先使用微信文章，因为处理速度通常更快
    test_urls = TEST_WECHAT_URLS 
    #test_urls = TEST_VIDEO_URLS
    
    if not test_urls:
        pytest.skip("缺少测试URL")
    
    # 只测试第一个URL
    url = test_urls[0]
    result = await test_full_url_article_generation(url)
    
    assert result["success"], f"URL文章生成失败: {result.get('error', '未知错误')}"
    assert result["has_content"], "生成结果缺少内容"
    
    # 检查内容结构是否完整
    # 从markdown_output中提取内容结构
    markdown_content = result["markdown_output"]
    assert "# " in markdown_content, "文章缺少标题"
    
    # 确保result中包含final_content
    if "final_content" not in result and "article_data" in result:
        logger.info("从article_data中获取final_content")
        result["final_content"] = result.get("article_data", {}).get("final_content", {})
    
    # 判断文章结构类型，流畅型结构可能没有章节标题
    is_fluid_structure = False
    if "final_content" in result and isinstance(result["final_content"], dict):
        is_fluid_structure = "content" in result["final_content"] and result["final_content"].get("content")
        
    if not is_fluid_structure:
        # 章节型结构文章必须有章节标题
        assert "## " in markdown_content, "文章缺少章节"
    else:
        # 对于流畅型结构，如果有引言或结论，也会有章节标题
        has_introduction = result.get("final_content", {}).get("introduction", "")
        has_conclusion = result.get("final_content", {}).get("conclusion", "")
        if has_introduction or has_conclusion or "## " in markdown_content:
            logger.info("流畅型结构文章包含章节标题")
        else:
            logger.info("流畅型结构文章无需章节标题")
    
    # 检查内容长度
    assert result["content_length"] > 500, "生成内容太短"
    logger.info(f"内容长度: {result['content_length']}")
    
    # 检查质量分数
    assert result["quality_score"] >= 50, "生成质量分数太低"
    logger.info(f"质量分数: {result['quality_score']}")
    
    # 将结果写入文件以供后续分析
    timestamp = int(time.time())
    filename = f"url_generation_test_{timestamp}.json"
    with open(filename, "w", encoding="utf-8") as f:
        json.dump(result, f, ensure_ascii=False, indent=2)
    
    logger.info(f"测试结果已保存到文件: {filename}")
    return result

def check_api_keys():
    """检查API密钥是否设置"""
    missing_keys = []
    if not DEEPSEEK_API_KEY:
        missing_keys.append("DEEPSEEK_API_KEY")
    if not QIANWEN_API_KEY:
        missing_keys.append("QIANWEN_API_KEY")
    
    if missing_keys:
        print(f"⚠️ 以下API密钥未设置: {', '.join(missing_keys)}")
        print("部分测试可能会被跳过或失败")
    else:
        print("✅ 所有API密钥已设置")

async def test_direct_content_article_generation(title: str, keywords: List[str], content_intro: str) -> Dict[str, Any]:
    """测试通过直接提供内容而非URL的文章生成流程
    
    Args:
        title: 文章标题
        keywords: 关键词列表
        content_intro: 内容介绍
        
    Returns:
        测试结果
    """
    logger.info(f"开始测试直接内容文章生成流程: {title}")
    
    # 初始化处理器
    processor = ArticleProcessor(config={
        "api_keys": {
            "deepseek_api_key": DEEPSEEK_API_KEY,
            "qianwen_api_key": QIANWEN_API_KEY,
        },
        "content_extractor_config": {
            "article_api_key": DEEPSEEK_API_KEY,
        }
    })
    
    try:
        # 使用process_article方法处理文章
        result = await processor.process_article(
            topic=title,
            keywords=keywords,
            selected_direction=content_intro,
            tone="informative",
            target_length=3500,
            language="zh-CN"
        )
        
        article_id = result.get("article_id")
        
        if not article_id:
            logger.error(f"文章生成请求失败: {result}")
            return {
                "title": title,
                "success": False,
                "error": "无法获取article_id"
            }
        
        logger.info(f"文章生成请求已提交，article_id: {article_id}")
        
        # 等待文章处理完成
        max_wait_time = 780  # 最多等待13分钟
        wait_interval = 10   # 每10秒检查一次
        wait_time = 0
        
        article_data = None
        while wait_time < max_wait_time:
            article_data = await processor.generate_article(article_id)
            
            if article_data.get("status") in ["completed", "failed"]:
                break
            
            logger.info(f"文章处理中，当前状态: {article_data.get('status')}, 已等待: {wait_time}秒")
            await asyncio.sleep(wait_interval)
            wait_time += wait_interval
        
        if not article_data:
            logger.error(f"无法获取文章数据: {article_id}")
            return {
                "title": title,
                "article_id": article_id,
                "success": False,
                "error": "无法获取文章数据"
            }
        
        success = article_data.get("status") == "completed"
        
        if success:
            logger.info(f"文章生成成功: {article_id}")
            
            # 生成Markdown格式输出
            markdown_output = generate_markdown_output(article_data)
            logger.info(f"生成的Markdown内容长度: {len(markdown_output)} 字符")
            
            # 将Markdown输出保存到文件
            markdown_filename = f"./output/article_output_direct.md"
            with open(markdown_filename, "w", encoding="utf-8") as f:
                f.write(markdown_output)
            
            logger.info(f"文章内容已保存为Markdown格式: {markdown_filename}")
            
            # 简单的质量检查
            has_content = "final_content" in article_data and article_data["final_content"]
         
            
            # 计算内容长度
            content_length = 0
            if isinstance(article_data["final_content"], dict):
                content = article_data["final_content"]
                content_text = ""
                
                # 处理直接的final_content结构
                if content.get('title'):
                    content_text += content.get('title', '') + " "
                
                if content.get('introduction'):
                    content_text += content.get('introduction', '') + " "
                  
                # 处理不同的内容结构
                if "content" in content and content["content"]:
                    # 流畅型结构
                    content_text += content.get('content', '')
                elif isinstance(content.get('sections'), list):
                    # 章节型结构
                    for section in content.get('sections', []):
                        if isinstance(section, dict):
                            if section.get('title'):
                                content_text += section.get('title', '') + " "
                            
                            if section.get('content'):
                                content_text += section.get('content', '') + " "
                            
                            # 处理子节
                            for subsection in section.get('subsections', []):
                                if isinstance(subsection, dict):
                                    if subsection.get('title'):
                                        content_text += subsection.get('title', '') + " "
                                    
                                    if subsection.get('content'):
                                        content_text += subsection.get('content', '') + " "
                    
                if content.get('conclusion'):
                    content_text += content.get('conclusion', '')
                
                content_length = len(content_text)
            else:
                content_length = len(str(article_data["final_content"]))
            
            logger.info(f"文章内容长度: {content_length}")
            
            # 修复语法错误
            if "formatted_content" in article_data:
                logger.info(f"生成的格式化内容: {type(article_data['formatted_content'])}")
          
            quality_score = 0
            if has_content:
                quality_score += 60
            
            if content_length > 1000:
                quality_score += 20
            
            return {
                "title": title,
                "article_id": article_id,
                "success": success,
                "has_content": has_content,
                "content_length": content_length,
                "quality_score": quality_score,
                "processing_time": wait_time,
                "markdown_output": markdown_output,
                "markdown_filename": markdown_filename,
                "final_content": article_data.get("final_content", {})
            }
        else:
            logger.error(f"文章生成失败: {article_id}, 状态: {article_data.get('status')}")
            return {
                "title": title,
                "article_id": article_id,
                "success": False,
                "status": article_data.get("status"),
                "errors": article_data.get("errors", [])
            }
    except Exception as e:
        logger.exception(f"测试过程中发生异常: {str(e)}")
        return {
            "title": title,
            "success": False,
            "error": str(e)
        }

@pytest.mark.asyncio
async def test_direct_content_full_generation():
    """测试直接内容文章生成流程"""
    # 检查是否设置了API密钥
    if not DEEPSEEK_API_KEY or not QIANWEN_API_KEY:
        pytest.skip("未设置所需API密钥(DEEPSEEK_API_KEY, QIANWEN_API_KEY)，无法执行完整生成流程")
    
    # 测试数据
    title = "人工智能在医疗领域的应用"
    keywords = ["AI", "医疗", "诊断", "机器学习", "医学影像"]
    content_intro = "人工智能技术正在彻底改变医疗行业，从疾病诊断到药物研发，AI带来了前所未有的创新。本文探讨AI在医疗领域的主要应用场景及未来发展趋势。"
    
    result = await test_direct_content_article_generation(title, keywords, content_intro)
    
    assert result["success"], f"直接内容文章生成失败: {result.get('error', '未知错误')}"
    assert result["has_content"], "生成结果缺少内容"
    
    # 检查内容结构是否完整
    # 从markdown_output中提取内容结构
    markdown_content = result["markdown_output"]
    assert "# " in markdown_content, "文章缺少标题"
    
    # 确保result中包含final_content
    if "final_content" not in result and "article_data" in result:
        logger.info("从article_data中获取final_content")
        result["final_content"] = result.get("article_data", {}).get("final_content", {})
    
    # 判断文章结构类型，流畅型结构可能没有章节标题
    is_fluid_structure = False
    if "final_content" in result and isinstance(result["final_content"], dict):
        is_fluid_structure = "content" in result["final_content"] and result["final_content"].get("content")
        
    if not is_fluid_structure:
        # 章节型结构文章必须有章节标题
        assert "## " in markdown_content, "文章缺少章节"
    else:
        # 对于流畅型结构，如果有引言或结论，也会有章节标题
        has_introduction = result.get("final_content", {}).get("introduction", "")
        has_conclusion = result.get("final_content", {}).get("conclusion", "")
        if has_introduction or has_conclusion or "## " in markdown_content:
            logger.info("流畅型结构文章包含章节标题")
        else:
            logger.info("流畅型结构文章无需章节标题")
    
    # 检查内容长度
    assert result["content_length"] > 500, "生成内容太短"
    logger.info(f"内容长度: {result['content_length']}")
    
    # 检查质量分数
    assert result["quality_score"] >= 50, "生成质量分数太低"
    logger.info(f"质量分数: {result['quality_score']}")
    # 将结果写入文件以供后续分析
    timestamp = int(time.time())
    filename = f"direct_content_generation_test_{timestamp}.json"
    with open(filename, "w", encoding="utf-8") as f:
        json.dump(result, f, ensure_ascii=False, indent=2)
    
    logger.info(f"测试结果已保存到文件: {filename}")
    return result

# 修改main函数以支持两种测试方式
if __name__ == "__main__":
    # 检查API密钥
    check_api_keys()
    
    # 创建解析器
    import argparse
    parser = argparse.ArgumentParser(description='测试文章生成')
    parser.add_argument('--mode', type=str, default='url', choices=['url', 'direct'],
                        help='测试模式: url(从URL生成) 或 direct(从直接内容生成)')
    
    args = parser.parse_args()
    
    if args.mode == 'direct':
        # 直接内容测试
        asyncio.run(test_direct_content_full_generation())
    else:
        # URL测试 (默认)
        asyncio.run(test_url_full_generation()) 