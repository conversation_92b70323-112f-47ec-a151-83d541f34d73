"""
动态加载与内容提取测试脚本。

用于测试统一内容提取和分析功能是否正常工作。
测试包括：
1. 微信文章内容提取
2. 文章内容AI分析
3. 统一内容提取和分析接口
"""
import logging
import sys
import asyncio
import time
from pathlib import Path
import os

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

# 导入旧的提取接口（用于对比）
from src.services.extractor.wechat_extractor import extract_article
from src.services.extractor.get_wordcloud import get_wordcloud

# 导入新的统一提取和分析接口
from src.services.extractor import (
    extract_and_analyze, 
    extract_only,
    analyze_from_extraction_result,
    analyze_text_content
)

# 导入API配置模块
from src.config.api_config import get_deepseek_api_key, get_qianwen_api_key

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 从配置获取API密钥
DEEPSEEK_API_KEY = get_deepseek_api_key()
QIANWEI_API_KEY = get_qianwen_api_key()

# 测试URL
TEST_URL = "https://mp.weixin.qq.com/s/C_0j0vBUp9lpEdfhoJ1LYA"  # 使用真实的微信文章URL进行测试

async def test_dynamic_extraction():
    """测试动态加载提取功能"""
    logger.info("开始测试动态加载提取功能")
    
    try:
        # 使用旧的接口提取内容
        result = await extract_article(TEST_URL, use_dynamic=False)
        
        # 检查结果
        if "error" in result:
            logger.error(f"测试失败: {result['error']}")
            return False
        
        # 打印结果
        logger.info(f"旧接口测试成功: 成功提取标题 '{result.get('title', '')}'")
        
        # 使用新的统一接口提取内容
        start_time = time.time()
        extraction_result = await extract_only(TEST_URL)
        end_time = time.time()
        
        if "error" in extraction_result:
            logger.error(f"新接口测试失败: {extraction_result['error']}")
            return False
        
        logger.info(f"新接口测试成功: 成功提取标题 '{extraction_result.get('title', '')}'")
        logger.info(f"提取耗时: {end_time - start_time:.2f}秒")
        
        # 生成词云（可选）
        try:
            if "content" in extraction_result and extraction_result["content"]:
                get_wordcloud(extraction_result['content'])
                logger.info("成功生成词云")
        except Exception as e:
            logger.warning(f"生成词云失败: {e}")
        
        return True
    except Exception as e:
        logger.error(f"测试过程中发生错误: {e}")
        return False

async def test_unified_analysis():
    """测试统一分析功能"""
    logger.info("开始测试统一内容分析功能")
    
    # 检查API密钥是否设置
    if not DEEPSEEK_API_KEY:
        logger.warning("未设置DEEPSEEK_API_KEY环境变量，无法执行文章分析功能")
        print("\n请设置DEEPSEEK_API_KEY环境变量后再运行此测试")
        return False
    
    try:
        # 使用统一接口提取并分析内容
        logger.info("使用一站式提取和分析...")
        start_time = time.time()
        result = await extract_and_analyze(TEST_URL)
        end_time = time.time()
        
        # 检查提取和分析结果
        if "error" in result:
            logger.error(f"提取和分析失败: {result['error']}")
            return False
        
        # 打印分析结果
        print("\n" + "="*50)
        print("统一分析接口结果:")
        print(f"标题: {result.get('title', '未找到标题')}")
        print(f"处理时间: {end_time - start_time:.2f}秒")
        
        # 提取分析结果
        analysis = result.get("analysis_result", {})
        
        # 打印关键词
        keywords = analysis.get('keywords', [])
        if keywords:
            print("\n关键词:")
            for keyword in keywords[:10]:
                print(f"- {keyword}")
        
        # 打印推荐标题
        recommended_titles = analysis.get('recommended_titles', [])
        if recommended_titles:
            print("\n推荐标题:")
            for i, title in enumerate(recommended_titles, 1):
                print(f"{i}. {title}")
        
        # 打印内容方向
        content_directions = analysis.get('content_directions', [])
        if content_directions:
            print("\n内容方向:")
            for i, direction in enumerate(content_directions, 1):
                print(f"{i}. {direction}")
        
        # 打印摘要
        summary = analysis.get('summary', '')
        if summary:
            print(f"\n摘要: {summary[:200]}...")
            
        print("="*50)
        return True
        
    except Exception as e:
        logger.error(f"分析文章内容时发生错误: {e}")
        return False

async def test_direct_text_analysis():
    """测试直接文本分析功能"""
    logger.info("开始测试直接文本分析功能")
    
    # 检查API密钥是否设置
    if not DEEPSEEK_API_KEY:
        logger.warning("未设置DEEPSEEK_API_KEY环境变量，无法执行文本分析功能")
        print("\n请设置DEEPSEEK_API_KEY环境变量后再运行此测试")
        return False
    
    try:
        # 测试文本
        title = "人工智能的未来发展趋势"
        content = """
        人工智能技术正在迅速发展，改变着我们的生活和工作方式。从自动驾驶汽车到智能家居系统，
        从医疗诊断到金融分析，AI的应用范围不断扩大。未来，人工智能将更加注重与人类的协作，
        成为人类的助手而非替代者。同时，AI伦理和安全问题也日益受到重视，如何确保AI技术造福
        人类社会而非带来威胁，将是未来研究的重点方向。
        """
        
        # 使用直接文本分析接口
        start_time = time.time()
        result = await analyze_text_content(title, content)
        end_time = time.time()
        
        if "error" in result:
            logger.error(f"文本分析失败: {result['error']}")
            return False
        
        # 打印分析结果
        print("\n" + "="*50)
        print("直接文本分析结果:")
        print(f"处理时间: {end_time - start_time:.2f}秒")
        
        # 提取分析结果
        analysis = result.get("analysis_result", {})
        
        # 打印关键词和推荐标题
        keywords = analysis.get('keywords', [])
        recommended_titles = analysis.get('recommended_titles', [])
        
        if keywords:
            print("\n关键词:")
            for keyword in keywords[:5]:
                print(f"- {keyword}")
        
        if recommended_titles:
            print("\n推荐标题:")
            for title in recommended_titles[:3]:
                print(f"- {title}")
                
        print("="*50)
        return True
    except Exception as e:
        logger.error(f"直接文本分析失败: {e}")
        return False

async def main():
    """主函数"""
    print("=== 内容提取和分析测试 ===")
    
    # 检查API密钥是否设置
    if not DEEPSEEK_API_KEY:
        print("⚠️ 警告: DEEPSEEK_API_KEY 未设置，部分测试将被跳过")
    
    # 测试提取功能
    extraction_success = await test_dynamic_extraction()
    if extraction_success:
        print("✅ 内容提取测试通过")
    else:
        print("❌ 内容提取测试失败")
    
    # 测试统一分析功能
    analysis_success = await test_unified_analysis()
    if analysis_success:
        print("✅ 统一分析测试通过")
    else:
        print("❌ 统一分析测试失败")
    
    # 测试直接文本分析
    text_analysis_success = await test_direct_text_analysis()
    if text_analysis_success:
        print("✅ 直接文本分析测试通过")
    else:
        print("❌ 直接文本分析测试失败")

if __name__ == "__main__":
    # 运行异步测试
    asyncio.run(main())
