"""文章数据库访问服务"""
from typing import Dict, Any, Optional
import logging
from sqlalchemy.orm import Session
from src.database.session import get_db
from src.domain.article import (
    ArticleAnalysisRepository, 
    ArticleExtractionRepository
)

logger = logging.getLogger(__name__)

async def save_article_analysis(url: str, data: Dict[str, Any]) -> None:
    """保存文章分析结果
    
    Args:
        url: 文章URL
        data: 分析结果数据
    """
    try:
        db_generator = get_db()
        db: Session = next(db_generator)
        
        try:
            repo = ArticleAnalysisRepository(db)
            
            # 检查是否已存在
            existing = repo.get_by_url(url)
            if existing:
                # 如果存在则不重复保存
                logger.info(f"文章分析已存在，不重复保存: {url}")
                return
            
            # 保存新的分析
            repo.create_from_dict(url, data)
            logger.info(f"保存文章分析成功: {url}")
        finally:
            db.close()
    except Exception as e:
        logger.error(f"保存文章分析失败: {url} - {str(e)}")

async def get_article_analysis_by_url(url: str) -> Optional[Dict[str, Any]]:
    """获取文章分析结果
    
    Args:
        url: 文章URL
        
    Returns:
        分析结果数据，如果不存在则返回None
    """
    try:
        db_generator = get_db()
        db: Session = next(db_generator)
        
        try:
            repo = ArticleAnalysisRepository(db)
            analysis = repo.get_by_url(url)
            
            if analysis:
                logger.info(f"获取文章分析成功: {url}")
                return analysis.raw_data
            
            logger.info(f"未找到文章分析: {url}")
            return None
        finally:
            db.close()
    except Exception as e:
        logger.error(f"获取文章分析失败: {url} - {str(e)}")
        return None

async def save_article_extraction(url: str, data: Dict[str, Any]) -> None:
    """保存文章提取结果
    
    Args:
        url: 文章URL
        data: 提取结果数据
    """
    try:
        db_generator = get_db()
        db: Session = next(db_generator)
        
        try:
            repo = ArticleExtractionRepository(db)
            
            # 检查是否已存在
            existing = repo.get_by_url(url)
            if existing:
                # 如果存在则不重复保存
                logger.info(f"文章提取已存在，不重复保存: {url}")
                return
            
            # 保存新的提取
            repo.create_from_dict(url, data)
            logger.info(f"保存文章提取成功: {url}")
        finally:
            db.close()
    except Exception as e:
        logger.error(f"保存文章提取失败: {url} - {str(e)}")

async def get_article_extraction_by_url(url: str) -> Optional[Dict[str, Any]]:
    """获取文章提取结果
    
    Args:
        url: 文章URL
        
    Returns:
        提取结果数据，如果不存在则返回None
    """
    try:
        db_generator = get_db()
        db: Session = next(db_generator)
        
        try:
            repo = ArticleExtractionRepository(db)
            extraction = repo.get_by_url(url)
            
            if extraction:
                logger.info(f"获取文章提取成功: {url}")
                return extraction.raw_data
            
            logger.info(f"未找到文章提取: {url}")
            return None
        finally:
            db.close()
    except Exception as e:
        logger.error(f"获取文章提取失败: {url} - {str(e)}")
        return None 