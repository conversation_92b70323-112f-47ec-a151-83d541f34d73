"""用户数据库访问服务"""
from typing import Dict, Any, Optional
import logging
from sqlalchemy.orm import Session
from src.database.session import get_db
from src.domain.user import UserRepository
from src.domain.user_repository import RepositoryRepository

logger = logging.getLogger(__name__)

# 内存存储用户使用量
_user_usage_count = {}

async def get_user_by_id(user_id: str) -> Optional[Dict[str, Any]]:
    """获取用户信息
    
    Args:
        user_id: 用户ID
        
    Returns:
        用户信息，如果不存在则返回None
    """
    try:
        db_generator = get_db()
        db: Session = next(db_generator)
        
        try:
            repo = UserRepository(db)
            user = repo.get(int(user_id))
            
            if user:
                logger.info(f"获取用户成功: {user_id}")
                return {
                    "id": user.id,
                    "name": user.name,
                    "email": user.email,
                    "is_active": user.is_active
                }
            
            logger.info(f"未找到用户: {user_id}")
            return None
        finally:
            db.close()
    except Exception as e:
        logger.error(f"获取用户失败: {user_id} - {str(e)}")
        # 如果出错，返回一个最小的用户信息以避免阻止功能使用
        return {"id": user_id, "name": "未知用户"}

async def increase_user_usage_count(user_id: str, feature: str) -> None:
    """增加用户功能使用次数
    
    Args:
        user_id: 用户ID
        feature: 功能名称
    """
    try:
        # 首先更新内存计数
        if user_id not in _user_usage_count:
            _user_usage_count[user_id] = {}
        if feature not in _user_usage_count[user_id]:
            _user_usage_count[user_id][feature] = 0
        _user_usage_count[user_id][feature] += 1
        
        # 尝试更新数据库
        try:
            db_generator = get_db()
            db: Session = next(db_generator)
            
            try:
                repo = RepositoryRepository(db)
                # 检查是否存在记录
                repo_record = repo.get_user_repository(int(user_id), feature)
                
                if repo_record:
                    # 更新计数
                    repo.increment_usage_count(repo_record.id)
                    logger.info(f"更新用户使用量成功: {user_id} - {feature}")
                else:
                    # 创建新记录
                    repo.create_user_repository(int(user_id), feature, 1)
                    logger.info(f"创建用户使用量记录成功: {user_id} - {feature}")
            finally:
                db.close()
        except Exception as e:
            logger.error(f"更新用户使用量数据库记录失败: {user_id} - {feature} - {str(e)}")
            # 数据库操作失败，但内存计数已更新，不阻塞功能使用
    except Exception as e:
        logger.error(f"增加用户使用量失败: {user_id} - {feature} - {str(e)}")

# 仅用于测试目的
def get_memory_usage_count() -> Dict[str, Dict[str, int]]:
    """获取内存中的使用量统计"""
    return _user_usage_count.copy() 