"""数据库会话管理"""
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, Session
from src.config.settings import settings
from src.utils.logger import log_info, log_error
from src.config.logging_config import LoggerName

# 创建数据库引擎
engine = create_engine(
    settings.MYSQL_URL,
    pool_pre_ping=True,
    echo=settings.SQL_ECHO
)

# 创建会话工厂
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

def init_db() -> None:
    """初始化数据库"""
    try:
        from src.domain.base import Base
        Base.metadata.create_all(bind=engine)
        log_info(LoggerName.DATABASE, "数据库表初始化成功")
    except Exception as e:
        log_error(LoggerName.DATABASE,
                 "数据库表初始化失败",
                 error_type=type(e).__name__,
                 error_message=str(e)
        )
        raise

def get_db() -> Session:
    """获取数据库会话"""
    db = SessionLocal()
    try:
        yield db
        log_info(LoggerName.DATABASE, "数据库会话正常关闭")
    except Exception as e:
        log_error(LoggerName.DATABASE,
                 "数据库会话异常",
                 error_type=type(e).__name__,
                 error_message=str(e)
        )
        db.rollback()
        raise
    finally:
        db.close()
