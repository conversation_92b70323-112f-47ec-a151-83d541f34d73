"""
文章生成服务模块，提供文章自动生成和优化功能。

主要模块:
- article_processor: 文章处理器，提供完整的文章生成流程
- agents: 各种专门的文章处理代理
- tools: 提供各种辅助工具
- text_generator: 文本生成工具
- image_generator: 图像生成工具
- seo: SEO优化相关功能
"""

from src.services.generator.article_processor import (
    ArticleProcessor, 
    ArticleState,
    ArticleStatus
)

# 导出主要代理
from src.services.generator.agents import (
    ArticleAgent,
    PlanningAgent,
    WritingAgent,
    FinalizingAgent,
    SearchAgent
)

# 导出工具API
from src.services.generator.tools import (
    ToolRegistry,
    ToolNode,
    WebScraperTool,
    SearchEngineTool
)

# 导出SEO相关
from src.services.generator.seo import (
    SEOManager,
    ChineseSEOAnalyzer,
    SEOScore,
    SEOSuggestion
)

__all__ = [
    # 核心处理器
    'ArticleProcessor',
    'ArticleState',
    'ArticleStatus',
    
    # 代理
    'ArticleAgent',
    'PlanningAgent',
    'WritingAgent',
    'ImageGenerationAgent',
    'SEOAgent',
    'FinalizingAgent',
    'SearchAgent',
    
    # 工具
    'ToolRegistry',
    'ToolNode',
    'WebScraperTool',
    'SearchEngineTool',
    
    # SEO
    'SEOManager',
    'ChineseSEOAnalyzer',
    'SEOScore',
    'SEOSuggestion'
] 