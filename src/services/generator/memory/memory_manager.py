import os
import json
import time
from typing import Dict, List, Any, Optional, Callable

from src.config.logging_config import Lo<PERSON><PERSON><PERSON>
from src.utils.logger import CrawlerLogger, log_info, log_error, log_warning

class MemoryManager:
    """管理文章生成的长期记忆"""
    
    def __init__(self, storage_path: Optional[str] = None):
        """初始化记忆管理器
        
        Args:
            storage_path: 可选的记忆存储路径，用于持久化记忆
        """
        self.storage_path = storage_path
        self.memory = {
            "conversation_history": [],  # 对话历史
            "article_history": [],       # 文章生成历史
            "reflections": [],           # 反思记录
            "knowledge_base": {},        # 领域知识库
            "performance": {},           # 性能数据
        }
        self._load_memory()
    
    def _load_memory(self) -> None:
        """加载持久化记忆"""
        if not self.storage_path:
            return
        
        try:
            if os.path.exists(self.storage_path):
                with open(self.storage_path, 'r', encoding='utf-8') as f:
                    loaded_memory = json.load(f)
                    self.memory.update(loaded_memory)
                log_info(LoggerName.API, f"加载记忆成功，共{sum(len(v) if isinstance(v, list) else 1 for v in self.memory.values())}条记录")
            else:
                log_info(LoggerName.API, "没有找到现有记忆，将创建新记忆存储")
        except Exception as e:
            log_error(LoggerName.API, f"加载记忆失败: {str(e)}")
    
    def _save_memory(self) -> None:
        """保存记忆到持久化存储"""
        if not self.storage_path:
            return
        
        try:
            os.makedirs(os.path.dirname(self.storage_path), exist_ok=True)
            with open(self.storage_path, 'w', encoding='utf-8') as f:
                json.dump(self.memory, f, ensure_ascii=False, indent=2)
            log_info(LoggerName.API, f"保存记忆成功，共{sum(len(v) if isinstance(v, list) else 1 for v in self.memory.values())}条记录")
        except Exception as e:
            log_error(LoggerName.API, f"保存记忆失败: {str(e)}")
    
    def add_conversation(self, conversation: Dict[str, Any]) -> None:
        """添加对话记录到记忆
        
        Args:
            conversation: 对话记录字典
        """
        if "timestamp" not in conversation:
            conversation["timestamp"] = time.time()
            
        self.memory["conversation_history"].append({
            "timestamp": conversation.get("timestamp", None),
            "user_query": conversation.get("user_query", ""),
            "system_response": conversation.get("system_response", ""),
            "metadata": conversation.get("metadata", {})
        })
        self._save_memory()
    
    def add_article(self, article: Dict[str, Any]) -> None:
        """添加文章记录到记忆
        
        Args:
            article: 文章记录字典
        """
        if "timestamp" not in article:
            article["timestamp"] = time.time()
            
        # 仅存储关键信息，避免记忆过大
        self.memory["article_history"].append({
            "timestamp": article.get("timestamp", None),
            "title": article.get("title", ""),
            "keywords": article.get("keywords", []),
            "summary": article.get("summary", ""),
            "metadata": article.get("metadata", {})
        })
        self._save_memory()
    
    def add_reflection(self, reflection: Dict[str, str]) -> None:
        """添加反思记录
        
        Args:
            reflection: 反思记录，包含观察、思考和行动
        """
        if "timestamp" not in reflection:
            reflection["timestamp"] = time.time()
            
        self.memory["reflections"].append({
            "timestamp": reflection.get("timestamp", None),
            "observation": reflection.get("observation", ""),
            "thought": reflection.get("thought", ""),
            "action": reflection.get("action", ""),
            "expected_result": reflection.get("expected_result", "")
        })
        self._save_memory()
    
    def update_knowledge(self, domain: str, knowledge: Dict[str, Any]) -> None:
        """更新领域知识库
        
        Args:
            domain: 领域名称
            knowledge: 知识内容
        """
        if domain not in self.memory["knowledge_base"]:
            self.memory["knowledge_base"][domain] = {}
        
        self.memory["knowledge_base"][domain].update(knowledge)
        self._save_memory()
    
    def update_performance(self, metrics: Dict[str, Any]) -> None:
        """更新性能指标
        
        Args:
            metrics: 性能指标字典
        """
        self.memory["performance"].update(metrics)
        self._save_memory()
    
    def get_relevant_memories(self, query: str, limit: int = 5) -> Dict[str, List[Dict[str, Any]]]:
        """检索与查询相关的记忆
        
        简单实现，实际应用中可以使用向量数据库进行语义检索
        
        Args:
            query: 查询字符串
            limit: 每类记忆返回的最大条数
        
        Returns:
            相关记忆的字典
        """
        relevant_memories = {
            "conversations": [],
            "articles": [],
            "reflections": []
        }
        
        # 简单关键词匹配
        # 在实际应用中应替换为向量相似度搜索
        query_terms = query.lower().split()
        
        # 检索相关对话
        for conv in reversed(self.memory["conversation_history"]):
            if any(term in conv.get("user_query", "").lower() for term in query_terms):
                relevant_memories["conversations"].append(conv)
                if len(relevant_memories["conversations"]) >= limit:
                    break
        
        # 检索相关文章
        for article in reversed(self.memory["article_history"]):
            if any(term in article.get("title", "").lower() for term in query_terms) or \
               any(term in keyword.lower() for keyword in article.get("keywords", []) for term in query_terms):
                relevant_memories["articles"].append(article)
                if len(relevant_memories["articles"]) >= limit:
                    break
        
        # 检索相关反思
        for reflection in reversed(self.memory["reflections"]):
            if any(term in reflection.get("observation", "").lower() for term in query_terms) or \
               any(term in reflection.get("thought", "").lower() for term in query_terms):
                relevant_memories["reflections"].append(reflection)
                if len(relevant_memories["reflections"]) >= limit:
                    break
        
        return relevant_memories
    
    def get_domain_knowledge(self, domain: str) -> Dict[str, Any]:
        """获取特定领域的知识
        
        Args:
            domain: 领域名称
        
        Returns:
            领域知识字典
        """
        return self.memory["knowledge_base"].get(domain, {})
    
    def clear_memory(self, memory_type: Optional[str] = None) -> None:
        """清除指定类型的记忆
        
        Args:
            memory_type: 记忆类型，为None时清除所有记忆
        """
        if memory_type is None:
            self.memory = {
                "conversation_history": [],
                "article_history": [],
                "reflections": [],
                "knowledge_base": {},
                "performance": {},
            }
        elif memory_type in self.memory:
            if isinstance(self.memory[memory_type], list):
                self.memory[memory_type] = []
            elif isinstance(self.memory[memory_type], dict):
                self.memory[memory_type] = {}
        
        self._save_memory()
        
    def get_memory_summary(self) -> Dict[str, Any]:
        """获取记忆摘要信息
        
        Returns:
            记忆摘要字典
        """
        return {
            "conversation_count": len(self.memory.get("conversation_history", [])),
            "article_count": len(self.memory.get("article_history", [])),
            "reflection_count": len(self.memory.get("reflections", [])),
            "knowledge_domains": list(self.memory.get("knowledge_base", {}).keys()),
            "performance_metrics": list(self.memory.get("performance", {}).keys())
        }
    
    def get_recent_reflections(self, limit: int = 3) -> List[Dict[str, Any]]:
        """获取最近的反思记录
        
        Args:
            limit: 返回的最大反思数量
            
        Returns:
            最近的反思记录列表
        """
        reflections = sorted(
            self.memory.get("reflections", []),
            key=lambda x: x.get("timestamp", 0),
            reverse=True
        )
        return reflections[:limit]
    
    def search_article_by_keywords(self, keywords: List[str], limit: int = 5) -> List[Dict[str, Any]]:
        """根据关键词搜索文章
        
        Args:
            keywords: 关键词列表
            limit: 返回的最大文章数量
            
        Returns:
            匹配的文章列表
        """
        matched_articles = []
        lowercase_keywords = [keyword.lower() for keyword in keywords]
        
        for article in self.memory.get("article_history", []):
            article_keywords = [keyword.lower() for keyword in article.get("keywords", [])]
            if any(keyword in article_keywords for keyword in lowercase_keywords):
                matched_articles.append(article)
                if len(matched_articles) >= limit:
                    break
                    
        return matched_articles 