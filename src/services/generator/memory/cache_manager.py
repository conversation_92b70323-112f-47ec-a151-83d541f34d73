import json
import hashlib
from typing import Dict, Callable, Any, Optional

from src.config.logging_config import <PERSON><PERSON><PERSON><PERSON>
from src.utils.logger import CrawlerLogger, log_info, log_error

logger = CrawlerLogger().get_logger(LoggerName.API)

class CacheManager:
    """管理文章生成过程中的缓存"""
    
    def __init__(self, max_cache_size: int = 100, storage_path: Optional[str] = None):
        """初始化缓存管理器
        
        Args:
            max_cache_size: 最大缓存项数量
            storage_path: 缓存持久化存储路径
        """
        self.cache = {}
        self.max_cache_size = max_cache_size
        self.storage_path = storage_path
        self._load_cache()
        
    def _generate_key(self, prefix: str, data: Any) -> str:
        """生成缓存键
        
        Args:
            prefix: 键前缀
            data: 用于生成键的数据
            
        Returns:
            缓存键
        """
        if isinstance(data, str):
            hash_input = data
        else:
            try:
                hash_input = json.dumps(data, sort_keys=True)
            except:
                hash_input = str(data)
        
        hash_value = hashlib.md5(hash_input.encode('utf-8')).hexdigest()
        return f"{prefix}_{hash_value}"
    
    def _load_cache(self) -> None:
        """从存储加载缓存"""
        if not self.storage_path:
            return
            
        try:
            import os
            if os.path.exists(self.storage_path):
                with open(self.storage_path, 'r', encoding='utf-8') as f:
                    self.cache = json.load(f)
                log_info(LoggerName.API, f"加载缓存成功, 共{len(self.cache)}项")
        except Exception as e:
            log_error(LoggerName.API, f"加载缓存失败: {str(e)}")
            self.cache = {}
    
    def _save_cache(self) -> None:
        """将缓存保存到存储"""
        if not self.storage_path:
            return
            
        try:
            import os
            os.makedirs(os.path.dirname(self.storage_path), exist_ok=True)
            with open(self.storage_path, 'w', encoding='utf-8') as f:
                json.dump(self.cache, f, ensure_ascii=False)
            log_info(LoggerName.API, f"保存缓存成功, 共{len(self.cache)}项")
        except Exception as e:
            log_error(LoggerName.API, f"保存缓存失败: {str(e)}")
    
    def _manage_cache_size(self) -> None:
        """管理缓存大小，超出限制时删除最早的项"""
        if len(self.cache) > self.max_cache_size:
            # 简单实现: 删除前max_cache_size/4个项
            items_to_delete = int(self.max_cache_size / 4)
            keys_to_delete = list(self.cache.keys())[:items_to_delete]
            for key in keys_to_delete:
                del self.cache[key]
            log_info(LoggerName.API, f"缓存清理完成, 删除了{items_to_delete}项, 当前{len(self.cache)}项")
    
    async def get_or_generate(self, key_prefix: str, key_data: Any, generator_func: Callable) -> Any:
        """从缓存获取数据或生成新数据
        
        Args:
            key_prefix: 缓存键前缀
            key_data: 用于生成缓存键的数据
            generator_func: 生成数据的异步函数
            
        Returns:
            缓存的数据或新生成的数据
        """
        cache_key = self._generate_key(key_prefix, key_data)
        
        if cache_key in self.cache:
            log_info(LoggerName.API, f"缓存命中: {cache_key[:15]}...")
            return self.cache[cache_key]
        
        log_info(LoggerName.API, f"缓存未命中: {cache_key[:15]}..., 正在生成")
        try:
            result = await generator_func()
            self.cache[cache_key] = result
            self._manage_cache_size()
            self._save_cache()
            return result
        except Exception as e:
            log_error(LoggerName.API, f"生成数据失败: {str(e)}")
            raise
    
    async def get_or_generate_article(self, title: str, keywords: list, generator_func: Callable) -> Any:
        """从缓存获取文章或生成新文章
        
        Args:
            title: 文章标题
            keywords: 关键词列表
            generator_func: 生成文章的异步函数
            
        Returns:
            缓存的文章或新生成的文章
        """
        cache_key_data = {
            "title": title,
            "keywords": sorted(keywords)
        }
        return await self.get_or_generate("article", cache_key_data, generator_func)
    
    async def get_or_generate_image(self, prompt: str, style: str, generator_func: Callable) -> Any:
        """从缓存获取图片或生成新图片
        
        Args:
            prompt: 图片提示词
            style: 图片风格
            generator_func: 生成图片的异步函数
            
        Returns:
            缓存的图片或新生成的图片
        """
        cache_key_data = {
            "prompt": prompt,
            "style": style
        }
        return await self.get_or_generate("image", cache_key_data, generator_func)
    
    async def get_or_generate_section(self, title: str, keywords: list, generator_func: Callable) -> Any:
        """从缓存获取文章章节或生成新章节
        
        Args:
            title: 章节标题
            keywords: 关键词列表
            generator_func: 生成章节的异步函数
            
        Returns:
            缓存的章节或新生成的章节
        """
        cache_key_data = {
            "title": title,
            "keywords": sorted(keywords)
        }
        return await self.get_or_generate("section", cache_key_data, generator_func)
    
    def clear_cache(self, prefix: Optional[str] = None) -> None:
        """清除缓存
        
        Args:
            prefix: 要清除的缓存键前缀，为None时清除所有缓存
        """
        if prefix is None:
            self.cache = {}
            log_info(LoggerName.API, "已清空所有缓存")
        else:
            keys_to_delete = [key for key in self.cache.keys() if key.startswith(prefix)]
            for key in keys_to_delete:
                del self.cache[key]
            log_info(LoggerName.API, f"已清除前缀为 '{prefix}' 的缓存, 共{len(keys_to_delete)}项")
        
        self._save_cache()
        
    def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息
        
        Returns:
            缓存统计信息字典
        """
        prefixes = {}
        for key in self.cache.keys():
            prefix = key.split('_')[0]
            prefixes[prefix] = prefixes.get(prefix, 0) + 1
            
        return {
            "total_items": len(self.cache),
            "max_size": self.max_cache_size,
            "usage_percent": len(self.cache) / self.max_cache_size * 100 if self.max_cache_size > 0 else 0,
            "categories": prefixes
        } 