import os
import json
import logging
import asyncio
from typing import Dict, List, Any, Optional, Union, Tuple
from dataclasses import dataclass, field, asdict

from langchain_community.vectorstores import FAISS
from langchain_community.embeddings import HuggingFaceEmbeddings
from langchain_text_splitters import RecursiveCharacterTextSplitter
from langchain_community.document_loaders import (
    TextLoader, PyPDFLoader, CSVLoader, WebBaseLoader, JSONLoader
)

# 配置日志
logger = logging.getLogger("rag_manager")

@dataclass
class DocumentChunk:
    """文档片段数据类"""
    content: str
    metadata: Dict[str, Any] = field(default_factory=dict)
    score: float = 0.0  # 相关性分数
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return asdict(self)


class DocumentRetriever:
    """文档检索器，负责加载、索引和检索文档"""
    
    def __init__(self, collection_name: str = "default", embedding_model: str = None):
        """初始化文档检索器
        
        Args:
            collection_name: 集合名称
            embedding_model: 嵌入模型名称，默认为None（使用默认模型）
        """
        self.collection_name = collection_name
        self.embedding_model = embedding_model or "moka-ai/m3e-base"  # 默认使用中文友好的嵌入模型
        
        # 数据目录
        self.data_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "data")
        self.index_dir = os.path.join(self.data_dir, "indexes", collection_name)
        os.makedirs(self.index_dir, exist_ok=True)
        
        # 初始化嵌入模型和向量存储
        self.embeddings = None
        self.vector_store = None
    
    def _ensure_embeddings(self) -> None:
        """确保嵌入模型已加载"""
        if self.embeddings is None:
            try:
                logger.info(f"加载嵌入模型: {self.embedding_model}")
                self.embeddings = HuggingFaceEmbeddings(
                    model_name=self.embedding_model,
                    model_kwargs={'device': 'cpu'},
                    encode_kwargs={'normalize_embeddings': True}
                )
                logger.info("嵌入模型加载成功")
            except Exception as e:
                logger.error(f"加载嵌入模型失败: {str(e)}")
                # 使用备用模型
                try:
                    logger.info("尝试加载备用嵌入模型")
                    self.embeddings = HuggingFaceEmbeddings(
                        model_name="shibing624/text2vec-base-chinese",
                        model_kwargs={'device': 'cpu'},
                        encode_kwargs={'normalize_embeddings': True}
                    )
                    logger.info("备用嵌入模型加载成功")
                except Exception as e:
                    logger.error(f"加载备用嵌入模型失败: {str(e)}")
                    raise
    
    def _ensure_vector_store(self) -> None:
        """确保向量存储已加载"""
        if self.vector_store is None:
            self._ensure_embeddings()
            
            # 尝试加载现有索引
            if os.path.exists(self.index_dir):
                try:
                    logger.info(f"加载向量索引: {self.collection_name}")
                    self.vector_store = FAISS.load_local(self.index_dir, self.embeddings)
                    logger.info(f"向量索引加载成功")
                    return
                except Exception as e:
                    logger.error(f"加载向量索引失败: {str(e)}")
            
            # 创建新的空向量存储
            logger.info(f"创建新的向量索引: {self.collection_name}")
            self.vector_store = FAISS.from_texts(["初始化向量数据库"], self.embeddings)
            self._save_vector_store()
    
    def _save_vector_store(self) -> None:
        """保存向量存储"""
        if self.vector_store is None:
            return
        
        try:
            logger.info(f"保存向量索引: {self.collection_name}")
            self.vector_store.save_local(self.index_dir)
            logger.info(f"向量索引保存成功")
        except Exception as e:
            logger.error(f"保存向量索引失败: {str(e)}")
    
    def load_document(self, file_path: str, metadata: Dict[str, Any] = None) -> List[DocumentChunk]:
        """加载文档
        
        Args:
            file_path: 文件路径
            metadata: 附加元数据
            
        Returns:
            文档片段列表
        """
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"文件不存在: {file_path}")
        
        # 根据文件扩展名选择合适的加载器
        _, ext = os.path.splitext(file_path.lower())
        
        try:
            if ext in ['.txt', '.md']:
                loader = TextLoader(file_path, encoding='utf-8')
            elif ext == '.pdf':
                loader = PyPDFLoader(file_path)
            elif ext == '.csv':
                loader = CSVLoader(file_path)
            elif ext == '.json':
                def json_func(data, metadata):
                    return data
                loader = JSONLoader(file_path, jq_schema='.', content_key='content', json_lines=False)
            else:
                raise ValueError(f"不支持的文件类型: {ext}")
            
            logger.info(f"加载文档: {file_path}")
            documents = loader.load()
            
            # 添加元数据
            base_metadata = {
                "source": file_path,
                "file_type": ext[1:],  # 去掉点号
            }
            
            if metadata:
                base_metadata.update(metadata)
            
            for doc in documents:
                doc.metadata.update(base_metadata)
            
            # 分割文档
            text_splitter = RecursiveCharacterTextSplitter(
                chunk_size=1000,
                chunk_overlap=200,
                separators=["\n\n", "\n", "。", "！", "？", ".", "!", "?", " ", ""],
                is_separator_regex=False
            )
            
            split_docs = text_splitter.split_documents(documents)
            logger.info(f"文档分割为 {len(split_docs)} 个片段")
            
            # 转换为DocumentChunk
            chunks = []
            for doc in split_docs:
                chunks.append(DocumentChunk(
                    content=doc.page_content,
                    metadata=doc.metadata
                ))
            
            return chunks
        
        except Exception as e:
            logger.error(f"加载文档失败: {str(e)}")
            raise
    
    def add_chunks(self, chunks: List[DocumentChunk]) -> None:
        """添加文档片段到向量存储
        
        Args:
            chunks: 文档片段列表
        """
        if not chunks:
            return
        
        self._ensure_vector_store()
        
        try:
            texts = [chunk.content for chunk in chunks]
            metadatas = [chunk.metadata for chunk in chunks]
            
            logger.info(f"添加 {len(chunks)} 个文档片段到向量存储")
            self.vector_store.add_texts(texts, metadatas)
            self._save_vector_store()
        except Exception as e:
            logger.error(f"添加文档片段失败: {str(e)}")
            raise
    
    def search(self, query: str, top_k: int = 3) -> List[DocumentChunk]:
        """搜索相关文档片段
        
        Args:
            query: 查询文本
            top_k: 返回结果数量
            
        Returns:
            相关文档片段列表
        """
        self._ensure_vector_store()
        
        try:
            logger.info(f"搜索: {query} (top_k={top_k})")
            results = self.vector_store.similarity_search_with_score(query, k=top_k)
            
            chunks = []
            for doc, score in results:
                # 将分数转换为0-1范围，其中1为最相关
                normalized_score = float(1.0 - min(1.0, max(0.0, score / 2.0)))
                
                chunks.append(DocumentChunk(
                    content=doc.page_content,
                    metadata=doc.metadata,
                    score=normalized_score
                ))
            
            return chunks
        except Exception as e:
            logger.error(f"搜索失败: {str(e)}")
            raise
    
    def clear(self) -> None:
        """清空向量存储"""
        # 重置向量存储
        self.vector_store = None
        
        # 删除索引文件
        if os.path.exists(self.index_dir):
            for filename in os.listdir(self.index_dir):
                try:
                    os.remove(os.path.join(self.index_dir, filename))
                except Exception as e:
                    logger.error(f"删除索引文件 {filename} 失败: {str(e)}")
        
        # 创建新的空向量存储
        self._ensure_vector_store()


class RAGManager:
    """RAG管理器，协调文档检索和增强生成"""
    
    def __init__(self):
        """初始化RAG管理器"""
        self.retrievers: Dict[str, DocumentRetriever] = {}
    
    def get_retriever(self, collection_name: str) -> DocumentRetriever:
        """获取或创建文档检索器
        
        Args:
            collection_name: 集合名称
            
        Returns:
            文档检索器
        """
        if collection_name not in self.retrievers:
            self.retrievers[collection_name] = DocumentRetriever(collection_name)
        
        return self.retrievers[collection_name]
    
    async def add_document(self, file_path: str, collection_name: str = "default", metadata: Dict[str, Any] = None) -> Dict[str, Any]:
        """添加文档到指定集合
        
        Args:
            file_path: 文件路径
            collection_name: 集合名称
            metadata: 附加元数据
            
        Returns:
            添加结果
        """
        retriever = self.get_retriever(collection_name)
        
        try:
            # 加载文档
            chunks = retriever.load_document(file_path, metadata)
            
            # 添加到向量存储
            retriever.add_chunks(chunks)
            
            return {
                "success": True,
                "collection": collection_name,
                "file_path": file_path,
                "chunk_count": len(chunks)
            }
        except Exception as e:
            logger.error(f"添加文档失败: {str(e)}")
            return {
                "success": False,
                "collection": collection_name,
                "file_path": file_path,
                "error": str(e)
            }
    
    async def search_documents(self, query: str, collection_name: str = "default", top_k: int = 3) -> Dict[str, Any]:
        """搜索文档
        
        Args:
            query: 查询文本
            collection_name: 集合名称
            top_k: 返回结果数量
            
        Returns:
            搜索结果
        """
        retriever = self.get_retriever(collection_name)
        
        try:
            # 搜索文档
            chunks = retriever.search(query, top_k)
            
            # 格式化结果
            results = []
            for chunk in chunks:
                results.append({
                    "content": chunk.content,
                    "metadata": chunk.metadata,
                    "score": chunk.score
                })
            
            return {
                "success": True,
                "collection": collection_name,
                "query": query,
                "results": results,
                "result_count": len(results)
            }
        except Exception as e:
            logger.error(f"搜索文档失败: {str(e)}")
            return {
                "success": False,
                "collection": collection_name,
                "query": query,
                "error": str(e)
            }
    
    async def generate_with_rag(self, query: str, collection_name: str = "default", top_k: int = 3, llm_manager = None) -> Dict[str, Any]:
        """使用RAG生成回答
        
        Args:
            query: 查询文本
            collection_name: 集合名称
            top_k: 检索结果数量
            llm_manager: LLM管理器
            
        Returns:
            生成结果
        """
        # 搜索相关文档
        search_result = await self.search_documents(query, collection_name, top_k)
        
        if not search_result.get("success", False):
            return search_result
        
        results = search_result.get("results", [])
        
        # 如果没有检索到结果
        if not results:
            return {
                "success": True,
                "collection": collection_name,
                "query": query,
                "answer": "没有找到相关信息，无法回答该问题。",
                "contexts": [],
                "has_context": False
            }
        
        # 提取上下文
        contexts = []
        for result in results:
            contexts.append(result["content"])
        
        context_text = "\n\n".join(contexts)
        
        # 如果没有LLM管理器
        if llm_manager is None:
            return {
                "success": True,
                "collection": collection_name,
                "query": query,
                "contexts": results,
                "has_context": True,
                "answer": "检索到相关信息，但无法生成回答，因为未提供LLM管理器。"
            }
        
        # 构建提示
        prompt = f"""请基于以下信息回答用户的问题。如果提供的信息不足以回答问题，请坦诚表明无法回答。

[问题]
{query}

[相关信息]
{context_text}

[回答]
"""
        
        try:
            # 使用LLM生成回答
            logger.info("使用LLM生成RAG回答")
            response = await llm_manager.generate(prompt)
            answer = response.get("content", "")
            
            return {
                "success": True,
                "collection": collection_name,
                "query": query,
                "contexts": results,
                "has_context": True,
                "answer": answer
            }
        except Exception as e:
            logger.error(f"生成RAG回答失败: {str(e)}")
            return {
                "success": False,
                "collection": collection_name,
                "query": query,
                "contexts": results,
                "has_context": True,
                "error": str(e)
            }
    
    async def clear_collection(self, collection_name: str) -> Dict[str, Any]:
        """清空集合
        
        Args:
            collection_name: 集合名称
            
        Returns:
            操作结果
        """
        if collection_name not in self.retrievers:
            return {
                "success": False,
                "collection": collection_name,
                "error": f"集合不存在: {collection_name}"
            }
        
        try:
            # 清空向量存储
            self.retrievers[collection_name].clear()
            
            return {
                "success": True,
                "collection": collection_name,
                "message": f"集合已清空: {collection_name}"
            }
        except Exception as e:
            logger.error(f"清空集合失败: {str(e)}")
            return {
                "success": False,
                "collection": collection_name,
                "error": str(e)
            } 