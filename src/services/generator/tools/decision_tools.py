import json
import re
import time
import logging
from typing import Dict, List, Any, Optional, Tuple, Union
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.output_parsers import StrOutputParser
from langchain_deepseek import ChatDeepSeek
from src.services.generator.tools.tool_node import Tool, tool
from src.config.api_config import get_deepseek_api_key

# 替换不存在的日志配置导入
logger = logging.getLogger("decision_tools")

class ReActReasoningTool(Tool):
    """ReAct推理工具 - 实现思考-行动-观察循环推理"""
    
    def __init__(self):
        """初始化ReAct推理工具"""
        super().__init__(
            name="react_reasoning",
            description="使用思考-行动-观察循环进行任务推理和解决问题"
        )
        self.llm = ChatDeepSeek(
            model="deepseek-chat",
            temperature=0.7,
            api_key=get_deepseek_api_key()
        )
    
    async def execute(self, params: Dict[str, Any], state: Dict[str, Any]) -> Dict[str, Any]:
        """执行ReAct推理
        
        Args:
            params: 
                task: 要执行的任务描述
                context: 任务上下文
                max_iterations: 最大迭代次数，默认为5
                stop_on_success: 成功时是否停止，默认为True
            state: 当前状态
            
        Returns:
            推理结果
        """
        try:
            # 获取参数
            task = params.get('task')
            context = params.get('context', '')
            max_iterations = params.get('max_iterations', 5)
            stop_on_success = params.get('stop_on_success', True)
            
            # 验证参数
            if not task:
                raise ValueError("必须提供task参数")
            
            # 初始化ReAct循环
            prompt_template = """
            你是一个通过思考-行动-观察循环来解决问题的AI助手。
            你需要执行以下任务:
            {task}
            
            相关上下文:
            {context}
            
            每一步，你都需要:
            1. 思考(Thought): 分析当前情况，思考可能的解决方案
            2. 行动(Action): 决定要采取的行动
            3. 观察(Observation): 观察行动的结果
            
            以下是已经进行的步骤:
            {history}
            
            请继续这个思考-行动-观察循环，提供下一步。
            如果你认为任务已经完成，或者无法继续，请在最后一步提供总结。
            
            请使用以下格式:
            思考: <你的思考过程>
            行动: <你决定采取的行动>
            观察: <行动后的观察>
            
            不要跳过任何步骤，保持严格的思考-行动-观察循环。
            """
            
            prompt = ChatPromptTemplate.from_template(prompt_template)
            chain = prompt | self.llm | StrOutputParser()
            
            # 初始化历史记录
            history = []
            completed = False
            final_answer = None
            
            # 执行ReAct循环
            for i in range(max_iterations):
                if completed:
                    break
                
                # 构建历史记录字符串
                history_str = ""
                for idx, h in enumerate(history):
                    history_str += f"步骤 {idx+1}:\n"
                    history_str += f"思考: {h.get('thought', '')}\n"
                    history_str += f"行动: {h.get('action', '')}\n"
                    history_str += f"观察: {h.get('observation', '')}\n\n"
                
                # 获取下一步
                result = await chain.ainvoke({
                    "task": task,
                    "context": context,
                    "history": history_str
                })
                
                # 解析结果
                thought_match = re.search(r"思考:(.*?)行动:", result, re.DOTALL)
                action_match = re.search(r"行动:(.*?)观察:", result, re.DOTALL)
                observation_match = re.search(r"观察:(.*?)($|思考:)", result, re.DOTALL)
                
                thought = thought_match.group(1).strip() if thought_match else ""
                action = action_match.group(1).strip() if action_match else ""
                observation = observation_match.group(1).strip() if observation_match else ""
                
                # 记录步骤
                step = {
                    "thought": thought,
                    "action": action,
                    "observation": observation
                }
                history.append(step)
                
                # 检查是否完成
                if stop_on_success and ("任务完成" in observation or "完成任务" in observation):
                    completed = True
                    final_answer = observation
            
            # 提取最终结果
            if not final_answer and history:
                final_answer = history[-1].get('observation', '')
            
            return {
                "task": task,
                "steps": history,
                "iterations": len(history),
                "completed": completed,
                "final_answer": final_answer
            }
            
        except Exception as e:
            logger.error(f"ReAct推理失败: {str(e)}")
            raise


class TaskDecompositionTool(Tool):
    """任务分解工具 - 将复杂任务分解为简单步骤"""
    
    def __init__(self):
        """初始化任务分解工具"""
        super().__init__(
            name="task_decomposition",
            description="将复杂任务分解为一系列简单的步骤，以便更容易执行"
        )
        self.llm = ChatDeepSeek(
            model="deepseek-chat",
            temperature=0.5,
            api_key=get_deepseek_api_key()
        )
    
    async def execute(self, params: Dict[str, Any], state: Dict[str, Any]) -> Dict[str, Any]:
        """执行任务分解
        
        Args:
            params: 
                task: 要分解的任务描述
                context: 任务上下文
                detail_level: 详细程度，可选"low", "medium", "high"，默认为"medium"
                max_steps: 最大步骤数，默认为10
                return_format: 返回格式，可选"text", "json"，默认为"json"
            state: 当前状态
            
        Returns:
            分解后的任务步骤
        """
        try:
            # 获取参数
            task = params.get('task')
            context = params.get('context', '')
            detail_level = params.get('detail_level', 'medium')
            max_steps = params.get('max_steps', 10)
            return_format = params.get('return_format', 'json')
            
            # 验证参数
            if not task:
                raise ValueError("必须提供task参数")
            
            # 按照详细程度调整提示词
            level_descriptions = {
                "low": "提供3-5个高级步骤",
                "medium": "提供5-8个中等详细程度的步骤",
                "high": "提供8-10个非常详细的步骤，包括每个步骤的子步骤"
            }
            detail_description = level_descriptions.get(detail_level, level_descriptions["medium"])
            
            # 准备提示词
            prompt_template = """
            你是一个擅长任务分解的AI助手。请将以下复杂任务分解为可执行的步骤:
            
            任务: {task}
            
            上下文信息: {context}
            
            请{detail_level}，确保:
            1. 步骤是合乎逻辑且按顺序排列的
            2. 每个步骤都是具体、可行的
            3. 每个步骤都有明确的目标
            4. 步骤总数不超过{max_steps}个
            
            {format_instruction}
            """
            
            # 根据返回格式设置格式指令
            if return_format == 'json':
                format_instruction = """
                以JSON格式返回结果，使用以下格式:
                {
                    "task": "原始任务",
                    "steps": [
                        {
                            "step_number": 1,
                            "description": "步骤描述",
                            "expected_outcome": "预期结果",
                            "sub_steps": ["子步骤1", "子步骤2"] // 如果适用
                        },
                        // 更多步骤...
                    ]
                }
                """
            else:
                format_instruction = """
                按照以下格式返回步骤:
                
                任务: [原始任务]
                
                步骤 1: [步骤描述]
                预期结果: [预期结果]
                子步骤(如适用):
                  - [子步骤1]
                  - [子步骤2]
                
                步骤 2: [步骤描述]
                ...
                """
            
            prompt = ChatPromptTemplate.from_template(prompt_template)
            chain = prompt | self.llm | StrOutputParser()
            
            # 执行任务分解
            result = await chain.ainvoke({
                "task": task,
                "context": context,
                "detail_level": detail_description,
                "max_steps": max_steps,
                "format_instruction": format_instruction
            })
            
            # 处理结果
            if return_format == 'json':
                try:
                    # 尝试提取JSON
                    json_match = re.search(r'```json\s*(.*?)\s*```', result, re.DOTALL)
                    if json_match:
                        result = json_match.group(1)
                    
                    # 解析JSON
                    parsed_result = json.loads(result)
                    return parsed_result
                except json.JSONDecodeError:
                    # 如果JSON解析失败，返回原始文本
                    logger.warning("JSON解析失败，返回原始文本")
                    return {
                        "task": task,
                        "steps": [],
                        "raw_result": result
                    }
            else:
                # 返回原始文本
                return {
                    "task": task,
                    "raw_result": result
                }
            
        except Exception as e:
            logger.error(f"任务分解失败: {str(e)}")
            raise


class SelfCorrectionTool(Tool):
    """自我校正工具 - 用于自我评估和校正生成内容"""
    
    def __init__(self):
        """初始化自我校正工具"""
        super().__init__(
            name="self_correction",
            description="对生成的内容进行自我评估和校正，提高质量和准确性"
        )
        self.llm = ChatDeepSeek(
            model="deepseek-chat",
            temperature=0.3,
            api_key=get_deepseek_api_key()
        )
    
    async def execute(self, params: Dict[str, Any], state: Dict[str, Any]) -> Dict[str, Any]:
        """执行自我校正
        
        Args:
            params: 
                content: 要校正的内容
                criteria: 校正标准，如"准确性"、"完整性"、"逻辑性"、"all"
                correction_depth: 校正深度，可选"light", "medium", "thorough"，默认为"medium"
                domain: 专业领域，用于专业知识校正
            state: 当前状态
            
        Returns:
            校正结果
        """
        try:
            # 获取参数
            content = params.get('content')
            criteria = params.get('criteria', 'all')
            correction_depth = params.get('correction_depth', 'medium')
            domain = params.get('domain', '')
            
            # 验证参数
            if not content:
                raise ValueError("必须提供content参数")
            
            # 准备校正标准
            criteria_descriptions = {
                "accuracy": "内容的准确性，包括事实、数据和论点",
                "completeness": "内容是否完整，是否涵盖了所有必要信息",
                "logic": "内容的逻辑性和连贯性",
                "style": "内容的风格、语调和表达方式",
                "readability": "内容的可读性和清晰度",
                "objectivity": "内容的客观性和公正性"
            }
            
            if criteria == 'all':
                criteria_description = "所有方面，包括准确性、完整性、逻辑性、风格、可读性和客观性"
                criteria_list = list(criteria_descriptions.keys())
            else:
                criteria_list = criteria.split(',')
                criteria_description = ", ".join([criteria_descriptions.get(c.strip(), c.strip()) for c in criteria_list])
            
            # 校正深度设置
            depth_descriptions = {
                "light": "进行轻度校正，主要关注明显错误",
                "medium": "进行中度校正，关注主要问题和改进空间",
                "thorough": "进行彻底校正，全面评估并详细改进内容"
            }
            depth_description = depth_descriptions.get(correction_depth, depth_descriptions["medium"])
            
            # 准备提示词
            prompt_template = """
            你是一个专业的内容校正助手。请对以下内容进行评估和校正:
            
            内容:
            ```
            {content}
            ```
            
            校正标准: {criteria_description}
            校正深度: {depth_description}
            {domain_instruction}
            
            请按照以下步骤进行校正:
            
            1. 评估: 对内容进行整体评估，指出优点和不足
            2. 详细分析: 针对每个校正标准，详细分析内容的问题
            3. 建议修改: 提供具体的修改建议
            4. 重写内容: 提供校正后的完整内容
            
            请使用以下JSON格式返回结果:
            ```json
            {
                "evaluation": {
                    "overall_score": "1-10分",
                    "strengths": ["优点1", "优点2", ...],
                    "weaknesses": ["不足1", "不足2", ...]
                },
                "detailed_analysis": {
                    "criterion1": {
                        "score": "1-10分",
                        "issues": ["问题1", "问题2", ...],
                        "suggestions": ["建议1", "建议2", ...]
                    },
                    // 更多标准...
                },
                "corrected_content": "校正后的完整内容"
            }
            ```
            """
            
            # 添加领域特定指令
            domain_instruction = f"专业领域: {domain}，请根据该领域的专业知识和标准进行校正" if domain else ""
            
            prompt = ChatPromptTemplate.from_template(prompt_template)
            chain = prompt | self.llm | StrOutputParser()
            
            # 执行自我校正
            result = await chain.ainvoke({
                "content": content,
                "criteria_description": criteria_description,
                "depth_description": depth_description,
                "domain_instruction": domain_instruction
            })
            
            # 解析JSON结果
            try:
                # 尝试提取JSON
                json_match = re.search(r'```json\s*(.*?)\s*```', result, re.DOTALL)
                if json_match:
                    result = json_match.group(1)
                
                # 解析JSON
                parsed_result = json.loads(result)
                
                # 添加元数据
                parsed_result["metadata"] = {
                    "criteria": criteria_list,
                    "correction_depth": correction_depth,
                    "domain": domain if domain else "general",
                    "timestamp": time.time()
                }
                
                return parsed_result
            except json.JSONDecodeError:
                # 如果JSON解析失败，返回原始文本和错误信息
                logger.warning("自我校正结果的JSON解析失败")
                return {
                    "error": "JSON解析失败",
                    "raw_result": result,
                    "metadata": {
                        "criteria": criteria_list,
                        "correction_depth": correction_depth,
                        "domain": domain if domain else "general",
                        "timestamp": time.time()
                    }
                }
            
        except Exception as e:
            logger.error(f"自我校正失败: {str(e)}")
            raise


# 使用装饰器注册简单工具
@tool(
    name="brainstorm",
    description="进行头脑风暴，生成创意想法",
    category="decision"
)
async def brainstorm_tool(params: Dict[str, Any], state: Dict[str, Any]) -> Dict[str, Any]:
    """进行头脑风暴，生成创意想法
    
    Args:
        params:
            topic: 头脑风暴主题
            num_ideas: 要生成的想法数量，默认为5
            creativity_level: 创造性级别，范围1-10，默认为7
        state: 当前状态
        
    Returns:
        头脑风暴结果
    """
    # 获取参数
    topic = params.get('topic')
    num_ideas = params.get('num_ideas', 5)
    creativity_level = params.get('creativity_level', 7)
    
    # 验证参数
    if not topic:
        raise ValueError("必须提供topic参数")
    
    # 准备提示词
    prompt_template = """
    进行头脑风暴，为以下主题生成{num_ideas}个有创意的想法:
    
    主题: {topic}
    
    创造性级别: {creativity_level}/10 (数字越大，想法越创新但可能越不切实际)
    
    为每个想法提供:
    1. 简短标题
    2. 简要描述
    3. 可能的应用或用途
    
    以JSON数组格式返回结果。
    """
    
    # 创建临时LLM实例并执行
    llm = ChatDeepSeek(
        model="deepseek-chat",
        temperature=creativity_level / 10,
        api_key=get_deepseek_api_key()
    )
    prompt = ChatPromptTemplate.from_template(prompt_template)
    chain = prompt | llm | StrOutputParser()
    
    result = await chain.ainvoke({
        "topic": topic,
        "num_ideas": num_ideas,
        "creativity_level": creativity_level
    })
    
    # 尝试解析JSON
    try:
        # 尝试提取JSON
        json_match = re.search(r'```json\s*(.*?)\s*```', result, re.DOTALL)
        if json_match:
            result = json_match.group(1)
        
        # 解析JSON
        ideas = json.loads(result)
        
        return {
            "topic": topic,
            "creativity_level": creativity_level,
            "ideas": ideas
        }
    except json.JSONDecodeError:
        # 如果JSON解析失败，尝试手动解析
        ideas_pattern = r'(\d+\.\s*.+?(?=\d+\.\s*|\Z))'
        matches = re.findall(ideas_pattern, result, re.DOTALL)
        ideas = [m.strip() for m in matches]
        
        return {
            "topic": topic,
            "creativity_level": creativity_level,
            "ideas": ideas,
            "note": "JSON解析失败，返回手动提取的想法"
        }


@tool(
    name="decision_matrix",
    description="创建决策矩阵，帮助在多个选项中做出选择",
    category="decision"
)
async def decision_matrix_tool(params: Dict[str, Any], state: Dict[str, Any]) -> Dict[str, Any]:
    """创建决策矩阵，帮助在多个选项中做出选择
    
    Args:
        params:
            options: 选项列表
            criteria: 评估标准列表
            weights: 各标准的权重字典
        state: 当前状态
        
    Returns:
        决策矩阵分析结果
    """
    # 获取参数
    options = params.get('options', [])
    criteria = params.get('criteria', [])
    weights = params.get('weights', {})
    
    # 验证参数
    if not options:
        raise ValueError("必须提供options参数")
    if not criteria:
        raise ValueError("必须提供criteria参数")
    
    # 为未提供权重的标准设置默认权重1.0
    for criterion in criteria:
        if criterion not in weights:
            weights[criterion] = 1.0
    
    # 准备提示词
    prompt_template = """
    请根据以下信息创建决策矩阵，并做出推荐:
    
    选项: {options}
    评估标准: {criteria}
    各标准权重: {weights}
    
    对每个选项在每个标准上评分(1-10)，然后计算加权总分。
    提供评分理由，并最终推荐最佳选项。
    
    以JSON格式返回结果。
    """
    
    # 创建临时LLM实例并执行
    llm = ChatDeepSeek(
        model="deepseek-chat",
        temperature=0.2,
        api_key=get_deepseek_api_key()
    )
    prompt = ChatPromptTemplate.from_template(prompt_template)
    chain = prompt | llm | StrOutputParser()
    
    result = await chain.ainvoke({
        "options": options,
        "criteria": criteria,
        "weights": weights
    })
    
    # 尝试解析JSON
    try:
        # 尝试提取JSON
        json_match = re.search(r'```json\s*(.*?)\s*```', result, re.DOTALL)
        if json_match:
            result = json_match.group(1)
        
        # 解析JSON
        parsed_result = json.loads(result)
        
        return parsed_result
    except json.JSONDecodeError:
        # 如果JSON解析失败，返回原始文本
        return {
            "error": "JSON解析失败",
            "raw_result": result,
            "inputs": {
                "options": options,
                "criteria": criteria,
                "weights": weights
            }
        } 