"""
基础工具类模块，定义ArticleTool基类和相关接口

提供给文章生成系统使用的各种工具的基础类，所有具体工具都应继承此基类
"""
from typing import Dict, Any, Optional
import logging
import asyncio
from abc import ABC, abstractmethod

# 导入日志工具
from src.utils.logger import log_info, log_error, CrawlerLogger
from src.config.logging_config import LoggerName
logger = CrawlerLogger().get_logger(LoggerName.TOOLS)

class ArticleTool(ABC):
    """文章工具基类，提供通用接口和实现"""
    
    def __init__(self, name: str, description: str):
        """初始化工具
        
        Args:
            name: 工具名称
            description: 工具描述
        """
        self.name = name
        self.description = description
    
    @abstractmethod
    async def run(self, **kwargs) -> Dict[str, Any]:
        """运行工具
        
        Args:
            **kwargs: 工具参数
            
        Returns:
            工具执行结果
        """
        raise NotImplementedError("子类必须实现此方法")
    
    async def execute(self, params: Dict[str, Any], state: Dict[str, Any] = None) -> Dict[str, Any]:
        """执行工具
        
        提供给ArticleProcessor使用的标准接口，用于执行工具并返回结果
        
        Args:
            params: 工具参数
            state: 当前文章状态（可选）
            
        Returns:
            工具执行结果
        """
        try:
            logger.info(f"执行工具 {self.name}")
            
            # 执行工具
            result = await self.run(**params)
            
            # 添加元数据
            result.update({
                "tool_name": self.name,
                "params": params
            })
            
            logger.info(f"工具 {self.name} 执行完成")
            return result
        except Exception as e:
            logger.error(f"工具 {self.name} 执行失败: {str(e)}")
            return {
                "error": str(e),
                "tool_name": self.name,
                "params": params
            }
    
    def get_info(self) -> Dict[str, Any]:
        """获取工具信息
        
        Returns:
            工具信息字典
        """
        return {
            "name": self.name,
            "description": self.description,
            "type": self.__class__.__name__
        } 