import asyncio
import inspect
import json
import time
import logging
from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional, Callable, Tuple, Union, Type
from functools import wraps
from langgraph.graph import StateGraph

# 导入日志工具
from src.utils.logger import log_info, log_error, CrawlerLogger
from src.config.logging_config import LoggerName
logger = CrawlerLogger().get_logger(LoggerName.TOOLS)

class Tool(ABC):
    """工具基类"""
    
    def __init__(self, name: str, description: str):
        """初始化工具
        
        Args:
            name: 工具名称
            description: 工具描述
        """
        self.name = name
        self.description = description
        self.execution_count = 0
        self.execution_time = 0
    
    @abstractmethod
    async def execute(self, params: Dict[str, Any], state: Dict[str, Any]) -> Dict[str, Any]:
        """执行工具
        
        Args:
            params: 工具参数
            state: 当前状态
            
        Returns:
            执行结果
        """
        pass
    
    def to_dict(self) -> Dict[str, Any]:
        """将工具转换为字典表示
        
        Returns:
            工具字典表示
        """
        return {
            "name": self.name,
            "description": self.description,
            "type": self.__class__.__name__,
            "execution_count": self.execution_count,
            "execution_time": self.execution_time
        }
    
    def __str__(self) -> str:
        return f"{self.name}: {self.description}"


class ToolRegistry:
    """工具注册表 - 管理工具的注册和获取"""
    
    _instance = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(ToolRegistry, cls).__new__(cls)
            cls._instance._tools = {}
            cls._instance._categories = {}
        return cls._instance
    
    @classmethod
    def get_instance(cls):
        """获取ToolRegistry单例实例
        
        Returns:
            ToolRegistry实例
        """
        return cls()
    
    def register_tool(self, tool: Tool, category: str = "general") -> None:
        """注册工具
        
        Args:
            tool: 要注册的工具
            category: 工具类别
        """
        if tool.name in self._tools:
            logger.warning(f"工具 '{tool.name}' 已存在，将被覆盖")
        
        self._tools[tool.name] = tool
        
        if category not in self._categories:
            self._categories[category] = []
        
        if tool.name not in self._categories[category]:
            self._categories[category].append(tool.name)
        
        logger.info(f"已注册工具 '{tool.name}' 到类别 '{category}'")
    
    def get_tool(self, name: str) -> Optional[Tool]:
        """获取工具
        
        Args:
            name: 工具名称
            
        Returns:
            工具实例，如果不存在则返回None
        """
        return self._tools.get(name)
    
    def get_tools_by_category(self, category: str) -> List[Tool]:
        """获取特定类别的所有工具
        
        Args:
            category: 工具类别
            
        Returns:
            工具列表
        """
        tools = []
        for tool_name in self._categories.get(category, []):
            tool = self._tools.get(tool_name)
            if tool:
                tools.append(tool)
        return tools
    
    def get_all_tools(self) -> List[Tool]:
        """获取所有工具
        
        Returns:
            所有工具列表
        """
        return list(self._tools.values())
    
    def get_all_categories(self) -> List[str]:
        """获取所有工具类别
        
        Returns:
            所有类别列表
        """
        return list(self._categories.keys())
    
    def get_tools_info(self) -> Dict[str, List[Dict[str, Any]]]:
        """获取所有工具信息，按类别组织
        
        Returns:
            工具信息字典，键为类别，值为工具信息列表
        """
        result = {}
        for category, tool_names in self._categories.items():
            result[category] = []
            for tool_name in tool_names:
                tool = self._tools.get(tool_name)
                if tool:
                    result[category].append(tool.to_dict())
        return result
    
    def get_tools_dict(self) -> Dict[str, Callable]:
        """获取工具字典，用于LangGraphToolNode
        
        Returns:
            工具名称到执行函数的映射字典
        """
        tools_dict = {}
        
        # 定义工具包装器工厂函数，避免闭包问题
        def create_tool_wrapper(tool_name):
            # 为每个工具创建一个独立的包装函数
            async def wrapper(*args, **kwargs):
                tool_instance = self._tools.get(tool_name)
                if not tool_instance:
                    raise ValueError(f"Tool {tool_name} not found")
                return await tool_instance.execute(*args, **kwargs)
            
            # 设置函数属性
            wrapper.__name__ = f"{tool_name}_wrapper"
            wrapper.__qualname__ = f"ToolRegistry.{tool_name}_wrapper"
            wrapper.name = tool_name
            
            return wrapper
        
        # 为每个工具创建包装函数
        for name in self._tools:
            tools_dict[name] = create_tool_wrapper(name)
        
        return tools_dict
    
    def unregister_tool(self, name: str) -> bool:
        """取消注册工具
        
        Args:
            name: 工具名称
            
        Returns:
            是否成功取消注册
        """
        if name in self._tools:
            # 从工具字典中移除
            tool = self._tools.pop(name)
            
            # 从类别中移除
            for category, tool_names in self._categories.items():
                if name in tool_names:
                    self._categories[category].remove(name)
                    
                    # 如果类别为空，删除该类别
                    if not self._categories[category]:
                        del self._categories[category]
                    
                    break
            
            logger.info(f"已取消注册工具 '{name}'")
            return True
        
        return False


class ToolNode:
    """工具节点 - 集成到langgraph工作流中的工具执行节点"""
    
    def __init__(self, registry: Optional[ToolRegistry] = None):
        """初始化工具节点
        
        Args:
            registry: 工具注册表，如果为None则使用全局单例
        """
        self.registry = registry or ToolRegistry()
    
    async def __call__(self, state: Dict[str, Any]) -> Dict[str, Any]:
        """执行工具节点
        
        Args:
            state: 当前状态
            
        Returns:
            更新后的状态
        """
        # 获取要执行的工具请求
        tool_requests = state.get('tool_requests', [])
        
        if not tool_requests:
            logger.info("没有工具请求，跳过工具节点")
            return state
        
        logger.info(f"工具节点收到 {len(tool_requests)} 个工具请求")
        
        # 初始化工具结果列表
        results = []
        
        # 并发执行所有工具请求
        tasks = []
        for request in tool_requests:
            tasks.append(self._execute_tool_request(request, state))
        
        if tasks:
            # 等待所有任务完成
            results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 更新状态
        state['tools_results'] = results
        
        # 清除工具请求
        state['tool_requests'] = []
        
        # 记录工具使用历史
        state['tools_history'] = state.get('tools_history', [])
        state['tools_history'].extend([
            {
                "timestamp": time.time(),
                "tool": r.get("tool", "unknown"),
                "status": "success" if not isinstance(r, Exception) else "error",
                "result": str(r) if isinstance(r, Exception) else r.get("result", {})
            }
            for r in results
        ])
        
        logger.info(f"工具节点完成了 {len(results)} 个工具请求的执行")
        
        return state
    
    async def _execute_tool_request(self, request: Dict[str, Any], state: Dict[str, Any]) -> Dict[str, Any]:
        """执行单个工具请求
        
        Args:
            request: 工具请求
            state: 当前状态
            
        Returns:
            工具执行结果
        """
        tool_name = request.get('tool')
        params = request.get('params', {})
        
        # 获取工具
        tool = self.registry.get_tool(tool_name)
        
        if not tool:
            error_msg = f"找不到工具 '{tool_name}'"
            logger.error(error_msg)
            return {
                "tool": tool_name,
                "error": error_msg,
                "status": "error"
            }
        
        # 执行工具
        try:
            start_time = time.time()
            logger.info(f"开始执行工具 '{tool_name}'")
            
            result = await tool.execute(params, state)
            
            execution_time = time.time() - start_time
            tool.execution_count += 1
            tool.execution_time += execution_time
            
            logger.info(f"工具 '{tool_name}' 执行完成，耗时 {execution_time:.2f} 秒")
            
            return {
                "tool": tool_name,
                "result": result,
                "execution_time": execution_time,
                "status": "success"
            }
        except Exception as e:
            error_msg = f"工具 '{tool_name}' 执行失败: {str(e)}"
            logger.error(error_msg)
            return {
                "tool": tool_name,
                "error": str(e),
                "status": "error"
            }
    
    def add_to_graph(self, graph: StateGraph, node_name: str = "tools") -> StateGraph:
        """将工具节点添加到图中
        
        Args:
            graph: StateGraph实例
            node_name: 节点名称
            
        Returns:
            更新后的图
        """
        graph.add_node(node_name, self)
        return graph


# 工具装饰器，用于快速创建工具
def tool(name: str, description: str, category: str = "general"):
    """工具装饰器，用于将函数转换为工具
    
    Args:
        name: 工具名称
        description: 工具描述
        category: 工具类别
        
    Returns:
        装饰器函数
    """
    def decorator(func):
        # 不要在类上使用@wraps
        class FunctionTool(Tool):
            def __init__(self):
                super().__init__(name, description)
                self.func = func
                # 保留原函数的一些属性
                self.__name__ = func.__name__
                self.__doc__ = func.__doc__
                self.__module__ = func.__module__
            
            async def execute(self, params: Dict[str, Any], state: Dict[str, Any]) -> Dict[str, Any]:
                # 检查是否为异步函数
                if inspect.iscoroutinefunction(self.func):
                    return await self.func(params, state)
                else:
                    return self.func(params, state)
        
        # 创建工具实例并注册
        tool_instance = FunctionTool()
        ToolRegistry.get_instance().register_tool(tool_instance, category)
        
        return tool_instance
    
    return decorator 