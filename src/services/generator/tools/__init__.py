"""
工具模块 - 为文章生成器提供各种辅助工具和功能扩展

此模块提供了一系列工具，用于增强文章生成器的功能，包括：
1. 多模态处理工具：图像、视频、音频分析
2. 网络工具：网页抓取、搜索引擎、API请求
3. 知识工具：向量数据库、知识图谱、RAG检索
4. 决策和规划工具：ReAct推理、任务分解、自我纠正
5. 时间工具：获取当前时间和日历信息

工具节点系统允许工具的灵活集成和调度。
"""

from typing import List, Dict, Any, Optional, Type
import logging
from pathlib import Path
import importlib
import inspect

from src.services.generator.tools.tool_node import Tool, ToolRegistry, ToolNode, tool

# 导入原始的WebScraperTool和其他网络工具
from src.services.generator.tools.network_tools import (
    WebScraperTool,
    SearchEngineTool,
    APIRequestTool,
    fetch_rss_tool,
    check_website_status_tool,
    register_network_tools
)
from src.services.generator.tools.decision_tools import (
    ReActReasoningTool,
    TaskDecompositionTool,
    SelfCorrectionTool
)
# 导入新的时间工具
from src.services.generator.tools.time_tools import (
    TimeInformationTool,
    get_current_time_tool,
    register_time_tools
)

logger = logging.getLogger(__name__)

# 导出所有工具类
__all__ = [
    # 工具基础设施
    'Tool', 'ToolRegistry', 'ToolNode', 'tool',
    
    # 网络工具 - 使用原始的WebScraperTool
    'WebScraperTool', 'SearchEngineTool', 'APIRequestTool',
    'fetch_rss_tool', 'check_website_status_tool',
    
    # 知识工具
    'VectorDBTool', 'KnowledgeGraphTool', 'RAGTool', 'document_loader_tool',
    
    # 决策和规划工具
    'ReActReasoningTool', 'TaskDecompositionTool', 'SelfCorrectionTool',
    
    # 时间工具
    'TimeInformationTool', 'get_current_time_tool',
]

class ToolRegistry:
    """工具注册表类，负责管理所有可用的工具"""
    
    _instance = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(ToolRegistry, cls).__new__(cls)
            cls._instance._tools = {}
            cls._instance._initialized = False
        return cls._instance
    
    def __init__(self):
        """初始化工具注册表"""
        if not self._initialized:
            self._tools = {}
            self._initialized = True
            logger.debug("工具注册表已初始化")
    
    def register(self, tool: Tool) -> None:
        """注册工具
        
        Args:
            tool: 要注册的工具实例
        """
        if not isinstance(tool, Tool):
            raise TypeError(f"只能注册Tool类的实例，而不是 {type(tool)}")
        
        self._tools[tool.name] = tool
        logger.debug(f"工具已注册: {tool.name}")
    
    def get(self, name: str) -> Optional[Tool]:
        """通过名称获取工具
        
        Args:
            name: 工具名称
            
        Returns:
            Tool实例或None（如果未找到）
        """
        return self._tools.get(name)
    
    def list_tools(self) -> List[str]:
        """列出所有已注册的工具名称
        
        Returns:
            工具名称列表
        """
        return list(self._tools.keys())
    
    def get_tools(self) -> Dict[str, Tool]:
        """获取所有工具
        
        Returns:
            工具字典，键为工具名称
        """
        return self._tools.copy()

def register_default_tools():
    """注册默认工具集"""
    registry = ToolRegistry()
    
    # 注册时间工具
    register_time_tools()
    
    # 注册网络工具（包含原始的WebScraperTool）
    register_network_tools()
    
    logger.info(f"已注册 {len(registry.list_tools())} 个默认工具")
    logger.debug(f"默认工具列表: {registry.list_tools()}")
    
    return registry 