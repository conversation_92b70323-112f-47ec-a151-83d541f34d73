"""
时间工具模块 - 提供时间相关功能
"""
import time
import datetime
import calendar
from typing import Dict, Any, Optional
from pytz import timezone, all_timezones
from zoneinfo import available_timezones

from src.services.generator.tools.base_tool import ArticleTool
from src.services.generator.tools.tool_node import Tool, tool

class TimeInformationTool(ArticleTool):
    """获取当前时间信息的工具"""
    
    def __init__(self):
        """初始化时间信息工具"""
        super().__init__(
            name="时间信息工具",
            description="获取当前时间、日期和日历信息"
        )
    
    async def run(self, params: Dict[str, Any], context: Dict[str, Any] = None) -> Dict[str, Any]:
        """执行时间信息获取
        
        Args:
            params: 工具参数，可以包含:
                - timezone: 时区名称（如"Asia/Shanghai"），默认为UTC
                - format: 日期时间格式（可选）
                - include_calendar_info: 是否包含日历信息，默认为False
            context: 上下文信息（未使用）
            
        Returns:
            包含当前时间信息的字典
        """
        # 解析参数
        timezone = params.get("timezone", "UTC")
        date_format = params.get("format", "%Y-%m-%d")
        time_format = params.get("time_format", "%H:%M:%S")
        include_calendar = params.get("include_calendar_info", False)
        
        try:
            # 设置时区
            import pytz
            tz = pytz.timezone(timezone)
            now = datetime.datetime.now(tz)
        except ImportError:
            # 如果没有pytz，使用本地时间
            now = datetime.datetime.now()
        except Exception as e:
            # 如果时区无效，使用UTC
            import pytz
            tz = pytz.UTC
            now = datetime.datetime.now(tz)
        
        # 构建基本时间信息
        result = {
            "timestamp": time.time(),
            "formatted_date": now.strftime(date_format),
            "formatted_time": now.strftime(time_format),
            "timezone": timezone,
            "day_of_week": now.strftime("%A"),
            "day_of_week_cn": ["星期一", "星期二", "星期三", "星期四", "星期五", "星期六", "星期日"][now.weekday()],
            "is_weekend": now.weekday() >= 5,
            "year": now.year,
            "month": now.month,
            "day": now.day,
            "hour": now.hour,
            "minute": now.minute,
            "second": now.second
        }
        
        # 添加时段描述
        hour = now.hour
        if 5 <= hour < 8:
            result["time_of_day"] = "清晨"
        elif 8 <= hour < 12:
            result["time_of_day"] = "上午"
        elif 12 <= hour < 13:
            result["time_of_day"] = "中午"
        elif 13 <= hour < 18:
            result["time_of_day"] = "下午"
        elif 18 <= hour < 22:
            result["time_of_day"] = "晚上"
        else:
            result["time_of_day"] = "深夜"
        
        # 季节信息
        month = now.month
        if 3 <= month <= 5:
            result["season"] = "春季"
        elif 6 <= month <= 8:
            result["season"] = "夏季"
        elif 9 <= month <= 11:
            result["season"] = "秋季"
        else:
            result["season"] = "冬季"
        
        # 如果需要，添加日历信息
        if include_calendar:
            # 获取当月第一天和最后一天
            first_day = datetime.date(now.year, now.month, 1)
            # 获取下个月的第一天，然后减去一天
            if now.month == 12:
                last_day = datetime.date(now.year + 1, 1, 1) - datetime.timedelta(days=1)
            else:
                last_day = datetime.date(now.year, now.month + 1, 1) - datetime.timedelta(days=1)
            
            result["month_name"] = now.strftime("%B")
            result["month_name_cn"] = ["一月", "二月", "三月", "四月", "五月", "六月", 
                                     "七月", "八月", "九月", "十月", "十一月", "十二月"][now.month - 1]
            result["days_in_month"] = last_day.day
            result["first_day_of_month"] = first_day.strftime(date_format)
            result["last_day_of_month"] = last_day.strftime(date_format)
            
            # 检查是否接近某些节日/假日（中国）
            major_holidays = {
                (1, 1): "元旦",
                (5, 1): "劳动节",
                (10, 1): "国庆节"
            }
            
            # 农历节日需要额外的农历转换库，这里简化处理
            special_holiday = None
            for (month, day), holiday_name in major_holidays.items():
                # 检查未来7天内是否有节日
                days_until = 0
                check_date = now.date()
                while days_until < 7:
                    if check_date.month == month and check_date.day == day:
                        special_holiday = holiday_name
                        break
                    check_date += datetime.timedelta(days=1)
                    days_until += 1
                
                if special_holiday:
                    result["upcoming_holiday"] = special_holiday
                    result["days_until_holiday"] = days_until
                    break
        
        return result

# 获取当前时间工具实例
get_current_time_tool = TimeInformationTool()

# 直接可调用的函数版本
async def get_current_time(params: Dict[str, Any] = None, context: Dict[str, Any] = None) -> Dict[str, Any]:
    """直接获取当前时间信息的函数版本
    
    与工具版本功能相同，但可以直接调用
    
    Args:
        params: 时间工具参数
        context: 上下文信息
        
    Returns:
        当前时间信息字典
    """
    if params is None:
        params = {"timezone": "Asia/Shanghai", "include_calendar_info": True}
        
    tool = TimeInformationTool()
    return await tool.run(params, context)

def register_time_tools(registry):
    """注册时间相关工具
    
    Args:
        registry: 工具注册对象
    """
    registry.register_tool(get_current_time_tool)


@tool(
    name="get_current_time", 
    description="获取当前准确时间和日期信息",
    category="utility"
)
async def get_current_time_tool(params: Dict[str, Any], state: Dict[str, Any]) -> Dict[str, Any]:
    """
    获取当前准确时间工具
    
    Args:
        params:
            timezone: 时区（可选，默认为"Asia/Shanghai"）
        state: 当前状态
        
    Returns:
        当前时间信息
    """
    timezone_name = params.get("timezone", "Asia/Shanghai")
    
    # 检查时区是否有效
    valid_timezones = available_timezones() if hasattr(datetime, 'zoneinfo') else all_timezones
    if timezone_name not in valid_timezones:
        timezone_name = "Asia/Shanghai"
        
    try:
        # 获取指定时区的当前时间
        tz = timezone(timezone_name)
        now = datetime.datetime.now(tz)
        
        # 构建标准响应
        result = {
            "timestamp": int(time.time()),
            "timezone": timezone_name,
            "year": now.year,
            "month": now.month,
            "day": now.day,
            "hour": now.hour,
            "minute": now.minute,
            "second": now.second,
            "weekday": now.strftime("%A"),
            "formatted_date": now.strftime("%Y-%m-%d"),
            "formatted_time": now.strftime("%H:%M:%S"),
            "formatted_datetime": now.strftime("%Y-%m-%d %H:%M:%S")
        }
        
        return result
    except Exception as e:
        return {
            "error": f"获取时间失败: {str(e)}",
            "timestamp": int(time.time())
        } 