import re
import json
import asyncio
import logging
import aiohttp
import base64
import time
import random
from typing import Dict, List, Any, Optional, Union
from bs4 import BeautifulSoup
from urllib.parse import urlparse, quote
import requests

from src.services.generator.tools.tool_node import Tool, tool

# 导入日志工具
from src.utils.logger import log_info, log_error, CrawlerLogger
from src.config.logging_config import LoggerName
logger = CrawlerLogger().get_logger(LoggerName.TOOLS)

class WebScraperTool(Tool):
    """网页抓取工具 - 提取网页内容和信息"""
    
    def __init__(self):
        """初始化网页抓取工具"""
        super().__init__(
            name="web_scraper",
            description="抓取和解析网页内容，提取文本、图片链接和结构化数据"
        )
        # 高质量User-Agent池，模拟真实浏览器
        self.user_agents = [
            # Chrome on Windows
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (<PERSON><PERSON><PERSON>, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            # Chrome on Mac
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            # Firefox on Windows
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:120.0) Gecko/20100101 Firefox/120.0",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/119.0",
            # Edge
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.0.0",
            # Safari
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Safari/605.1.15",
        ]
        
        # 无价值网站黑名单 - 只过滤热搜榜相关网站
        self.blacklisted_domains = [
            "top.baidu.com/board",     # 百度热搜榜 - 更精确的匹配
            "tophub.today",            # 今日热榜
            "hot.weibo.com",           # 微博热搜
            "s.weibo.com/top",         # 微博热搜榜
            "zhihu.com/hot",           # 知乎热榜
            "rank.chinaz.com",         # 站长之家排行榜
            "resou.today",             # 热搜榜
            "douyin.com/hot",          # 抖音热榜
            "tieba.baidu.com/hottopic", # 贴吧热议
            "board?platform=pc",       # 百度热搜板块
            "/board?tab=realtime",     # 百度实时热搜
            "hot-list",                # 热榜相关
            "trending",                # 趋势榜
            "hotrank"                  # 热门排行
        ]
    
    def _is_blacklisted_url(self, url: str) -> bool:
        """检查URL是否在黑名单中"""
        url_lower = url.lower()
        for domain in self.blacklisted_domains:
            if domain in url_lower:
                logger.info(f"跳过黑名单URL: {url}")
                return True
        return False
    
    def _get_random_headers(self, url: str = "") -> Dict[str, str]:
        """获取随机化的请求头，针对不同网站优化"""
        base_headers = {
            'User-Agent': random.choice(self.user_agents),
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Sec-Fetch-User': '?1',
            'Cache-Control': 'max-age=0',
            'DNT': '1',
            'Sec-GPC': '1'
        }
        
        # 针对知乎的特殊处理
        if 'zhihu.com' in url:
            base_headers.update({
                'Referer': 'https://www.zhihu.com/',
                'Origin': 'https://www.zhihu.com',
                'X-Requested-With': 'XMLHttpRequest',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'Accept-Language': 'zh-CN,zh;q=0.8,zh-TW;q=0.7,zh-HK;q=0.5,en-US;q=0.3,en;q=0.2',
            })
            
            # 添加知乎常见的Cookie
            cookie_parts = [
                f"_zap={self._random_string(36)}",
                f"d_c0=\"{self._random_string(32)}|{int(time.time())}.{random.randint(100, 999)}.{random.randint(1000000, 9999999)}\"",
                f"_xsrf={self._random_string(8)}-{self._random_string(4)}-{self._random_string(4)}-{self._random_string(4)}-{self._random_string(12)}",
                f"Hm_lvt_98beee57fd2ef70ccdd5ca52b9740c49={int(time.time())},{int(time.time())-3600},{int(time.time())-7200}",
                f"Hm_lpvt_98beee57fd2ef70ccdd5ca52b9740c49={int(time.time())}",
                f"SESSIONID={self._random_string(32)}",
                f"JOID={self._random_string(8)}-{self._random_string(4)}-{self._random_string(4)}-{self._random_string(4)}-{self._random_string(12)}",
                f"osd={self._random_string(8)}-{self._random_string(4)}-{self._random_string(4)}-{self._random_string(4)}-{self._random_string(12)}"
            ]
            base_headers['Cookie'] = "; ".join(cookie_parts)
        
        # 针对微信公众号的特殊处理
        elif 'mp.weixin.qq.com' in url:
            base_headers.update({
                'Referer': 'https://mp.weixin.qq.com/',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8'
            })
        
        # 针对简书的特殊处理
        elif 'jianshu.com' in url:
            base_headers.update({
                'Referer': 'https://www.jianshu.com/',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8'
            })
        
        # 针对CSDN的特殊处理
        elif 'csdn.net' in url:
            base_headers.update({
                'Referer': 'https://www.csdn.net/',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8'
            })
        
        return base_headers
    
    def _random_string(self, length: int) -> str:
        """生成随机字符串"""
        chars = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
        return ''.join(random.choice(chars) for _ in range(length))
    
    async def execute(self, params: Dict[str, Any], state: Dict[str, Any]) -> Dict[str, Any]:
        """执行网页抓取
        
        Args:
            params: 
                url: 要抓取的URL
                extract_type: 提取类型，支持"text"(文本), "images"(图片), "links"(链接),
                              "tables"(表格), "all"(全部)
                css_selector: 可选的CSS选择器，用于提取特定内容
                include_metadata: 是否包含元数据（标题、描述等）
            state: 当前状态
            
        Returns:
            抓取结果
        """
        try:
            # 获取参数
            url = params.get('url')
            extract_type = params.get('extract_type', 'text')
            css_selector = params.get('css_selector', '')
            include_metadata = params.get('include_metadata', True)
            
            # 验证参数
            if not url:
                raise ValueError("必须提供url参数")
            
            # 检查是否为黑名单URL
            if self._is_blacklisted_url(url):
                return {"error": f"URL在黑名单中，跳过爬取: {url}"}
            
            # 配置请求头
            headers = self._get_random_headers(url)
            
            # 发送请求
            logger.info(f"抓取网址: {url}")
            
            # 设置超时时间
            timeout = aiohttp.ClientTimeout(total=30, connect=10)
            
            try:
                async with aiohttp.ClientSession(timeout=timeout) as session:
                    async with session.get(url, headers=headers, allow_redirects=True) as response:
                        if response.status == 403:
                            logger.warning(f"遇到403错误: {url}")
                            return {"error": f"请求被拒绝，状态码: {response.status}"}
                        
                        if response.status != 200:
                            logger.warning(f"状态码{response.status}: {url}")
                            return {"error": f"请求失败，状态码: {response.status}"}
                        
                        # 获取内容类型
                        content_type = response.headers.get('Content-Type', '')
                        if 'text/html' not in content_type and 'application/xhtml+xml' not in content_type:
                            if 'text/' in content_type:
                                # 如果是其他文本类型，也尝试处理
                                pass
                            else:
                                return {"error": f"不支持的内容类型: {content_type}"}
                        
                        # 读取响应内容
                        html = await response.text()
                        
                        # 检查是否被重定向到登录页面或验证页面
                        if self._is_login_or_verification_page(html, str(response.url)):
                            logger.warning(f"检测到登录或验证页面: {url}")
                            return {"error": "页面需要登录或验证"}
                        
            except asyncio.TimeoutError:
                logger.warning(f"请求超时: {url}")
                return {"error": "请求超时"}
            except aiohttp.ClientError as e:
                logger.warning(f"网络错误: {url} - {str(e)}")
                return {"error": f"网络错误: {str(e)}"}
            except Exception as e:
                logger.error(f"未知错误: {url} - {str(e)}")
                return {"error": f"未知错误: {str(e)}"}
            
            # 解析HTML
            soup = BeautifulSoup(html, 'html.parser')
            
            # 应用CSS选择器（如果提供）
            if css_selector:
                selected_elements = soup.select(css_selector)
                if not selected_elements:
                    logger.warning(f"CSS选择器 '{css_selector}' 没有匹配任何元素")
                # 创建新的BeautifulSoup对象，只包含选定的元素
                selected_html = ''.join(str(elem) for elem in selected_elements)
                soup = BeautifulSoup(selected_html, 'html.parser')
            
            # 初始化结果
            result = {
                "url": url,
                "extracted_type": extract_type
            }
            
            # 提取元数据
            if include_metadata:
                metadata = self._extract_metadata(soup)
                result['metadata'] = metadata
            
            # 根据提取类型获取数据
            if extract_type == 'text' or extract_type == 'all':
                text = self._extract_text_content(soup, url)
                result['text'] = text
            
            if extract_type == 'images' or extract_type == 'all':
                images = self._extract_images(soup, url)
                result['images'] = images
            
            if extract_type == 'links' or extract_type == 'all':
                links = self._extract_links(soup, url)
                result['links'] = links
            
            if extract_type == 'tables' or extract_type == 'all':
                tables = self._extract_tables(soup)
                result['tables'] = tables
            
            return result
        
        except Exception as e:
            logger.error(f"网页抓取失败: {str(e)}")
            return {"error": f"网页抓取失败: {str(e)}"}
    
    def _is_login_or_verification_page(self, html: str, url: str) -> bool:
        """检查是否为登录页面或验证页面 - 针对现代单页应用优化"""
        # 如果页面内容太短，可能是简单的登录页面
        if len(html) < 500:
            return True
        
        # 检查是否有明显的验证码页面特征（这些通常是真正的阻拦页面）
        verification_indicators = [
            "请完成安全验证", "点击完成验证", "滑动完成验证", 
            "人机验证失败", "访问异常", "请稍后再试",
            "安全验证中", "验证失败", "验证码错误",
            "请输入验证码", "图形验证码"
        ]
        
        html_lower = html.lower()
        verification_count = 0
        for indicator in verification_indicators:
            if indicator in html_lower:
                verification_count += 1
        
        # 如果有多个明确的验证相关词汇，可能是验证页面
        if verification_count >= 2:
            return True
        
        # 检查页面标题是否明确表示这是一个错误或验证页面
        soup = BeautifulSoup(html, 'html.parser')
        title = soup.find('title')
        if title:
            title_text = title.text.lower().strip()
            # 只有当标题明确表示错误或验证时才判断
            error_titles = [
                '验证失败', '访问异常', '安全验证', '人机验证',
                'verification failed', 'access denied', 'captcha'
            ]
            if any(error_title in title_text for error_title in error_titles):
                return True
        
        # 检查是否有简单的登录表单（传统登录页面）
        login_forms = soup.find_all('form')
        simple_login_form_count = 0
        
        for form in login_forms:
            form_text = form.get_text().lower()
            password_inputs = form.find_all('input', {'type': 'password'})
            username_inputs = form.find_all('input', {'type': ['text', 'email']})
            
            # 如果有密码框和用户名框，且表单文本包含登录相关词汇
            if (password_inputs and username_inputs and 
                any(indicator in form_text for indicator in ['登录', 'login', 'sign in'])):
                simple_login_form_count += 1
        
        # 只有当页面很简单且有明确的登录表单时才判断为登录页面
        if simple_login_form_count > 0 and len(html) < 5000:
            return True
        
        # 对于现代单页应用（如知乎），即使URL包含signin，也不应该被拦截
        # 因为它们通常会加载完整的应用内容
        
        return False
    
    def _extract_metadata(self, soup: BeautifulSoup) -> Dict[str, str]:
        """提取页面元数据"""
        metadata = {}
        
        # 标题
        title_tag = soup.find('title')
        if title_tag:
            metadata['title'] = title_tag.text.strip()
        
        # 描述
        description_meta = soup.find('meta', attrs={'name': 'description'})
        if description_meta:
            metadata['description'] = description_meta.get('content', '')
        
        # 关键词
        keywords_meta = soup.find('meta', attrs={'name': 'keywords'})
        if keywords_meta:
            metadata['keywords'] = keywords_meta.get('content', '')
        
        # 作者
        author_meta = soup.find('meta', attrs={'name': 'author'})
        if author_meta:
            metadata['author'] = author_meta.get('content', '')
        
        # 发布日期
        published_meta = soup.find('meta', attrs={'property': 'article:published_time'})
        if published_meta:
            metadata['published_date'] = published_meta.get('content', '')
        
        return metadata
    
    def _extract_text_content(self, soup: BeautifulSoup, url: str) -> str:
        """提取文本内容，针对不同网站优化"""
        # 移除脚本和样式内容
        for script in soup(["script", "style", "nav", "header", "footer", "aside"]):
            script.decompose()
        
        # 针对知乎的特殊处理
        if 'zhihu.com' in url:
            # 知乎问题页面
            content_selectors = [
                '.QuestionAnswer-content',  # 回答内容
                '.RichContent-inner',       # 富文本内容
                '.QuestionDetail-content',  # 问题详情
                '.Post-RichTextContainer',  # 文章内容
                '.ContentItem-content',     # 内容项
                '.AnswerItem .RichContent', # 回答项
                'article',                  # 文章标签
                '.ztext'                   # 知乎文本
            ]
            
            extracted_content = []
            for selector in content_selectors:
                elements = soup.select(selector)
                for elem in elements:
                    text = elem.get_text(separator='\n', strip=True)
                    if text and len(text) > 50:  # 过滤太短的内容
                        extracted_content.append(text)
            
            if extracted_content:
                return '\n\n'.join(extracted_content)
        
        # 针对微信公众号的特殊处理
        elif 'mp.weixin.qq.com' in url:
            content_elem = soup.find('div', {'id': 'js_content'})
            if content_elem:
                return content_elem.get_text(separator='\n', strip=True)
        
        # 针对简书的特殊处理
        elif 'jianshu.com' in url:
            content_elem = soup.find('div', {'class': 'show-content'})
            if content_elem:
                return content_elem.get_text(separator='\n', strip=True)
        
        # 针对CSDN的特殊处理
        elif 'csdn.net' in url:
            content_elem = soup.find('div', {'id': 'content_views'})
            if content_elem:
                return content_elem.get_text(separator='\n', strip=True)
        
        # 通用文本提取
        # 尝试找到主要内容区域
        main_content_selectors = [
            'main', 'article', '.content', '.post-content', '.entry-content',
            '.article-content', '.post-body', '.content-body', '#content',
            '.main-content', '.article-body', '.post', '.entry'
        ]
        
        for selector in main_content_selectors:
            content_elem = soup.select_one(selector)
            if content_elem:
                text = content_elem.get_text(separator='\n', strip=True)
                if len(text) > 500:  # 确保内容足够长
                    return self._clean_text(text)
        
        # 如果没有找到主要内容区域，使用全页面文本
        text = soup.get_text(separator='\n', strip=True)
        return self._clean_text(text)
    
    def _clean_text(self, text: str) -> str:
        """清理文本内容"""
        # 清理文本（移除多余空行等）
        lines = (line.strip() for line in text.splitlines())
        chunks = (phrase.strip() for line in lines for phrase in line.split("  "))
        cleaned_text = '\n'.join(chunk for chunk in chunks if chunk)
        
        # 移除过多的连续换行
        cleaned_text = re.sub(r'\n{3,}', '\n\n', cleaned_text)
        
        return cleaned_text
    
    def _extract_images(self, soup: BeautifulSoup, url: str) -> List[Dict[str, str]]:
        """提取图片"""
        images = []
        for img in soup.find_all('img'):
            src = img.get('src', '')
            if src:
                # 转换为绝对URL
                if not (src.startswith('http://') or src.startswith('https://')):
                    base_url = urlparse(url)
                    if src.startswith('/'):
                        src = f"{base_url.scheme}://{base_url.netloc}{src}"
                    else:
                        path = '/'.join(base_url.path.split('/')[:-1]) + '/'
                        src = f"{base_url.scheme}://{base_url.netloc}{path}{src}"
                
                images.append({
                    'url': src,
                    'alt': img.get('alt', ''),
                    'title': img.get('title', '')
                })
        
        return images
    
    def _extract_links(self, soup: BeautifulSoup, url: str) -> List[Dict[str, str]]:
        """提取链接"""
        links = []
        for link in soup.find_all('a'):
            href = link.get('href', '')
            if href and not href.startswith('#') and not href.startswith('javascript:'):
                # 转换为绝对URL
                if not (href.startswith('http://') or href.startswith('https://')):
                    base_url = urlparse(url)
                    if href.startswith('/'):
                        href = f"{base_url.scheme}://{base_url.netloc}{href}"
                    else:
                        path = '/'.join(base_url.path.split('/')[:-1]) + '/'
                        href = f"{base_url.scheme}://{base_url.netloc}{path}{href}"
                
                links.append({
                    'url': href,
                    'text': link.text.strip(),
                    'title': link.get('title', '')
                })
        
        return links
    
    def _extract_tables(self, soup: BeautifulSoup) -> List[Dict[str, Any]]:
        """提取表格"""
        tables = []
        for table in soup.find_all('table'):
            table_data = []
            rows = table.find_all('tr')
            
            # 检查是否有表头
            headers = []
            th_elements = rows[0].find_all('th') if rows else []
            if th_elements:
                headers = [th.text.strip() for th in th_elements]
            
            # 如果没有表头，尝试使用第一行作为表头
            if not headers and rows:
                first_row = rows[0].find_all(['th', 'td'])
                if first_row:
                    headers = [cell.text.strip() for cell in first_row]
                    rows = rows[1:]  # 排除第一行，因为已作为表头
            
            # 处理表格数据行
            for row in rows:
                cells = row.find_all(['td'])
                if cells:
                    row_data = [cell.text.strip() for cell in cells]
                    
                    # 如果有表头，创建字典；否则使用列表
                    if headers and len(headers) == len(row_data):
                        table_data.append(dict(zip(headers, row_data)))
                    else:
                        table_data.append(row_data)
            
            tables.append({
                'headers': headers,
                'data': table_data
            })
        
        return tables


class SearchEngineTool(Tool):
    """搜索引擎工具 - 提供搜索引擎查询功能"""
    
    def __init__(self):
        """初始化搜索引擎工具"""
        super().__init__(
            name="search_engine",
            description="执行搜索引擎查询，支持百度、必应和谷歌"
        )
        self.user_agent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
    
    async def execute(self, params: Dict[str, Any], state: Dict[str, Any]) -> Dict[str, Any]:
        """执行搜索引擎查询
        
        Args:
            params: 
                query: 搜索查询
                engine: 搜索引擎，支持"baidu"(百度), "bing"(必应), "google"(谷歌)
                num_results: 返回结果数量，默认为5
                language: 搜索语言，默认为"zh-cn"
            state: 当前状态
            
        Returns:
            搜索结果
        """
        try:
            # 获取参数
            query = params.get('query')
            engine = params.get('engine', 'baidu')
            num_results = params.get('num_results', 5)
            language = params.get('language', 'zh-cn')
            
            # 验证参数
            if not query:
                raise ValueError("必须提供query参数")
            
            # 确保num_results在合理范围内
            num_results = max(1, min(num_results, 10))
            
            # 调用search方法
            results = await self.search(query, engine, num_results, language)
            return {"results": results}
            
        except Exception as e:
            logger.error(f"搜索引擎查询失败: {str(e)}")
            return {"error": f"搜索引擎查询失败: {str(e)}"}
    
    async def search(self, query: str, engine: str = "baidu", num_results: int = 5, language: str = "zh-cn") -> List[Dict[str, Any]]:
        """执行搜索，返回搜索结果列表
        
        Args:
            query: 搜索查询
            engine: 搜索引擎，默认"baidu"
            num_results: 最终返回结果数量，默认5
            language: 搜索语言，默认"zh-cn"
            
        Returns:
            搜索结果列表
        """
        try:
            # 确保num_results在合理范围内
            final_num_results = max(1, min(num_results, 10))
            # 搜索更多结果以便过滤，搜索数量是最终需要数量的2倍，最少10个
            search_num_results = max(10, final_num_results * 2)
            
            # 构建搜索URL
            search_url = self._build_search_url(query, engine, language, search_num_results)
            
            # 配置更真实的浏览器请求头
            headers = self._get_realistic_headers(engine, language, randomize=True)
            
            # 记录搜索操作
            logger.info(f"执行搜索: {query} (引擎: {engine}, 搜索{search_num_results}个结果，最终返回{final_num_results}个)")
            
            try:
                # 尝试实际搜索请求
                async with aiohttp.ClientSession() as session:
                    # 增加连接超时和总超时时间
                    timeout = aiohttp.ClientTimeout(total=20, connect=10, sock_connect=10)
                    
                    # 增加重试逻辑
                    max_retries = 2
                    for retry in range(max_retries + 1):
                        try:
                            async with session.get(search_url, headers=headers, timeout=timeout) as response:
                                if response.status != 200:
                                    logger.warning(f"搜索请求失败，状态码: {response.status}")
                                    # 如果不是最后一次重试，继续尝试
                                    if retry < max_retries:
                                        logger.info(f"重试搜索请求 ({retry+1}/{max_retries})...")
                                        await asyncio.sleep(1)  # 等待1秒再重试
                                        continue
                                    # 如果是最后一次重试，返回空结果
                                    logger.warning(f"搜索请求失败，状态码: {response.status}，返回空结果")
                                    return []
                                
                                # 获取响应内容
                                html = await response.text()
                                
                                # 检查响应是否包含验证码或限制访问
                                if self._is_access_limited(html, engine):
                                    logger.warning(f"检测到{engine}访问限制或验证码")
                                    # 如果不是最后一次重试，尝试更换请求头再试
                                    if retry < max_retries:
                                        logger.info(f"更换请求头并重试 ({retry+1}/{max_retries})...")
                                        headers = self._get_realistic_headers(engine, language, randomize=True)
                                        await asyncio.sleep(2)  # 等待2秒再重试
                                        continue
                                    # 如果是最后一次重试，返回空结果
                                    logger.warning(f"检测到{engine}访问限制或验证码，返回空结果")
                                    return []
                                
                                # 解析搜索结果HTML - 使用更大的搜索数量
                                parsed_results = self._parse_search_results(html, engine, search_num_results)
                                
                                if parsed_results:
                                    logger.info(f"成功从{engine}解析到{len(parsed_results)}个原始结果")
                                    
                                    # 过滤和排序结果
                                    filtered_results = self._filter_and_rank_results(parsed_results, final_num_results)
                                    
                                    logger.info(f"过滤后返回{len(filtered_results)}个高质量结果")
                                    return filtered_results
                                else:
                                    logger.warning(f"无法从{engine}解析到搜索结果")
                                    # 如果不是最后一次重试，尝试更换请求方式再试
                                    if retry < max_retries:
                                        logger.info(f"更换请求头并重试 ({retry+1}/{max_retries})...")
                                        headers = self._get_realistic_headers(engine, language, randomize=True)
                                        await asyncio.sleep(1)  # 等待1秒再重试
                                        continue
                                    # 如果是最后一次重试，返回空结果
                                    logger.warning("无法解析搜索结果，返回空结果")
                                    return []
                                
                        except asyncio.TimeoutError:
                            logger.warning(f"搜索请求超时")
                            # 如果不是最后一次重试，继续尝试
                            if retry < max_retries:
                                logger.info(f"重试搜索请求 ({retry+1}/{max_retries})...")
                                await asyncio.sleep(1)  # 等待1秒再重试
                                continue
                            # 如果是最后一次重试，返回空结果
                            logger.warning("搜索请求超时，返回空结果")
                            return []
            except Exception as e:
                logger.warning(f"实际搜索请求失败: {str(e)}，返回空结果")
                return []
                        
        except Exception as e:
            logger.warning(f"执行搜索失败: {str(e)}，返回空结果")
            return []
    
    def _get_realistic_headers(self, engine: str, language: str, randomize: bool = False) -> Dict[str, str]:
        """获取真实的请求头
        
        Args:
            engine: 搜索引擎，如"baidu", "google", "bing"
            language: 语言
            randomize: 是否随机化
            
        Returns:
            请求头
        """
        # 高质量User-Agent列表
        user_agents = [
            # Chrome on Windows
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            # Chrome on Mac
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            # Firefox on Windows
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:120.0) Gecko/20100101 Firefox/120.0",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/119.0",
            # Firefox on Mac
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:120.0) Gecko/20100101 Firefox/120.0",
            # Edge
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.0.0",
            # Safari
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Safari/605.1.15",
        ]
        
        # 根据搜索引擎选择合适的请求头
        headers = {
            'User-Agent': random.choice(user_agents),
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'Accept-Language': language.lower().replace('_', '-') + ',' + language.split('_')[0] + ';q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Sec-Fetch-User': '?1',
            'Cache-Control': 'max-age=0',
        }

        # 根据搜索引擎添加特定的请求头
        if engine == 'baidu':
            headers['Referer'] = 'https://www.baidu.com/'
        elif engine == 'bing':
            headers['Referer'] = 'https://www.bing.com/'
        elif engine == 'google':
            headers['Referer'] = 'https://www.google.com/'
        
        # 添加随机Cookie
        if randomize:
            cookie_parts = [
                f"BDUSS_{random.randint(100, 999)}={self._random_string(32)}",
                f"BAIDUID={self._random_string(32)}:FG=1",
                f"BD_UPN={self._random_string(8)}",
                f"BIDUPSID={self._random_string(24)}",
                f"PSTM={int(time.time())}"
            ]
            headers['Cookie'] = "; ".join(cookie_parts)
            
            # 添加随机的X-Forwarded-For以模拟不同IP来源
            headers['X-Forwarded-For'] = f"{random.randint(1, 255)}.{random.randint(1, 255)}.{random.randint(1, 255)}.{random.randint(1, 255)}"
        
        return headers
        
    def _random_string(self, length: int) -> str:
        """生成随机字符串
        
        Args:
            length: 字符串长度
            
        Returns:
            随机字符串
        """
        chars = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
        return ''.join(random.choice(chars) for _ in range(length))
    
    def _is_access_limited(self, html: str, engine: str) -> bool:
        """检查是否遇到搜索限制或验证码
        
        Args:
            html: 响应HTML
            engine: 搜索引擎
            
        Returns:
            是否存在访问限制
        """
        # 检查是否有访问限制或验证码
        if engine == "google":
            # Google验证码或限制访问的特征
            limited_indicators = [
                "Our systems have detected unusual traffic",
                "人机身份验证",
                "请完成以下验证",
                "We're sorry...",
                "detected unusual traffic",
                "CAPTCHA",
                "验证您是真人",
                "please enable JavaScript"
            ]
        elif engine == "bing":
            # Bing验证码或限制访问的特征
            limited_indicators = [
                "我们注意到你的计算机所在的 IP",
                "需要验证",
                "验证您的身份",
                "我们想确保这不是自动程序",
                "CAPTCHA",
                "人机验证",
                "验证码"
            ]
        elif engine == "baidu":
            # 百度验证码或限制访问的特征
            limited_indicators = [
                "请输入验证码",
                "请完成下方验证",
                "百度一下，你就知道",  # 可能的重定向到百度首页
                "异常请求",
                "请您检查输入是否正确",
                "验证您是真人"
            ]
        else:
            return False
        
        # 检查HTML中是否包含这些特征
        for indicator in limited_indicators:
            if indicator in html:
                return True
        
        return False
    
    def _parse_search_results(self, html: str, engine: str, num_results: int) -> List[Dict[str, Any]]:
        """解析搜索结果HTML
        
        Args:
            html: 搜索结果页面HTML
            engine: 搜索引擎
            num_results: 结果数量
            
        Returns:
            解析后的搜索结果列表
        """
        results = []
        
        # 定义黑名单域名和URL模式 - 只过滤明确的热搜榜网站
        blacklisted_patterns = [
            # 热搜榜类网站 - 只过滤明确的热搜榜页面
            "top.baidu.com/board",     # 百度热搜榜
            "tophub.today",            # 今日热榜
            "hot.weibo.com",           # 微博热搜
            "s.weibo.com/top",         # 微博热搜榜
            "zhihu.com/hot",           # 知乎热榜
            "douyin.com/hot",          # 抖音热榜
            "tieba.baidu.com/hottopic", # 贴吧热议
            # 明确的广告网站
            "ad.doubleclick.net",      # 广告网站
            "googleads.g.doubleclick.net", # 谷歌广告
            "pagead2.googlesyndication.com", # 谷歌广告联盟
        ]

        def is_blacklisted_url(url: str) -> bool:
            """检查URL是否在黑名单中 - 更宽松的过滤"""
            url_lower = url.lower()
            for pattern in blacklisted_patterns:
                if pattern in url_lower:
                    logger.info(f"搜索阶段过滤黑名单URL: {url}")
                    return True
            return False

        try:
            soup = BeautifulSoup(html, 'html.parser')
            
            if engine == 'baidu':
                # 百度搜索结果解析 - 使用更全面的选择器策略
                search_results = []
                
                # 多层级选择器策略，从最精确到最宽泛
                selector_strategies = [
                    # 策略1: 标准百度搜索结果
                    {
                        'name': '标准搜索结果',
                        'container_selectors': [
                            '.result.c-container',
                            '.result',
                            '.c-container[mu]',
                            '.c-container',
                            '#content_left > div[mu]',
                            '#content_left > .result',
                        ],
                        'title_selectors': ['h3 a', '.t a', '.c-title a', 'h3', '.c-title'],
                        'snippet_selectors': ['.c-abstract', '.c-span9', '.c-span-last']
                    },
                    # 策略2: 新版百度搜索结果
                    {
                        'name': '新版搜索结果',
                        'container_selectors': [
                            '[data-click]',
                            '.new-pmd .c-result',
                            '.new-pmd .result-op',
                            '#content_left > div:not(.sp-separator)',
                        ],
                        'title_selectors': ['h3 a', '.c-title-text a', '.c-title a', 'h3'],
                        'snippet_selectors': ['.content-right_8Zs40', '.c-abstract', '.c-span9']
                    },
                    # 策略3: 移动版或特殊版本
                    {
                        'name': '移动版搜索结果',
                        'container_selectors': [
                            '.results .result',
                            '.results > div',
                            '#results .g',
                            '.page-bd article'
                        ],
                        'title_selectors': ['h3 a', '.t a', 'h3', 'a[href^="http"]'],
                        'snippet_selectors': ['.c-abstract', '.st', 'p']
                    }
                ]
                
                # 尝试每种策略
                for strategy in selector_strategies:
                    logger.info(f"尝试使用{strategy['name']}策略解析百度搜索结果")
                    
                    # 尝试容器选择器
                    for container_selector in strategy['container_selectors']:
                        containers = soup.select(container_selector)
                        if containers:
                            logger.info(f"使用容器选择器'{container_selector}'找到{len(containers)}个结果")
                            search_results = containers
                            break
                    
                    if search_results:
                        break
                
                # 如果所有策略都失败，使用通用链接查找法
                if not search_results:
                    logger.warning("所有选择器策略失败，使用通用链接查找法")
                    # 查找所有外部链接
                    all_links = soup.select('a[href^="http"]')
                    valid_links = []
                    
                    for link in all_links:
                        href = link.get('href', '')
                        text = link.get_text(strip=True)
                        
                        # 过滤掉明显的导航链接和无效链接
                        if (not href.startswith('http://www.baidu.com/') and 
                            not href.startswith('https://www.baidu.com/') and
                            text and len(text) > 5 and len(text) < 200 and
                            not any(skip in href.lower() for skip in ['javascript:', 'mailto:', '#'])):
                            valid_links.append(link)
                    
                    if valid_links:
                        search_results = valid_links[:num_results * 3]  # 取更多链接以备过滤
                        logger.info(f"通用链接查找法找到{len(search_results)}个候选结果")
                
                # 处理找到的结果
                processed_count = 0
                for i, item in enumerate(search_results):
                    if processed_count >= num_results:
                        break
                        
                    try:
                        title = ""
                        url = ""
                        snippet = ""
                        
                        # 根据项目类型确定提取方法
                        if item.name == 'a':  # 直接链接
                            title = item.get_text(strip=True)
                            url = item.get('href', '')
                        else:  # 容器
                            # 提取标题和URL
                            title_elem = None
                            for strategy in selector_strategies:
                                for title_selector in strategy['title_selectors']:
                                    title_elem = item.select_one(title_selector)
                                    if title_elem:
                                        break
                                if title_elem:
                                    break
                            
                            if title_elem:
                                title = title_elem.get_text(strip=True)
                                if title_elem.name == 'a':
                                    url = title_elem.get('href', '')
                                else:
                                    # 在标题元素中查找链接
                                    link_elem = title_elem.select_one('a')
                                    if link_elem:
                                        url = link_elem.get('href', '')
                            
                            # 如果没有找到URL，在容器中查找任何外部链接
                            if not url:
                                link_elem = item.select_one('a[href^="http"]')
                                if link_elem:
                                    url = link_elem.get('href', '')
                                    if not title:
                                        title = link_elem.get_text(strip=True)
                            
                            # 提取摘要
                            snippet_elem = None
                            for strategy in selector_strategies:
                                for snippet_selector in strategy['snippet_selectors']:
                                    snippet_elem = item.select_one(snippet_selector)
                                    if snippet_elem:
                                        break
                                if snippet_elem:
                                    break
                            
                            if snippet_elem:
                                snippet = snippet_elem.get_text(strip=True)
                        
                        # 基本验证
                        if not title or not url or len(title) < 3:
                            continue
                        
                        # 处理百度重定向链接
                        if 'baidu.com/link?url=' in url:
                            try:
                                # 简单的重定向处理
                                response = requests.head(url, headers=self._get_realistic_headers('baidu', 'zh-cn'), 
                                                       allow_redirects=True, timeout=5)
                                if response.url and response.url != url:
                                    url = response.url
                            except:
                                pass  # 如果重定向失败，使用原URL
                        
                        # 检查黑名单 - 使用更宽松的过滤
                        if is_blacklisted_url(url):
                            continue
                        
                        # 提取域名
                        domain = ""
                        try:
                            from urllib.parse import urlparse
                            parsed = urlparse(url)
                            domain = parsed.netloc.replace('www.', '')
                        except:
                            domain = "unknown"
                        
                        # 构建结果
                        result = {
                            "title": title[:200],  # 限制标题长度
                            "url": url,
                            "snippet": snippet[:500] if snippet else "",  # 限制摘要长度
                            "domain": domain,
                            "date": "",
                            "source": engine
                        }
                        
                        results.append(result)
                        processed_count += 1
                        
                    except Exception as e:
                        logger.warning(f"处理搜索结果项时出错: {str(e)}")
                        continue
            
            elif engine == 'bing':
                # 必应搜索结果解析
                for item in soup.select('.b_algo')[:num_results]:
                    try:
                        title_elem = item.select_one('h2 a')
                        snippet_elem = item.select_one('.b_caption p')
                        
                        if not title_elem or not snippet_elem:
                            continue
                            
                        title = title_elem.get_text(strip=True)
                        url = title_elem.get('href', '')
                        snippet = snippet_elem.get_text(strip=True)
                        
                        # 检查是否为黑名单URL
                        if is_blacklisted_url(url):
                            continue
                        
                        # 尝试提取域名
                        domain = ''
                        cite_elem = item.select_one('cite')
                        if cite_elem:
                            domain = cite_elem.get_text(strip=True)
                        
                        results.append({
                            "title": title,
                            "url": url,
                            "snippet": snippet,
                            "domain": domain,
                            "source": "bing"
                        })
                    except Exception as e:
                        logger.warning(f"解析必应搜索结果项失败: {str(e)}")
                        continue
            
            elif engine == 'google':
                # 谷歌搜索结果解析
                for item in soup.select('.g')[:num_results]:
                    try:
                        title_elem = item.select_one('h3')
                        snippet_elem = item.select_one('.VwiC3b')
                        url_elem = item.select_one('a')
                        
                        if not title_elem or not snippet_elem or not url_elem:
                            continue
                            
                        title = title_elem.get_text(strip=True)
                        url = url_elem.get('href', '')
                        snippet = snippet_elem.get_text(strip=True)
                        
                        # 检查是否为黑名单URL
                        if is_blacklisted_url(url):
                            continue
                        
                        # 尝试提取域名
                        domain = ''
                        if url:
                            parsed_url = urlparse(url)
                            domain = parsed_url.netloc
                        
                        results.append({
                            "title": title,
                            "url": url,
                            "snippet": snippet,
                            "domain": domain,
                            "source": "google"
                        })
                    except Exception as e:
                        logger.warning(f"解析谷歌搜索结果项失败: {str(e)}")
                        continue
        
        except Exception as e:
            logger.error(f"解析搜索结果失败: {str(e)}")
        
        return results
    
    async def fetch_page_content(self, url: str) -> Optional[Dict[str, Any]]:
        """获取页面内容
        
        Args:
            url: 要获取内容的页面URL
            
        Returns:
            页面内容或None
        """
        try:
            # 这里简单实现，实际应使用WebScraperTool获取真实内容
            return {
                "title": f"页面标题 - {url.split('/')[-1]}",
                "text": f"这是从{url}获取的页面正文内容。包含相关信息、段落和详细描述...",
                "type": "article"
            }
        except Exception as e:
            logger.error(f"获取页面内容失败: {str(e)}")
            return None
    
    def _build_search_url(self, query: str, engine: str, language: str, search_num_results: int) -> str:
        """构建搜索URL
        
        Args:
            query: 搜索查询
            engine: 搜索引擎
            language: 搜索语言
            search_num_results: 搜索结果数量
            
        Returns:
            搜索URL
        """
        # 编码查询
        encoded_query = quote(query)
        
        # 根据引擎构建URL
        if engine == 'baidu':
            # 移除tn=json参数，使用标准搜索结果页面格式
            return f"https://www.baidu.com/s?wd={encoded_query}&rn={search_num_results}"
        elif engine == 'bing':
            return f"https://cn.bing.com/search?q={encoded_query}&ensearch=0&setlang={language}"
        elif engine == 'google':
            return f"https://www.google.com/search?q={encoded_query}&hl={language}&num={search_num_results}"
        else:
            raise ValueError(f"不支持的搜索引擎: {engine}")

    def _filter_and_rank_results(self, results: List[Dict[str, Any]], target_count: int) -> List[Dict[str, Any]]:
        """过滤和排序搜索结果，选择最佳结果
        
        Args:
            results: 原始搜索结果列表
            target_count: 目标返回数量
            
        Returns:
            过滤和排序后的结果列表
        """
        if not results:
            return []
        
        # 定义高质量域名（知识类、技术类、新闻媒体等）
        high_quality_domains = {
            # 知识问答类
            'zhihu.com': 10,
            'stackoverflow.com': 9,
            'segmentfault.com': 8,
            'v2ex.com': 7,
            
            # 技术博客类
            'csdn.net': 8,
            'cnblogs.com': 8,
            'jianshu.com': 7,
            'juejin.cn': 8,
            'oschina.net': 7,
            
            # 官方文档类
            'docs.python.org': 10,
            'developer.mozilla.org': 10,
            'github.com': 9,
            'gitee.com': 8,
            
            # 新闻媒体类
            'xinhuanet.com': 8,
            'people.com.cn': 8,
            'chinanews.com': 7,
            '36kr.com': 7,
            'ithome.com': 7,
            
            # 微信公众号
            'mp.weixin.qq.com': 8,
            
            # 学术类
            'edu.cn': 9,  # 教育网站
            'ac.cn': 9,   # 科研院所
        }
        
        # 为每个结果计算质量分数
        scored_results = []
        for result in results:
            score = 0
            domain = result.get('domain', '').lower()
            title = result.get('title', '').lower()
            snippet = result.get('snippet', '').lower()
            
            # 域名质量分数
            for quality_domain, domain_score in high_quality_domains.items():
                if quality_domain in domain:
                    score += domain_score
                    break
            else:
                # 如果不是高质量域名，给基础分数
                score += 3
            
            # 内容质量分数
            # 标题长度合适加分
            if 10 <= len(result.get('title', '')) <= 100:
                score += 2
            
            # 有摘要内容加分
            if len(result.get('snippet', '')) > 50:
                score += 2
            
            # 包含关键技术词汇加分
            tech_keywords = ['教程', '指南', '详解', '分析', '实战', '案例', '经验', '技巧', '方法', '解决方案']
            for keyword in tech_keywords:
                if keyword in title or keyword in snippet:
                    score += 1
                    break
            
            # 避免明显的广告或低质量内容
            spam_keywords = ['广告', '推广', '代理', '加盟', '招商', '赚钱', '兼职']
            for keyword in spam_keywords:
                if keyword in title or keyword in snippet:
                    score -= 3
                    break
            
            scored_results.append((result, score))
        
        # 按分数排序，分数高的在前
        scored_results.sort(key=lambda x: x[1], reverse=True)
        
        # 返回前target_count个结果
        final_results = [result for result, score in scored_results[:target_count]]
        
        # 记录过滤统计
        logger.info(f"结果质量分析: 原始{len(results)}个 -> 过滤后{len(final_results)}个")
        if scored_results:
            top_scores = [score for _, score in scored_results[:min(5, len(scored_results))]]
            logger.info(f"前5个结果分数: {top_scores}")
        
        return final_results


class APIRequestTool(Tool):
    """API请求工具 - 发送API请求并处理响应"""
    
    def __init__(self):
        """初始化API请求工具"""
        super().__init__(
            name="api_request",
            description="发送API请求并处理响应，支持GET、POST、PUT、DELETE方法"
        )
    
    async def execute(self, params: Dict[str, Any], state: Dict[str, Any]) -> Dict[str, Any]:
        """执行API请求
        
        Args:
            params: 
                url: API请求URL
                method: 请求方法，支持"GET", "POST", "PUT", "DELETE"，默认为"GET"
                headers: 请求头
                data: 请求数据，用于POST/PUT请求
                json: JSON请求数据，用于POST/PUT请求
                params: 查询参数
                timeout: 超时时间（秒），默认为30
            state: 当前状态
            
        Returns:
            API响应
        """
        try:
            # 获取参数
            url = params.get('url')
            method = params.get('method', 'GET').upper()
            headers = params.get('headers', {})
            data = params.get('data')
            json_data = params.get('json')
            query_params = params.get('params')
            timeout = params.get('timeout', 30)
            
            # 验证参数
            if not url:
                raise ValueError("必须提供url参数")
            
            if method not in ['GET', 'POST', 'PUT', 'DELETE']:
                raise ValueError(f"不支持的请求方法: {method}")
            
            # 发送请求
            logger.info(f"发送 {method} 请求到 {url}")
            async with aiohttp.ClientSession() as session:
                request_kwargs = {
                    'headers': headers,
                    'timeout': timeout
                }
                
                if query_params:
                    request_kwargs['params'] = query_params
                
                if data:
                    request_kwargs['data'] = data
                
                if json_data:
                    request_kwargs['json'] = json_data
                
                async with getattr(session, method.lower())(url, **request_kwargs) as response:
                    # 获取响应状态码
                    status_code = response.status
                    
                    # 获取响应头
                    response_headers = dict(response.headers)
                    
                    # 获取响应内容类型
                    content_type = response_headers.get('Content-Type', '')
                    
                    # 根据内容类型处理响应
                    if 'application/json' in content_type:
                        # JSON响应
                        response_data = await response.json()
                    elif 'text/' in content_type:
                        # 文本响应
                        response_data = await response.text()
                    else:
                        # 二进制响应
                        response_data = base64.b64encode(await response.read()).decode('utf-8')
                        response_data = f"binary data ({len(response_data)} bytes)"
                    
                    return {
                        "url": url,
                        "method": method,
                        "status_code": status_code,
                        "headers": response_headers,
                        "content_type": content_type,
                        "data": response_data,
                        "is_success": 200 <= status_code < 300
                    }
        
        except Exception as e:
            logger.error(f"API请求失败: {str(e)}")
            raise


# 使用装饰器注册简单工具
@tool(
    name="fetch_rss",
    description="获取RSS提要内容",
    category="network"
)
async def fetch_rss_tool(params: Dict[str, Any], state: Dict[str, Any]) -> Dict[str, Any]:
    """获取RSS提要内容
    
    Args:
        params:
            url: RSS提要URL
            limit: 返回条目数量，默认为10
        state: 当前状态
        
    Returns:
        RSS提要内容
    """
    try:
        # 获取参数
        url = params.get('url')
        limit = params.get('limit', 10)
        
        # 验证参数
        if not url:
            raise ValueError("必须提供url参数")
        
        # 确保limit在合理范围内
        limit = max(1, min(limit, 50))
        
        # 发送请求
        async with aiohttp.ClientSession() as session:
            async with session.get(url, timeout=30) as response:
                if response.status != 200:
                    raise ValueError(f"RSS请求失败，状态码: {response.status}")
                
                # 读取响应内容
                xml = await response.text()
        
        # 解析RSS
        feed = {}
        soup = BeautifulSoup(xml, 'xml')
        
        # 尝试解析RSS和Atom格式
        if soup.rss:
            # RSS格式
            channel = soup.channel
            
            # 获取基本信息
            feed['title'] = channel.title.text if channel.title else ''
            feed['description'] = channel.description.text if channel.description else ''
            feed['link'] = channel.link.text if channel.link else ''
            feed['language'] = channel.language.text if channel.language else ''
            feed['lastBuildDate'] = channel.lastBuildDate.text if channel.lastBuildDate else ''
            
            # 获取条目
            items = []
            for item in channel.find_all('item')[:limit]:
                items.append({
                    'title': item.title.text if item.title else '',
                    'link': item.link.text if item.link else '',
                    'description': item.description.text if item.description else '',
                    'pubDate': item.pubDate.text if item.pubDate else '',
                    'author': item.author.text if item.author else '',
                    'categories': [cat.text for cat in item.find_all('category')]
                })
            
            feed['items'] = items
        
        elif soup.feed:
            # Atom格式
            atom_feed = soup.feed
            
            # 获取基本信息
            feed['title'] = atom_feed.title.text if atom_feed.title else ''
            feed['subtitle'] = atom_feed.subtitle.text if atom_feed.subtitle else ''
            feed['id'] = atom_feed.id.text if atom_feed.id else ''
            feed['updated'] = atom_feed.updated.text if atom_feed.updated else ''
            
            # 获取链接
            links = {}
            for link in atom_feed.find_all('link'):
                rel = link.get('rel', 'alternate')
                links[rel] = link.get('href', '')
            
            feed['links'] = links
            
            # 获取条目
            entries = []
            for entry in atom_feed.find_all('entry')[:limit]:
                entry_links = {}
                for link in entry.find_all('link'):
                    rel = link.get('rel', 'alternate')
                    entry_links[rel] = link.get('href', '')
                
                entries.append({
                    'title': entry.title.text if entry.title else '',
                    'id': entry.id.text if entry.id else '',
                    'updated': entry.updated.text if entry.updated else '',
                    'published': entry.published.text if entry.published else '',
                    'summary': entry.summary.text if entry.summary else '',
                    'content': entry.content.text if entry.content else '',
                    'author': entry.author.name.text if entry.author and entry.author.name else '',
                    'links': entry_links
                })
            
            feed['entries'] = entries
        
        return {
            "url": url,
            "feed_type": "rss" if soup.rss else "atom" if soup.feed else "unknown",
            "feed": feed
        }
    
    except Exception as e:
        logger.error(f"获取RSS提要失败: {str(e)}")
        raise


@tool(
    name="check_website_status",
    description="检查网站状态和可访问性",
    category="network"
)
async def check_website_status_tool(params: Dict[str, Any], state: Dict[str, Any]) -> Dict[str, Any]:
    """检查网站状态和可访问性
    
    Args:
        params:
            url: 网站URL
            timeout: 超时时间（秒），默认为10
            check_ssl: 是否检查SSL证书，默认为True
        state: 当前状态
        
    Returns:
        网站状态信息
    """
    try:
        # 获取参数
        url = params.get('url')
        timeout = params.get('timeout', 10)
        check_ssl = params.get('check_ssl', True)
        
        # 验证参数
        if not url:
            raise ValueError("必须提供url参数")
        
        # 如果URL不包含协议，默认添加https
        if not url.startswith('http://') and not url.startswith('https://'):
            url = f"https://{url}"
        
        # 开始计时
        start_time = time.time()
        
        # 发送请求
        async with aiohttp.ClientSession() as session:
            try:
                async with session.get(
                    url, 
                    timeout=timeout, 
                    ssl=None if not check_ssl else True,
                    allow_redirects=True
                ) as response:
                    # 计算响应时间
                    response_time = time.time() - start_time
                    
                    # 获取最终URL（考虑重定向）
                    final_url = str(response.url)
                    
                    # 获取响应状态码
                    status_code = response.status
                    
                    # 获取响应头
                    headers = dict(response.headers)
                    
                    # 检查是否有服务器信息
                    server = headers.get('Server', '')
                    
                    # 检查内容类型
                    content_type = headers.get('Content-Type', '')
                    
                    # 检查重定向
                    is_redirected = len(response.history) > 0
                    redirect_chain = [str(r.url) for r in response.history]
                    
                    # 提取响应内容（仅用于状态信息，不返回完整内容）
                    content = await response.read()
                    content_length = len(content)
                    
                    return {
                        "url": url,
                        "final_url": final_url,
                        "status_code": status_code,
                        "response_time": round(response_time, 3),
                        "is_up": status_code < 400,
                        "server": server,
                        "content_type": content_type,
                        "content_length": content_length,
                        "is_redirected": is_redirected,
                        "redirect_chain": redirect_chain if is_redirected else [],
                        "headers": headers
                    }
            
            except asyncio.TimeoutError:
                return {
                    "url": url,
                    "is_up": False,
                    "error": "请求超时",
                    "response_time": timeout
                }
            
            except aiohttp.ClientSSLError:
                return {
                    "url": url,
                    "is_up": False,
                    "error": "SSL证书验证失败",
                    "response_time": time.time() - start_time
                }
            
            except aiohttp.ClientConnectorError as e:
                return {
                    "url": url,
                    "is_up": False,
                    "error": f"连接错误: {str(e)}",
                    "response_time": time.time() - start_time
                }
    
    except Exception as e:
        logger.error(f"检查网站状态失败: {str(e)}")
        raise


# 注册网络工具函数
def register_network_tools(registry):
    """向工具注册表注册网络相关工具
    
    Args:
        registry: 工具注册表
    """
    # 检查并注册网页抓取工具
    if not registry.get_tool("web_scraper"):
        registry.register_tool(WebScraperTool())
    
    # 注册搜索引擎工具
    if not registry.get_tool("search_engine"):
        registry.register_tool(SearchEngineTool())
    
    # 注册API请求工具
    if not registry.get_tool("api_request"):
        registry.register_tool(APIRequestTool())
    
    # 注意：fetch_rss_tool 和 check_website_status_tool 已通过 @tool 装饰器注册
    # 不需要再次注册它们
    
    logger.info("网络工具注册完成") 