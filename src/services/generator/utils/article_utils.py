"""
文章处理工具函数
提供计算可读性、字数统计、内容质量分析等功能
"""
import re
import logging
import json
from typing import Dict, List, Any, Optional
import numpy as np

logger = logging.getLogger(__name__)

def count_words(text: str) -> int:
    """
    计算文本中的字/词数量
    
    Args:
        text: 要计算的文本
        
    Returns:
        字/词数量
    """
    if not text:
        return 0
        
    # 中文计算字数
    chinese_count = len(re.findall(r'[\u4e00-\u9fff]', text))
    
    # 英文和其他语言计算词数
    words = re.findall(r'\b\w+\b', text)
    word_count = len(words)
    
    # 如果中文字符占比超过50%，则主要返回中文字数
    if chinese_count > 0 and chinese_count / len(text) > 0.5:
        return chinese_count
    else:
        return word_count

def calculate_readability(text: str) -> float:
    """
    计算文本可读性评分
    
    使用简化的算法，返回0-1之间的评分，1表示最易读
    
    Args:
        text: 要分析的文本
        
    Returns:
        可读性评分 (0.0-1.0)
    """
    if not text or len(text) < 10:
        return 0.0
    
    try:
        # 计算平均句子长度
        sentences = re.split(r'[。！？.!?]', text)
        sentences = [s for s in sentences if s.strip()]
        
        if not sentences:
            return 0.5  # 没有句子，返回中等值
        
        # 平均句子长度
        avg_sentence_length = sum(len(s) for s in sentences) / len(sentences)
        
        # 长句子占比
        long_sentences = sum(1 for s in sentences if len(s) > 50)
        long_sentence_ratio = long_sentences / len(sentences) if sentences else 0
        
        # 计算复杂词比例
        words = re.findall(r'\b\w+\b', text)
        # 简单定义复杂词：长度超过5个字符的词
        complex_words = sum(1 for w in words if len(w) > 5)
        complex_word_ratio = complex_words / len(words) if words else 0
        
        # 计算标点符号密度
        punctuation_count = len(re.findall(r'[，。、！？,.!?:;；：]', text))
        punctuation_density = punctuation_count / len(text) if text else 0
        
        # 根据以上指标计算可读性分数
        # 句子越短、复杂词比例越低，可读性越高
        readability = 1.0 - (
            0.3 * min(1.0, avg_sentence_length / 40) +  # 惩罚长句子
            0.3 * complex_word_ratio +                 # 惩罚复杂词
            0.2 * long_sentence_ratio +                # 惩罚长句子比例
            0.2 * max(0, 0.2 - punctuation_density)    # 适当的标点密度有利于可读性
        )
        
        return max(0.0, min(1.0, readability))  # 确保在0-1范围内
    except Exception as e:
        logger.error(f"计算可读性时出错: {str(e)}")
        return 0.5  # 出错时返回中等值

def extract_keywords(text: str, top_n: int = 10) -> List[str]:
    """
    从文本中提取关键词
    
    使用简单的TF算法，未来可以集成更复杂的方法
    
    Args:
        text: 要分析的文本
        top_n: 返回的关键词数量
        
    Returns:
        关键词列表
    """
    if not text:
        return []
        
    try:
        # 简单的中英文分词
        words = []
        
        # 英文分词
        english_words = re.findall(r'\b[a-zA-Z]{3,}\b', text.lower())
        words.extend(english_words)
        
        # 中文分词 (简单实现，真实项目应使用jieba等专业分词库)
        for i in range(len(text) - 1):
            if '\u4e00' <= text[i] <= '\u9fff' and '\u4e00' <= text[i+1] <= '\u9fff':
                words.append(text[i:i+2])
        
        # 停用词
        stop_words = set(['的', '了', '是', '在', '我', '有', '和', '就', '不', '人', '都', 
                          'the', 'and', 'for', 'that', 'have', 'not', 'with', 'you', 'this', 'but'])
        
        # 统计词频
        word_freq = {}
        for word in words:
            if word not in stop_words and len(word) > 1:
                word_freq[word] = word_freq.get(word, 0) + 1
        
        # 按词频排序
        sorted_words = sorted(word_freq.items(), key=lambda x: x[1], reverse=True)
        
        # 返回前N个关键词
        return [word for word, _ in sorted_words[:top_n]]
    except Exception as e:
        logger.error(f"提取关键词时出错: {str(e)}")
        return []

def analyze_content_quality(text: str) -> Dict[str, Any]:
    """
    分析内容质量
    
    评估内容的质量，返回各种指标
    
    Args:
        text: 要分析的文本
        
    Returns:
        包含质量评分和各项指标的字典
    """
    if not text:
        return {"score": 0.0, "metrics": {}}
        
    try:
        # 字数
        word_count = count_words(text)
        
        # 可读性
        readability = calculate_readability(text)
        
        # 段落数
        paragraphs = [p for p in text.split('\n\n') if p.strip()]
        paragraph_count = len(paragraphs)
        
        # 平均段落长度
        avg_paragraph_length = word_count / paragraph_count if paragraph_count > 0 else 0
        
        # 句子数
        sentences = re.split(r'[。！？.!?]', text)
        sentences = [s for s in sentences if s.strip()]
        sentence_count = len(sentences)
        
        # 平均句子长度
        avg_sentence_length = word_count / sentence_count if sentence_count > 0 else 0
        
        # 计算信息密度 (基于每段句子数)
        sentences_per_paragraph = sentence_count / paragraph_count if paragraph_count > 0 else 0
        
        # 计算复杂结构的比例 (包含中文引号或列表标记的句子)
        complex_structures = sum(1 for s in sentences if re.search(r'[""]|[（）()]|[①②③④⑤]|[0-9]\.|[a-z]\)', s))
        complex_structure_ratio = complex_structures / sentence_count if sentence_count > 0 else 0
        
        # 指标权重和评分计算
        metrics = {
            "word_count": word_count,
            "readability": readability,
            "paragraph_count": paragraph_count,
            "avg_paragraph_length": avg_paragraph_length,
            "sentence_count": sentence_count,
            "avg_sentence_length": avg_sentence_length,
            "sentences_per_paragraph": sentences_per_paragraph,
            "complex_structure_ratio": complex_structure_ratio
        }
        
        # 评分计算 (0-1 之间)
        score_components = [
            readability * 0.3,  # 可读性
            min(1.0, word_count / 1000) * 0.2,  # 内容长度
            min(1.0, complex_structure_ratio * 3) * 0.2,  # 结构复杂性
            min(1.0, (1 - abs(avg_paragraph_length - 100) / 100)) * 0.15,  # 段落长度接近最佳值
            min(1.0, (1 - abs(avg_sentence_length - 25) / 25)) * 0.15  # 句子长度接近最佳值
        ]
        
        overall_score = sum(score_components)
        
        return {
            "score": min(1.0, overall_score),
            "metrics": metrics
        }
    except Exception as e:
        logger.error(f"分析内容质量时出错: {str(e)}")
        return {"score": 0.5, "metrics": {}}

def calculate_keyword_density(text: str, keywords: List[str]) -> Dict[str, float]:
    """
    计算关键词密度
    
    Args:
        text: 要分析的文本
        keywords: 关键词列表
        
    Returns:
        关键词密度字典 {关键词: 密度}
    """
    if not text or not keywords:
        return {}
        
    word_count = count_words(text)
    if word_count == 0:
        return {keyword: 0.0 for keyword in keywords}
    
    result = {}
    for keyword in keywords:
        if not keyword:
            continue
            
        # 计算关键词出现次数
        count = len(re.findall(re.escape(keyword), text, re.IGNORECASE))
        
        # 计算密度
        density = count / word_count
        
        result[keyword] = density
    
    return result

def extract_content_structure(text: str) -> Dict[str, Any]:
    """
    提取内容结构
    
    Args:
        text: 要分析的文本
        
    Returns:
        内容结构字典
    """
    if not text:
        return {"paragraphs": 0, "sections": []}
    
    # 分割段落
    paragraphs = [p for p in text.split('\n\n') if p.strip()]
    
    # 尝试识别标题和小节
    sections = []
    current_section = None
    
    for para in paragraphs:
        # 判断是否为标题
        if len(para) < 40 and (para.endswith('：') or para.endswith(':') or para.strip().isupper()):
            # 看起来像标题
            if current_section:
                sections.append(current_section)
            current_section = {"title": para, "content": []}
        else:
            # 普通段落
            if current_section:
                current_section["content"].append(para)
            else:
                # 没有遇到标题前的段落
                if not sections:
                    # 创建默认章节
                    current_section = {"title": "", "content": [para]}
                else:
                    # 添加到最后一个章节
                    sections[-1]["content"].append(para)
    
    # 添加最后一个章节
    if current_section:
        sections.append(current_section)
    
    # 计算每个章节的字数
    for section in sections:
        section["word_count"] = sum(count_words(para) for para in section["content"])
        section["content"] = "\n\n".join(section["content"])
    
    return {
        "paragraphs": len(paragraphs),
        "sections": sections
    } 