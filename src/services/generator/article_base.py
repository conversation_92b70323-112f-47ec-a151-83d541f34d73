from abc import ABC, abstractmethod
from typing import Dict, Optional, List, Any, Union
from langchain_core.language_models.base import BaseLanguageModel

from src.config.logging_config import LoggerName
from src.utils.logger import CrawlerLogger, log_info, log_error
from src.services.generator.state import ArticleState as ArticleStateClass

logger = CrawlerLogger().get_logger(LoggerName.API)

class ArticleAgent(ABC):
    """文章处理Agent基类 - 所有文章处理Agent都应继承此类"""
    
    def __init__(self, llm: Optional[BaseLanguageModel] = None):
        """初始化Agent
        
        Args:
            llm: 语言模型
        """
        self.llm = llm
        self.memory_manager = None
    
    def set_memory_manager(self, memory_manager):
        """设置记忆管理器
        
        Args:
            memory_manager: 记忆管理器实例
        """
        self.memory_manager = memory_manager
    
    @abstractmethod
    async def process(self, state: Dict) -> Dict:
        """处理状态
        
        Args:
            state: 当前状态
            
        Returns:
            更新后的状态
        """
        pass
    
    @abstractmethod
    def get_description(self) -> str:
        """获取Agent描述
        
        Returns:
            Agent描述字符串
        """
        pass
    
    def get_relevant_memories(self, query: str, limit: int = 3) -> Dict[str, List[Dict[str, Any]]]:
        """获取与查询相关的记忆
        
        Args:
            query: 查询字符串
            limit: 每类记忆返回的最大条数
            
        Returns:
            相关记忆的字典
        """
        if self.memory_manager:
            return self.memory_manager.get_relevant_memories(query, limit)
        return {"conversations": [], "articles": [], "reflections": []}
    
    def _format_memories_for_prompt(self, memories: Dict[str, List[Dict[str, Any]]]) -> str:
        """将记忆格式化为提示词
        
        Args:
            memories: 记忆字典
            
        Returns:
            格式化后的提示词
        """
        formatted = []
        
        # 格式化对话记忆
        if memories["conversations"]:
            formatted.append("相关对话:")
            for i, conv in enumerate(memories["conversations"], 1):
                formatted.append(f"  {i}. 用户: {conv.get('user_query', '')}")
                formatted.append(f"     回答: {conv.get('system_response', '')[:100]}...")
        
        # 格式化文章记忆
        if memories["articles"]:
            formatted.append("\n相关文章:")
            for i, article in enumerate(memories["articles"], 1):
                formatted.append(f"  {i}. 标题: {article.get('title', '')}")
                formatted.append(f"     关键词: {', '.join(article.get('keywords', []))}")
                formatted.append(f"     摘要: {article.get('summary', '')[:100]}...")
        
        # 格式化反思记忆
        if memories["reflections"]:
            formatted.append("\n相关反思:")
            for i, reflection in enumerate(memories["reflections"], 1):
                formatted.append(f"  {i}. 观察: {reflection.get('observation', '')[:100]}...")
                formatted.append(f"     思考: {reflection.get('thought', '')[:100]}...")
                formatted.append(f"     行动: {reflection.get('action', '')[:100]}...")
        
        return "\n".join(formatted) if formatted else "无相关记忆"


def add_messages_to_messages(messages: List, new_messages: List) -> List:
    """添加新消息到消息列表
    
    Args:
        messages: 原消息列表
        new_messages: 要添加的新消息
        
    Returns:
        更新后的消息列表
    """
    if not messages:
        return new_messages
    
    return messages + new_messages


# 基础的状态类型
ArticleState = Union[ArticleStateClass, Dict[str, Any]] 