from typing import Dict, List, Any, Optional, Tuple, Union
import logging
import random
import json
import re
import time
import uuid
import traceback
import ast
import json_repair  # 添加json_repair库导入

from src.services.generator.agents.base import AgentOutput, BaseAgent
from src.services.generator.tools.decision_tools import TaskDecompositionTool
from src.services.generator.state import ArticleState, ArticleStatus
from src.services.generator.text_generator import LLMAdapter, LLMRole
from src.services.generator.prompts import planning_combined_article_plan_prompt, planning_search_query_generation_prompt

logger = logging.getLogger("planning_agent")

class PlanningAgent(BaseAgent):
    """规划代理，负责为文章制定结构和大纲"""
    
    def __init__(self):
        """初始化规划代理"""
        super().__init__(
            name="规划代理",
            description="为文章制定结构和大纲"
        )
        # 初始化任务分解工具
        self.task_decomposition = TaskDecompositionTool()
        self.llm_adapter = LLMAdapter(default_provider="deepseek")
        self.logger = logger
        
    async def run(self, state: ArticleState) -> AgentOutput:
        """运行规划代理，并生成搜索查询
        
        Args:
            state: 当前的文章生成状态
            
        Returns:
            包含是否成功以及更新后的状态的代理输出
        """
        self.logger.info("规划代理开始运行")
        start_time = time.time()

        # 验证输入
        if not self._validate_input(state):
            self.logger.error("输入参数无效")
            return AgentOutput(success=False, error="输入参数无效", state=state)

        try:
            
            self.logger.info(f"选择了最佳内容方向: {state.selected_direction}")
            
            # 获取当前时间信息
            current_time = await self._get_current_time()
            self.logger.info(f"获取到当前时间信息: {current_time.get('formatted_date', '')}")
                
            # 直接生成完整文章计划
            self.logger.info(f"开始为主题 '{state.topic}' 生成完整文章计划")
            plan = await self._generate_combined_article_plan(state, current_time)
                
            # 设置文章计划到状态中
            state.article_plan = plan
                
            # 提取大纲并设置到状态中
            if "outline" in plan and isinstance(plan["outline"], list):
                if plan["outline"]:  # 确保大纲不为空
                    state.outline = plan["outline"]
                    self.logger.info(f"已设置大纲，包含 {len(state.outline)} 个章节")
                else:
                    self.logger.warning("文章计划中的大纲为空列表，创建默认大纲")
                    # 创建默认大纲
              
            else:
                self.logger.warning("文章计划中没有有效的大纲，创建默认大纲")
                # 创建默认大纲
           
            # 生成搜索查询
            try:
                state.search_queries = await self._generate_search_queries(state, current_time)
                self.logger.info(f"生成了 {len(state.search_queries)} 个搜索查询")
            except Exception as search_error:
                self.logger.error(f"生成搜索查询失败: {str(search_error)}")
                # 如果生成搜索查询失败，创建基本查询
                state.search_queries = [f"{state.topic} 资料", f"{state.topic} 案例", f"{state.topic} 分析"]
                self.logger.info(f"使用基本搜索查询: {state.search_queries}")
            
            # 更新状态
            state.update_status(ArticleStatus.PLANNING)
            
            # 记录执行时间
            execution_time = time.time() - start_time
            
            self.logger.info(f"规划代理运行完成，耗时: {execution_time:.2f}秒")
            return AgentOutput(success=True, state=state)
        except Exception as e:
            self.logger.error(f"规划代理运行出错: {str(e)}")
            traceback.print_exc()
                
            
    
    def _validate_input(self, state: ArticleState) -> bool:
        """验证输入状态是否有效
        
        Args:
            state: 文章生成状态
            
        Returns:
            是否有效
        """
        # 必须有主题或关键词之一
        has_topic = bool(state.topic)
        has_keywords = bool(state.keywords)
        
        if not has_topic and not has_keywords:
            self.logger.error("缺少主题和关键词")
            return False
        
        return True
    
    def _get_logger(self) -> logging.Logger:
        """获取当前Agent的logger

        Returns:
            logging.Logger: 日志记录器
        """
        return self.logger
            
    def _update_topic_and_keywords_for_direction(self, state: ArticleState, selected_direction: str, direction_index: int = -1) -> None:
        """基于选定的内容方向更新topic和keywords
        
        Args:
            state: 文章状态
            selected_direction: 选定的内容方向
            direction_index: 内容方向在列表中的索引，如果无法确定则为-1
        """
        # 获取analysis_result，可能是一个列表
        analysis_result = []
        if hasattr(state, "analysis_result") and state.analysis_result:
            if isinstance(state.analysis_result, list):
                analysis_result = state.analysis_result
            elif isinstance(state.analysis_result, dict) and "analysis_result" in state.analysis_result:
                if isinstance(state.analysis_result["analysis_result"], list):
                    analysis_result = state.analysis_result["analysis_result"]
        
        # 如果找到了对应的analysis_result项
        if direction_index >= 0 and direction_index < len(analysis_result):
            item = analysis_result[direction_index]
            if isinstance(item, dict):
                # 更新topic - 使用推荐标题
                if "recommended_title" in item and item["recommended_title"]:
                    new_topic = item["recommended_title"]
                    self.logger.info(f"根据选定方向更新主题: {new_topic}")
                    state.topic = new_topic
                
                # 更新keywords - 使用对应的关键词
                if "keywords" in item and isinstance(item["keywords"], list) and item["keywords"]:
                    new_keywords = item["keywords"]
                    self.logger.info(f"根据选定方向更新关键词: {new_keywords}")
                    state.keywords = new_keywords
                    
                return
        
        # 如果没有找到精确匹配或无法确定方向索引，尝试查找匹配的内容方向
        if analysis_result:
            # 尝试在analysis_result中查找匹配的内容方向
            for item in analysis_result:
                if isinstance(item, dict) and "content_direction" in item and item["content_direction"] == selected_direction:
                    # 更新topic - 使用推荐标题
                    if "recommended_title" in item and item["recommended_title"]:
                        new_topic = item["recommended_title"]
                        self.logger.info(f"找到匹配的内容方向，更新主题: {new_topic}")
                        state.topic = new_topic
                    
                    # 更新keywords - 使用对应的关键词
                    if "keywords" in item and isinstance(item["keywords"], list) and item["keywords"]:
                        new_keywords = item["keywords"]
                        self.logger.info(f"找到匹配的内容方向，更新关键词: {new_keywords}")
                        state.keywords = new_keywords
                        
                    return
            
            # 如果找不到精确匹配，使用第一个分析项
            first_item = analysis_result[0]
            if isinstance(first_item, dict):
                # 如果没有topic，使用第一个推荐标题
                if not state.topic and "recommended_title" in first_item and first_item["recommended_title"]:
                    state.topic = first_item["recommended_title"]
                    self.logger.info(f"未找到匹配内容方向，使用第一个推荐标题: {state.topic}")
                
                # 如果没有keywords，使用第一个关键词列表
                if not state.keywords and "keywords" in first_item and isinstance(first_item["keywords"], list):
                    state.keywords = first_item["keywords"]
                    self.logger.info(f"未找到匹配内容方向，使用第一个关键词列表: {state.keywords}")
        
        # 如果仍然没有主题，从选定的方向中提取作为主题
        if not state.topic:
            # 将方向作为主题
            direction_as_topic = selected_direction
            if len(direction_as_topic) > 50:  # 如果太长，截断
                direction_as_topic = direction_as_topic[:50] + "..."
            state.topic = direction_as_topic
            self.logger.info(f"使用选定方向作为主题: {state.topic}")
        
        # 如果仍然没有关键词，从选定的方向中提取关键词
        if not state.keywords:
            # 从方向中提取关键词
            extracted_keywords = self._extract_key_phrases(selected_direction)
            if extracted_keywords:
                state.keywords = extracted_keywords[:5]  # 最多取5个关键词
                self.logger.info(f"从选定方向提取关键词: {state.keywords}")
            else:
                # 使用方向中的单词作为关键词
                words = selected_direction.split()
                state.keywords = [word for word in words if len(word) > 3][:5]
                self.logger.info(f"使用方向中的单词作为关键词: {state.keywords}")
    
    async def _generate_search_queries(self, state: ArticleState, current_time: Dict[str, Any] = None) -> List[str]:
        """生成搜索查询列表
        
        Args:
            state: 当前的文章生成状态
            current_time: 当前时间信息
            
        Returns:
            搜索查询列表
        """
        try:
            self.logger.info("使用大模型生成搜索查询")
            topic = state.topic
            selected_direction = getattr(state, "selected_direction", "")
            keywords = getattr(state, "keywords", [])
            
            # 使用从prompts.py导入的函数生成提示词
            try:
                prompt = planning_search_query_generation_prompt(
                    topic=state.topic,
                    outline=state.outline if hasattr(state, "outline") else [],
                    content_direction=selected_direction,
                    keywords=keywords,
                    article_type=state.article_plan.get("article_type", "") if hasattr(state, "article_plan") else "",
                    current_time=current_time
                )
                self.logger.info(f"提示词长度: {len(prompt)}")
            except Exception as prompt_err:
                self.logger.error(f"生成提示词时出错: {str(prompt_err)}")
                raise ValueError(f"生成搜索查询提示词失败: {str(prompt_err)}")
            
            # 调用LLM生成查询
            try:
                response = await self.llm_adapter.ask_llm(
                    role="search_query_generator",
                    prompt=prompt,
                    temperature=0.7,
                    max_tokens=3048
                )
                self.logger.info(f"LLM返回响应，长度: {len(response)}")
            except Exception as llm_err:
                self.logger.error(f"调用LLM时出错: {str(llm_err)}")
                raise ValueError(f"调用语言模型生成搜索查询失败: {str(llm_err)}")
            
            # 解析响应中的查询
            queries = []
            try:
                # 清理响应中的代码块标记
                cleaned_response = re.sub(r'```(json|)\s*|\s*```', '', response.strip())
                
                # 尝试解析JSON响应
                try:
                    data = json.loads(cleaned_response)
                    # 新格式: {"search_queries": ["查询1", "查询2", ...]}
                    if "search_queries" in data and isinstance(data["search_queries"], list):
                        queries = data["search_queries"]
                        self.logger.info(f"从新格式JSON中提取到 {len(queries)} 个查询")
                    # 旧格式: {"queries": ["查询1", "查询2", ...]}
                    elif "queries" in data and isinstance(data["queries"], list):
                        queries = data["queries"]
                        self.logger.info(f"从旧格式JSON中提取到 {len(queries)} 个查询")
                    # 其他可能的格式
                    elif isinstance(data, list):
                        queries = data
                        self.logger.info(f"从JSON数组中提取到 {len(queries)} 个查询")
                    else:
                        self.logger.warning("JSON响应中没有有效的查询字段")
                        # 尝试文本解析方法
                        lines = response.strip().split('\n')
                        queries = [line.strip() for line in lines if line.strip() and not line.startswith('```')]
                        self.logger.info(f"通过分行方式提取到 {len(queries)} 个查询")
                except json.JSONDecodeError:
                    # 直接按行分割文本提取查询
                    lines = response.strip().split('\n')
                    queries = []
                    for line in lines:
                        line = line.strip()
                        if line and not line.startswith('```'):
                            # 清理可能的序号前缀和引号
                            line = re.sub(r'^(\d+\.\s+|\-\s+|\*\s+)', '', line)
                            line = line.strip('"\'')
                            if line and len(line) > 3:
                                queries.append(line)
                    self.logger.info(f"通过文本处理提取到 {len(queries)} 个查询")
            except Exception as parse_err:
                self.logger.error(f"解析查询时出错: {str(parse_err)}")
                # 如果解析失败但有返回内容，尝试更简单的分割方法
                if response and len(response) > 10:
                    try:
                        # 简单地按行分割，移除空行和太短的行
                        simple_queries = [
                            line.strip() for line in response.splitlines()
                            if line.strip() and len(line.strip()) > 5
                        ]
                        if simple_queries:
                            self.logger.info(f"使用简单分割提取到 {len(simple_queries)} 个查询")
                            queries = simple_queries
                    except Exception:
                        pass
            
            # 如果无法解析出查询，则抛出异常
            if not queries:
                raise ValueError("无法从LLM响应中提取搜索查询")
            
            # 确保查询数量在合理范围内，最多返回10个查询
            if len(queries) > 10:
                queries = queries[:10]
                self.logger.info(f"截取前10个查询")
            
            # 过滤无效查询
            queries = [q for q in queries if q and isinstance(q, str) and len(q.strip()) > 3]
            
            self.logger.info(f"成功生成 {len(queries)} 个搜索查询")
            return queries
            
        except Exception as e:
            self.logger.error(f"使用大模型生成搜索查询失败: {str(e)}")
            raise ValueError(f"生成搜索查询失败: {str(e)}")
    
    def _rank_search_queries(self, queries: List[str], topic: str, selected_direction: Optional[str] = None) -> List[str]:
        """根据查询质量对搜索查询进行排序
        
        Args:
            queries: 要排序的查询列表
            topic: 文章主题
            selected_direction: 选定的内容方向
            
        Returns:
            排序后的查询列表
        """
        # 创建评分函数
        def score_query(query: str) -> float:
            score = 0.0
            
            # 基础分
            score += 1.0
            
            # 如果包含所选方向，加分
            if selected_direction and selected_direction in query:
                score += 3.0
                
            # 如果包含主题，加分
            if topic in query:
                score += 2.0
                
            # 如果长度适中，加分
            query_len = len(query)
            if 10 <= query_len <= 50:
                score += 1.0
            elif query_len > 50:
                score -= (query_len - 50) / 50  # 长度过长扣分
                
            # 如果包含数字，加分（可能是数据相关）
            if re.search(r'\d+', query):
                score += 0.5
                
            # 如果包含特定关键词，加分
            boost_keywords = ["研究", "数据", "案例", "专家", "观点", "分析", "趋势", "争议", "最新"]
            for keyword in boost_keywords:
                if keyword in query:
                    score += 0.5
                    
            return score
            
        # 对查询进行评分和排序
        scored_queries = [(query, score_query(query)) for query in queries]
        scored_queries.sort(key=lambda x: x[1], reverse=True)
        
        # 返回排序后的查询列表
        return [query for query, _ in scored_queries]
    
    def _extract_key_phrases(self, text: str) -> List[str]:
        """从文本中提取关键短语
        
        Args:
            text: 要分析的文本
            
        Returns:
            关键短语列表
        """
        # 简单实现：按句子分割，然后取每句的前半部分
        phrases = []
        sentences = re.split(r'[.!?；。！？]', text)
        
        for sentence in sentences:
            sentence = sentence.strip()
            if len(sentence) > 8:  # 忽略太短的句子
                # 取句子的前半部分或前20个字符
                cutoff = min(len(sentence) // 2, 20)
                phrase = sentence[:cutoff].strip()
                if phrase and len(phrase) > 3:
                    phrases.append(phrase)
        
        # 如果没有提取到足够的短语，取整个文本的前部分
        if len(phrases) < 2 and len(text) > 5:
            phrases.append(text[:20].strip())
        
        return phrases
    
    async def _generate_combined_article_plan(self, state: ArticleState, current_time: Dict[str, Any] = None) -> Dict[str, Any]:
        """生成合并了大纲和完整计划功能的文章计划
        
        Args:
            state: 文章生成状态
            current_time: 当前时间信息
            
        Returns:
            完整的文章计划数据
        """
        # 准备提示词材料
        topic = state.topic
        keywords = state.keywords or []
        
        # 防御性编程：确保topic存在
        if not topic:
            self.logger.warning("主题为空，使用默认主题")
            topic = "未定义主题"
        
        # 使用选定的方向
        content_directions = None
        if hasattr(state, "selected_direction") and state.selected_direction:
            content_directions = state.selected_direction
        else:
            self.logger.warning("未找到有效的内容方向，使用通用方向")
            content_directions = "通用内容"
           
        # 使用prompts.py中的提示词生成计划
        try:
            prompt = planning_combined_article_plan_prompt(
                topic=topic,
                keywords=keywords,
                content_directions=content_directions,
                recommended_titles=topic,
                current_time=current_time
            )
        except Exception as e:
            self.logger.error(f"生成计划提示词失败: {str(e)}")
            prompt = f"""为主题"{topic}"生成一个文章计划，包括文章类型、大纲和写作策略。
            关键词: {', '.join(keywords) if keywords else '无'}
            内容方向: {content_directions}
            请提供JSON格式的计划。"""

        try:
            # 调用LLM生成完整计划
            self.logger.info("调用LLM生成完整文章计划")
            response = await self.llm_adapter.ask_llm(
                role="planner",
                prompt=prompt,
                temperature=0.7,
                max_tokens=4000
            )
            
            self.logger.info(f"LLM返回完整计划，共 {len(response)} 个字符")
            
            # 解析响应
            if not response or len(response.strip()) < 20:
                self.logger.error("LLM返回计划为空或过短")
                # 不抛出异常，而是使用基本回退计划
                return self._create_fallback_plan(topic, keywords)
            
            # 尝试从响应中提取JSON
            try:
                # 清理响应文本并提取JSON对象
                plan_data = self._extract_json_from_response(response)
                
                # 验证计划结构
                validated_plan = self._validate_plan_structure(plan_data)
                self.logger.info("成功提取和验证LLM返回的计划")
                return validated_plan
                    
            except Exception as e:
                self.logger.error(f"计划解析失败: {str(e)}")
                # 不抛出异常，而是使用基本回退计划
                return self._create_fallback_plan(topic, keywords, error_msg=str(e))
                
        except Exception as e:
            self.logger.error(f"计划生成失败: {str(e)}")
            # 不抛出异常，而是使用基本回退计划
            return self._create_fallback_plan(topic, keywords, error_msg=str(e))
    
    def _create_fallback_plan(self, topic: str, keywords: List[str], error_msg: str = None) -> Dict[str, Any]:
        """创建基本的回退计划
        
        当计划生成或解析失败时使用
        
        Args:
            topic: 文章主题
            keywords: 关键词列表
            error_msg: 可选的错误信息
            
        Returns:
            基本的文章计划
        """
        self.logger.info("创建基本回退计划")
        
        # 决定文章类型 - 根据关键词和主题特征
        article_type = "通用"
        if any(kw in " ".join(keywords).lower() for kw in ["如何", "教程", "步骤", "方法"]):
            article_type = "教程/指南"
        elif any(kw in " ".join(keywords).lower() for kw in ["比较", "vs", "对比"]):
            article_type = "对比分析"
        elif any(kw in " ".join(keywords).lower() for kw in ["故事", "经历", "案例"]):
            article_type = "故事+感触+干货"
        elif any(kw in " ".join(keywords).lower() for kw in ["观点", "分析", "认为"]):
            article_type = "观点+案例+金句"
        
        # 创建基本大纲
        basic_outline = [
            {"title": "引言", "content": f"介绍{topic}的背景和重要性"},
            {"title": f"{topic}的主要方面", "content": "详细探讨主题的核心内容"},
            {"title": "实际应用和案例", "content": "提供实例和应用场景"},
            {"title": "总结与展望", "content": "总结要点并展望未来发展"}
        ]
        
        # 构建完整计划
        fallback_plan = {
            "title": f"{topic}的全面解析",
            "article_type": article_type,
            "outline": basic_outline,
            "keywords": keywords,
            "writing_style": self._get_writing_style_for_article_type(article_type),
            "is_fallback": True  # 标记这是回退计划
        }
        
        # 添加错误信息
        if error_msg:
            fallback_plan["error_message"] = error_msg
        
        return fallback_plan
    
    def _extract_json_from_response(self, text: str) -> dict:
        """从LLM响应中提取JSON结构
        
        使用多种策略和方法提取和解析JSON，优化解析成功率:
        1. 直接解析尝试
        
        Args:
            text: LLM返回的文本
            
        Returns:
            解析后的JSON字典
        """
        logger = self._get_logger()
        
        # 确保输入为字符串
        if not isinstance(text, str):
            logger.warning(f"输入不是字符串，而是 {type(text)}，尝试转换")
            try:
                text = str(text)
            except Exception as e:
                logger.error(f"将输入转换为字符串失败: {str(e)}")
                return {"error": "无法处理非字符串输入"}
        
        # 记录原始响应长度，用于调试
        logger.info(f"开始解析JSON响应，原始长度: {len(text)} 字符")
        
        # 保存原始文本以便后续处理
        original_text = text
        
        # 1. 预处理文本 - 移除BOM和控制字符
        text = text.replace('\ufeff', '')
        text = re.sub(r'[\x00-\x08\x0B\x0C\x0E-\x1F]', '', text)
        
        # 2. 尝试直接解析JSON
        try:
            data = json.loads(text)
            logger.info("成功直接解析JSON响应")
            return data
        except json.JSONDecodeError as e:
            logger.warning(f"JSON直接解析失败: {str(e)}")
        except Exception as e:
            logger.warning(f"JSON解析过程中出现未知异常: {str(e)}")
        
        # 尝试处理{{}}格式的JSON（非标准格式）
        try:
            # 检查是否使用了{{}}格式
            if '{{' in text and '}}' in text:
                logger.info("检测到使用{{}}格式的JSON，尝试转换")
                # 替换双大括号为标准大括号
                converted_text = text.replace('{{', '{').replace('}}', '}')
                try:
                    data = json.loads(converted_text)
                    logger.info("成功解析{{}}格式的JSON")
                    return data
                except:
                    logger.warning("转换{{}}格式后仍然无法解析")
        except Exception as e:
            logger.warning(f"尝试处理{{}}格式时出错: {str(e)}")
        
        # 新增：强化JSON提取 - 移除前缀和后缀文本
        # 有时LLM会在返回的JSON前后添加说明文本
        try:
            # 查找第一个 { 出现的位置
            start_idx = text.find('{')
            # 查找最后一个 } 出现的位置
            end_idx = text.rfind('}')
            
            if start_idx != -1 and end_idx != -1 and start_idx < end_idx:
                json_text = text[start_idx:end_idx+1]
                try:
                    data = json.loads(json_text)
                    logger.info("通过剔除前缀后缀成功解析JSON")
                    return data
                except:
                    logger.warning("剔除前缀后缀后解析JSON仍然失败")
        except Exception as e:
            logger.warning(f"尝试剔除前缀后缀时出错: {str(e)}")
        
        # 新增：处理多行JSON中的错误
        try:
            # 将文本按行分割
            lines = text.split('\n')
            # 收集可能是JSON的行
            json_lines = []
            bracket_count = 0
            in_json = False
            
            for line in lines:
                # 检测JSON开始
                if '{' in line and not in_json:
                    in_json = True
                    bracket_count = line.count('{') - line.count('}')
                    json_lines.append(line)
                    continue
                
                # 如果在JSON块内，继续收集行
                if in_json:
                    json_lines.append(line)
                    bracket_count += line.count('{') - line.count('}')
                    
                    # 检测JSON结束
                    if bracket_count <= 0 and '}' in line:
                        in_json = False
                        break
            
            # 尝试解析收集到的JSON行
            if json_lines:
                json_text = '\n'.join(json_lines)
                # 应用修复
                fixed_json = self._attempt_json_correction(json_text)
                try:
                    data = json.loads(fixed_json)
                    logger.info("通过多行JSON处理成功解析JSON")
                    return data
                except:
                    logger.warning("多行JSON处理后仍然无法解析")
        except Exception as e:
            logger.warning(f"多行JSON处理过程中出错: {str(e)}")
        
  
    
    def _attempt_json_correction(self, text: str) -> str:
        """尝试修复常见的JSON错误
        
        优先使用json_repair库修复JSON格式问题，失败后回退到传统方法
        
        Args:
            text: 要修复的JSON文本
            
        Returns:
            修复后的JSON文本
        """
        logger = self._get_logger()
        logger.info(f"开始修复JSON，长度: {len(text)}")
        
        # 预处理文本
        # 移除BOM标记
        text = text.replace('\ufeff', '')
        
        # 移除所有控制字符，但保留基本的空白字符
        text = re.sub(r'[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]', '', text)
        
        # 移除注释
        text = re.sub(r'//.*?$', '', text, flags=re.MULTILINE)
        text = re.sub(r'/\*.*?\*/', '', text, flags=re.DOTALL)
        
        # 处理代码块格式
        if text.startswith("```json"):
            text = text.replace("```json", "", 1)
            logger.info("移除JSON代码块标记")
        elif text.startswith("```"):
            match = re.match(r"```(\w*)\s*\n", text)
            if match:
                text = text[match.end():]
                logger.info(f"移除{match.group(1)}代码块标记")
                
        # 处理结尾的代码块标记
        if text.endswith("```"):
            text = text.replace("```", "", 1)
            logger.info("移除结尾代码块标记")
            
        # 1. 首先使用json_repair尝试修复
        try:
            # 如果已经是JSON格式，直接返回
            json.loads(text)
            logger.info("输入文本已经是有效的JSON格式")
            return text
        except json.JSONDecodeError as e:
            logger.info(f"检测到JSON格式错误: {str(e)}，尝试使用json_repair修复")
            
        # 尝试使用json_repair库修复
        try:
            repaired_content = json_repair.loads(text)
            fixed_json = json.dumps(repaired_content, ensure_ascii=False)
            logger.info("使用json_repair成功修复JSON")
            return fixed_json
        except Exception as e:
            logger.warning(f"json_repair修复失败: {str(e)}，回退到传统修复方法")
            
        # 2. 传统修复方式 - 如果json_repair失败
        # 转换双大括号{{}}为标准JSON的{}
        text = re.sub(r'{{', '{', text)
        text = re.sub(r'}}', '}', text)
        
        # 修复键名没有使用双引号的问题
        text = re.sub(r'([{,])\s*([a-zA-Z_][a-zA-Z0-9_]*)\s*:', r'\1"\2":', text)
        
        # 修复单引号包围的值
        text = re.sub(r':\s*\'([^\']*?)\'([,}\s])', r':"\1"\2', text)
        
        # 修复末尾多余的逗号
        text = re.sub(r',\s*([}\]])', r'\1', text)
        
        # 修复值没有使用双引号的问题
        text = re.sub(r':\s*([a-zA-Z][a-zA-Z0-9_]*)\s*([,}])', r':"\1"\2', text)
        
        # 修复冒号使用中文全角冒号的问题
        text = re.sub(r'：', ':', text)
        
        # 修复键值对之间分隔符错误（使用分号而非逗号）
        text = re.sub(r'(["}])\s*;', r'\1,', text)
        
        # 修复缺失右括号的问题
        open_braces = text.count('{')
        close_braces = text.count('}')
        if open_braces > close_braces:
            text = text + '}' * (open_braces - close_braces)
            logger.info(f"添加 {open_braces - close_braces} 个缺失的右大括号")
            
        # 修复缺失右方括号的问题
        open_brackets = text.count('[')
        close_brackets = text.count(']')
        if open_brackets > close_brackets:
            pattern = r'\[.*'
            match = re.search(pattern, text, re.DOTALL)
            if match:
                insert_pos = len(text)
                text = text[:insert_pos] + ']' * (open_brackets - close_brackets) + text[insert_pos:]
                logger.info(f"添加 {open_brackets - close_brackets} 个缺失的右中括号")
        
        # 3. 尝试再次使用json_repair进行修复
        try:
            repaired_content = json_repair.loads(text)
            fixed_json = json.dumps(repaired_content, ensure_ascii=False)
            logger.info("经过基本修复后，使用json_repair成功修复JSON")
            return fixed_json
        except Exception as e:
            logger.warning(f"二次尝试修复仍然失败: {str(e)}")

        
        # 所有修复方法都失败，返回最后修改的版本
        logger.info("返回最终修复的JSON文本，虽然可能仍有格式问题")
        return text
    
    def _validate_plan_structure(self, plan_structure: Dict[str, Any]) -> Dict[str, Any]:
        """验证并标准化计划结构
        
        Args:
            plan_structure: 从LLM获取的计划结构
            
        Returns:
            标准化后的计划结构
        """
        # 确保plan_structure是字典类型
        if not isinstance(plan_structure, dict):
            self.logger.error(f"计划结构不是字典类型: {type(plan_structure)}")
            plan_structure = {}
        
        # 确保关键词策略字段存在
        if "keyword_strategy" not in plan_structure:
            plan_structure["keyword_strategy"] = {
                "primary_keyword": "",
                "secondary_keywords": [],
                "lsi_keywords": [],
                "keyword_placement": "自然分布在文章各部分"
            }
            
        # 确保写作计划字段存在
        if "writing_plan" not in plan_structure:
            self.logger.warning("文章计划中缺少writing_plan字段，添加默认值")
            plan_structure["writing_plan"] = {
                "tone": "专业且平易近人",
                "style": self._get_writing_style_for_article_type(plan_structure.get("article_type", "")),
                "target_audience": "对该主题感兴趣的普通读者",
                "key_points_emphasis": "案例与观点结合"
            }
            self.logger.info(f"添加默认writing_plan: {plan_structure['writing_plan']}")
        elif not plan_structure["writing_plan"] or not isinstance(plan_structure["writing_plan"], dict):
            self.logger.warning(f"writing_plan字段无效或为空: {plan_structure['writing_plan']}")
            plan_structure["writing_plan"] = {
                "tone": "专业且平易近人",
                "style": self._get_writing_style_for_article_type(plan_structure.get("article_type", "")),
                "target_audience": "对该主题感兴趣的普通读者",
                "key_points_emphasis": "案例与观点结合"
            }
            self.logger.info(f"替换为默认writing_plan: {plan_structure['writing_plan']}")
            
        # 确保writing_plan是字典类型
        if not isinstance(plan_structure["writing_plan"], dict):
            self.logger.warning(f"writing_plan不是字典类型: {type(plan_structure['writing_plan'])}")
            plan_structure["writing_plan"] = {
                "tone": "专业且平易近人",
                "style": self._get_writing_style_for_article_type(plan_structure.get("article_type", "")),
                "target_audience": "对该主题感兴趣的普通读者",
                "key_points_emphasis": "案例与观点结合"
            }
            self.logger.info(f"将writing_plan转换为字典: {plan_structure['writing_plan']}")
            
        # 确保article_type字段存在
        if "article_type" not in plan_structure or not plan_structure["article_type"]:
            self.logger.warning("文章计划中缺少article_type字段，添加默认值")
            plan_structure["article_type"] = "观点+案例+金句"
        
        # 检查并确保outline字段存在且有效
        if "outline" not in plan_structure:
            self.logger.warning("文章计划中缺少outline字段，添加默认大纲")
            plan_structure["outline"] = self._create_default_outline(plan_structure.get("title", ""), plan_structure.get("article_type", ""))
        elif not isinstance(plan_structure["outline"], list):
            self.logger.warning(f"outline字段不是列表类型: {type(plan_structure['outline'])}")
            plan_structure["outline"] = self._create_default_outline(plan_structure.get("title", ""), plan_structure.get("article_type", ""))
        elif len(plan_structure["outline"]) == 0:
            self.logger.warning("outline字段为空列表，添加默认大纲项")
            plan_structure["outline"] = self._create_default_outline(plan_structure.get("title", ""), plan_structure.get("article_type", ""))
      
        # 如果标准化后的大纲为空，使用默认大纲
        if not plan_structure["outline"]:
            self.logger.warning("标准化后的大纲为空，使用默认大纲")
            plan_structure["outline"] = self._create_default_outline(plan_structure.get("title", ""), plan_structure.get("article_type", ""))
        else:
            pass
        
        self.logger.debug(f"Validated plan structure: {plan_structure}")
        return plan_structure
    
    async def _get_current_time(self) -> Dict[str, Any]:
        """获取当前时间信息
        
        调用时间工具获取当前时间、日期、季节等信息
        
        Returns:
            包含时间信息的字典
        """
        try:
            # 尝试从工具导入直接可调用的函数版本
            from src.services.generator.tools.time_tools import get_current_time
            
            # 调用直接可调用的函数获取当前时间
            params = {"timezone": "Asia/Shanghai", "include_calendar_info": True}
            result = await get_current_time(params, {})
            
            self.logger.info(f"成功获取当前时间信息: {result.get('formatted_date', '')}")
            return result
        except ImportError:
            self.logger.error("无法导入时间工具，使用本地时间")
            # 如果工具不存在，使用系统时间
            now = time.localtime()
            current_time = {
                "timestamp": time.time(),
                "formatted_date": time.strftime("%Y-%m-%d", now),
                "formatted_time": time.strftime("%H:%M:%S", now),
                "day_of_week": ["星期一", "星期二", "星期三", "星期四", "星期五", "星期六", "星期日"][now.tm_wday],
                "month": now.tm_mon,
                "year": now.tm_year,
                "day": now.tm_mday
            }
            
            # 确定季节
            month = now.tm_mon
            if 3 <= month <= 5:
                current_time["season"] = "春季"
            elif 6 <= month <= 8:
                current_time["season"] = "夏季"
            elif 9 <= month <= 11:
                current_time["season"] = "秋季"
            else:
                current_time["season"] = "冬季"
                
            return current_time
    