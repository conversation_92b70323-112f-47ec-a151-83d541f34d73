from typing import Dict, List, Any, Optional
import logging
import json
import re
import traceback
import time
import unicodedata
import os
import hashlib

from src.services.generator.state import ArticleState, ArticleStatus
from src.services.generator.agents.base import AgentOutput, BaseAgent
from src.services.generator.text_generator import LLMAdapter, LLMRole
from src.services.generator.prompts import (
    finalizing_edit_prompt
)
import json_repair  # 添加json_repair库导入

logger = logging.getLogger("finalizing_agent")

class FinalizingAgent(BaseAgent):
    """最终处理代理，负责整合文章内容并准备最终输出"""
    
    def __init__(self):
        """初始化最终处理代理"""
        super().__init__(
            name="最终处理代理",
            description="整合优化后的文章内容并准备最终输出"
        )
        self.logger = logger
    
    async def run(self, state: ArticleState) -> AgentOutput:
        """执行最终化处理，包括编辑和格式化等
        
        Args:
            state: 当前的文章状态
            
        Returns:
            代理输出对象，包含是否成功以及更新后的状态
        """
        self.logger.info("正在运行FinalizingAgent...")
        
        try:
            # 复制状态
            state = state.copy(deep=True)
            
            # 验证输入
            if not self._validate_input(state):
                return AgentOutput(
                    success=False, 
                    state=state, 
                    error="无效的输入参数"
                )
            
            # 在编辑前应用SEO优化
            try:
                from src.services.generator.tools.seo_tools import SEOTool
                seo_tool = SEOTool()
                
                self.logger.info("在润色前执行SEO优化")
                seo_result = await seo_tool.run(state)
                
                if seo_result.get("success", False):
                    self.logger.info(f"SEO优化成功完成，评分: {seo_result.get('seo_metadata', {}).get('seo_score', 0):.1f}/10")
                    # 将SEO优化结果作为下一步的输入
                    state.article_content = seo_result.get("optimized_content", state.article_content)
                    state.seo_metadata = seo_result.get("seo_metadata", {})
                else:
                    error_msg = seo_result.get("error", "未知错误")
                    self.logger.warning(f"SEO优化失败: {error_msg}")
            except Exception as seo_err:
                self.logger.error(f"SEO优化过程出错: {str(seo_err)}")
            
            # 编辑和优化文章
            try:
                self.logger.info("开始编辑和优化文章内容...")
                formatted_article = self._format_article_for_editing(state)
                edited_content = await self._edit_and_finalize_article(state, formatted_article)
                if edited_content:
                    state.edited_content = edited_content
                    self.logger.info("文章编辑和优化完成")
                else:
                    self.logger.warning("文章编辑未返回有效内容，将使用原始内容")
            except Exception as edit_err:
                self.logger.error(f"编辑和优化文章过程出错: {str(edit_err)}")
            print(state.edited_content)
            # 选择最佳内容版本
            state = await self._select_best_content_version(state)
            
        
            # 格式化内容
            state = await self._format_content(state)
            
            # 为最终内容添加元数据
            state = await self._generate_metadata(state)
            
            # 确保内容有效
            state = self._ensure_valid_content(state)
            
            # 设置最终结果
            state.update_status(ArticleStatus.COMPLETED)
            
            return AgentOutput(success=True, state=state)
        except Exception as e:
            self.logger.error(f"FinalizingAgent执行失败: {str(e)}")
            state.add_error("finalizing", str(e))
            return AgentOutput(success=False, state=state, error=str(e))
    
    async def _edit_and_finalize_article(self, state: ArticleState, formatted_article: str) -> Dict[str, Any]:
        """
        一次性编辑和优化文章内容
        
        Args:
            state: 文章状态
            formatted_article: 格式化的文章内容
            
        Returns:
            Dict[str, Any]: 编辑后的内容
        """
        try:
            # 获取LLM适配器
            llm_adapter = self._get_text_generator()
            
            # 准备参数
            topic = state.topic if hasattr(state, "topic") else "未知主题"
            keywords = state.keywords if hasattr(state, "keywords") else []
            
            # 获取文章类型（如果有）
            article_type = None
            if hasattr(state, "article_plan") and isinstance(state.article_plan, dict):
                article_type = state.article_plan.get("article_type")
                self.logger.info(f"检测到文章类型: {article_type}")

            # 检查是否是流畅型文章类型
            is_narrative_style = article_type in ["故事+感触+干货", "观点+案例+金句", "对比分析"]
            self.logger.info(f"文章类型: {article_type}, 使用流畅型结构: {is_narrative_style}")

            # 准备文章内容字典 - 确保不丢失任何内容
            content = {
                "title": state.article_content.get("title", topic),
                "introduction": state.article_content.get("introduction", ""),
                "sections": state.article_content.get("sections", []),
                "conclusion": state.article_content.get("conclusion", ""),
            }
            
            # 根据文章类型调整内容格式，流畅型结构（故事型或观点型）
            if article_type in ["故事+感触+干货", "观点+案例+金句", "对比分析"] :
                self.logger.info(f"检测到流畅型文章类型: {article_type}，调整内容格式")
                # 流畅型的文章可能没有sections而是直接使用content
                content = {
                    "title": state.article_content.get("title", topic),
                    "content": state.article_content.get("content", "")
                }
            
            # 确保状态中包含FAQ相关信息（用于指导生成）
            need_faq = False
            if article_type == "分析/教程型":
                need_faq = True
                self.logger.info("分析/教程型文章，建议包含FAQ")
            elif hasattr(state, "faqs") and state.faqs:
                need_faq = True
                self.logger.info(f"状态中已有 {len(state.faqs)} 个FAQ")
            else:
                self.logger.info("当前文章不需要强制生成FAQ")
                
            # 添加FAQ指导信息到内容中
            content["need_faq"] = need_faq
            
            # 将内容字典格式化为字符串
            formatted_article = self._format_article_for_editing(state)
            
            # 获取当前时间信息
            current_time = None
            if hasattr(state, "current_time") and state.current_time:
                current_time = state.current_time
                self.logger.info(f"使用状态中的时间信息: {current_time}")
            else:
                # 尝试构建基础的时间信息
                from datetime import datetime
                now = datetime.now()
                current_time = {
                    "year": now.year,
                    "month": now.month,
                    "day": now.day,
                    "season": self._get_season(now.month)
                }
                self.logger.info(f"使用系统时间: {current_time}")
            
            # 使用从prompts.py导入的编辑提示词函数，使用正确的参数名
            prompt = finalizing_edit_prompt(
                draft_content=formatted_article,
                article_type=article_type,
                current_time=current_time
            )
            
            # 调用LLM
            self.logger.info("调用LLM进行最终内容优化")
            response = await llm_adapter.generate(
                prompt=prompt,
                role=LLMRole.EDITOR,
                max_tokens=8000
            )
            
            # 解析响应
            if not response:
                self.logger.warning("LLM返回空响应，使用原始内容")
                return state.article_content
            
            # 尝试JSON解析
            edited_content = None
            
            # 0. 先记录更多日志以便调试
            self.logger.info(f"清理后的响应(前100字符): {response[:100] if response else ''}")
            
            # 1. 尝试直接解析为JSON
            try:
                # 使用健壮的JSON解析方法
                edited_content = self._robust_json_parse(response)
                self.logger.info("成功直接解析LLM响应为JSON格式")
            except Exception as e:
                self.logger.info(f"直接JSON解析失败: {str(e)}, 尝试进一步提取")
                
                # 2. 尝试从代码块中提取JSON
                json_block_match = re.search(r'```(?:json)?\s*([\s\S]*?)\s*```', response, re.DOTALL)
                if json_block_match:
                    json_text = json_block_match.group(1).strip()
                    try:
                        edited_content = self._robust_json_parse(json_text)
                        self.logger.info("成功从代码块中提取并解析JSON")
                    except Exception as block_error:
                        self.logger.info(f"从代码块解析JSON失败: {str(block_error)}, 尝试修复JSON格式")
                        
                        # 尝试修复JSON
                        try:
                            fixed_json = self._fix_json(json_text)
                            edited_content = json.loads(fixed_json)
                            self.logger.info("修复JSON格式后成功解析")
                        except (json.JSONDecodeError, Exception) as fix_error:
                            self.logger.warning(f"JSON修复失败: {str(fix_error)}, 尝试提取大括号内容")
                            
                            # 尝试从代码块文本中提取所有大括号内容
                            brackets_in_block = re.search(r'(\{[\s\S]*\})', json_text, re.DOTALL)
                            if brackets_in_block:
                                try:
                                    bracket_content = brackets_in_block.group(0).strip()
                                    fixed_bracket_content = self._fix_json(bracket_content)
                                    edited_content = json.loads(fixed_bracket_content)
                                    self.logger.info("从代码块内的大括号内容解析JSON成功")
                                except json.JSONDecodeError:
                                    # 回退到全文搜索大括号
                                    self.logger.info("尝试从完整响应中提取大括号内容")
                                    bracket_match = re.search(r'(\{[\s\S]*\})', response, re.DOTALL)
                                    if bracket_match:
                                        try:
                                            bracket_content = bracket_match.group(0).strip()
                                            fixed_bracket_content = self._fix_json(bracket_content)
                                            edited_content = json.loads(fixed_bracket_content)
                                            self.logger.info("从响应中的大括号内容解析JSON成功")
                                        except json.JSONDecodeError as bracket_error:
                                            self.logger.warning(f"大括号内容解析失败: {str(bracket_error)}, 尝试解析为结构化内容")
                                            edited_content = self._parse_edited_article(response)
                                        else:
                                            self.logger.warning("无法找到JSON大括号，尝试解析为结构化内容")
                                            edited_content = self._parse_edited_article(response)
                            else:
                                # 回退到全文搜索大括号
                                bracket_match = re.search(r'(\{[\s\S]*\})', response, re.DOTALL)
                                if bracket_match:
                                    try:
                                        bracket_content = bracket_match.group(0).strip()
                                        fixed_bracket_content = self._fix_json(bracket_content)
                                        edited_content = json.loads(fixed_bracket_content)
                                        self.logger.info("从响应中的大括号内容解析JSON成功")
                                    except json.JSONDecodeError as bracket_error:
                                        self.logger.warning(f"大括号内容解析失败: {str(bracket_error)}, 尝试解析为结构化内容")
                                        edited_content = self._parse_edited_article(response)
                                    else:
                                        self.logger.warning("无法找到JSON大括号，尝试解析为结构化内容")
                                        edited_content = self._parse_edited_article(response)
                else:
                    # 4. 提取所有大括号 { ... } 内容
                    self.logger.info("未找到代码块，尝试提取大括号内容")
                    all_brackets_match = re.search(r'(\{[\s\S]*\})', response, re.DOTALL)
                    if all_brackets_match:
                        try:
                            bracket_content = all_brackets_match.group(0).strip()
                            # 使用健壮的JSON解析方法替代旧的解析流程
                            edited_content = self._robust_json_parse(bracket_content)
                            self.logger.info("从文本提取大括号内容并解析JSON成功")
                        except Exception as bracket_error:
                            self.logger.warning(f"大括号内容解析失败: {str(bracket_error)}, 尝试解析为结构化内容")
                            edited_content = self._parse_edited_article(response)
                    else:
                        self.logger.warning("未找到任何JSON格式内容，尝试解析为结构化内容")
                        edited_content = self._parse_edited_article(response)
            
            # 5. 最后的回退：如果所有JSON解析方法都失败，尝试将原始内容转换为简单结构
            if not edited_content or not isinstance(edited_content, dict):
                self.logger.warning("所有JSON解析方法失败，尝试直接提取标题和内容")
                
                # 强力解析方法 - 即使所有方法失败也能提取内容
                try:
                    # 尝试提取标题 (可能在Markdown或JSON中)
                    title = topic  # 默认使用topic
                    
                    # 查找title字段
                    title_match = re.search(r'"title"\s*:\s*"([^"]+)"', response)
                    if title_match:
                        title = title_match.group(1).strip()
                    else:
                        # 尝试从Markdown标题中提取
                        md_title_match = re.search(r'#\s+(.+?)(?:\n|$)', response)
                        if md_title_match:
                            title = md_title_match.group(1).strip()
                    
                    # 提取内容部分
                    content_text = ""
                    
                    # 先尝试提取content字段
                    content_match = re.search(r'"content"\s*:\s*"([\s\S]+?)(?:"\s*,|\"\s*})', response)
                    if content_match:
                        content_text = content_match.group(1).strip()
                        # 修复内容中的转义字符，保留换行符格式
                        content_text = content_text.replace('\\"', '"').replace('\\n', '\n')
                    else:
                        # 尝试另一种模式匹配不带结束引号的情况
                        content_match = re.search(r'"content"\s*:\s*"([\s\S]+?)(?="\s*,|\"\s*}|$)', response)
                        if content_match:
                            content_text = content_match.group(1).strip()
                            # 修复内容中的转义字符，保留换行符格式
                            content_text = content_text.replace('\\"', '"').replace('\\n', '\n')
                        else:
                            # 尝试从Markdown结构提取内容
                            content_text = re.sub(r'^#.+?\n', '', response, 1, re.MULTILINE).strip()
                            
                            # 删除可能的JSON语法
                            content_text = re.sub(r'[\{\}"\\]', '', content_text)
                    
                    # 创建简单的文章结构
                    edited_content = {
                        "title": title,
                        "content": content_text
                    }
                    
                    self.logger.info(f"使用强力解析提取内容成功：标题长度={len(title)}，内容长度={len(content_text)}")
                except Exception as extract_err:
                    self.logger.error(f"强力解析失败：{str(extract_err)}")
                    # 最后的无奈之举 - 简单返回原始文章
                    return state.article_content
            
            # 验证内容结构并确保不丢失原始内容
            edited_content = self._validate_content_structure(edited_content, content, article_type)
            
            # 如果LLM生成了FAQs，添加到文章内容中
            if "faqs" in edited_content and isinstance(edited_content["faqs"], list) and edited_content["faqs"]:
                state.faqs = edited_content["faqs"]
                self.logger.info(f"LLM生成了{len(edited_content['faqs'])}个FAQ，已添加到状态中")
            
            # 确保流畅型文章只包含title和content字段
            if is_narrative_style or "content" in edited_content:
                self.logger.info("确保流畅型文章只包含title和content字段")
                title = edited_content.get("title", "")
                
                # 如果有content字段，使用它
                if "content" in edited_content:
                    content_text = edited_content.get("content", "")
                    
                    # 检查content是否为JSON字符串，如果是则提取纯文本
                    if isinstance(content_text, str) and content_text.strip().startswith('{') and content_text.strip().endswith('}'):
                        try:
                            # 使用全局json模块，移除局部导入
                            json_content = json.loads(content_text)
                            if isinstance(json_content, dict) and "content" in json_content:
                                content_text = json_content["content"]
                                self.logger.info("从嵌套JSON中提取了纯文本内容")
                        except Exception as e:
                            self.logger.warning(f"尝试解析嵌套JSON失败: {str(e)}")
                # 否则，尝试从其他字段构建content
                else:
                    content_parts = []
                    if "introduction" in edited_content:
                        content_parts.append(edited_content.get("introduction", ""))
                    
                    if "sections" in edited_content and isinstance(edited_content.get("sections"), list):
                        for section in edited_content.get("sections", []):
                            if isinstance(section, dict):
                                if "title" in section:
                                    content_parts.append(f"## {section.get('title')}")
                                if "content" in section:
                                    content_parts.append(section.get("content", ""))
                    
                    if "conclusion" in edited_content:
                        content_parts.append(edited_content.get("conclusion", ""))
                    
                    content_text = "\n\n".join(content_parts)
                
                # 创建标准的流畅型文章结构
                final_content = {
                    "title": title,
                    "content": content_text
                }
                
                # 如果有FAQs，保留它们
                if "faqs" in edited_content and isinstance(edited_content.get("faqs"), list):
                    final_content["faqs"] = edited_content.get("faqs", [])
                
                self.logger.info("将最终内容转换为标准流畅型结构")
                return final_content
            
            return edited_content
            
        except Exception as e:
            self.logger.error(f"编辑和优化文章时出错: {str(e)}")
            traceback.print_exc()
            return state.article_content
    
    def _format_article_for_editing(self, state: ArticleState) -> str:
        """
        格式化文章以进行编辑
        
        Args:
            state: 文章状态
            
        Returns:
            str: 格式化的文章内容
        """
        content = state.article_content
        if not content:
            return ""
            
        formatted_text = ""
        
        # 添加标题
        if "title" in content:
            formatted_text += f"# {content['title']}\n\n"
        
        if "content" in content:
            formatted_text += f"{content['content']}\n\n"
            return formatted_text
        # 添加引言
        if "introduction" in content:
            formatted_text += f"## 引言\n{content['introduction']}\n\n"
            
        # 添加章节
        if "sections" in content and isinstance(content["sections"], list):
            for i, section in enumerate(content["sections"]):
                if isinstance(section, dict) and "title" in section and "content" in section:
                    formatted_text += f"## {section['title']}\n{section['content']}\n\n"
                    
                    # 添加子章节
                    if "subsections" in section and isinstance(section["subsections"], list):
                        for subsection in section["subsections"]:
                            if isinstance(subsection, dict):
                              
                                if "title" in subsection and "content" in subsection:
                                    # 文本子章节
                                    formatted_text += f"### {subsection['title']}\n{subsection['content']}\n\n"
        
        # 添加结论
        if "conclusion" in content:
            formatted_text += f"## 结论\n{content['conclusion']}\n\n"
            
        return formatted_text
    
    def _parse_edited_article(self, text: str) -> Dict[str, Any]:
        """
        解析编辑后的文章，支持多种格式，优先使用代码块和JSON格式
        
        Args:
            text: 文章文本
            
        Returns:
            Dict[str, Any]: 解析后的内容
        """
        try:
            # 更详细的日志，记录尝试分析的文本前100个字符
            self.logger.info(f"开始解析文本内容: {text[:100]}...")
            
            # 1. 首先检查是否有完整JSON代码块
            json_block_match = re.search(r'```(?:json|ts)?\s*([\s\S]*?)```', text)
            if json_block_match:
                json_content = json_block_match.group(1).strip()
                try:
                    # 使用json_repair尝试解析代码块内容
                    parsed_content = json_repair.loads(json_content)
                    if isinstance(parsed_content, dict):
                        self.logger.info("成功解析JSON代码块")
                        return self._validate_content_structure(parsed_content)
                except Exception as e:
                    self.logger.info(f"JSON代码块解析失败: {str(e)}")
            
            # 2. 尝试提取任何JSON对象
            json_match = re.search(r'({[\s\S]*})', text)
            if json_match:
                content_json = json_match.group(1)
                try:
                    # 使用_robust_json_parse处理任何JSON内容
                    parsed_content = self._robust_json_parse(content_json)
                    if isinstance(parsed_content, dict):
                        self.logger.info("成功解析嵌入的JSON对象")
                        return self._validate_content_structure(parsed_content)
                except Exception as e:
                    self.logger.info(f"JSON对象解析失败: {str(e)}")
        
            # 3. 无法解析JSON，尝试手动提取结构
            self.logger.info("开始结构化提取文章内容")
            content = {}
            
            # 提取标题 (尝试多种模式)
            title = None
            # 3.1 尝试Markdown标题
            title_match = re.search(r'#\s+(.+?)(?:\n|$)', text)
            if title_match:
                title = title_match.group(1).strip()
                self.logger.info(f"从Markdown提取标题: {title}")
            # 3.2 尝试提取JSON样式标题
            if not title:
                title_json_match = re.search(r'"title"\s*:\s*"([^"]+)"', text)
                if title_json_match:
                    title = title_json_match.group(1).strip()
                    self.logger.info(f"从JSON格式提取标题: {title}")
            
            content["title"] = title if title else "未命名文章"
                
            # 检查是否为流畅型内容 (只有标题和正文，没有明显的章节)
            has_sections = re.search(r'##\s+(?!引言|结论)(.+?)(?:\n|$)', text) is not None
            
            if not has_sections:
                # 流畅型内容，提取整个正文
                self.logger.info("未检测到章节标题，按流畅型内容处理")
                content_body = ""
                
                # 3.3.1 尝试提取JSON style content
                content_match = re.search(r'"content"\s*:\s*"([\s\S]+?)(?:"\s*[,}]|$)', text)
                if content_match:
                    content_body = content_match.group(1).strip()
                    # 修复转义字符
                    content_body = content_body.replace('\\"', '"').replace('\\n', '\n')
                    self.logger.info(f"从JSON格式提取内容，长度: {len(content_body)}")
                else:
                    # 3.3.2 尝试提取Markdown内容 (移除标题后的所有内容)
                    if title_match:
                        title_pos = text.find(title_match.group(0)) + len(title_match.group(0))
                        content_body = text[title_pos:].strip()
                        self.logger.info(f"从Markdown提取内容，长度: {len(content_body)}")
                    else:
                        # 直接使用整个文本
                        content_body = text
                        self.logger.info(f"使用整个文本作为内容，长度: {len(content_body)}")
                
                content["content"] = content_body
                return content
            
            # 章节型内容，提取引言、章节和结论
            self.logger.info("检测到章节标题，按章节型内容处理")
            
            # 提取引言
            intro_match = re.search(r'##\s+引言\n([\s\S]*?)(?=##|$)', text)
            if intro_match:
                content["introduction"] = intro_match.group(1).strip()
                self.logger.info(f"提取引言，长度: {len(content['introduction'])}")
            else:
                content["introduction"] = ""
                
            # 提取章节
            sections = []
            section_matches = re.finditer(r'##\s+(?!引言|结论)(.*)\n([\s\S]*?)(?=##|$)', text)
            
            for match in section_matches:
                section_title = match.group(1).strip()
                section_content = match.group(2).strip()
                
                # 检查是否有子章节
                subsections = []
                subsection_matches = re.finditer(r'###\s+(.*)\n([\s\S]*?)(?=###|##|$)', section_content)
                
                for submatch in subsection_matches:
                    subsection_title = submatch.group(1).strip()
                    subsection_content = submatch.group(2).strip()
                    
                    subsections.append({
                        "title": subsection_title,
                        "content": subsection_content
                    })
                
                # 如果有子章节，从主章节内容中删除子章节部分
                if subsections:
                    for subsection in subsections:
                        section_content = section_content.replace(f"### {subsection['title']}\n{subsection['content']}", "").strip()
                
                sections.append({
                    "title": section_title,
                    "content": section_content,
                    "subsections": subsections
                })
                
            content["sections"] = sections
            self.logger.info(f"提取{len(sections)}个章节")
            
            # 提取结论
            conclusion_match = re.search(r'##\s+结论\n([\s\S]*?)(?=##|$)', text)
            if conclusion_match:
                content["conclusion"] = conclusion_match.group(1).strip()
                self.logger.info(f"提取结论，长度: {len(content['conclusion'])}")
            else:
                content["conclusion"] = ""
            
            return content
            
        except Exception as e:
            self.logger.error(f"解析编辑后的文章失败: {str(e)}")
            self.logger.error(traceback.format_exc())
            
            # 应急解析 - 提取任何可能有用的内容
            emergency_content = {
                "title": "解析失败的文章",
                "content": text[:5000] if len(text) > 5000 else text  # 限制内容长度
            }
            
            return emergency_content
    
    def _recover_content(self, state: ArticleState) -> None:
        """恢复内容，如果处理失败
        
        Args:
            state: 文章状态
        """
        logger.info("尝试恢复内容")
        
        # 检查是否已有final_content
        if hasattr(state, 'final_content') and state.final_content:
            logger.info("已有final_content，无需恢复")
            return
            
        # 尝试从article_content恢复
        if hasattr(state, 'article_content') and state.article_content:
            logger.info("从article_content恢复内容")
            state.final_content = state.article_content
        # 尝试从seo_optimized_content恢复
        elif hasattr(state, 'seo_optimized_content') and state.seo_optimized_content:
            logger.info("从seo_optimized_content恢复内容")
            state.final_content = state.seo_optimized_content
        else:
            logger.error("没有可用于恢复的内容")
            # 创建空的最终内容
            state.final_content = {
                "title": state.topic or "未命名文章",
                "introduction": "文章生成过程中出现错误，无法提供完整内容。",
                "sections": [],
                "conclusion": ""
            }
                
    def _validate_content_structure(self, content: Dict[str, Any], original_content: Dict[str, Any] = None, article_type: str = None) -> Dict[str, Any]:
        """
        验证内容结构是否完整，如果缺少必要字段，则从原始内容中补充
        
        Args:
            content: 要验证的内容
            original_content: 原始内容，用于回退
            article_type: 文章类型
            
        Returns:
            验证后的内容
        """
        # 确保输入是字典
        if not isinstance(content, dict):
            self.logger.warning(f"内容不是字典，而是 {type(content)}，无法验证")
            if isinstance(original_content, dict):
                return original_content
            else:
                return {"title": "内容格式错误", "content": str(content)}
        
        # 记录正在验证的内容结构
        self.logger.info(f"内容包含以下字段: {', '.join(content.keys())}")
        
        # 判断是否为流畅型文章
        is_narrative_style = False
        
        # 明确定义流畅型文章类型列表
        narrative_types = [
            "故事+感触+干货", "观点+案例+金句", "对比分析",
            "blog", "narrative", "story", "article", "essay", "opinión", 
            "opinion", "editorial", "review", "技术博客", "观点", "评论", 
            "叙事", "故事", "文章", "随笔", "散文", "故事性", "记叙文", 
            "小说", "人物传记"
        ]
        
        if article_type:
            # 检查完整匹配
            if article_type in narrative_types:
                is_narrative_style = True
                self.logger.info(f"基于文章类型 '{article_type}' 验证，使用流畅型结构")
            # 检查包含关系
            else:
                for narrative_type in narrative_types:
                    if narrative_type in article_type:
                        is_narrative_style = True
                        self.logger.info(f"文章类型 '{article_type}' 包含流畅型结构类型 '{narrative_type}'")
                        break
        else:
            # 尝试从内容结构判断类型
            if "content" in content and isinstance(content.get("content"), str) and content.get("content"):
                is_narrative_style = True
                self.logger.info("基于内容结构判断为流畅型文章")
        
        # 根据文章类型验证结构
        if not is_narrative_style:
            # 章节型结构验证
            self.logger.info("验证章节型结构内容")
            
            # 检查必需字段
            for field in ["title", "introduction", "sections", "conclusion"]:
                if field not in content or not content[field]:
                    self.logger.warning(f"内容缺少必需字段: {field}，使用原始内容")
                    
                    # 尝试从原始内容中补充
                    if original_content and field in original_content and original_content[field]:
                        content[field] = original_content[field]
                        self.logger.info(f"从原始内容中补充 {field} 字段")
                    else:
                        # 设置默认值
                        if field == "title":
                            content[field] = "未命名文章"
                        elif field == "introduction":
                            content[field] = ""
                        elif field == "sections":
                            content[field] = []
                        elif field == "conclusion":
                            content[field] = ""
            
            # 检查sections字段是否是列表
            if not isinstance(content["sections"], list):
                self.logger.warning("sections字段不是列表，设置为空列表")
                content["sections"] = []
            
            # 确保章节有效
            for i, section in enumerate(content["sections"]):
                if not isinstance(section, dict):
                    self.logger.warning(f"章节 {i+1} 不是字典，替换为默认章节")
                    content["sections"][i] = {"title": f"章节 {i+1}", "content": ""}
                elif "title" not in section or not section["title"]:
                    self.logger.warning(f"章节 {i+1} 缺少标题，设置默认标题")
                    section["title"] = f"章节 {i+1}"
                elif "content" not in section or not section["content"]:
                    self.logger.warning(f"章节 '{section['title']}' 缺少内容，设置为空")
                    section["content"] = ""
                    
            # 重要：检查是否有content字段，如果有则移除，避免混淆
            if "content" in content and content["sections"]:
                self.logger.warning("章节型文章包含content字段，由于sections已存在，移除content字段")
                del content["content"]
                
        else:
            # 流畅型结构验证
            self.logger.info("验证流畅型结构内容")
            
            # 检查必需字段
            if "title" not in content or not content["title"]:
                self.logger.warning("内容缺少必需字段: title，设置默认标题")
                content["title"] = "未命名文章"
                
            # 确保有content字段
            if "content" not in content or not content["content"]:
                self.logger.warning("流畅型文章缺少必需字段: content")
                
                # 尝试从sections合成content（如果有sections）
                if "sections" in content and isinstance(content["sections"], list) and content["sections"]:
                    self.logger.info("尝试从sections合成content")
                    
                    combined_content = []
                    if "introduction" in content and content["introduction"]:
                        combined_content.append(content["introduction"])
                        
                    for section in content["sections"]:
                        if isinstance(section, dict):
                            if "title" in section and section["title"]:
                                combined_content.append(f"## {section['title']}")
                            if "content" in section and section["content"]:
                                combined_content.append(section["content"])
                                
                    if "conclusion" in content and content["conclusion"]:
                        combined_content.append(f"## 结论\n{content['conclusion']}")
                        
                    if combined_content:
                        content["content"] = "\n\n".join(combined_content)
                        self.logger.info(f"成功从sections合成content，长度: {len(content['content'])}")
                else:
                    # 尝试从原始内容中补充
                    if original_content and "content" in original_content and original_content["content"]:
                        content["content"] = original_content["content"]
                        self.logger.info("从原始内容中补充content字段")
                    else:
                        # 设置默认值
                        content["content"] = ""
                        self.logger.warning("无法构建content内容，设置为空")
        
        self.logger.info(f"内容结构验证完成，最终字段: {', '.join(content.keys())}")
        return content
    
    def _validate_final_state(self, state: ArticleState) -> None:
        """验证最终状态
        
        Args:
            state: 文章状态
        """
        # 检查必要的属性
        required_attrs = ["final_content", "formatted_content", "final_metadata", "meta_description"]
        for attr in required_attrs:
            if not hasattr(state, attr):
                logger.warning(f"状态缺少必要属性: {attr}")
                if attr == "final_content":
                    self._recover_content(state)
                elif attr == "formatted_content":
                    state.formatted_content = state.final_content
                elif attr == "final_metadata":
                    state.final_metadata = {"title": state.topic if hasattr(state, "topic") else "无标题"}
                elif attr == "meta_description":
                    state.meta_description = state.topic if hasattr(state, "topic") and state.topic else "文章"
                    
        # 确保FAQ存在（现在它是可选的）
        if not hasattr(state, "faqs"):
            state.faqs = []
            
    def _get_text_generator(self):
        """获取文本生成器实例"""
        # 导入LLMAdapter
        from src.services.generator.text_generator.llm_adapter import LLMAdapter
        from src.config.api_config import get_deepseek_api_key, get_qianwen_api_key
        
        # 创建并返回LLMAdapter实例
        return LLMAdapter(default_provider="deepseek_reasoner")
        
    async def _select_best_content_version(self, state: ArticleState) -> ArticleState:
        """选择最佳内容版本
        
        优先顺序：edited_content，article_content
        
        Args:
            state: 文章状态
            
        Returns:
            更新后的文章状态
        """
        self.logger.info("选择最佳内容版本...")
        
        # 检查是否有编辑后的内容
        has_edited_content = hasattr(state, "edited_content") and bool(state.edited_content)
        
        # 检查是否有原始文章内容
        has_article_content = hasattr(state, "article_content") and bool(state.article_content)
        
        # 根据优先级选择最佳版本
        if has_edited_content:
            self.logger.info("使用编辑内容作为最终内容")
            state.final_content = state.edited_content
        elif has_article_content:
            self.logger.info("使用原始文章内容作为最终内容")
            state.final_content = state.article_content
        else:
            err_msg = "无法选择最佳内容版本：没有可用的内容"
            self.logger.error(err_msg)
            raise ValueError(err_msg)
        
        return state
      
    async def _format_content(self, state: ArticleState) -> ArticleState:
        """格式化内容为所需格式
        
        Args:
            state: 文章状态
            
        Returns:
            更新后的文章状态
        """
        if not isinstance(state.final_content, dict):
            return state
            
        # 保存原始内容的副本，确保不会丢失数据
        original_content = state.final_content.copy()
        
        # 获取文章类型（如果有）
        article_type = None
        if hasattr(state, "article_plan") and isinstance(state.article_plan, dict):
            article_type = state.article_plan.get("article_type")
        
        # 检查是否是流畅型文章类型
        is_narrative_style = False
        
        # 明确定义流畅型文章类型列表
        narrative_types = [
            "故事+感触+干货", "观点+案例+金句", "对比分析",
            "blog", "narrative", "story", "article", "essay", "opinión", 
            "opinion", "editorial", "review", "技术博客", "观点", "评论", 
            "叙事", "故事", "文章", "随笔", "散文", "故事性", "记叙文", 
            "小说", "人物传记"
        ]
        
        if article_type:
            # 检查完整匹配
            if article_type in narrative_types:
                is_narrative_style = True
                self.logger.info(f"格式化流畅型文章内容，类型: {article_type}")
            # 检查包含关系
            else:
                for narrative_type in narrative_types:
                    if narrative_type in article_type:
                        is_narrative_style = True
                        self.logger.info(f"文章类型 '{article_type}' 包含流畅型结构类型 '{narrative_type}'")
                        break
        
        # 如果没有定义文章类型，尝试从内容判断
        if not article_type and "content" in original_content and original_content["content"]:
            is_narrative_style = True
            self.logger.info("从内容结构判断为流畅型风格")
        
        # 创建格式化后的内容，包含灵活的文章结构
        formatted = {
            "title": original_content.get("title", "")
        }
        
        # 处理流畅型和章节型两种不同的文章结构
        if is_narrative_style:
            # 流畅型结构：保留content字段
            self.logger.info(f"格式化流畅型文章内容")
            formatted["content"] = original_content.get("content", "")
            
            # 可能仍有introduction和conclusion，保留它们
            if "introduction" in original_content:
                formatted["introduction"] = original_content.get("introduction", "")
            if "conclusion" in original_content:
                formatted["conclusion"] = original_content.get("conclusion", "")
            
        else:
            # 章节型结构：使用标准字段
            self.logger.info("格式化章节型文章内容")
            
            # 可选字段：引言
            if "introduction" in original_content:
                formatted["introduction"] = original_content.get("introduction", "")
            
            # 处理章节
            formatted["sections"] = []
            for section in original_content.get("sections", []):
                if isinstance(section, dict):
                    formatted_section = {
                        "title": section.get("title", ""),
                        "content": section.get("content", "")
                    }
                    
                    # 处理子章节（如果有）
                    if "subsections" in section and isinstance(section.get("subsections", []), list):
                        formatted_section["subsections"] = []
                        for subsection in section.get("subsections", []):
                            if isinstance(subsection, dict):
                                formatted_section["subsections"].append(subsection)
                    
                    formatted["sections"].append(formatted_section)
            
            # 可选字段：结论
            if "conclusion" in original_content:
                formatted["conclusion"] = original_content.get("conclusion", "")
        
        # 处理FAQs（如果有）
        if "faqs" in original_content and isinstance(original_content.get("faqs", []), list):
            formatted["faqs"] = original_content.get("faqs", [])
            
        # 将原始内容的所有其他字段复制到formatted中（除了已处理过的字段）
        excluded_fields = ["title", "introduction", "sections", "conclusion", "faqs", "content"]
        for key, value in original_content.items():
            if key not in excluded_fields:
                formatted[key] = value
        
        # 保存最终内容
        state.final_content = formatted
        
        # 记录内容尺寸，便于调试
        content_size = self._calculate_content_size(formatted)
        if is_narrative_style and "content" in formatted:
            self.logger.info(f"格式化后的流畅型内容大小: 标题={len(formatted.get('title', ''))}, "
                          f"正文内容={len(formatted.get('content', ''))}, "
                          f"FAQ数={len(formatted.get('faqs', []))}, "
                          f"估计总长度={content_size}")
        else:
            self.logger.info(f"格式化后的章节型内容大小: 标题={len(formatted.get('title', ''))}, "
                          f"引言={len(formatted.get('introduction', ''))}, "
                          f"章节数={len(formatted.get('sections', []))}, "
                          f"结论={len(formatted.get('conclusion', ''))}, "
                          f"FAQ数={len(formatted.get('faqs', []))}, "
                          f"估计总长度={content_size}")
        
        return state
        
    def _calculate_content_size(self, content: Dict[str, Any]) -> int:
        """计算内容总字符数
        
        Args:
            content: 内容字典
            
        Returns:
            总字符数
        """
        total_chars = 0
        
        if isinstance(content, dict):
            # 计算标题字符数
            total_chars += len(content.get("title", ""))
            
            # 流畅型结构：直接计算content字段
            if "content" in content:
                total_chars += len(content.get("content", ""))
            
            # 章节型结构：计算introduction, sections, conclusion
            # 计算引言字符数（如果有）
            total_chars += len(content.get("introduction", ""))
            
            # 计算章节字符数
            for section in content.get("sections", []):
                if isinstance(section, dict):
                    # 计算章节标题
                    total_chars += len(section.get("title", ""))
                    
                    # 计算章节内容
                    total_chars += len(section.get("content", ""))
                    
                    # 计算子章节内容
                    for subsection in section.get("subsections", []):
                        if isinstance(subsection, dict):
                            total_chars += len(subsection.get("title", ""))
                            total_chars += len(subsection.get("content", ""))
            
            # 计算结论字符数（如果有）
            total_chars += len(content.get("conclusion", ""))
            
            # 计算FAQ字符数（如果有）
            for faq in content.get("faqs", []):
                if isinstance(faq, dict):
                    total_chars += len(faq.get("question", ""))
                    total_chars += len(faq.get("answer", ""))
        
        return total_chars
        
    async def _generate_metadata(self, state: ArticleState) -> ArticleState:
        """生成元数据
        
        Args:
            state: 文章状态
            
        Returns:
            更新后的文章状态
        """
        metadata = {
            "title": state.topic if hasattr(state, "topic") else "无标题",
            "keywords": state.keywords if hasattr(state, "keywords") else [],
            "description": state.meta_description if hasattr(state, "meta_description") else "",
            "author": "AI生成",
            "date": time.strftime("%Y-%m-%d"),
            "category": "AI生成文章",
            "tags": state.keywords if hasattr(state, "keywords") else []
        }
        
        state.final_metadata = metadata
        return state
        
    def _generate_meta_description(self, state: ArticleState) -> str:
        """生成元描述
        
        Args:
            state: 文章状态
            
        Returns:
            str: 元描述
        """
        if hasattr(state, "meta_description") and state.meta_description:
            return state.meta_description
            
        # 从文章内容生成描述
        content = state.final_content if hasattr(state, "final_content") else {}
        introduction = content.get("introduction", "")
        
        if introduction:
            # 限制描述长度
            return introduction[:460] + "..." if len(introduction) > 460 else introduction
            
        return state.topic if hasattr(state, "topic") and state.topic else "文章"
        
    def _ensure_valid_content(self, state: ArticleState) -> ArticleState:
        """
        确保内容有效且保持原始文章类型结构(章节型或流畅型)
        
        Args:
            state: 文章状态
            
        Returns:
            ArticleState: 更新后的文章状态
        """
        if not hasattr(state, 'final_content') or not state.final_content:
            self.logger.warning("final_content不存在或为空，尝试恢复")
            self._recover_content(state)
            return state
        
        # 确保final_content是字典类型
        if not isinstance(state.final_content, dict):
            self.logger.warning(f"final_content不是字典类型: {type(state.final_content)}")
            if isinstance(state.final_content, str):
                try:
                    # 尝试解析为JSON
                    state.final_content = json.loads(state.final_content)
                except:
                    # 创建基本结构
                    state.final_content = {
                        "title": "未命名文章",
                        "content": state.final_content
                    }
            else:
                # 非字符串类型，创建基本结构
                state.final_content = {
                    "title": "未命名文章",
                    "content": str(state.final_content)
                }
        
        # 确定文章类型：章节型或流畅型
        article_type = None
        if hasattr(state, "article_plan") and isinstance(state.article_plan, dict):
            article_type = state.article_plan.get("article_type")
            self.logger.info(f"处理文章类型: {article_type}")
        
        # 定义章节型和流畅型文章类型列表
        chapter_types = [
            "教程/分析型", "教程", "分析", "指南", "攻略", "总分总", "深度解析", 
            "客观分析", "步骤演示", "评测", "技术指南", "检视型"
        ]
        
        narrative_types = [
            "故事+感触+干货", "观点+案例+金句", "对比分析", "故事", "观点", 
            "博客", "个人经历", "感悟", "随笔", "叙事文", "议论文"
        ]
        
        # 判断文章类型
        is_chapter_based = False
        if article_type:
            # 根据文章类型判断
            if any(chap_type in article_type for chap_type in chapter_types):
                is_chapter_based = True
            elif any(narr_type in article_type for narr_type in narrative_types):
                is_chapter_based = False
        
        # 如果无法根据类型判断，则根据内容结构判断
        if article_type is None:
            # 检查是否有sections字段并且有内容
            if "sections" in state.final_content and isinstance(state.final_content.get("sections"), list) and state.final_content.get("sections"):
                is_chapter_based = True
            # 检查是否有content字段并且有内容
            elif "content" in state.final_content and state.final_content.get("content"):
                is_chapter_based = False
            # 默认为章节型
            else:
                is_chapter_based = True
        
        self.logger.info(f"文章类型: {article_type}, 是否章节型: {is_chapter_based}")
        
        # 根据文章类型确保内容结构正确
        if is_chapter_based:
            # 章节型文章必须有sections字段
            if "sections" not in state.final_content or not isinstance(state.final_content.get("sections"), list):
                if "content" in state.final_content and state.final_content.get("content"):
                    # 如果是章节型但只有content，不要尝试转换为sections
                    # 而是保留content结构，避免强制转换
                    self.logger.info("章节型文章没有sections字段，但包含content字段，保留现有结构")
                else:
                    # 如果既没有sections也没有content，创建默认sections
                    self.logger.warning("章节型文章缺少sections字段，创建默认章节")
                    state.final_content["sections"] = [
                        {"title": "内容", "content": ""}
                    ]
            
            # 确保introduction和conclusion字段存在
            if "introduction" not in state.final_content:
                state.final_content["introduction"] = ""
            
            if "conclusion" not in state.final_content:
                state.final_content["conclusion"] = ""
        else:
            # 流畅型文章必须有content字段
            if "content" not in state.final_content or not state.final_content.get("content"):
                if "sections" in state.final_content and isinstance(state.final_content.get("sections"), list):
                    # 如果是流畅型但有sections字段，不要尝试合并为content
                    # 而是保留sections结构，避免强制转换
                    self.logger.info("流畅型文章没有content字段，但包含sections字段，保留现有结构")
                else:
                    # 如果既没有content也没有sections，创建默认content
                    self.logger.warning("流畅型文章缺少content字段，创建默认内容")
                    state.final_content["content"] = ""
        
        # 确保有标题
        if "title" not in state.final_content or not state.final_content.get("title"):
            state.final_content["title"] = state.topic if hasattr(state, "topic") and state.topic else "未命名文章"
        
        return state
    
    def _validate_input(self, state: ArticleState) -> bool:
        """验证输入状态是否有效
        
        Args:
            state: 文章状态
            
        Returns:
            bool: 是否有效
        """
        # 检查必要的属性
        if not hasattr(state, "topic") or not state.topic:
            self.logger.warning("状态缺少必要属性: topic")
            return False
            
        # 检查是否有文章内容
        if not hasattr(state, "article_content") or not state.article_content:
            self.logger.warning("状态缺少必要属性: article_content")
            # 尝试从其他属性恢复
            if hasattr(state, "seo_optimized_content") and state.seo_optimized_content:
                self.logger.info("从seo_optimized_content恢复article_content")
                state.article_content = state.seo_optimized_content
            else:
                return False
            
        return True
    
    def _fix_json(self, json_text: str) -> str:
        """
        修复JSON字符串中的常见格式问题，使用json_repair库优先修复
        
        Args:
            json_text: 需要修复的JSON字符串
            
        Returns:
            str: 修复后的JSON字符串
        """
        if not json_text:
            return "{}"
        
        # 记录原始输入的一些信息
        self.logger.info(f"开始修复JSON，长度: {len(json_text)}")
        if len(json_text) > 50:
            self.logger.info(f"JSON前50字符: {json_text[:50]}...")
        
        # 优化：先尝试直接解析，如果成功则不需要修复
        try:
            json.loads(json_text)
            self.logger.info("JSON已经是有效格式，无需修复")
            return json_text
        except json.JSONDecodeError:
            self.logger.info("JSON解析失败，尝试修复")
            
        # 先尝试基本清理
        processed_text = json_text.strip()
        
        # 处理代码块格式
        if processed_text.startswith("```json"):
            processed_text = processed_text.removeprefix("```json")
            self.logger.info("移除JSON代码块标记")
        elif processed_text.startswith("```ts"):
            processed_text = processed_text.removeprefix("```ts")
            self.logger.info("移除TypeScript代码块标记")
        elif processed_text.startswith("```"):
            match = re.match(r"```(\w*)\s*\n", processed_text)
            if match:
                processed_text = processed_text[match.end():]
                self.logger.info(f"移除{match.group(1)}代码块标记")
                
        # 处理结尾的代码块标记
        if processed_text.endswith("```"):
            processed_text = processed_text.removesuffix("```")
            self.logger.info("移除结尾代码块标记")
            
        # 使用json_repair尝试修复
        try:
            repaired_content = json_repair.loads(processed_text)
            fixed_json = json.dumps(repaired_content, ensure_ascii=False)
            self.logger.info("使用json_repair成功修复JSON")
            return fixed_json
        except Exception as e:
            self.logger.warning(f"json_repair修复失败: {str(e)}，尝试回退到传统修复方法")
       
        raise Exception(f"JSON修复失败: {str(e)}")
        
    
    def _robust_json_parse(self, text: str) -> Dict[str, Any]:
        """
        尝试使用多种方法解析JSON，确保即使有错误也能获取有效内容
        
        Args:
            text: 要解析的JSON文本
            
        Returns:
            Dict[str, Any]: 解析后的JSON对象
        """
        # 1. 尝试使用代码块提取
        json_content = text
        
        # 检查是否有JSON代码块
        json_block_match = re.search(r'```(?:json|ts)?\s*([\s\S]*?)```', text)
        if json_block_match:
            json_content = json_block_match.group(1).strip()
            self.logger.info("从代码块中提取JSON内容")
            
        # 2. 尝试直接解析
        try:
            return json.loads(json_content)
        except json.JSONDecodeError:
            self.logger.info("直接解析JSON失败，尝试修复")
        
        # 3. 尝试使用增强的_fix_json修复后再解析
        try:
            fixed_text = self._fix_json(json_content)
            return json.loads(fixed_text)
        except json.JSONDecodeError:
            self.logger.info("修复后解析仍然失败，尝试结构化提取")
        
        # 4. 尝试使用正则表达式提取关键字段
        result = {}
        # 提取标题
        title_match = re.search(r'"title"\s*:\s*"([^"]*)"', text)
        if title_match:
            result["title"] = title_match.group(1)
        else:
            result["title"] = "未命名文章"
            
        # 提取内容，保留换行符
        content_match = re.search(r'"content"\s*:\s*"([\s\S]*?)(?="\s*[,}]|$)', text)
        if content_match:
            # 恢复转义后的换行符为实际换行符，保留格式
            content = content_match.group(1)
            content = content.replace('\\n', '\n').replace('\\"', '"')
            result["content"] = content
        
        # 如果至少有标题或内容，认为解析成功
        if result.get("title") or result.get("content"):
            self.logger.info("通过正则表达式成功提取JSON关键字段")
            return result
            
        # 5. 尝试从Markdown结构中提取内容
        try:
            title = None
            content = ""
            
            # 提取标题 (尝试Markdown标题)
            title_match = re.search(r'#\s+(.+?)(?:\n|$)', text)
            if title_match:
                title = title_match.group(1).strip()
                
            # 提取正文内容 (标题后的所有内容)
            if title and title_match:
                content = text[text.find(title_match.group(0)) + len(title_match.group(0)):].strip()
            else:
                content = text
                
            return {
                "title": title or "从Markdown提取的标题",
                "content": content or "从Markdown提取的内容"
            }
        except Exception as e:
            self.logger.warning(f"Markdown结构提取失败: {str(e)}")
            
        # 最后的回退：返回尽可能的结果
        self.logger.warning("所有JSON解析方法都失败，返回空对象并保留原始内容")
        return {"title": "解析失败", "content": text[:2000] if len(text) > 2000 else text}
    

        return text
    
    def _get_season(self, month: int) -> str:
        """根据月份获取季节
        
        Args:
            month: 月份(1-12)
            
        Returns:
            季节名称
        """
        if not isinstance(month, int) or month < 1 or month > 12:
            return "未知季节"
            
        if month in [3, 4, 5]:
            return "春季"
        elif month in [6, 7, 8]:
            return "夏季"
        elif month in [9, 10, 11]:
            return "秋季"
        else:  # month in [12, 1, 2]
            return "冬季"