from typing import Dict, List, Any, Optional, Union
import logging
import json
import time
import uuid
import re
import asyncio
import json_repair  # 添加json_repair库导入
import traceback
import random

from src.services.generator.state import ArticleState, ArticleStatus
from src.services.generator.agents.base import AgentOutput, BaseAgent
from src.services.generator.utils.article_utils import calculate_readability, count_words
from src.services.generator.text_generator import LLMAdapter, LLMRole
from src.services.generator.text_generator.base import TextGenerationModel
from src.services.generator.tools.time_tools import get_current_time
from src.services.generator.prompts import writing_article_generation_prompt, content_enhancement_prompt
# 导入原始的WebScraperTool而不是UnifiedCrawlerTool
from src.services.generator.tools.network_tools import WebScraperTool

logger = logging.getLogger("writing_agent")

class WritingAgent(BaseAgent):
    """
    负责内容生成
    """
    def __init__(self):
        """
        初始化WritingAgent
        """
        super().__init__(
            name="写作代理",
            description="根据大纲和写作计划生成文章内容"
        )
        self.llm_adapter = LLMAdapter(default_provider="deepseek_reasoner")
        self.logger = logger
    
    async def run(self, state: ArticleState) -> AgentOutput:
        """
        执行文章内容生成
        
        Args:
            state: 文章状态
            
        Returns:
            AgentOutput: 包含是否成功以及更新后的状态
        """
        self.logger.info("正在运行WritingAgent...")
        # 复制状态以避免修改输入状态
        state = state.copy(deep=True)
        state.update_status(ArticleStatus.WRITING)

        # 获取当前搜索轮次
        if hasattr(state, "search_rounds") and state.search_rounds > 0:
            # 已有有效的搜索轮次，直接使用
            search_round = state.search_rounds
            self.logger.info(f"当前搜索轮次: {search_round}")
        elif hasattr(state, "evaluation_rounds") and state.evaluation_rounds > 0:
            # 没有有效搜索轮次但有评估轮次，使用评估轮次
            search_round = state.evaluation_rounds
            # 更新state中的搜索轮次
            state.search_rounds = search_round
            self.logger.info(f"使用评估轮次({state.evaluation_rounds})更新搜索轮次: {search_round}")
        else:
            # 既没有搜索轮次也没有评估轮次，设为1
            search_round = 1
            state.search_rounds = 1
            self.logger.info(f"未找到有效的搜索轮次，设置为1")

        # 检查输入是否有效
        if not self._validate_input(state):
            msg = "写作所需的输入参数无效"
            self.logger.error(msg)
            state.add_error("writing_agent", msg)
            return AgentOutput(success=False, state=state, error=msg)

        start_time = time.time()
        try:
            # 单次调用LLM生成/增强文章内容
            self.logger.info("开始生成文章内容...")
            article_content = await self._generate_article_content(state)
            
            # 记录生成的文章结构
            content_keys = list(article_content.keys())
            self.logger.info(f"成功生成文章内容，包含以下字段: {', '.join(content_keys)}")
            
            # 更新状态
            state.article_content = article_content
            
            # 添加写作数据
            quality_score = self._calculate_quality_score(article_content)
            words_count = self._count_total_words(state)
            readability_score = self._calculate_readability_score(state)
            generation_time = time.time() - start_time
            
            write_data = {
                "content_quality_score": quality_score,
                "generation_time": generation_time,
                "words_count": words_count,
                "readability_score": readability_score,
                "search_round": search_round
            }
            self.logger.info(f"文章字数: {words_count}, 质量评分: {quality_score:.2f}, 可读性评分: {readability_score:.2f}")
            
            # 存储写作数据
            if not hasattr(state, "writing_data_history"):
                state.writing_data_history = []
            
            # 添加到历史记录
            state.writing_data_history.append(write_data)
            
            # 同时保持兼容旧代码
            state.writing_data = write_data
            
            # 更新完成状态
            state.completion_status["writing"] = True
            
            # 根据轮次记录不同的反思内容
            if search_round <= 1:
                reflection_message = "完成文章内容写作，生成了完整的文章结构和内容"
            else:
                reflection_message = f"完成第{search_round}轮文章内容增强，填补了知识空缺，提高了内容深度"
            
            # 添加反思
            state.add_reflection(
                "writing",
                reflection_message,
                {
                    "writing_time": write_data["generation_time"],
                    "quality_score": write_data["content_quality_score"],
                    "readability_score": write_data["readability_score"],
                    "word_count": write_data["words_count"],
                    "search_round": search_round
                }
            )
            
            # 准备进入下一阶段
            if search_round <= 1:
                # 第一轮后进入评估阶段
                state.update_status(ArticleStatus.EVALUATING)
            else:
                # 其他轮次根据是否已达到最大轮次决定
                max_rounds = 3  # 最大搜索轮次
                if search_round > max_rounds:
                    # 达到最大轮次，进入图像生成阶段
                    self.logger.info(f"已达到最大搜索轮次({search_round}/{max_rounds})，进入编辑阶段")
                    state.update_status(ArticleStatus.EDITING)
                else:
                    # 未达到最大轮次，进入评估阶段
                    self.logger.info(f"当前搜索轮次({search_round}/{max_rounds})，进入评估阶段")
                    state.update_status(ArticleStatus.EVALUATING)

            return AgentOutput(success=True, state=state)
            
        except Exception as e:
            error_msg = f"WritingAgent运行出错: {str(e)}"
            self.logger.error(f"{error_msg}\n{traceback.format_exc()}")
            state.add_error("writing", error_msg)
            return AgentOutput(success=False, state=state, error=error_msg)
    
    async def _get_current_time(self) -> Dict[str, Any]:
        """
        获取当前时间信息
        
        Returns:
            Dict[str, Any]: 包含当前时间信息的字典
        """
        try:
            # 调用时间工具获取时间信息
            time_info = await get_current_time({
                "timezone": "Asia/Shanghai", 
                "include_calendar_info": True
            })
            self.logger.info(f"成功获取当前时间信息: {time_info['formatted_date']} {time_info['formatted_time']}")
            return time_info
        except Exception as e:
            self.logger.error(f"获取时间信息失败: {str(e)}")
            # 返回基本时间信息作为备选
            now = time.localtime()
            return {
                "timestamp": time.time(),
                "formatted_date": time.strftime("%Y-%m-%d", now),
                "formatted_time": time.strftime("%H:%M:%S", now),
                "year": now.tm_year,
                "month": now.tm_mon,
                "day": now.tm_mday,
                "season": self._get_season(now.tm_mon)
            }
    
    def _get_season(self, month: int) -> str:
        """
        根据月份获取季节
        
        Args:
            month: 月份
            
        Returns:
            str: 季节名称
        """
        if 3 <= month <= 5:
            return "春季"
        elif 6 <= month <= 8:
            return "夏季"
        elif 9 <= month <= 11:
            return "秋季"
        else:
            return "冬季"
            
    async def _generate_article_content(self, state: ArticleState) -> Dict[str, Any]:
        """
        生成文章内容
        
        Args:
            state: 文章状态
            
        Returns:
            Dict[str, Any]: 生成的文章内容
        """
        # 获取当前搜索轮次
        if hasattr(state, "search_rounds") and state.search_rounds > 0:
            # 已有有效的搜索轮次，直接使用
            search_round = state.search_rounds
            self.logger.info(f"【文章生成】当前搜索轮次: {search_round}")
        elif hasattr(state, "evaluation_rounds") and state.evaluation_rounds > 0:
            # 没有有效搜索轮次但有评估轮次，使用评估轮次
            search_round = state.evaluation_rounds
            # 同步state中的搜索轮次（不递增）
            state.search_rounds = search_round
            self.logger.info(f"【文章生成】使用评估轮次({state.evaluation_rounds})同步搜索轮次: {search_round}")
        else:
            # 都没有，则使用1
            search_round = 1
            state.search_rounds = 1
            self.logger.info(f"【文章生成】未找到有效的搜索轮次，使用默认值1")
        
        # 获取当前时间信息
        current_time = await self._get_current_time()
        self.logger.info(f"当前时间: {current_time['formatted_date']} {current_time['formatted_time']}")
        
        # 尝试获取当前轮次的搜索内容
        search_content = await self._extract_search_content(state)
        
        # 检查是否有搜索内容
        if not search_content or len(search_content) == 0:
            self.logger.warning(f"搜索结果为空，将生成没有外部信息的文章内容")
        
        try:
            # 区分第一轮和后续轮次的内容生成逻辑
            if search_round <= 1 or not state.article_content:
                # 第一轮: 使用标准提示词生成文章
                self.logger.info("第一轮内容生成，使用标准提示词")
                
                # 获取文章类型
                article_type = None
                if hasattr(state, "article_plan") and isinstance(state.article_plan, dict):
                    article_type = state.article_plan.get("article_type")
                    self.logger.info(f"从article_plan中获取文章类型: {article_type}")
                
                # 获取写作计划
                writing_plan = {}
                if hasattr(state, "article_plan") and isinstance(state.article_plan, dict):
                    # 检查article_plan中的writing_plan
                    if "writing_plan" in state.article_plan:
                        if isinstance(state.article_plan["writing_plan"], dict):
                            writing_plan = state.article_plan.get("writing_plan", {})
                            self.logger.info(f"从article_plan中获取writing_plan: {len(writing_plan)} 个字段, 键: {list(writing_plan.keys())}")
                        else:
                            # 如果writing_plan存在但不是字典，记录类型并创建默认字典
                            self.logger.warning(f"article_plan中的writing_plan类型错误: {type(state.article_plan['writing_plan'])}")
                            
                    else:
                        # 如果article_plan中没有writing_plan字段，创建默认值
                        self.logger.warning("article_plan中缺少writing_plan字段")
                        
                else:
                    # 如果既没有writing_plan也没有article_plan，创建默认值
                    self.logger.warning("未找到writing_plan或article_plan")
                   

                # 输出写作计划的详细信息，便于调试
                self.logger.info(f"最终使用的writing_plan: {json.dumps(writing_plan, ensure_ascii=False)}")
                
                # 获取案例和例子
                cases_and_examples = []
                if hasattr(state, "cases_and_examples") and isinstance(state.cases_and_examples, list):
                    cases_and_examples = state.cases_and_examples
                    self.logger.info(f"使用文章状态中的cases_and_examples: {len(cases_and_examples)} 个案例")
                elif hasattr(state, "case_studies") and isinstance(state.case_studies, list):
                    cases_and_examples = state.case_studies
                    self.logger.info(f"使用文章状态中的case_studies: {len(cases_and_examples)} 个案例")
                elif hasattr(state, "article_plan") and isinstance(state.article_plan, dict):
                    # 优先尝试新字段名
                    if "cases_and_examples" in state.article_plan:
                        cases_and_examples = state.article_plan.get("cases_and_examples", [])
                        self.logger.info(f"从article_plan中获取cases_and_examples: {len(cases_and_examples)} 个案例")
                    elif "case_studies" in state.article_plan:
                        cases_and_examples = state.article_plan.get("case_studies", [])
                        self.logger.info(f"从article_plan中获取case_studies: {len(cases_and_examples)} 个案例")
                
                # 获取引用建议
                quotations_to_use = []
                if hasattr(state, "quotations_to_use") and isinstance(state.quotations_to_use, list):
                    quotations_to_use = state.quotations_to_use
                    self.logger.info(f"使用文章状态中的quotations_to_use: {len(quotations_to_use)} 个引用")
                elif hasattr(state, "citation_suggestions") and isinstance(state.citation_suggestions, list):
                    quotations_to_use = state.citation_suggestions
                    self.logger.info(f"使用文章状态中的citation_suggestions: {len(quotations_to_use)} 个引用")
                elif hasattr(state, "article_plan") and isinstance(state.article_plan, dict):
                    # 优先尝试新字段名
                    if "quotations_to_use" in state.article_plan:
                        quotations_to_use = state.article_plan.get("quotations_to_use", [])
                        self.logger.info(f"从article_plan中获取quotations_to_use: {len(quotations_to_use)} 个引用")
                    elif "citation_suggestions" in state.article_plan:
                        quotations_to_use = state.article_plan.get("citation_suggestions", [])
                        self.logger.info(f"从article_plan中获取citation_suggestions: {len(quotations_to_use)} 个引用")
                
                # 构建生成提示词
                prompt = writing_article_generation_prompt(
                    topic=state.topic,
                    outline=state.outline,
                    keywords=state.keywords,
                    search_content=search_content,
                    writing_plan=writing_plan,
                    article_type=article_type,
                    cases_and_examples=cases_and_examples,
                    quotations_to_use=quotations_to_use,
                    current_time=current_time  # 添加时间信息
                )
            else:
                # 后续轮次: 使用增强提示词来改进文章
                self.logger.info(f"第{search_round}轮内容生成，使用内容增强提示词")
                
                # 直接使用现有文章内容（字典格式）
                # 不再调用 self._format_existing_content
                existing_content = state.article_content
                
                # 检查existing_content是否为空或无效
                if not existing_content:
                    self.logger.error(f"第{search_round}轮内容增强失败: existing_content为空")
                    # 使用第一轮的内容或创建默认内容
                    if hasattr(state, "writing_data_history") and len(state.writing_data_history) > 0:
                        self.logger.info("使用第一轮的内容作为基础")
                        if hasattr(state, "writing_history") and state.writing_history:
                            for history in state.writing_history:
                                if isinstance(history, dict) and "article_content" in history:
                                    existing_content = history["article_content"]
                                    self.logger.info("从历史中恢复了文章内容")
                                    break
                    
                    if not existing_content:
                        self.logger.warning("无法从历史中恢复内容，创建基本内容结构")
                        existing_content = {
                            "title": state.topic or "生成的文章",
                            "introduction": "本文探讨" + (state.topic or "相关主题"),
                            "sections": [{"title": "主要内容", "content": "内容生成中..."}],
                            "conclusion": "总结与展望"
                        }
                
                # 检查existing_content是否是有效的字典
                if not isinstance(existing_content, dict):
                    self.logger.warning(f"existing_content类型无效: {type(existing_content)}, 尝试转换为字典")
                    try:
                        if isinstance(existing_content, str):
                            # 尝试将字符串解析为JSON
                            try:
                                parsed_content = json.loads(existing_content)
                                if isinstance(parsed_content, dict):
                                    existing_content = parsed_content
                                    self.logger.info("成功将existing_content从字符串解析为字典")
                                else:
                                    raise ValueError("解析后不是字典")
                            except:
                                # 如果无法解析为JSON，创建一个基本结构
                                existing_content = self._create_basic_content_structure(existing_content)
                                self.logger.info("无法解析JSON，已创建基本内容结构")
                        else:
                            # 其他类型，创建一个基本结构
                            existing_content = {
                                "title": state.topic or "生成的文章",
                                "content": str(existing_content)
                            }
                            self.logger.info("已将非字典类型的existing_content转换为基本内容结构")
                    except Exception as e:
                        self.logger.error(f"转换existing_content失败: {str(e)}")
                        existing_content = {
                            "title": state.topic or "生成的文章",
                            "introduction": "本文探讨" + (state.topic or "相关主题"),
                            "sections": [{"title": "主要内容", "content": "内容生成中..."}],
                            "conclusion": "总结与展望"
                        }
                
                # 获取文章类型
                article_type = None
                if hasattr(state, "article_plan") and isinstance(state.article_plan, dict):
                    article_type = state.article_plan.get("article_type")
                    self.logger.info(f"从article_plan中获取文章类型: {article_type}")
                
                # 记录现有内容的结构
                self.logger.info(f"existing_content包含以下字段: {', '.join(existing_content.keys())}")
                
                # 构建增强提示词
                try:
                    prompt = content_enhancement_prompt(
                        existing_content=existing_content,
                        search_content=search_content,
                        knowledge_gaps=state.knowledge_gaps if hasattr(state, "knowledge_gaps") else None,
                        humanizing_suggestions=state.humanizing_suggestions if hasattr(state, "humanizing_suggestions") else None,
                        improvement_suggestion=state.improvement_suggestion if hasattr(state, "improvement_suggestion") else None,
                        topic=state.topic,
                        keywords=state.keywords,
                        article_type=article_type,
                        current_time=current_time  # 添加时间信息
                    )
                except Exception as e:
                    self.logger.error(f"构建增强提示词失败: {str(e)}")
                   
            prompt_length = len(prompt) if prompt else 0
            self.logger.info(f"生成提示词长度: {prompt_length}")
            
            # 调用LLM生成内容
            response = await self.llm_adapter.ask_llm(
                role="writer",
                prompt=prompt,
                temperature=0.7,
                max_tokens=8000
            )
            
            # 处理返回的内容，确保是字典格式
            response_text = response.strip()
            
            try:
                # 记录原始响应的前200个字符
                preview_text = response_text[:200] + ("..." if len(response_text) > 200 else "")
                self.logger.info(f"LLM响应预览: {preview_text}")
                
                # 使用优化后的 JSON 解析函数处理响应
                try:
                    article_content = self._parse_json_response(response_text)
                    self.logger.info("成功解析文章内容JSON")
                except ValueError as e:
                    self.logger.error(f"JSON解析失败: {str(e)}")
                    raise ValueError(f"无法解析模型返回的JSON: {str(e)}")
                
                # 验证返回的内容至少包含必要的字段
                if not isinstance(article_content, dict):
                    self.logger.error("解析后的内容不是字典类型")
                    raise ValueError("解析后的内容必须是字典类型")
                
                # 确保包含所有必要字段
                if "title" not in article_content:
                    article_content["title"] = state.topic or "生成的文章"
                    self.logger.warning("添加缺失的标题字段")
                
                # 检查文章类型，仅根据article_type判断结构
                article_type = None
                if hasattr(state, "article_plan") and isinstance(state.article_plan, dict):
                    article_type = state.article_plan.get("article_type")
                    self.logger.info(f"从article_plan中获取文章类型: {article_type}")

                # 
                is_narrative_style = article_type in ["故事+感触+干货", "观点+案例+金句", "对比分析"]
                self.logger.info(f"文章类型: {article_type}, 使用流畅型结构: {is_narrative_style}")

                # 根据文章类型处理内容
                if is_narrative_style:
                    # 流畅型结构（故事型或观点型）
                    self.logger.info(f"检测到流畅型结构文章类型: {article_type}，处理为连续内容格式")
                    
                    # 检查是否有content字段(整篇文章内容)
                    if "content" in article_content and article_content["content"]:                 # 已有完整内容，保持不变
                        self.logger.info("已有content字段，保持原始结构")
                    else:
                        # 尝试创建连续内容结构
                        full_content = []
                        
                        raise ValueError("没有content")
                        
                        # 设置连续内容
                        article_content["content"] = "\n\n".join(full_content)
                        self.logger.info(f"创建流畅型结构的content字段，内容长度: {len(article_content['content'])}")
                        
                        # 保留原始字段
                        self.logger.info("保留原始字段，以便后续处理")
                else:
                    # 章节型结构（教程/分析型）
                    self.logger.info(f"检测到章节型结构文章类型: {article_type}，处理为结构化内容格式")
                    
                    # 如果有content字段但没有sections，尝试将content转换为结构化内容
                    if "content" in article_content and article_content["content"] and (
                        "sections" not in article_content or 
                        not isinstance(article_content["sections"], list) or
                        len(article_content["sections"]) == 0
                    ):
                        content_text = self._ensure_text_content(article_content["content"], "content")
                        
                        # 尝试从content中提取章节
                        if content_text:
                            self.logger.info("尝试从content中提取章节结构")
                            
                            # 基本的章节提取正则表达式模式
                            section_matches = re.finditer(r'##\s+(.*?)\n(.*?)(?=\n##|\n#\s|$)', content_text, re.DOTALL)
                            
                            sections = []
                            for match in section_matches:
                                section_title = match.group(1).strip()
                                section_content = match.group(2).strip()
                                
                                sections.append({
                                    "title": section_title,
                                    "content": section_content
                                })
                            
                            if sections:
                                self.logger.info(f"从content中提取了 {len(sections)} 个章节")
                                article_content["sections"] = sections
                                
                                # 尝试提取引言（第一个##标题之前的内容）
                                first_heading = content_text.find('##')
                                if first_heading > 0:
                                    intro_text = content_text[:first_heading].strip()
                                    if intro_text:
                                        article_content["introduction"] = intro_text
                                        self.logger.info(f"从content中提取了引言，长度: {len(intro_text)}")
                                
                                # 删除content字段，避免冗余
                                if "content" in article_content:
                                    del article_content["content"]
                            else:
                                self.logger.warning("无法从content中提取章节，创建默认章节")
                                article_content["sections"] = [{
                                    "title": "文章内容",
                                    "content": content_text
                                }]
                    
                    # 确保结构中包含introduction和conclusion
                    if "introduction" not in article_content or not article_content["introduction"]:
                        self.logger.info("添加默认introduction字段")
                        article_content["introduction"] = ""
                    
                    if "conclusion" not in article_content or not article_content["conclusion"]:
                        self.logger.info("添加默认conclusion字段")
                        article_content["conclusion"] = ""
                
                return article_content
                    
            except Exception as e:
                self.logger.error(f"处理模型响应时出错: {str(e)}")
                raise ValueError(f"处理文章内容失败: {str(e)}")
                
        except Exception as e:
            self.logger.error(f"文章生成过程中出错: {str(e)}")
            raise ValueError(f"文章生成失败: {str(e)}")
    
  
    def _format_existing_content(self, content: str) -> str:
        """
        格式化现有的文章内容，用于内容增强
        
        Args:
            content: 现有的文章内容
            
        Returns:
            str: 格式化后的内容
        """
        if not content:
            return ""
            
        # 简单清理内容，确保没有多余的空行
        lines = content.strip().split("\n")
        cleaned_lines = [line.strip() for line in lines if line.strip()]
        return "\n".join(cleaned_lines)
        
    async def _extract_search_content(self, state: ArticleState) -> List[Dict[str, Any]]:
        """
        从状态中提取搜索内容，并爬取网页完整内容
        
        Args:
            state: 文章状态
            
        Returns:
            List[Dict[str, Any]]: 格式化后的搜索结果列表，包含完整网页内容
        """
        try:
            # 获取当前搜索轮次
            if hasattr(state, "search_rounds") and state.search_rounds > 0:
                search_round = state.search_rounds
            elif hasattr(state, "evaluation_rounds") and state.evaluation_rounds > 0:
                search_round = state.evaluation_rounds
                state.search_rounds = search_round
            else:
                search_round = 1
                state.search_rounds = 1
            
            self.logger.info(f"开始爬取第{search_round}轮搜索内容")
            
            # 仅获取当前轮次搜索结果
            current_round_results = []
            
            # 从搜索历史中查找当前轮次结果
            if hasattr(state, "search_history") and isinstance(state.search_history, list):
                for history_item in state.search_history:
                    if isinstance(history_item, dict) and "results" in history_item:
                        if history_item.get("round") == search_round:
                            current_round_results.extend(history_item["results"])
            
            # 如果没有在历史记录中找到，从当前状态获取
            if not current_round_results and hasattr(state, "search_results"):
                if isinstance(state.search_results, list):
                    for item in state.search_results:
                        if not isinstance(item, dict):
                            continue
                        if (item.get("round") == search_round or 
                            item.get("search_round") == search_round or
                            ("round" not in item and "search_round" not in item)):
                            current_round_results.append(item)
                elif isinstance(state.search_results, dict):
                    current_round_results.append(state.search_results)
            
            if not current_round_results:
                self.logger.warning(f"第{search_round}轮没有找到搜索结果")
                return []
            
            # 初始化统一爬虫工具（替换原有的WebScraperTool）
            unified_crawler = WebScraperTool()
            
            # 爬取每个搜索结果的完整网页内容
            enriched_results = []
            min_content_length = 1500  # 最小内容长度阈值调整为1500字
            max_content_length = 25000  # 最大内容长度为25000字
            
            # 逐个爬取搜索结果
            for result in current_round_results:
                try:
                    url = result.get("url", "")
                    if not url:
                        continue
                    
                    self.logger.info(f"爬取URL: {url}")
                    
                    # 实现最多2次重试机制
                    scrape_success = False
                    scrape_result = None
                    max_attempts = 2  # 总共最多2次尝试（1次初始 + 1次重试）
                    
                    for attempt in range(max_attempts):  # 0, 1 = 总共2次尝试
                        try:
                            if attempt > 0:
                                self.logger.info(f"重试第{attempt}次爬取: {url}")
                                # 等待一段时间后重试，避免被反爬
                                await asyncio.sleep(attempt * 1.5)
                            
                            scrape_result = await unified_crawler.execute(
                                params={
                                    "url": url,
                                    "extract_type": "text"
                                },
                                state={}
                            )
                            
                            # 检查爬取是否成功
                            if (scrape_result and 
                                isinstance(scrape_result, dict) and 
                                not scrape_result.get("error") and
                                scrape_result.get("text", "").strip()):
                                
                                scrape_success = True
                                self.logger.info(f"成功爬取内容: {url}")
                                break
                            else:
                                self.logger.warning(f"爬取尝试{attempt + 1}失败: {url} - {scrape_result.get('error', '无有效内容')}")
                                
                        except Exception as scrape_error:
                            self.logger.warning(f"爬取尝试{attempt + 1}失败: {url} - {str(scrape_error)}")
                            
                        # 如果不是最后一次尝试，继续重试
                        if attempt < max_attempts - 1:
                            continue
                        else:
                            self.logger.warning(f"经过{max_attempts}次尝试后爬取失败: {url}")
                    
                    if scrape_success and scrape_result:
                        # 处理爬取结果
                        content_field = "text"
                        if "text" not in scrape_result and "content" in scrape_result:
                            content_field = "content"
                        
                        if content_field in scrape_result and scrape_result[content_field]:
                            scraped_content = scrape_result[content_field]
                            content_length = len(scraped_content)
                            
                            # 确保内容长度在1500-25000之间
                            if content_length >= min_content_length and content_length <= max_content_length:
                                # 更新搜索结果内容
                                result["content"] = scraped_content
                                result["snippet"] = scraped_content[:500] + "..."
                                result["content_length"] = content_length
                                
                                # 添加统一爬虫可能提供的额外信息
                                if "site_type" in scrape_result:
                                    result["site_type"] = scrape_result["site_type"]
                                if "metadata" in scrape_result:
                                    result["metadata"] = scrape_result["metadata"]
                                
                                enriched_results.append(result)
                                self.logger.info(f"成功爬取内容(长度:{content_length}): {result.get('title', '')[:30]}")
                            else:
                                if content_length < min_content_length:
                                    self.logger.warning(f"爬取内容过短(长度:{content_length}): {result.get('title', '')[:30]}")
                                else:
                                    # 如果内容太长，截取到25000字符以内
                                    scraped_content = scraped_content[:max_content_length]
                                    content_length = len(scraped_content)
                                    result["content"] = scraped_content
                                    result["snippet"] = scraped_content[:500] + "..."
                                    result["content_length"] = content_length
                                    
                                    if "site_type" in scrape_result:
                                        result["site_type"] = scrape_result["site_type"]
                                    if "metadata" in scrape_result:
                                        result["metadata"] = scrape_result["metadata"]
                                    
                                    enriched_results.append(result)
                                    self.logger.info(f"内容超长已截取(长度:{content_length}): {result.get('title', '')[:30]}")
                        else:
                            self.logger.warning(f"爬取结果无有效内容: {url}")
                except Exception as e:
                    self.logger.error(f"爬取异常: {str(e)}, URL: {url}")
                    # 继续处理下一个URL，不中断整个流程
            
            # 根据搜索轮次随机选择符合长度要求的内容
            if enriched_results:
                # 过滤出长度在1500-25000之间的结果
                valid_results = [r for r in enriched_results if 1500 <= r.get("content_length", 0) <= 25000]
                
                # 根据搜索轮次确定需要返回的结果数量
                if search_round <= 1:
                    # 第一轮随机选取8条
                    target_count = 8
                    result_msg = "随机选取8条"
                else:
                    # 第二轮及以后随机选取4条
                    target_count = 4
                    result_msg = "随机选取4条"
                
                # 随机选择
                if len(valid_results) > target_count:
                    selected_results = random.sample(valid_results, target_count)
                    self.logger.info(f"第{search_round}轮从{len(valid_results)}条有效结果中{result_msg}(长度1500-25000字符)")
                else:
                    selected_results = valid_results
                    self.logger.info(f"第{search_round}轮只有{len(valid_results)}条有效结果，全部选择")
                
                # 按内容长度排序（仍然记录排序信息以便调试）
                selected_results.sort(key=lambda x: x.get("content_length", 0), reverse=True)
                for i, result in enumerate(selected_results):
                    self.logger.info(f"选中内容{i+1}: 长度{result.get('content_length', 0)} - {result.get('title', '')[:30]}")
                
                enriched_results = selected_results
            
            # 保存结果
            if not hasattr(state, "round_search_contents"):
                state.round_search_contents = {}
                
            state.round_search_contents[search_round] = enriched_results
            
            return enriched_results
        except Exception as e:
            self.logger.error(f"提取搜索内容时出错: {str(e)}")
            return []
    
    def _format_outline_for_prompt(self, outline: List[Dict[str, Any]]) -> str:
        """
        将大纲格式化为可读字符串，用于提示词
        
        Args:
            outline: 文章大纲
            
        Returns:
            格式化后的大纲字符串
        """
        if not outline:
            self.logger.error("大纲为空，无法格式化")
            return "无大纲内容"
        
        self.logger.info(f"格式化大纲，共有 {len(outline)} 个章节")
            
        formatted_outline = []
        
        for i, section in enumerate(outline):
            section_title = section.get("title", f"章节 {i+1}")
            section_desc = section.get("description", "")
            
            formatted_outline.append(f"{i+1}. {section_title}")
            if section_desc:
                formatted_outline.append(f"   描述: {section_desc}")
        
        result = "\n".join(formatted_outline)
        self.logger.info(f"大纲格式化完成，长度 {len(result)} 字符")
        return result
    
    def _calculate_quality_score(self, article_content: Dict[str, Any]) -> float:
        """
        计算文章内容质量分数
        
        Args:
            article_content: 文章内容
            
        Returns:
            质量分数（0-100）
        """
        score = 0.0
        # 检查是否有标题
        if article_content.get("title"):
            score += 10.0
            # 标题长度适中（5-20个字）
            title_length = len(article_content["title"])
            if 5 <= title_length <= 20:
                score += 5.0
        
        # 检查引言
        intro = article_content.get("introduction", "")
        if intro:
            score += 10.0
            # 引言长度适中（100-300字）
            intro_length = len(intro)
            if 100 <= intro_length <= 300:
                score += 5.0
        
        # 检查章节数量和内容
        sections = article_content.get("sections", [])
        section_count = len(sections)
        
        # 有2-7个章节是合理的
        if 2 <= section_count <= 7:
            score += 10.0
        elif section_count > 0:
            score += 5.0
        
        # 检查每个章节的内容
        total_sections = 0
        non_empty_sections = 0
        
        for section in sections:
            total_sections += 1
            if section.get("content", "").strip():
                non_empty_sections += 1
                
            # 检查子章节
            subsections = section.get("subsections", [])
            for subsection in subsections:
                total_sections += 1
                if subsection.get("content", "").strip():
                    non_empty_sections += 1
        
        # 计算非空章节比例
        if total_sections > 0:
            content_ratio = non_empty_sections / total_sections
            score += 30.0 * content_ratio
        
        # 检查结论
        conclusion = article_content.get("conclusion", "")
        if conclusion:
            score += 10.0
            # 结论长度适中（100-300字）
            conclusion_length = len(conclusion)
            if 100 <= conclusion_length <= 300:
                score += 5.0
        
        # 确保分数在0-100范围内
        score = max(0.0, min(100.0, score))
        return score
    
    def _count_total_words(self, state: ArticleState) -> int:
        """
        计算文章总字数
        
        Args:
            state: 文章状态
            
        Returns:
            总字数
        """
        if not state.article_content:
            return 0
            
        # 检查文章结构类型
        if "content" in state.article_content and state.article_content["content"]:
            # 流畅型结构，直接计算content字段
            self.logger.info("使用流畅型结构计算字数")
            return count_words(state.article_content["content"])
        
        # 否则使用传统章节结构计算
        total = 0
        
        # 计算引言字数
        if "introduction" in state.article_content:
            total += count_words(state.article_content["introduction"])
        
        # 计算各章节字数
        if "sections" in state.article_content:
            for section in state.article_content["sections"]:
                if "content" in section:
                    total += count_words(section["content"])
                
                # 计算子章节字数
                if "subsections" in section:
                    for subsection in section["subsections"]:
                        if "content" in subsection:
                            total += count_words(subsection["content"])
        
        # 计算结论字数
        if "conclusion" in state.article_content:
            total += count_words(state.article_content["conclusion"])
        
        return total
    
    def _calculate_readability_score(self, state: ArticleState) -> float:
        """
        计算文章可读性评分
        
        Args:
            state: 文章状态
            
        Returns:
            可读性评分 (0.0-1.0)
        """
        if not state.article_content:
            return 0.0
            
        # 检查文章结构类型
        if "content" in state.article_content and state.article_content["content"]:
            # 流畅型结构，直接使用content字段
            self.logger.info("使用流畅型结构计算可读性")
            return calculate_readability(state.article_content["content"])
        
        # 否则使用传统章节结构计算
        # 获取所有文本内容
        texts = []
        
        # 引言
        if "introduction" in state.article_content:
            texts.append(state.article_content["introduction"])
        
        # 章节内容
        if "sections" in state.article_content:
            for section in state.article_content["sections"]:
                if "content" in section:
                    texts.append(section["content"])
                
                # 子章节内容
                if "subsections" in section:
                    for subsection in section["subsections"]:
                        if "content" in subsection:
                            texts.append(subsection["content"])
        
        # 结论
        if "conclusion" in state.article_content:
            texts.append(state.article_content["conclusion"])
        
        # 合并所有文本，计算可读性
        full_text = "\n\n".join(texts)
        return calculate_readability(full_text)
    
    def _calculate_keyword_density(self, article_content: Dict[str, Any], keywords: str) -> float:
        """
        计算关键词密度
        
        Args:
            article_content: 文章内容
            keywords: 关键词列表
            
        Returns:
            关键词密度分数
        """
        # 检查是否有流畅型结构的content字段
        if "content" in article_content and article_content["content"]:
            # 使用流畅型结构计算关键词密度
            full_text = article_content["content"]
            self.logger.info("使用流畅型结构计算关键词密度")
        else:
            # 使用传统章节结构计算
            # 提取所有文本内容
            text_content = []
            text_content.append(article_content.get("introduction", ""))
            text_content.append(article_content.get("conclusion", ""))
            
            for section in article_content.get("sections", []):
                text_content.append(section.get("content", ""))
                for subsection in section.get("subsections", []):
                    text_content.append(subsection.get("content", ""))
            
            # 合并所有文本
            full_text = " ".join(text_content)
            
        word_count = len(full_text.split())
        
        # 计算每个关键词的出现次数
        keyword_list = [k.strip() for k in keywords.split(",")]
        keyword_counts = {}
        for keyword in keyword_list:
            count = full_text.lower().count(keyword.lower())
            keyword_counts[keyword] = count
        
        # 计算总密度
        total_keyword_count = sum(keyword_counts.values())
        density = (total_keyword_count / word_count) * 100 if word_count > 0 else 0
        
        return min(density, 5.0)  # 限制最大密度为5%

    def _calculate_content_diversity(self, article_content: Dict[str, Any]) -> float:
        """
        计算内容多样性分数
        
        Args:
            article_content: 文章内容
            
        Returns:
            多样性分数 (0-1)
        """
        # 检查是否有流畅型结构的content字段
        if "content" in article_content and article_content["content"]:
            # 使用流畅型结构计算内容多样性
            full_text = article_content["content"]
            self.logger.info("使用流畅型结构计算内容多样性")
        else:
            # 使用传统章节结构计算
            # 提取所有文本内容
            text_content = []
            text_content.append(article_content.get("introduction", ""))
            text_content.append(article_content.get("conclusion", ""))
            
            for section in article_content.get("sections", []):
                text_content.append(section.get("content", ""))
                for subsection in section.get("subsections", []):
                    text_content.append(subsection.get("content", ""))
            
            # 合并所有文本
            full_text = " ".join(text_content)
            
        words = full_text.lower().split()
        
        # 计算词汇多样性
        unique_words = set(words)
        total_words = len(words)
        diversity = len(unique_words) / total_words if total_words > 0 else 0
        
        # 计算句子长度多样性
        sentences = full_text.split(".")
        sentence_lengths = [len(s.split()) for s in sentences if s.strip()]
        if sentence_lengths:
            length_variance = sum((l - sum(sentence_lengths)/len(sentence_lengths))**2 
                                for l in sentence_lengths) / len(sentence_lengths)
            length_diversity = min(length_variance / 100, 1.0)  # 归一化
        else:
            length_diversity = 0
        
        # 综合评分
        return (diversity * 0.7 + length_diversity * 0.3)

    def _validate_quality_metrics(self, metadata: Dict[str, Any]) -> bool:
        """
        验证质量指标是否达标
        
        Args:
            metadata: 文章元数据
            
        Returns:
            是否达标
        """
        # 检查字数
        if metadata["word_count"] < 1000:
            return False
            
        # 检查可读性
        if metadata["readability_score"] < 60:
            return False
            
        # 检查关键词密度
        if metadata["keyword_density"] < 1.0 or metadata["keyword_density"] > 5.0:
            return False
            
        # 检查内容多样性
        if metadata["content_diversity"] < 0.3:
            return False
            
        return True

    async def _regenerate_article_content(self, state: ArticleState) -> Dict[str, Any]:
        """
        重新生成文章内容
        
        Args:
            state: 文章状态
            
        Returns:
            重新生成的文章内容
        """
        self.logger.info("尝试重新生成文章内容...")
        
        # 增加温度参数以增加多样性
        state.temperature = min(state.temperature + 0.2, 0.9)
        
        # 重新生成
        return await self._generate_article_content(state)
    
    def _validate_input(self, state: ArticleState) -> bool:
        """
        验证输入参数是否有效
        
        Args:
            state: 文章状态
            
        Returns:
            是否有效
        """
        # 检查大纲
        if not state.outline:
            self.logger.error("缺少文章大纲")
            return False
        
        # 输出大纲的详细信息
        self.logger.info(f"大纲状态：类型={type(state.outline)}, 章节数量={len(state.outline) if isinstance(state.outline, list) else 'N/A'}")
        
        # 检查大纲类型
        if not isinstance(state.outline, list):
            self.logger.error(f"大纲类型错误：{type(state.outline)}，应为列表")
            return False
        
        # 检查大纲章节
        if len(state.outline) == 0:
            self.logger.error("大纲不包含任何章节")
            return False
            
        # 检查文章计划
        if not state.article_plan:
            self.logger.warning("缺少文章计划，但将继续处理")
        else:
            self.logger.info(f"文章计划：类型={type(state.article_plan)}, 键={list(state.article_plan.keys()) if isinstance(state.article_plan, dict) else 'N/A'}")
            
                
            # 检查写作计划
        if "writing_plan" in state.article_plan:
            writing_plan = state.article_plan.get("writing_plan")
            self.logger.info(f"写作计划：类型={type(writing_plan)}, 键={list(writing_plan.keys()) if isinstance(writing_plan, dict) else 'N/A'}")
        else:
            self.logger.warning("文章计划中缺少写作计划字段")
        
        # 确认主题存在
        if not state.topic:
            self.logger.warning("缺少文章主题，但将使用生成的标题或默认文本")
            
        return True

    def _format_article_content(self, article_content: Dict) -> str:
        """
        将文章内容字典格式化为文本
        
        Args:
            article_content: 文章内容字典
            
        Returns:
            str: 格式化后的文章文本
        """
        text = f"# {article_content.get('title', '')}\n\n"
        
        # 检查是否有流畅型结构的content字段
        if "content" in article_content and article_content["content"]:
            # 流畅型结构，直接使用content字段
            self.logger.info("使用流畅型结构格式化文章内容")
            text += article_content["content"]
            return text
        
        # 否则使用传统章节结构格式化
        introduction = article_content.get('introduction', '')
        if introduction:
            text += f"{introduction}\n\n"
            
        for section in article_content.get('sections', []):
            title = section.get('title', '')
            content = section.get('content', '')
            
            if title:
                text += f"## {title}\n\n"
            if content:
                text += f"{content}\n\n"
                
        conclusion = article_content.get('conclusion', '')
        if conclusion:
            text += f"## 结论\n\n{conclusion}\n\n"
            
        return text
    
    def _attempt_json_correction(self, text: str) -> str:
        """
        增强版JSON修复函数，使用json_repair库处理
        
        Args:
            text: 可能包含错误的JSON文本
            
        Returns:
            str: 修复后的JSON文本
        """
        logger = self._get_logger()
        
        # 输入验证
        if not text or not isinstance(text, str):
            logger.error("输入文本无效")
            raise ValueError("输入文本必须是非空字符串")
            
        # 预处理文本
        text = text.strip()
        
        # 移除无效控制字符
        control_chars_pattern = r'[\x00-\x08\x0B\x0C\x0E-\x1F]'
        if re.search(control_chars_pattern, text):
            text = re.sub(control_chars_pattern, '', text)
            logger.info("已移除文本中的无效控制字符")
        
        # 处理代码块格式
        if text.startswith("```json") or text.startswith("```"):
            text = re.sub(r"^```(?:json)?\s*", "", text)
            text = re.sub(r"\s*```$", "", text)
            logger.info("移除了代码块标记")
        
        # 1. 使用json_repair修复
        try:
            repaired_content = json_repair.repair_json(text)
            logger.info("使用json_repair成功修复JSON")
            return repaired_content
        except Exception as e:
            logger.warning(f"json_repair修复失败: {str(e)}")
        
        # 2. 如果json_repair失败，检查是否能找到有效JSON片段
        start_idx = text.find('{')
        end_idx = text.rfind('}')
        
        if start_idx >= 0 and end_idx > start_idx:
            json_text = text[start_idx:end_idx + 1]
            try:
                # 再次尝试修复提取的JSON部分
                repaired_content = json_repair.repair_json(json_text)
                logger.info("成功修复JSON片段")
                return repaired_content
            except Exception as e:
                logger.error(f"修复JSON片段失败: {str(e)}")
                
        # 所有修复尝试都失败
        logger.error("所有JSON修复尝试失败")
        raise ValueError("无法修复JSON格式")

    def _parse_json_response(self, response_text: str) -> Dict[str, Any]:
        """
        解析模型返回的JSON响应
        
        Args:
            response_text: 模型的响应文本
            
        Returns:
            解析后的字典
        """
        try:
            # 1. 首先尝试直接解析
            try:
                return json.loads(response_text)
            except json.JSONDecodeError:
                self.logger.info("直接JSON解析失败，尝试进一步处理")
            
            # 2. 尝试从代码块中提取JSON
            json_match = re.search(r'```(?:json)?\s*([\s\S]*?)\s*```', response_text)
            if json_match:
                try:
                    return json.loads(json_match.group(1))
                except json.JSONDecodeError:
                    self.logger.info("代码块中的JSON解析失败，尝试修复")
            
            # 3. 使用json_repair修复
            try:
                fixed_json = self._attempt_json_correction(response_text)
                return json.loads(fixed_json)
            except Exception as e:
                self.logger.error(f"JSON修复失败: {str(e)}")
            
            # 4. 尝试抽取JSON部分
            json_obj = self._advanced_json_extraction(response_text)
            if json_obj:
                return json_obj
                
            raise ValueError("无法解析JSON响应")
                
        except Exception as e:
            self.logger.error(f"解析JSON响应时发生错误: {str(e)}")
            raise ValueError(f"无法解析JSON响应: {str(e)}")
    
    def _advanced_json_extraction(self, text: str) -> Dict[str, Any]:
        """
        使用更高级的方法从文本中提取JSON
        
        Args:
            text: 包含可能JSON的文本
            
        Returns:
            提取的JSON对象
        """
        self.logger.info("开始高级JSON提取")
        
        # 1. 尝试找到最长的匹配括号
        start_indices = [i for i, char in enumerate(text) if char == '{']
        if not start_indices:
            self.logger.error("找不到JSON开始的大括号")
            raise ValueError("无法在文本中找到JSON大括号")
            
        # 对每个开始位置尝试找到匹配的结束位置
        valid_jsons = []
        for start in start_indices:
            # 跟踪括号的嵌套
            depth = 0
            for i in range(start, len(text)):
                if text[i] == '{':
                    depth += 1
                elif text[i] == '}':
                    depth -= 1
                    if depth == 0:  # 找到了匹配的结束括号
                        json_str = text[start:i+1]
                        try:
                            # 尝试使用json_repair修复并解析
                            fixed_json = json_repair.repair_json(json_str)
                            json_obj = json.loads(fixed_json)
                            valid_jsons.append((json_str, json_obj))
                            break
                        except Exception:
                            # 继续查找，可能不是有效的JSON
                            pass
        
        # 如果找到了有效的JSON，返回最长的一个
        if valid_jsons:
            valid_jsons.sort(key=lambda x: len(x[0]), reverse=True)
            self.logger.info(f"找到有效的JSON，长度: {len(valid_jsons[0][0])}")
            return valid_jsons[0][1]
        
        # 找不到有效JSON
        self.logger.error("无法提取有效JSON")
        raise ValueError("无法从文本中提取有效JSON")

    def _ensure_text_content(self, value, field_name="field") -> str:
        """
        确保值为文本内容，处理不同类型的值
        
        Args:
            value: 需要确保为文本的值
            field_name: 字段名称，用于日志记录
            
        Returns:
            str: 转换后的文本内容
        """
        if isinstance(value, str):
            return value.strip()
        elif isinstance(value, dict):
            # 如果是字典，尝试提取文本内容
            self.logger.warning(f"发现{field_name}是字典类型，尝试提取文本")
            if "text" in value:
                return str(value["text"]).strip()
            elif "content" in value:
                return str(value["content"]).strip()
            else:
                return json.dumps(value, ensure_ascii=False)
        elif isinstance(value, list):
            # 如果是列表，尝试连接所有项
            self.logger.warning(f"发现{field_name}是列表类型，尝试连接")
            text_items = []
            for item in value:
                if isinstance(item, str):
                    text_items.append(item)
                elif isinstance(item, dict) and ("text" in item or "content" in item):
                    text_items.append(str(item.get("text", item.get("content", ""))))
                else:
                    text_items.append(str(item))
            return "\n".join(text_items).strip()
        else:
            # 其他类型转换为字符串
            self.logger.warning(f"发现{field_name}是非字符串类型: {type(value)}")
            return str(value).strip() 

    