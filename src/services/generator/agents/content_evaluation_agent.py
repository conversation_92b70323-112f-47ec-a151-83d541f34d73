from typing import Dict, List, Any, Optional
import logging
import json
import re
import time
import os
import traceback

import json_repair  # 添加json_repair库导入

from src.services.generator.state import ArticleState, ArticleStatus
from src.services.generator.agents.base import AgentOutput, BaseAgent
from src.services.generator.text_generator import LLMAdapter, LLMRole
from src.services.generator.prompts import content_evaluation_prompt

# 配置日志
logger = logging.getLogger("content_evaluation_agent")

class ContentEvaluationAgent(BaseAgent):
    """内容评估代理 - 评估文章内容是否需要更深度的搜索"""
    
    def __init__(self):
        """初始化ContentEvaluationAgent"""
        # 首先调用父类初始化
        super().__init__(
            name="内容评估代理",
            description="评估文章内容是否需要更深度搜索并生成相关查询"
        )
        
        # 设置日志记录器 - 确保logger不为None
        self.logger = logging.getLogger("ContentEvaluationAgent")
        
        # 初始化LLM适配器
        try:
            self.llm_adapter = LLMAdapter(default_provider="deepseek_reasoner")
            self._get_logger().info("初始化内容评估代理")
        except Exception as e:
            self._get_logger().error(f"初始化LLM适配器失败: {str(e)}")
            self.llm_adapter = None
        
        # 设置最大搜索轮次为可配置变量
        self.max_search_rounds = 3
        self._get_logger().info(f"最大搜索轮次设置为：{self.max_search_rounds}")
    
    def _get_logger(self):
        """获取安全的日志记录器对象
        即使self.logger为None也返回一个可用的记录器
        
        Returns:
            logging.Logger: 可用的日志记录器
        """
        if self.logger is None:
            return logging.getLogger("ContentEvaluationAgent")
        return self.logger
    
    async def run(self, state: ArticleState) -> AgentOutput:
        """执行内容评估
        
        Args:
            state: 当前文章状态
            
        Returns:
            代理输出，包含评估结果和可能的搜索查询
        """
        try:
            start_time = time.time()
            self._get_logger().info("开始评估文章内容...")
            
            # 获取当前搜索轮次，仅使用search_rounds字段
            current_round = 0
            if hasattr(state, "search_rounds") and state.search_rounds is not None:
                current_round = state.search_rounds
                self._get_logger().info(f"当前搜索轮次: {current_round}")
            
            # 检查是否已有评估轮次计数，确保与搜索轮次保持一致
            if not hasattr(state, "evaluation_rounds") or state.evaluation_rounds is None:
                # 如果没有评估轮次，但有搜索轮次，设置评估轮次等于搜索轮次
                if hasattr(state, "search_rounds") and state.search_rounds > 0:
                    state.evaluation_rounds = state.search_rounds
                    self._get_logger().info(f"初始化评估轮次等于搜索轮次: {state.search_rounds}")
                else:
                    # 都没有，初始化为1
                    state.evaluation_rounds = 1
                    self._get_logger().info("初始化评估轮次为1")
            
            # 检查评估轮次是否与搜索轮次同步
            if current_round > 0 and state.evaluation_rounds != current_round:
                self._get_logger().info(f"评估轮次({state.evaluation_rounds})与搜索轮次({current_round})不同步，更新评估轮次")
                state.evaluation_rounds = current_round
            
            # 使用当前评估轮次，不自动递增
            current_evaluation_round = state.evaluation_rounds
            self._get_logger().info(f"当前评估轮次: {current_evaluation_round}, 当前搜索轮次: {current_round}")
            
            # 确保评估轮次不超过最大值
            if current_evaluation_round > self.max_search_rounds:
                self._get_logger().info(f"评估轮次({current_evaluation_round})超过最大值{self.max_search_rounds}，设置为{self.max_search_rounds}")
                current_evaluation_round = self.max_search_rounds
                state.evaluation_rounds = self.max_search_rounds
            
            # 检查是否已达到最大评估轮次
            if current_evaluation_round >= self.max_search_rounds:
                self._get_logger().info(f"已达到最大评估轮次 ({self.max_search_rounds}轮)，跳过深度搜索，当前搜索轮次: {current_round}")
                state.needs_deeper_search = False
                return AgentOutput(success=True, state=state)
            
            # 检查搜索轮次上限
            if current_round > self.max_search_rounds:
                self._get_logger().info(f"已达到最大搜索轮次 ({current_round}/{self.max_search_rounds})，跳过深度搜索")
                state.needs_deeper_search = False
                return AgentOutput(success=True, state=state)
            
            # 验证输入
            if not self._validate_input(state):
                return AgentOutput(
                    success=False,
                    state=state,
                    error="缺少文章内容或主题"
                )
            
            # 执行内容评估
            evaluation_result = await self._evaluate_content(state)
            
            # 确保有明确的搜索查询
            needs_deeper_search = evaluation_result.get("needs_deeper_search", False)
            search_queries = evaluation_result.get("search_queries", [])
            
            # 特殊处理：第一轮搜索完成后，增加深度搜索概率
            # 不再强制执行，而是通过评估结果来提高概率
            if current_round == 1 and not needs_deeper_search:
                # 检查知识空缺，如果有明显空缺但模型未建议搜索，有50%概率仍执行搜索
                knowledge_gaps = evaluation_result.get("knowledge_gaps", [])
                if knowledge_gaps and len(knowledge_gaps) >= 2 and search_queries:
                    self._get_logger().info(f"虽然未建议深度搜索，但检测到{len(knowledge_gaps)}个知识空缺，增加搜索概率")
                    import random
                    if random.random() < 0.5:  # 50%概率做深度搜索
                        needs_deeper_search = True
                        self._get_logger().info("基于知识空缺决定进行深度搜索")
            
           
            if needs_deeper_search and not search_queries:
                self._get_logger().info("评估需要深度搜索但未生成查询，使用备选方法")
                # 尝试使用之前的搜索查询
                if hasattr(state, "search_history") and state.search_history:
                    previous_queries = []
                    # 确保search_history是列表且非空
                    if isinstance(state.search_history, list) and len(state.search_history) > 0:
                        # 取最后一个历史记录
                        last_history = state.search_history[-1]
                        if isinstance(last_history, dict) and "queries" in last_history:
                            previous_queries = last_history.get("queries", [])
                    
                    if previous_queries:
                        search_queries = previous_queries[:3]  # 使用前3个查询
                        self._get_logger().info(f"使用上一轮的{len(search_queries)}个查询作为深度搜索查询")
                
                # 如果仍然没有查询，使用主题作为查询
                if not search_queries and hasattr(state, "topic") and state.topic:
                    search_queries = [
                        state.topic,
                        f"如何{state.topic}" if not state.topic.startswith("如何") else state.topic,
                        f"{state.topic}详细分析" if len(state.topic) > 3 else state.topic
                    ]
                    self._get_logger().info(f"基于主题'{state.topic}'生成了{len(search_queries)}个备选搜索查询")
            
            # 只有当标志为True且有查询时才真正执行深度搜索
            if needs_deeper_search and search_queries:
                state.needs_deeper_search = True
                state.deeper_search_queries = search_queries
                
                # 获取reasons，如果不存在则创建空列表
                reasons = evaluation_result.get("reasons", [])
                if not isinstance(reasons, list):
                    reasons = [str(reasons)] if reasons else []
                state.deeper_search_reasons = reasons
                
                # 获取knowledge_gaps，如果不存在则创建空列表
                knowledge_gaps = evaluation_result.get("knowledge_gaps", [])
                if not isinstance(knowledge_gaps, list):
                    knowledge_gaps = [str(knowledge_gaps)] if knowledge_gaps else []
                state.knowledge_gaps = knowledge_gaps
                
                humanizing_suggestions = evaluation_result.get("humanizing_suggestions", [])
                if not isinstance(humanizing_suggestions, list):
                    humanizing_suggestions = [str(humanizing_suggestions)] if humanizing_suggestions else []
                state.humanizing_suggestions = humanizing_suggestions
                
                improvement_suggestion = evaluation_result.get("improvement_suggestion", "")
                if not isinstance(improvement_suggestion, str):
                    improvement_suggestion = str(improvement_suggestion)
                state.improvement_suggestion = improvement_suggestion
                
                
                # 使用运算符递增轮次，而非固定值
                next_round = current_round + 1
                # 确保不超过最大轮次
                next_round = min(next_round, self.max_search_rounds)
                
                state.evaluation_rounds = next_round
                state.search_rounds = next_round
                self._get_logger().info(f"递增搜索和评估轮次到{state.search_rounds}")
                
                # 记录评估结果
                self._get_logger().info(f"需要进行深度搜索，生成了 {len(search_queries)} 个查询，当前搜索轮次: {current_round}, 下一轮: {state.search_rounds}")
                for idx, query in enumerate(search_queries):
                    self._get_logger().info(f"深度搜索查询 {idx+1}: {query}")
                
                # 记录知识空缺
                if knowledge_gaps:
                    self._get_logger().info(f"识别到 {len(knowledge_gaps)} 个知识空缺")
                    for idx, gap in enumerate(knowledge_gaps[:3]):
                        self._get_logger().info(f"知识空缺 {idx+1}: {gap}")
                
                # 将状态更新为搜索中，使工作流程返回到搜索节点
                state.update_status(ArticleStatus.SEARCHING)
            else:
                state.needs_deeper_search = False
                if not search_queries:
                    self._get_logger().info(f"未生成有效的搜索查询，不执行深度搜索，当前搜索轮次: {current_round}")
                else:
                    self._get_logger().info(f"文章内容已足够深入，不需要额外搜索，当前搜索轮次: {current_round}")
                
                # 将状态更新为生成图片，继续到下一步
                state.update_status(ArticleStatus.EDITING)
                
            # 计算执行时间
            end_time = time.time()
            execution_time = end_time - start_time
            self._get_logger().info(f"内容评估代理完成执行，耗时: {execution_time:.2f}秒")
            
            # 返回成功结果
            return AgentOutput(
                success=True,
                state=state,
                execution_time=execution_time
            )
            
        except Exception as e:
            # 使用_get_logger而不是直接使用self.logger
            safe_logger = self._get_logger()
            safe_logger.error(f"内容评估代理执行过程中发生错误: {str(e)}")
            safe_logger.error(traceback.format_exc())
            
            # 发生错误时，设置为不需要深度搜索，避免卡住流程
            state.needs_deeper_search = False
            # 尝试更新状态到生成图片阶段，确保工作流程继续
            try:
                state.update_status(ArticleStatus.EDITING)
            except Exception as status_err:
                safe_logger.error(f"更新状态失败: {str(status_err)}")
            
            # 返回失败结果
            return AgentOutput(
                success=False,
                state=state,
                error=f"内容评估失败: {str(e)}"
            )
    
    async def _evaluate_content(self, state: ArticleState) -> Dict[str, Any]:
        """评估文章内容，决定是否需要更深度的搜索
        
        Args:
            state: 当前文章状态
            
        Returns:
            评估结果，包含是否需要深度搜索和建议的搜索查询
        """
        try:
            self._get_logger().info("开始评估文章内容是否需要更深度搜索...")
            
            # 构建评估提示词
            # 从article_content获取内容，而不是不存在的content属性
            article_content = state.article_content if hasattr(state, "article_content") and state.article_content else {}
            
            # 将article_content(字典)格式化为文本
            content = self._format_article_content(article_content)
            
            topic = state.topic if hasattr(state, "topic") and state.topic else ""
            outline = state.outline if hasattr(state, "outline") and state.outline else []
            keywords = state.keywords if hasattr(state, "keywords") and state.keywords else []
            
            # 获取当前搜索轮次
            current_round = 1
            if hasattr(state, "search_rounds") and state.search_rounds is not None:
                current_round = state.search_rounds
            
            # 获取当前时间信息
            current_time = None
            if hasattr(state, "current_time") and state.current_time:
                current_time = state.current_time
            else:
                # 尝试构建基础的时间信息
                from datetime import datetime
                now = datetime.now()
                current_time = {
                    "year": now.year,
                    "month": now.month,
                    "day": now.day,
                    "season": self._get_season(now.month)
                }
                self._get_logger().info(f"使用系统时间: {current_time}")
            
            # 构建提示词
            prompt = content_evaluation_prompt(
                topic=topic,
                evaluation_round=current_round,
                content=content,
                outline=outline,
                keywords=keywords,
                current_time=current_time
            )
            
            # 使用LLM评估
            self._get_logger().info(f"正在使用LLM评估文章内容(搜索轮次 {current_round}/{self.max_search_rounds})...")
            
            # 使用json格式
            response = await self.llm_adapter.ask_llm(
                prompt=prompt,
                role="content_evaluation",  # 使用字符串格式，
            )
            
            if not response:
                self._get_logger().warning("LLM返回空响应，假设不需要深度搜索")
                return {"needs_deeper_search": False}
            
            # 解析响应
            result = self._parse_evaluation_response(response)
            
            # 记录评估结果
            needs_deeper_search = result.get("needs_deeper_search", False)
            search_queries = result.get("search_queries", [])
            knowledge_gaps = result.get("knowledge_gaps", [])
            
            if needs_deeper_search:
                self._get_logger().info(f"评估结果：需要更深度搜索，生成了{len(search_queries)}个查询")
                if knowledge_gaps:
                    self._get_logger().info(f"识别到{len(knowledge_gaps)}个知识空缺")
                    for i, gap in enumerate(knowledge_gaps[:3]):  # 只记录前3个
                        self._get_logger().info(f"知识空缺{i+1}: {gap}")
            else:
                self._get_logger().info("评估结果：文章内容已足够深入，不需要额外搜索")
            
            return result
            
        except Exception as e:
            self._get_logger().error(f"评估文章内容失败: {str(e)}")
            self._get_logger().error(traceback.format_exc())
            # 返回默认结果
            return {"needs_deeper_search": False}
    
    def _format_article_content(self, content: Dict[str, Any]) -> str:
        """将文章内容格式化为文本
        
        Args:
            content: 文章内容字典
            
        Returns:
            格式化后的文章内容
        """
        if not isinstance(content, dict):
            return "文章内容无效"
        
        formatted = []
        
        # 检查是否为流畅型文章结构（带有content字段）
        if "content" in content and content["content"]:
            self._get_logger().info("检测到流畅型文章结构，使用整体内容格式化")
            # 标题
            title = content.get("title", "")
            if title:
                formatted.append(f"# {title}")
                formatted.append("")
            
            # 直接使用content字段的内容
            formatted.append(content["content"])
            
            return "\n".join(formatted)
        
        # 传统章节结构处理
        self._get_logger().info("使用传统章节结构格式化文章内容")
        
        # 标题
        title = content.get("title", "")
        if title:
            formatted.append(f"# {title}")
            formatted.append("")
        
        # 简介
        introduction = content.get("introduction", "")
        if introduction:
            formatted.append(introduction)
            formatted.append("")
        
        # 章节
        sections = content.get("sections", [])
        for section in sections:
            if isinstance(section, dict):
                section_title = section.get("title", "")
                section_content = section.get("content", "")
                
                if section_title:
                    formatted.append(f"## {section_title}")
                    formatted.append("")
                
                if section_content:
                    formatted.append(section_content)
                    formatted.append("")
        
        # 结论
        conclusion = content.get("conclusion", "")
        if conclusion:
            formatted.append("## 结论")
            formatted.append("")
            formatted.append(conclusion)
        
        return "\n".join(formatted)
    
    def _validate_input(self, state: ArticleState) -> bool:
        """验证输入是否有效
        
        Args:
            state: 文章状态
            
        Returns:
            是否有效
        """
        # 检查文章内容
        if not hasattr(state, "article_content") or not state.article_content:
            self._get_logger().warning("缺少文章内容")
            return False
        
        # 检查主题
        if not hasattr(state, "topic") or not state.topic:
            self._get_logger().warning("缺少文章主题")
            return False
        
        return True
    
    def _parse_evaluation_response(self, response: str) -> dict:
        """
        解析评估响应，提取需要深入搜索的内容、原因和搜索查询
        
        Args:
            response: 来自LLM的评估响应
            
        Returns:
            dict: 包含以下键的字典：
                - needs_deeper_search: 布尔值，是否需要更深入的搜索
                - reasons: 需要深入搜索的原因列表
                - knowledge_gaps: 知识缺口列表
                - search_queries: 搜索查询列表
        """
        logger = self._get_logger()
        logger.info("开始解析评估响应")
        
        # 处理空响应
        if not response or response.strip() == "":
            logger.warning("收到空的评估响应")
            return {
                "needs_deeper_search": False,
                "reasons": [],
                "knowledge_gaps": [],
                "search_queries": []
            }
        
        # 使用清理函数处理JSON响应
        result = self._extract_json_from_response(response)
        
        # 初始化返回值，设置默认值
        parsed_result = {
            "needs_deeper_search": False,
            "reasons": [],
            "knowledge_gaps": [],
            "search_queries": []
        }
        
        # 检查是否为空字典
        if not result:
            logger.warning("解析后的评估响应为空字典")
            return parsed_result
            
        # 处理needs_deeper_search字段
        if "needs_deeper_search" in result:
            value = result["needs_deeper_search"]
            # 处理字符串类型的布尔值
            if isinstance(value, str):
                parsed_result["needs_deeper_search"] = value.lower() in ("true", "yes", "1", "需要", "是")
            else:
                parsed_result["needs_deeper_search"] = bool(value)
        
        # 处理reasons字段
        if "reasons" in result:
            reasons = result["reasons"]
            # 如果是字符串，则按逗号或换行符分割
            if isinstance(reasons, str):
                if "," in reasons:
                    parsed_result["reasons"] = [r.strip() for r in reasons.split(",") if r.strip()]
                elif "\n" in reasons:
                    parsed_result["reasons"] = [r.strip() for r in reasons.split("\n") if r.strip()]
                else:
                    parsed_result["reasons"] = [reasons.strip()] if reasons.strip() else []
            # 如果是列表，直接使用
            elif isinstance(reasons, list):
                parsed_result["reasons"] = [str(r).strip() for r in reasons if r]
        
        # 处理knowledge_gaps字段
        if "knowledge_gaps" in result:
            gaps = result["knowledge_gaps"]
            # 如果是字符串，则按逗号或换行符分割
            if isinstance(gaps, str):
                if "," in gaps:
                    parsed_result["knowledge_gaps"] = [g.strip() for g in gaps.split(",") if g.strip()]
                elif "\n" in gaps:
                    parsed_result["knowledge_gaps"] = [g.strip() for g in gaps.split("\n") if g.strip()]
                else:
                    parsed_result["knowledge_gaps"] = [gaps.strip()] if gaps.strip() else []
            # 如果是列表，直接使用
            elif isinstance(gaps, list):
                parsed_result["knowledge_gaps"] = [str(g).strip() for g in gaps if g]
        
        # 处理search_queries字段
        if "search_queries" in result:
            queries = result["search_queries"]
            # 如果是字符串，则按逗号或换行符分割
            if isinstance(queries, str):
                if "," in queries:
                    parsed_result["search_queries"] = [q.strip() for q in queries.split(",") if q.strip()]
                elif "\n" in queries:
                    parsed_result["search_queries"] = [q.strip() for q in queries.split("\n") if q.strip()]
                else:
                    parsed_result["search_queries"] = [queries.strip()] if queries.strip() else []
            # 如果是列表，直接使用
            elif isinstance(queries, list):
                parsed_result["search_queries"] = [str(q).strip() for q in queries if q]
        
        # 记录解析结果
        logger.info(f"评估结果解析完成：需要深入搜索? {parsed_result['needs_deeper_search']}")
        logger.info(f"找到 {len(parsed_result['reasons'])} 个原因")
        logger.info(f"找到 {len(parsed_result['knowledge_gaps'])} 个知识缺口")
        logger.info(f"找到 {len(parsed_result['search_queries'])} 个搜索查询")
        
        return parsed_result
    
    def _extract_json_from_response(self, text: str) -> dict:
        """从文本中提取JSON结构
        
        使用json_repair库和多重尝试从文本中提取和解析JSON结构
        
        Args:
            text: 包含JSON的文本
            
        Returns:
            解析后的JSON对象
        """
        logger = self._get_logger()
        
        # 1. 预处理文本
        # 去除前后空白
        text = text.strip()
        
        # 移除代码块标记
        if text.startswith("```json"):
            text = text.replace("```json", "", 1)
            logger.info("移除JSON代码块标记")
        elif text.startswith("```"):
            match = re.match(r"```(\w*)\s*\n", text)
            if match:
                text = text[match.end():]
                logger.info(f"移除{match.group(1)}代码块标记")
        
        # 处理结尾的代码块标记
        if text.endswith("```"):
            text = text.replace("```", "", 1)
            logger.info("移除结尾代码块标记")
                
        # 2. 尝试使用json_repair修复和解析
        try:
            repaired_content = json_repair.loads(text)
            logger.info("使用json_repair成功解析JSON")
            return repaired_content if isinstance(repaired_content, dict) else {}
        except Exception as e:
            logger.warning(f"json_repair解析失败: {str(e)}，尝试其他方法")
            
        # 3. 尝试提取外层JSON对象
        outer_json_match = re.search(r'\{[\s\S]*\}', text)
        if outer_json_match:
            outer_json = outer_json_match.group(0)
            logger.info("提取出外层JSON对象，尝试解析")
            try:
                # 使用json_repair尝试修复提取的JSON
                repaired_content = json_repair.loads(outer_json)
                logger.info("成功修复并解析提取的JSON对象")
                return repaired_content if isinstance(repaired_content, dict) else {}
            except Exception as e:
                logger.warning(f"修复提取的JSON对象失败: {str(e)}，继续尝试其他方法")
     
    
    def _validate_evaluation_result(self, result: Dict[str, Any]) -> Dict[str, Any]:
        """验证和规范化评估结果
        
        Args:
            result: 解析后的原始评估结果
            
        Returns:
            验证和规范化后的评估结果
        """
        # 创建一个新的结果字典，确保包含所有必要的字段
        validated = {}
        
        # 检查并设置needs_deeper_search字段
        if "needs_deeper_search" in result:
            # 确保值为布尔类型
            if isinstance(result["needs_deeper_search"], bool):
                validated["needs_deeper_search"] = result["needs_deeper_search"]
            elif isinstance(result["needs_deeper_search"], str):
                validated["needs_deeper_search"] = result["needs_deeper_search"].lower() in ["true", "yes", "1", "需要", "是"]
            else:
                validated["needs_deeper_search"] = bool(result["needs_deeper_search"])
        else:
            # 如果没有此字段但有search_queries，则认为需要搜索
            has_queries = "search_queries" in result and isinstance(result["search_queries"], list) and len(result["search_queries"]) > 0
            validated["needs_deeper_search"] = has_queries
        
        # 检查并设置search_queries字段
        if "search_queries" in result and isinstance(result["search_queries"], list):
            # 过滤空字符串和None值，并确保每个查询都是字符串
            queries = [str(query).strip() for query in result["search_queries"] if query is not None and str(query).strip()]
            validated["search_queries"] = queries[:10]  # 限制最多10个查询
        else:
            validated["search_queries"] = []
        
        # 检查并设置knowledge_gaps字段
        if "knowledge_gaps" in result and isinstance(result["knowledge_gaps"], list):
            # 过滤空字符串和None值，并确保每个知识空缺都是字符串
            gaps = [str(gap).strip() for gap in result["knowledge_gaps"] if gap is not None and str(gap).strip()]
            validated["knowledge_gaps"] = gaps
        else:
            validated["knowledge_gaps"] = []
        
        # 检查并设置reasons字段
        if "reasons" in result and isinstance(result["reasons"], list):
            # 过滤空字符串和None值，并确保每个原因都是字符串
            reasons = [str(reason).strip() for reason in result["reasons"] if reason is not None and str(reason).strip()]
            validated["reasons"] = reasons
        else:
            validated["reasons"] = []
        
        # 如果设置了需要搜索但没有查询，则设置为不需要搜索
        if validated["needs_deeper_search"] and not validated["search_queries"]:
            self._get_logger().warning("评估结果表明需要深度搜索，但没有提供搜索查询，修正为不需要搜索")
            validated["needs_deeper_search"] = False
        
        return validated
    
    def _get_season(self, month: int) -> str:
        """根据月份获取季节
        
        Args:
            month: 月份(1-12)
            
        Returns:
            季节名称
        """
        if not isinstance(month, int) or month < 1 or month > 12:
            return "未知季节"
            
        if month in [3, 4, 5]:
            return "春季"
        elif month in [6, 7, 8]:
            return "夏季"
        elif month in [9, 10, 11]:
            return "秋季"
        else:  # month in [12, 1, 2]
            return "冬季"
               