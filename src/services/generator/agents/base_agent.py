"""
文章代理基类，为所有特定的文章生成代理提供通用功能和接口。
"""

from typing import Dict, List, Any, Optional
import logging
import time
from abc import ABC, abstractmethod

from src.services.generator.state import ArticleState

# 配置日志
logger = logging.getLogger(__name__)

class ArticleAgent(ABC):
    """
    所有文章生成代理的基类。
    提供通用方法和属性，定义代理接口。
    """
    
    def __init__(self, name: str, description: str):
        """
        初始化代理。
        
        Args:
            name: 代理名称
            description: 代理描述
        """
        self.name = name
        self.description = description
        self.execution_count = 0
        self.execution_time = 0
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
    
    @abstractmethod
    async def run(self, state: ArticleState) -> ArticleState:
        """
        运行代理的主要方法，由具体子类实现。
        
        Args:
            state: 文章状态对象，包含所有相关信息
            
        Returns:
            更新后的文章状态对象
        """
        pass
    
    def get_name(self) -> str:
        """获取代理名称"""
        return self.name
    
    def get_description(self) -> str:
        """获取代理描述"""
        return self.description
    
    def _update_article_state(self, article_state: Dict[str, Any], updates: Dict[str, Any]) -> Dict[str, Any]:
        """
        更新文章状态字典。
        
        Args:
            article_state: 当前文章状态
            updates: 要应用的更新
            
        Returns:
            更新后的文章状态
        """
        for key, value in updates.items():
            article_state[key] = value
        return article_state
    
    def _get_from_article_state(self, article_state: Dict[str, Any], key: str, default: Any = None) -> Any:
        """
        从文章状态中安全地获取值。
        
        Args:
            article_state: 文章状态字典
            key: 要获取的键
            default: 如果键不存在，返回的默认值
            
        Returns:
            键对应的值或默认值
        """
        return article_state.get(key, default)
    
    def _check_article_state(self, article_state: Dict[str, Any], required_keys: List[str]) -> bool:
        """
        检查文章状态是否包含所有必需的键。
        
        Args:
            article_state: 文章状态字典
            required_keys: 必需的键列表
            
        Returns:
            如果所有必需的键都存在，则为True；否则为False
        """
        missing_keys = [key for key in required_keys if key not in article_state]
        if missing_keys:
            self.logger.warning(f"文章状态缺少必需的键: {', '.join(missing_keys)}")
            return False
        return True
    
    def _log_start(self, message: str = None):
        """记录代理开始执行的消息"""
        if message is None:
            message = f"{self.name}开始执行"
        self.logger.info(message)
    
    def _log_end(self, message: str = None):
        """记录代理完成执行的消息"""
        if message is None:
            message = f"{self.name}执行完成"
        self.logger.info(message)
    
    def _log_error(self, message: str, exception: Exception = None):
        """记录错误消息"""
        if exception:
            self.logger.error(f"{message}: {str(exception)}", exc_info=True)
        else:
            self.logger.error(message)
    
    async def execute(self, state: ArticleState) -> ArticleState:
        """执行代理，包含通用处理逻辑
        
        Args:
            state: 当前文章状态
            
        Returns:
            更新后的文章状态
        """
        self._log_start()
        start_time = time.time()
        
        try:
            # 执行具体代理逻辑
            result = await self.run(state)
            
            # 更新执行统计信息
            execution_time = time.time() - start_time
            self.execution_count += 1
            self.execution_time += execution_time
            
            self._log_end()
            
            return result
        except Exception as e:
            self._log_error(f"代理执行失败: {str(e)}")
            state.add_error(self.name, f"代理执行失败: {str(e)}")
            return state
    
    def to_dict(self) -> Dict[str, Any]:
        """将代理转换为字典表示
        
        Returns:
            代理的字典表示
        """
        return {
            "name": self.name,
            "description": self.description,
            "type": self.__class__.__name__,
            "execution_count": self.execution_count,
            "execution_time": self.execution_time
        } 