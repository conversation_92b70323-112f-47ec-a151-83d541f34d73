"""
Agent基础类和数据结构
"""
from typing import Dict, List, Any, Optional, Union
from pydantic import BaseModel

# 导入ArticleState
from src.services.generator.state import ArticleState

class AgentOutput(BaseModel):
    """Agent执行结果"""
    success: bool
    state: ArticleState
    error: Optional[str] = None
    execution_time: Optional[float] = 0.0
    quality_assessment: Optional[Dict[str, Any]] = None
    
class BaseAgent:
    """Agent基类"""
    def __init__(self, name: str = "", description: str = ""):
        self.name = name
        self.description = description
        self.logger = None  # 会在子类中初始化
        
    async def run(self, agent_input: ArticleState) -> AgentOutput:
        """
        运行Agent
        
        Args:
            agent_input: Agent输入数据，ArticleState实例
            
        Returns:
            AgentOutput: 包含是否成功以及更新后的状态
        """
        raise NotImplementedError("子类必须实现run方法")
    
    def _validate_input(self, input_data: ArticleState) -> bool:
        """
        验证输入数据的有效性
        
        Args:
            input_data: 输入数据，ArticleState实例
            
        Returns:
            是否有效
        """
        return True
        
    def _calculate_quality_score(self, data: ArticleState) -> float:
        """
        计算质量评分
        
        Args:
            data: 待评分数据，ArticleState实例
            
        Returns:
            质量评分 (0.0-1.0)
        """
        return 0.8
        
    def _get_logger(self):
        """
        获取当前Agent的logger
        
        Returns:
            logging.Logger: 日志记录器
        """
        return self.logger 