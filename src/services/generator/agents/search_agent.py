"""
搜索代理：根据关键词和内容方向在网上搜索相关参考资料
"""
from typing import Dict, List, Any, Optional, Tuple
import logging
import re
import time
import random
from collections import Counter

from src.services.generator.state import ArticleState, ArticleStatus
from src.services.generator.agents.base_agent import ArticleAgent
from src.services.generator.tools.network_tools import SearchEngineTool
from src.services.generator.agents.base import AgentOutput, BaseAgent
from src.services.generator.models import ArticleGenerationState
from src.services.generator.text_generator import LLMAdapter, LLMRole

logger = logging.getLogger("search_agent")

class SearchAgent(BaseAgent):
    """
    负责生成和执行搜索策略的Agent
    """
    def __init__(self):
        """
        初始化SearchAgent
        """
        super().__init__(
            name="搜索代理",
            description="根据文章主题和关键词搜索高质量相关资料"
        )
        self.llm_adapter = LLMAdapter(default_provider="deepseek")
        self.logger = logging.getLogger(__name__)
        self.search_engine_tool = SearchEngineTool()
        # 设置最大结果数 - 修正为合理的数量
        self.max_results_per_query = 4   # 每个查询最多返回4个搜索结果
        
    async def run(self, state: ArticleState) -> AgentOutput:
        """执行搜索策略"""
        try:
            # 验证输入
            if not state.topic:
                raise ValueError("缺少文章主题")
            
            # 确保search_rounds字段存在且正确初始化
            if not hasattr(state, "search_rounds") or state.search_rounds is None:
                logger.warning("search_rounds字段不存在或为None，初始化为1")
                state.search_rounds = 1
            elif state.search_rounds == 0:
                # 如果search_rounds为0，更新为1
                logger.warning("search_rounds字段为0，更新为1")
                state.search_rounds = 1
            
            # 确定当前轮次（不递增，直接使用当前值）
            current_round = state.search_rounds
            logger.info(f"当前轮次: {current_round}")
            
            # 检查是否从评估代理传入了轮次信息
            if hasattr(state, "evaluation_rounds") and state.evaluation_rounds > 0:
                # 如果有评估轮次，检查搜索轮次是否与评估轮次匹配
                if state.search_rounds != state.evaluation_rounds:
                    logger.info(f"根据评估轮次({state.evaluation_rounds})更新搜索轮次")
                    state.search_rounds = state.evaluation_rounds
                    logger.info(f"搜索轮次已更新: {state.search_rounds}")
            
            # 使用当前已确定的轮次，不要递增
            search_round = state.search_rounds
            logger.info(f"执行搜索轮次 {search_round}")
            
            # 获取搜索查询
            queries = []
            
            if search_round > 1 and state.deeper_search_queries and len(state.deeper_search_queries) > 0:
                # 使用内容评估代理生成的深度搜索查询
                logger.info(f"使用内容评估代理提供的{len(state.deeper_search_queries)}个深度搜索查询")
                queries = state.deeper_search_queries
                # 清空深度搜索查询，防止重复使用
                state.deeper_search_queries = []
            # 优先使用planning_agent生成的查询
            elif state.search_queries and len(state.search_queries) > 0:
                logger.info(f"使用planning_agent提供的{len(state.search_queries)}个预定义搜索查询")
                queries = state.search_queries
          
            
            # 记录生成的查询
            for i, query in enumerate(queries[:8]):
                logger.info(f"查询[{i+1}]: {query}")
            
            # 限制查询数量为前8个
            queries = queries[:8]
            all_results = []
            
            # 使用多个搜索引擎
            search_engines = ['baidu']
            logger.info(f"使用多个搜索引擎进行查询: {', '.join(search_engines)}")
            
            # 记录本轮的搜索历史
            current_search_history = {
                "round": search_round,
                "timestamp": time.time(),
                "queries": queries.copy(),
                "results": []
            }
            
            # 对每个查询执行搜索，轮流使用不同搜索引擎
            for i, query in enumerate(queries):
                # 选择搜索引擎，轮流使用
                search_engine = search_engines[0]
                try:
                    logger.info(f"使用{search_engine}搜索: {query[:50]}...")
                    results = await self.search_engine_tool.execute(
                        {
                            "query": query,
                            "engine": search_engine,
                            "num_results": self.max_results_per_query  # 增加每个查询的结果数
                        },
                        {}  # 空状态字典
                    )
                    if "results" in results and results["results"]:
                        query_results = results["results"]
                        # 添加查询信息到结果中，便于追踪
                        for result in query_results:
                            result["search_query"] = query
                            result["search_round"] = search_round
                            result["search_engine"] = search_engine
                            
                        # 直接使用所有搜索结果，不基于内容长度过滤
                        all_results.extend(query_results)
                        # 添加到当前搜索历史
                        current_search_history["results"].extend(query_results)
                        logger.info(f"从{search_engine}获取到{len(query_results)}个结果，查询: '{query[:50]}'")
                    else:
                        logger.warning(f"{search_engine}搜索未返回结果，查询: '{query[:50]}'")
                        
                        # 如果当前引擎失败，尝试使用备选引擎
                        backup_engine = search_engines[(i + 1) % len(search_engines)]
                        logger.info(f"尝试使用备选引擎{backup_engine}搜索: {query[:50]}...")
                        
                        try:
                            backup_results = await self.search_engine_tool.execute(
                                {
                                    "query": query,
                                    "engine": backup_engine,
                                    "num_results": self.max_results_per_query
                                },
                                {}  # 空状态字典
                            )
                            
                            if "results" in backup_results and backup_results["results"]:
                                backup_query_results = backup_results["results"]
                                # 添加查询信息
                                for result in backup_query_results:
                                    result["search_query"] = query
                                    result["search_round"] = search_round
                                    result["search_engine"] = backup_engine
                                
                                all_results.extend(backup_query_results)
                                current_search_history["results"].extend(backup_query_results)
                                logger.info(f"从备选引擎{backup_engine}获取到{len(backup_query_results)}个结果")
                        except Exception as be:
                            logger.warning(f"备选引擎{backup_engine}也搜索失败: {str(be)}")
                except Exception as e:
                    logger.warning(f"{search_engine}搜索失败: {str(e)}，查询: '{query[:50]}'")
                    continue
                
        
            
            # 添加搜索历史记录
            if not hasattr(state, "search_history") or state.search_history is None:
                state.search_history = []
            state.search_history.append(current_search_history)
            
            # 确保不重置搜索轮次，保持轮次的一致性
            logger.info(f"搜索完成，保持搜索轮次为: {state.search_rounds}")
            
            # 去重结果
            unique_results = []
            seen_urls = set()
            for result in all_results:
                url = result.get("url", "")
                if url and url not in seen_urls:
                    seen_urls.add(url)
                    unique_results.append(result)
            
            logger.info(f"去重后剩余{len(unique_results)}/{len(all_results)}个结果")
            
            # 选择最相关的前10个结果
            final_results = self._select_best_results(unique_results, state.topic, 10)
            logger.info(f"选择了{len(final_results)}个最相关的结果")
            
            # 更新状态
            state.search_results = final_results
            
            # 保存所有搜索结果的累积列表
            if not hasattr(state, "all_search_results") or state.all_search_results is None:
                state.all_search_results = []
            state.all_search_results.extend(final_results)
            
            # 再次确认search_rounds已正确设置
            logger.info(f"搜索完成，当前搜索轮次: {state.search_rounds}")
            
            # 更新搜索查询列表（可能已经被消耗）
            state.search_queries = []
            
            # 记录搜索成功状态
            state.search_success = len(final_results) > 0
            
            # 记录搜索统计信息
            search_stats = self._get_search_statistics(final_results)
            logger.info(f"搜索统计: {search_stats}")
            
            # 更新状态为搜索完成
            state.update_status(ArticleStatus.SEARCH_COMPLETED)
            
            return AgentOutput(success=True, state=state)
            
        except Exception as e:
            logger.error(f"搜索执行失败: {str(e)}", exc_info=True)
            state.search_success = False
            state.add_error("search", f"搜索失败: {str(e)}")
            return AgentOutput(success=False, state=state, error=str(e))
        
    def _validate_input(self, state: ArticleGenerationState) -> bool:
        """
        验证输入参数是否有效
        """
        if not state.topic:
            self.logger.error("缺少文章主题")
            return False
            
        return True


    def _get_search_statistics(self, results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        生成简单的搜索统计信息，不做深度分析
        
        Args:
            results: 搜索结果列表
            
        Returns:
            简单的搜索统计信息
        """
        self.logger.info("生成搜索统计信息...")
        
        if not results:
            self.logger.warning("没有搜索结果可统计")
            return {
                "total_results": 0,
                "engines_used": [],
                "queries_used": []
            }
        
        # 统计结果数量
        total_items = sum(len(result.get("results", [])) for result in results)
        
        # 提取所有搜索结果的源域名
        domains = set()
        for result in results:
            for item in result.get("results", []):
                if "domain" in item:
                    domains.add(item["domain"])
                elif "source" in item:
                    domains.add(item["source"])
        
        # 提取使用的搜索引擎
        engines_used = set()
        for result in results:
            if "engine" in result:
                engines_used.add(result["engine"])
        
        # 提取查询关键词
        queries = [result.get("query", "") for result in results if "query" in result]
        
        # 构建统计信息
        stats = {
            "total_results": total_items,
            "unique_domains": len(domains),
            "domains": list(domains)[:10],
            "engines_used": list(engines_used),
            "queries_used": queries
        }
        
        self.logger.info(f"搜索统计：{total_items}个结果，{len(domains)}个来源，{len(engines_used)}个搜索引擎")
        return stats

   
    def _select_best_results(self, results: List[Dict[str, Any]], topic: str, limit: int = 10) -> List[Dict[str, Any]]:
        """
        从搜索结果中选择最相关的内容
        
        Args:
            results: 搜索结果列表
            topic: 文章主题
            limit: 最大结果数
            
        Returns:
            最相关的结果列表，不超过limit个
        """
        if not results:
            self.logger.warning("没有搜索结果可选择")
            return []
            
        self.logger.info(f"开始从{len(results)}个搜索结果中选择最佳结果")
        
        # 扩展的无价值网站黑名单
        blacklisted_domains = [
            # 热搜榜类网站
            "top.baidu.com",           # 百度热搜榜
            "tophub.today",            # 今日热榜
            "hot.weibo.com",           # 微博热搜
            "s.weibo.com/top",         # 微博热搜榜
            "zhihu.com/hot",           # 知乎热榜
            "rank.chinaz.com",         # 站长之家排行榜
            "resou.today",             # 热搜榜
            "douyin.com/hot",          # 抖音热榜
            "tieba.baidu.com/hottopic", # 贴吧热议
            "board?platform=pc",       # 百度热搜板块
            "/board?",                 # 各种热搜板块
            "hot-list",                # 热榜相关
            "trending",                # 趋势榜
            "hotrank",                 # 热门排行
            
            # 广告和垃圾内容网站
            "ad.doubleclick.net",      # 广告网站
            "googleads.g.doubleclick.net", # 谷歌广告
            "pagead2.googlesyndication.com", # 谷歌广告联盟
            "amazon-adsystem.com",     # 亚马逊广告
            "facebook.com/tr",         # Facebook追踪
            
            # 低质量聚合网站
            "so.com",                  # 360搜索结果页
            "sogou.com",               # 搜狗搜索结果页
            "sm.cn",                   # 神马搜索结果页
            "haosou.com",              # 好搜结果页
            
            # 其他无价值页面
            "login",                   # 登录页面
            "register",                # 注册页面
            "404",                     # 404页面
            "error",                   # 错误页面
        ]
        
        # 高质量域名加分列表
        high_quality_domains = [
            # 知识类网站
            "zhihu.com/question",      # 知乎问答
            "zhihu.com/p/",            # 知乎文章
            "baike.baidu.com",         # 百度百科
            "wiki",                    # 各类维基
            "wikipedia",               # 维基百科
            
            # 教育和学术网站
            "edu.cn",                  # 教育网站
            "ac.cn",                   # 学术网站
            "gov.cn",                  # 政府网站
            "cnki.net",                # 中国知网
            "xueshu.baidu.com",        # 百度学术
            "scholar.google",          # 谷歌学术
            "researchgate.net",        # ResearchGate
            
            # 技术类网站
            "github.com",              # GitHub
            "stackoverflow.com",       # Stack Overflow
            "csdn.net",                # CSDN
            "cnblogs.com",             # 博客园
            "jianshu.com",             # 简书
            "segmentfault.com",        # SegmentFault
            "juejin.cn",               # 掘金
            "infoq.cn",                # InfoQ中文站
            "36kr.com",                # 36氪
            "geekbang.org",            # 极客邦
            
            # 新闻媒体网站
            "xinhuanet.com",           # 新华网
            "people.com.cn",           # 人民网
            "chinanews.com",           # 中新网
            "163.com",                 # 网易
            "sina.com.cn",             # 新浪
            "sohu.com",                # 搜狐
            "qq.com",                  # 腾讯
            
            # 专业期刊和出版社
            "springer.com",            # Springer出版社
            "sciencedirect.com",       # ScienceDirect
            "nature.com",              # Nature期刊
            "science.org",             # Science期刊
            "ieee.org",                # IEEE
        ]
        
        # 进行URL标准化，解决搜狗返回link vs 百度返回url的问题
        for result in results:
            if "link" in result and "url" not in result:
                result["url"] = result["link"]
                
            # 如果域名为空，尝试提取
            if not result.get("domain") and result.get("url"):
                domain_match = re.search(r'https?://([^/]+)', result["url"])
                if domain_match:
                    result["domain"] = domain_match.group(1)
        
        # 预先过滤黑名单域名和内容为空的结果
        filtered_results = []
        for result in results:
            url = result.get('url', '')
            domain = result.get('domain', '')
            
            # 检查是否为黑名单域名
            is_blacklisted = False
            url_lower = url.lower()
            for blacklisted_domain in blacklisted_domains:
                if blacklisted_domain in url_lower:
                    self.logger.info(f"过滤黑名单网页: {url}")
                    is_blacklisted = True
                    break
            
            if is_blacklisted:
                continue
                
            # 检查内容是否太短
            snippet = result.get('snippet', '')
            title = result.get('title', '')
            
            # 过滤标题或摘要过短的结果
            if len(title) < 3 or len(snippet) < 20:
                self.logger.info(f"过滤内容过短的网页: {url}")
                continue
                
            # 添加到有效结果
            filtered_results.append(result)
            
        # 如果结果数量已经小于等于限制，直接返回所有结果
        if len(filtered_results) <= limit:
            return filtered_results
            
        # 计算每个结果的相关性得分
        scored_results = []
        topic_words = set(re.findall(r'\w+', topic.lower()))
        
        for result in filtered_results:
            # 基础分数从10开始
            relevance_score = 10
            
            # 获取基本属性
            title = result.get('title', '')
            snippet = result.get('snippet', '')
            url = result.get('url', '')
            domain = result.get('domain', '')
            
            # 计算标题和摘要中包含的主题词数量
            title_words = set(re.findall(r'\w+', title.lower()))
            snippet_words = set(re.findall(r'\w+', snippet.lower()))
            
            # 计算相关词出现次数
            title_match = len(topic_words.intersection(title_words))
            snippet_match = len(topic_words.intersection(snippet_words))
            
            # 计算相关性得分 - 标题匹配权重更高
            relevance_score += title_match * 3
            relevance_score += snippet_match
            
            # 检查URL是否包含关键词（通常表示页面更专注）
            url_match = sum(1 for word in topic_words if word in url.lower())
            if url_match > 0:
                relevance_score += url_match * 2
            
            # 高质量域名加分
            for quality_domain in high_quality_domains:
                if quality_domain in url.lower() or quality_domain in domain.lower():
                    # 不同域名给予不同权重
                    if "baike.baidu.com" in url:
                        relevance_score += 15  # 百度百科得分最高
                    elif "wiki" in domain or "wikipedia" in domain:
                        relevance_score += 12  # 维基百科
                    elif "zhihu.com/question" in url:
                        relevance_score += 10  # 知乎问答
                    elif "zhihu.com" in url:
                        relevance_score += 8   # 知乎其他内容
                    elif "edu.cn" in domain or "ac.cn" in domain:
                        relevance_score += 10  # 教育和学术网站
                    elif "gov.cn" in domain:
                        relevance_score += 8   # 政府网站
                    elif any(academic in domain for academic in ["cnki", "springer", "sciencedirect", "nature", "science", "researchgate"]):
                        relevance_score += 12  # 学术网站
                    else:
                        relevance_score += 5   # 其他优质域名
                    break
            
            # 内容长度也是衡量质量的指标
            content_length = len(snippet)
            if content_length > 500:
                relevance_score += 4
            elif content_length > 300:
                relevance_score += 2
            elif content_length > 150:
                relevance_score += 1
            
            # 时效性判断（如果有日期信息）
            if "date" in result and result["date"]:
                try:
                    # 尝试解析日期
                    date_str = result["date"]
                    if "2023" in date_str or "2024" in date_str:
                        relevance_score += 2  # 最近的内容加分
                except:
                    pass
            
            # 搜索引擎偏好（搜狗更适合某些本地内容）
            if result.get("engine") == "baidu":
                relevance_score += 1
            
            # 存储分数用于排序
            result["relevance_score"] = relevance_score
            scored_results.append((result, relevance_score))
        
        # 按得分降序排序
        scored_results.sort(key=lambda x: x[1], reverse=True)
        
        # 返回得分最高的结果
        top_results = [result for result, _ in scored_results[:limit]]
        
        # 打印选择的结果信息
        self.logger.info(f"从{len(filtered_results)}个过滤后的结果中选择了{len(top_results)}个最相关的结果")
        for i, result in enumerate(top_results[:3]):  # 只打印前3个
            self.logger.info(f"  Top {i+1}: {result.get('title', '')[:30]} - 分数:{result.get('relevance_score', 0)} - 来源:{result.get('domain', '')}")
        
        return top_results 