from typing import Dict, List, Any, Optional, Union, Tuple, Set, cast, Callable, Type, Awaitable
import os
import time
import json
import uuid
import asyncio
import logging
from dataclasses import dataclass, field, asdict
from enum import Enum
from datetime import datetime
import inspect
from functools import wraps
from pathlib import Path
import traceback

from langgraph.graph import StateGraph, END, START
from langgraph.prebuilt import ToolNode as LangGraphToolNode
import langchain_core
from langchain_core.messages import HumanMessage, AIMessage

from src.services.generator.state import ArticleState, ArticleStatus
from src.services.generator.agents import (
    PlanningAgent,
    WritingAgent,
    FinalizingAgent,
    SearchAgent,
    ContentEvaluationAgent
)
# 导入Agent输出类
from src.services.generator.agents.base import AgentOutput

from src.services.generator.tools import (
    ToolRegistry, ToolNode, register_default_tools
)
from src.services.generator.tools.network_tools import (
    register_network_tools,
    WebScraperTool
)

# 导入新的统一内容提取接口
from src.services.extractor.content_extractor import (
    ContentExtractor, 
    extract_and_analyze,
    extract_only
)
from src.services.extractor.wechat_extractor import WechatArticleExtractor
from src.services.extractor.unified_analyzer import (
    ContentType,
    analyze_from_extraction_result
)

# 配置日志
logger = logging.getLogger("article_processor")

class ArticleAgent:
    """文章代理基类"""
    
    def __init__(self, name: str):
        """初始化文章代理"""
        self.name = name
    
    async def run(self, state: ArticleState) -> ArticleState:
        """运行代理处理"""
        raise NotImplementedError("子类必须实现此方法")


class StateTracker:
    """State tracker for monitoring article generation progress and status"""
    
    def __init__(self):
        """Initialize state tracker"""
        self.progress = {}
        self.start_time = None
        self.end_time = None
        self.errors = []
        self.warnings = []
    
    def initialize(self, state: ArticleState):
        """Initialize state tracker
        
        Args:
            state: 文章状态
        """
        self.start_time = time.time()
        self.progress = {
            "content_extraction": 0,
            "planning": 0,
            "search": 0,
            "writing": 0,
            "finalizing": 0,
        }
    
    def update_progress(self, state: ArticleState, stage: str, progress: int):
        """Update progress
        
        Args:
            state: 文章状态
            stage: stage name
            progress: progress value (0-100)
        """
        if stage in self.progress:
            self.progress[stage] = progress
            
            # Record warning
            if progress < 50:
                self.warnings.append(f"{stage} stage progress is low: {progress}%")
            
            # Record error
            if progress == 0:
                self.errors.append(f"{stage} stage not completed")
    
    def generate_final_report(self, state: ArticleState) -> Dict[str, Any]:
        """Generate final report
        
        Args:
            state: 文章状态
            
        Returns:
            最终报告
        """
        self.end_time = time.time()
        total_time = self.end_time - self.start_time
        
        return {
            "total_time": total_time,
            "progress": self.progress,
            "errors": self.errors,
            "warnings": self.warnings,
            "completion_status": state.completion_status,
            "quality_metrics": {
                "word_count": state.article_content.get("metadata", {}).get("word_count", 0),
                "readability_score": state.article_content.get("metadata", {}).get("readability_score", 0),
                "keyword_density": state.article_content.get("metadata", {}).get("keyword_density", 0),
                "content_diversity": state.article_content.get("metadata", {}).get("content_diversity", 0)
            }
        }


class ArticleProcessor:
    """文章处理器类，负责整个文章生成流程的管理和执行"""

    def __init__(self, config: Dict[str, Any] = None):
        """初始化文章处理器
        
        Args:
            config: 配置信息
        """
        self.config = config or {}
        self.default_manager = None
        self.execution_graph = None
        self.node_configs = {}
        self.logger = logging.getLogger(__name__)
        
        # 初始化代理字典 - 移除已删除的代理
        self.agents = {
            "search": SearchAgent(),
            "planning": PlanningAgent(),
            "writing": WritingAgent(),
            "finalizing": FinalizingAgent(),  # 现在包含编辑功能
            "content_evaluation": ContentEvaluationAgent()  # 新增内容评估代理
        }
        
        # 内容提取器
        extractor_config = config.get("content_extractor_config", {})
        self.content_extractor = ContentExtractor(
            article_api_key=extractor_config.get("article_api_key"),
            video_api_key=extractor_config.get("video_api_key")
        )
        
        # 微信文章提取器
        self.wechat_extractor = WechatArticleExtractor(use_dynamic=True)
        
        # 直接创建工具函数列表，按照LangGraph原生方式注册
        tools_list = []
        
        # 注册核心工具函数
        async def web_scraper_tool(state, url, extract_type="all", include_metadata=True):
            """网页抓取工具，用于提取网页内容"""
            tool = WebScraperTool()
            result = await tool.execute({"url": url, "extract_type": extract_type, "include_metadata": include_metadata}, state)
            return result
        
        # 为函数添加name属性
        web_scraper_tool.name = "web_scraper"
        tools_list.append(web_scraper_tool)
        
        # 初始化工具节点
        self.tool_node = LangGraphToolNode(tools_list)
        
        # 初始化文章缓存
        self.cache_dir = Path(config.get("cache_dir", "/tmp/article_cache"))
        self.cache_dir.mkdir(exist_ok=True, parents=True)
        self.article_cache = {}
        
        # 初始化状态追踪器
        self.state_tracker = StateTracker()
        
        # 创建工作流图
        self._create_workflow_graph()
        
        logger.info("文章处理器初始化完成")
    
    def _create_workflow_graph(self):
        """创建工作流图，精简流程并调整执行顺序"""
        graph = StateGraph(ArticleState)
        
        # 节点定义 - 移除content_extraction节点，从planning开始
        graph.add_node("start", self._start_node)
        graph.add_node("planning", self._planning_node)        # 规划作为第一个主要节点
        graph.add_node("search", self._search_node)
        graph.add_node("writing", self._writing_node)
        graph.add_node("content_evaluation", self._content_evaluation_node)
        graph.add_node("finalizing", self._finalizing_node)
        graph.add_node("tools", self.tool_node)
        graph.add_node("end", self._end_node)
        
        # 从START到start节点的入口边
        graph.add_edge(START, "start")
        
        # start节点直接到planning的边
        graph.add_edge("start", "planning")
        
        # 处理分支
        graph.add_conditional_edges(
            "planning",
            self._has_tool_requests,
            {
                True: "tools",
                False: "search"  # 从planning到search
            }
        )
        
        graph.add_conditional_edges(
            "search",
            self._has_tool_requests,
            {
                True: "tools",
                False: "writing"
            }
        )
        
        graph.add_conditional_edges(
            "writing",
            self._has_tool_requests,
            {
                True: "tools",
                False: "content_evaluation"
            }
        )
        
        # 添加内容评估节点的条件边，根据是否需要深度搜索决定去向
        graph.add_conditional_edges(
            "content_evaluation",
            self._needs_deeper_search,
            {
                True: "search",  # 如果需要深度搜索，返回到搜索节点
                False: "finalizing"  # 如果不需要深度搜索，继续到finalizing
            }
        )
        
        graph.add_conditional_edges(
            "finalizing",
            self._has_tool_requests,
            {
                True: "tools",
                False: "end"  # 修改为直接到结束
            }
        )
        
        # 
        graph.add_conditional_edges(
            "tools",
            self._route_after_tools,
            {
                "planning": "planning",
                "search": "search",
                "writing": "writing",
                "content_evaluation": "content_evaluation",
                "finalizing": "finalizing"
            }
        )
        
        # 创建配置了中断处理的checkpointer
        from langgraph.checkpoint.memory import MemorySaver
        
        # 简化为直接使用MemorySaver
        memory_saver = MemorySaver()
        
        # 编译工作流并设置checkpointer
        logger.info("编译工作流图，使用基本内存存储")
        self.workflow = graph.compile(
            checkpointer=memory_saver
        )
        
        logger.info("工作流图已成功创建和编译")
        
        # 初始化活跃运行记录
        self.active_runs = {}
    
    def _get_pending_tool_requests(self, state: ArticleState) -> List[Dict[str, Any]]:
        """获取待处理的工具请求"""
        pending_requests = []
        
        
        node_fields = [
            "content_extraction_tool_requests",
            "search_tool_requests", 
            "planning_tool_requests", 
            "writing_tool_requests", 
            "finalizing_tool_requests"
        ]
        
        for field_name in node_fields:
            if hasattr(state, field_name):
                requests = getattr(state, field_name, [])
                pending_requests.extend([r for r in requests if r.get("status") == "pending"])
        
        # 也检查通用tool_requests字段
        if hasattr(state, "tool_requests"):
            requests = getattr(state, "tool_requests", [])
            pending_requests.extend([r for r in requests if r.get("status") == "pending"])
        
        return pending_requests
    
    async def _has_tool_requests(self, state: ArticleState) -> bool:
        """检查是否有工具请求
        
        Args:
            state: 文章状态
            
        Returns:
            bool: 是否有待处理的工具请求
        """
        try:
            # 确保state是ArticleState类型
            if isinstance(state, dict):
                # 如果是字典，先转换为ArticleState对象
                state = ArticleState.from_dict(state)
            
            # 使用辅助方法获取待处理请求
            pending_requests = self._get_pending_tool_requests(state)
            return len(pending_requests) > 0
        except Exception as e:
            # 错误处理，确保在失败时返回False避免工作流陷入循环
            logger.error(f"检查工具请求时出错: {str(e)}")
            return False
    
    def _route_after_tools(self, state: ArticleState) -> str:
        """工具处理后的路由决策"""
        try:
            # 确保state是ArticleState类型
            if isinstance(state, dict):
                # 如果是字典，检查status字段
                status = state.get("status")
                if isinstance(status, str):
                    # 根据状态字符串返回下一步
                    if status == "planning" or status == ArticleStatus.PLANNING:
                        return "planning"
                    elif status == "searching" or status == ArticleStatus.SEARCHING:
                        return "search"
                    elif status == "writing" or status == ArticleStatus.WRITING:
                        return "writing"
                    elif status == "content_evaluation" or status == ArticleStatus.EVALUATING:
                        return "content_evaluation"
                    elif status == "editing" or status == ArticleStatus.EDITING or status == "finalizing" or status == ArticleStatus.FINALIZING:
                        return "finalizing"
                    else:
                        logger.warning(f"未知的状态字符串: {status}，默认返回planning")
                        return "planning"
            
            # 如果是ArticleState对象，根据当前状态返回下一步
            if state.status == ArticleStatus.PLANNING:
                return "planning"
            elif state.status == ArticleStatus.SEARCHING:
                return "search"
            elif state.status == ArticleStatus.WRITING:
                return "writing"
            elif state.status == ArticleStatus.EVALUATING:
                return "content_evaluation"
            elif state.status == ArticleStatus.EDITING or state.status == ArticleStatus.FINALIZING:
                return "finalizing"
            else:
                logger.warning(f"未被明确处理的状态: {state.status}，默认返回planning")
                return "planning"
        except Exception as e:
            logger.error(f"工具路由决策出错: {str(e)}")
            return "planning"  # 出错时默认到planning
    
    

    
    async def _search_node(self, state: ArticleState) -> ArticleState:
        """搜索节点"""
        logger.info("执行搜索")
        
        try:
            # 执行搜索
            search_agent = self.agents["search"]
            state = await search_agent.run(state)
            
            # 更新进度
            self.state_tracker.update_progress(state, "search", 100)
            
            return state
        except Exception as e:
            logger.error(f"搜索失败: {str(e)}")
            state.add_error("search", str(e))
            return state

    async def _process_agent_node(self, state: ArticleState, agent_type: str, 
                           next_status: Optional[ArticleStatus] = None, config: Dict[str, Any] = None) -> ArticleState:
        """统一处理代理节点的方法，减少代码重复
        
        Args:
            state: 当前状态
            agent_type: 代理类型
            next_status: 下一步状态
            config: 代理配置
            
        Returns:
            更新后的状态
        """
        try:
            logger.info(f"开始执行{agent_type}节点")
            
            # 如果提供了下一步状态，则更新状态
            if next_status:
                state.update_status(next_status)
                
            # 创建节点配置
            node = {
                "type": "agent",
                "agent_type": agent_type,
                "config": config or {}
            }
            
            # 执行代理
            success = await self._execute_agent_node(node, state)
            if not success:
                state.add_error(agent_type, f"{agent_type}代理执行失败")
                
            return state
                
        except Exception as e:
            return self._handle_node_error(state, agent_type, e)
    
    def _handle_node_error(self, state: ArticleState, node_type: str, error: Exception) -> ArticleState:
        """统一处理节点错误
        
        Args:
            state: 当前状态
            node_type: 节点类型
            error: 异常
            
        Returns:
            更新后的状态
        """
        error_msg = f"{node_type}失败: {str(error)}"
        logger.error(error_msg)
        state.add_error(node_type, error_msg)
        return state
    
    async def _execute_agent_node(self, node: Dict[str, Any], state: ArticleState) -> Tuple[bool, Dict[str, Any]]:
        """执行代理节点
        
        Args:
            node: 节点配置
            state: 文章状态—
            
        Returns:
            (成功标志，节点输出)
        """
        start_time = time.time()
        node_type = node.get("type", "unknown")
        logger.info(f"执行节点: type={node_type}")
        
        try:
            # 根据节点类型调用相应的处理函数
            success = False
            
            if node_type == "planning":
                success, state = await self._execute_planning_node(node, state)
            elif node_type == "writing":
                success, state = await self._execute_writing_node(node, state)
            else:
                logger.error(f"未知节点类型: {node_type}")
                success = False
                
            # 计算执行时间
            elapsed_time = time.time() - start_time
            logger.info(f"节点执行完成: type={node_type}, success={success}, 用时={elapsed_time:.2f}秒")
            
            # 返回节点执行结果
            return success, state.node_outputs.get(node_type, {})
            
        except Exception as e:
            # 处理节点执行异常
            elapsed_time = time.time() - start_time
            logger.error(f"节点执行错误 [{node_type}]: {str(e)}, 用时={elapsed_time:.2f}秒")
            
            # 记录节点错误
            if hasattr(state, "add_error"):
                state.add_error(node_type, f"节点执行错误: {str(e)}")
                
            # 更新节点输出
            state.node_outputs[node_type] = {
                "success": False,
                "error": str(e),
                "execution_time": elapsed_time
            }
            
            return False, state.node_outputs.get(node_type, {})
    
    async def _planning_node(self, state: ArticleState) -> ArticleState:
        """LangGraph工作流的规划节点
        
        Args:
            state: 文章状态
            
        Returns:
            更新后的文章状态
        """
        logger.info("执行规划节点")
        try:
            # 确保state已初始化
            if not hasattr(state, "node_outputs"):
                state.node_outputs = {}
            
        
            
            # 创建明确的节点配置，确保使用planning类型
            node_config = {"type": "planning", "agent_type": "planning"}
            # 调用完整实现
            success, updated_state = await self._execute_planning_node(node_config, state)
            
            # 检查执行结果
            if not success:
                logger.error("规划节点执行失败")
                state.add_error("planning", "规划节点执行失败")
                return state
            else:
                logger.info(f"规划节点执行成功，大纲包含 {len(updated_state.outline) if updated_state.outline else 0} 个章节")
                return updated_state
        except Exception as e:
            logger.error(f"规划节点执行失败: {str(e)}")
            state.add_error("planning", str(e))
            return state


    async def _execute_planning_node(self, node_config: Dict[str, Any], state: ArticleState) -> Tuple[bool, ArticleState]:
        """执行规划节点
        
        Args:
            node_config: 节点配置
            state: 文章状态
            
        Returns:
            是否成功执行和更新后的文章状态
        """
        logger.info(f"正在执行规划节点...")
        
        # 确保状态对象已初始化
        if not hasattr(state, "node_outputs"):
            state.node_outputs = {}
            
        # 确保大纲字段存在，如果不存在则初始化为空列表
        if not hasattr(state, "outline") or state.outline is None:
            state.outline = []
     
        
        
        # 使用agents字典获取planning代理
        agent = self.agents["planning"]
        if not agent:
            logger.error("找不到规划代理")
            return False, state
        
        # 记录当前状态
        start_time = time.time()
        logger.info(f"开始规划: 主题='{state.topic}', 关键词={state.keywords}")
  
        
        try:
            # 执行规划代理
            agent_output = await agent.run(state)
            
            # 检查代理执行是否成功
            if not isinstance(agent_output, AgentOutput):
                # 如果返回的不是AgentOutput，而是直接返回的ArticleState
                if isinstance(agent_output, ArticleState):
                    updated_state = agent_output
                    # 验证outline是否存在
                    if not updated_state.outline:
                        logger.error("规划返回了ArticleState，但outline为空")
                        return False, state
                    return True, updated_state
                else:
                    logger.error(f"规划代理返回了意外的类型: {type(agent_output)}")
                    return False, state
                    
            if not agent_output.success:
                logger.error(f"规划代理执行失败: {agent_output.error}")
                return False, state
                
            # 获取更新后的状态
            updated_state = agent_output.state
            
            # 验证规划结果
            if not updated_state.outline:
                logger.error("规划节点未生成有效大纲")
                return False, state
            
            # 输出规划结果摘要
            outline = updated_state.outline
            section_count = len(outline)
            subsection_count = sum(len(section.get("subsections", [])) for section in outline if isinstance(section, dict))
            
            # 记录规划结果
            elapsed_time = time.time() - start_time
            logger.info(f"规划完成: {section_count}个章节, {subsection_count}个子章节, 耗时={elapsed_time:.2f}秒")
            
            # 添加节点执行记录
            node_output = {
                "node_type": "planning",
                "execution_time": elapsed_time,
                "sections_count": section_count,
                "subsections_count": subsection_count,
                "title": updated_state.title if hasattr(updated_state, "title") and updated_state.title else updated_state.topic,
            }
            
            # 更新节点状态
            updated_state.node_outputs["planning"] = node_output
            
            # 详细记录大纲结构
            logger.info(f"大纲结构:")
            for i, section in enumerate(outline):
                if not isinstance(section, dict):
                    continue
                logger.info(f"章节 {i+1}: {section.get('title', '无标题')}")
                subsections = section.get("subsections", [])
                for j, subsection in enumerate(subsections):
                    if not isinstance(subsection, dict):
                        continue
                    logger.info(f"  子章节 {i+1}.{j+1}: {subsection.get('title', '无标题')}")
            
            # 获取文章类型和结构类型
            article_type = "未知类型"
            structure_type = "章节型"
            
            if hasattr(updated_state, "article_plan") and updated_state.article_plan:
                article_type = updated_state.article_plan.get("article_type", "未知类型")
                structure_type = updated_state.article_plan.get("structure_type", "章节型")
            
            if structure_type == "流畅型":
                logger.info(f"规划节点执行成功，大纲为流畅型结构（{article_type}），包含 {len(updated_state.outline) if updated_state.outline else 0} 个内容部分")
            else:
                logger.info(f"规划节点执行成功，大纲包含 {len(updated_state.outline) if updated_state.outline else 0} 个章节")
            
            # 输出规划结果摘要
            outline = updated_state.outline
            section_count = len(outline)
            
            # 记录规划结果
            elapsed_time = time.time() - start_time
            
            if structure_type == "流畅型":
                logger.info(f"规划完成: 流畅型结构(无章节标题), {section_count}个内容部分, 耗时={elapsed_time:.2f}秒")
            else:
                subsection_count = sum(len(section.get("subsections", [])) for section in outline if isinstance(section, dict))
                logger.info(f"规划完成: {section_count}个章节, {subsection_count}个子章节, 耗时={elapsed_time:.2f}秒")
            
            # 添加节点执行记录
            node_output = {
                "node_type": "planning",
                "execution_time": elapsed_time,
                "sections_count": section_count,
                "article_type": article_type,
                "structure_type": structure_type,
                "title": updated_state.title if hasattr(updated_state, "title") and updated_state.title else updated_state.topic,
            }
            
            # 更新节点状态
            updated_state.node_outputs["planning"] = node_output
            
            # 详细记录大纲结构
            logger.info(f"大纲结构:")
            
            # 根据结构类型不同处理方式
            if structure_type == "流畅型":
                # 流畅型结构（议论文/观点型和故事/叙事型）
                for i, section in enumerate(outline):
                    if not isinstance(section, dict):
                        continue
                    
                    # 流畅型结构应该有content字段而非title字段
                    content = section.get("content", "")
                    content_preview = content[:5000] + "..." if content and len(content) > 5000 else content
                    key_points = section.get("key_points", [])
                    key_points_str = ", ".join(key_points[:3000]) if key_points else ""
                    
                    if key_points_str:
                        logger.info(f"内容部分 {i+1}: {content_preview} [要点: {key_points_str}]")
                    else:
                        logger.info(f"内容部分 {i+1}: {content_preview}")
            else:
                # 章节型结构
                for i, section in enumerate(outline):
                    if not isinstance(section, dict):
                        continue
                    
                    logger.info(f"章节 {i+1}: {section.get('title', '无标题')}")
                    subsections = section.get("subsections", [])
                    for j, subsection in enumerate(subsections):
                        if not isinstance(subsection, dict):
                            continue
                        logger.info(f"  子章节 {i+1}.{j+1}: {subsection.get('title', '无标题')}")
            
            # 确保article_plan已正确设置
            if updated_state.article_plan:
                logger.info(f"文章计划已生成，内含: {list(updated_state.article_plan.keys())}")
                
                
            else:
              raise Exception("规划节点未生成有效大纲")
            # 更新状态为规划已完成
            updated_state.update_status(ArticleStatus.PLANNING)
            
            return True, updated_state
            
        except Exception as e:
            logger.error(f"规划节点执行失败: {str(e)}")
            return False, state

    async def _writing_node(self, state: ArticleState) -> ArticleState:
        """LangGraph工作流的写作节点
        
        Args:
            state: 文章状态
            
        Returns:
            更新后的文章状态
        """
        logger.info("执行写作节点")
        try:
            # 创建空节点配置
            node_config = {"type": "writing"}
            # 调用完整实现
            success, updated_state = await self._execute_writing_node(node_config, state)
            if not success:
                state.add_error("writing", "写作节点执行失败")
            else:
                return updated_state
            return state
        except Exception as e:
            logger.error(f"写作节点执行失败: {str(e)}")
            state.add_error("writing", str(e))
            return state
    
    async def _execute_writing_node(self, node_config: Dict[str, Any], state: ArticleState) -> Tuple[bool, ArticleState]:
        """执行写作节点实际逻辑
        
        Args:
            node_config: 节点配置
            state: 文章状态
            
        Returns:
            是否成功执行和更新后的文章状态
        """
        logger.info("执行文章写作详细处理")
        
        try:
            # 执行写作
            writing_agent = self.agents["writing"]
            agent_output = await writing_agent.run(state)
            
            # 检查代理执行是否成功
            if not agent_output.success:
                logger.error(f"写作代理执行失败: {agent_output.error}")
                state.add_error("writing", agent_output.error or "未知错误")
                return False, state
            
            # 从AgentOutput获取更新后的状态
            updated_state = agent_output.state
            
            # 验证写作结果
            if not updated_state.article_content:
                logger.error("写作节点未生成有效内容")
                state.add_error("writing", "未生成有效内容")
                return False, state
            
            # 更新进度
            self.state_tracker.update_progress(updated_state, "writing", 100)
            
            return True, updated_state
        except Exception as e:
            logger.error(f"文章写作失败: {str(e)}")
            state.add_error("writing", str(e))
            return False, state

    async def _finalizing_node(self, state: ArticleState) -> ArticleState:
        """最终处理节点，包含编辑和最终处理功能
        
        Args:
            state: 文章状态
            
        Returns:
            更新后的文章状态
        """
        logger.info("执行最终处理节点(含编辑功能)")
        try:
            # 执行最终处理
            finalizing_agent = self.agents["finalizing"]
            agent_output = await finalizing_agent.run(state)
            
            # 检查代理执行是否成功
            if not agent_output.success:
                logger.error(f"最终处理代理执行失败: {agent_output.error}")
                state.add_error("finalizing", agent_output.error or "未知错误")
                return state
                
            # 从AgentOutput获取更新后的状态
            updated_state = agent_output.state
            
            # 更新节点输出
            if not hasattr(updated_state, "node_outputs"):
                updated_state.node_outputs = {}
            updated_state.node_outputs["finalizing"] = {
                "success": True,
                "editing_and_finalizing_completed": True,
                "execution_time": time.time() - getattr(state, "start_time", time.time())
            }
            
            # 更新进度
            self.state_tracker.update_progress(updated_state, "finalizing", 100)
            
            logger.info("最终处理节点(含编辑功能)完成")
            return updated_state
        except Exception as e:
            logger.error(f"最终处理节点执行失败: {str(e)}")
            state.add_error("finalizing", str(e))
            return state
   

    async def _content_evaluation_node(self, state: ArticleState) -> ArticleState:
        """内容评估节点 - 评估文章内容是否需要更深度的搜索
        
        Args:
            state: 文章状态
            
        Returns:
            更新后的文章状态
        """
        logger.info("执行内容评估节点")
        
        try:
            # 检查文章内容是否存在
            if not hasattr(state, "article_content") or not state.article_content:
                logger.warning("文章内容不存在，跳过内容评估")
                state.needs_deeper_search = False
                state.update_status(ArticleStatus.EDITING)
                return state
                
            # 确保内容评估代理存在并有效
            if "content_evaluation" not in self.agents or self.agents["content_evaluation"] is None:
                logger.error("内容评估代理不存在或无效")
                state.add_error("content_evaluation", "内容评估代理不存在或无效")
                state.needs_deeper_search = False
                state.update_status(ArticleStatus.EDITING)
                return state
                
            # 执行内容评估
            content_evaluation_agent = self.agents["content_evaluation"]
            try:
                agent_output = await content_evaluation_agent.run(state)
            except Exception as e:
                logger.error(f"内容评估代理执行失败: {str(e)}", exc_info=True)
                state.add_error("content_evaluation", f"内容评估代理执行异常: {str(e)}")
                state.needs_deeper_search = False
                state.update_status(ArticleStatus.EDITING)
                return state
            
            # 检查代理执行是否成功
            if agent_output is None:
                logger.error("内容评估代理返回None，可能执行失败")
                state.add_error("content_evaluation", "内容评估代理执行返回None")
                # 默认不需要深度搜索
                state.needs_deeper_search = False
                state.update_status(ArticleStatus.EDITING)
                return state
                
            if not agent_output.success:
                error_msg = getattr(agent_output, 'error', '未知错误')
                logger.error(f"内容评估代理执行失败: {error_msg}")
                state.add_error("content_evaluation", error_msg or "未知错误")
                # 默认不需要深度搜索
                state.needs_deeper_search = False
                state.update_status(ArticleStatus.EDITING)
                return state
            
            # 从AgentOutput获取更新后的状态
            if agent_output.state is None:
                logger.error("内容评估代理返回的state为None")
                state.add_error("content_evaluation", "内容评估代理返回的state为None")
                state.needs_deeper_search = False
                state.update_status(ArticleStatus.EDITING)
                return state
            
            updated_state = agent_output.state
            
            # 更新节点输出
            if not hasattr(updated_state, "node_outputs"):
                updated_state.node_outputs = {}
            
            updated_state.node_outputs["content_evaluation"] = {
                "success": True,
                "needs_deeper_search": getattr(updated_state, "needs_deeper_search", False),
                "search_queries": getattr(updated_state, "deeper_search_queries", []),
                "evaluation_rounds": getattr(updated_state, "evaluation_rounds", 0)
            }
            
            # 检查是否需要深度搜索
            needs_deeper_search = getattr(updated_state, "needs_deeper_search", False)
            deeper_search_queries = getattr(updated_state, "deeper_search_queries", [])
            
            # 如果需要深度搜索，将评估生成的查询添加到search_queries
            if needs_deeper_search and deeper_search_queries:
                logger.info(f"内容评估决定需要深度搜索，添加 {len(deeper_search_queries)} 个新查询")
                
                # 如果search_queries不存在，创建它
                if not hasattr(updated_state, "search_queries") or not updated_state.search_queries:
                    updated_state.search_queries = []
                
                # 添加深度搜索查询
                updated_state.search_queries.extend(deeper_search_queries)
                
                # 标记为需要回到搜索节点
                updated_state.update_status(ArticleStatus.SEARCHING)
            else:
                # 继续到下一步
                logger.info("内容评估决定不需要深度搜索，继续执行流程")
                updated_state.update_status(ArticleStatus.EDITING)
            
            return updated_state
            
        except Exception as e:
            logger.error(f"内容评估节点执行失败: {str(e)}", exc_info=True)
            state.add_error("content_evaluation", str(e))
            # 默认不需要深度搜索
            state.needs_deeper_search = False
            state.update_status(ArticleStatus.EDITING)
            return state

    

    async def _end_node(self, state: ArticleState) -> ArticleState:
        """结束节点"""
        logger.info("文章生成流程结束")
        
        # 计算总耗时
        state.end_time = time.time()
        state.total_time = state.end_time - state.start_time
        
        # 生成最终报告
        final_report = self.state_tracker.generate_final_report(state)
        state.final_report = final_report
        
        # 更新状态
        state.update_status(ArticleStatus.COMPLETED)
        
        return state

    async def _tool_node(self, state: ArticleState) -> ArticleState:
        """工具节点"""
        logger.info("执行工具请求")

        try:
            # 获取待处理的工具请求
            pending_requests = self._get_pending_tool_requests(state)
            
            if not pending_requests:
                logger.warning("没有待处理的工具请求")
                return state
                
            # 处理每个工具请求
            for request in pending_requests:
                try:
                    # 执行工具
                    result = await self._execute_tool(request, state)
                    
                    # 更新请求状态
                    request["status"] = "completed"
                    request["result"] = result
                    
                except Exception as e:
                    logger.error(f"工具执行失败: {str(e)}")
                    request["status"] = "failed"
                    request["error"] = str(e)
            
            return state
            
        except Exception as e:
            logger.error(f"工具节点执行失败: {str(e)}")
            state.add_error("tools", str(e))
            return state

    async def _run_article_generation(self, article_id: str) -> None:
        """运行文章生成流程
        
        Args:
            article_id: 文章ID
        """
        try:
            # 从缓存中获取状态
            state = self.article_cache.get(article_id)
            if not state:
                logger.error(f"找不到文章ID: {article_id}")
                return
                
            # 确保我们有一个ArticleState对象
            if not isinstance(state, ArticleState):
                logger.warning(f"缓存中的对象不是ArticleState实例，类型: {type(state).__name__}")
                try:
                    if isinstance(state, dict):
                        # 如果是带有state的字典结构
                        if "state" in state and isinstance(state["state"], ArticleState):
                            state = state["state"]
                        # 尝试从字典创建ArticleState
                        elif "article_id" in state:
                            state = ArticleState.from_dict(state)
                        else:
                            logger.error(f"缓存中的数据格式无法识别: {type(state)}")
                            return
                    else:
                        logger.error(f"缓存中的数据类型无法处理: {type(state)}")
                        return
                except Exception as e:
                    logger.error(f"转换为ArticleState失败: {str(e)}")
                    return
            
            # 确保工具请求字段存在
            if not hasattr(state, "content_extraction_tool_requests"):
                state.content_extraction_tool_requests = []
            if not hasattr(state, "search_tool_requests"):
                state.search_tool_requests = []
            if not hasattr(state, "planning_tool_requests"):
                state.planning_tool_requests = []
            if not hasattr(state, "writing_tool_requests"):
                state.writing_tool_requests = []
            if not hasattr(state, "finalizing_tool_requests"):
                state.finalizing_tool_requests = []
            
            # 日志记录
            logger.info(f"开始处理文章生成流程: {article_id}")
            start_time = time.time()
            
            # 更新状态为处理中
            state.update_status(ArticleStatus.PROCESSING)
            self.article_cache[article_id] = state  # 直接更新缓存，不调用保存方法
            
            try:
                # 使用LangGraph工作流运行整个流程
                logger.info("启动LangGraph工作流")
                state_dict = asdict(state)
                logger.debug(f"初始状态字段: {list(state_dict.keys())}")
                
                # 确保工具字段在字典中
                for field in ["search_tool_requests", 
                             "planning_tool_requests", "writing_tool_requests", 
                             "finalizing_tool_requests"]:
                    if field not in state_dict:
                        state_dict[field] = []
                
                # 准备config对象，提供thread_id
                config = {
                    "configurable": {
                        "thread_id": f"article_{article_id}"
                    }
                }
                
                # 调用工作流，提供config
                result = await self.workflow.ainvoke(state_dict, config=config)
                
                # 将结果转换回ArticleState
                if isinstance(result, dict):
                    logger.info(f"工作流返回字典，键: {list(result.keys())}")
                    try:
                        result_state = ArticleState.from_dict(result)
                        state = result_state
                    except Exception as conv_e:
                        logger.error(f"转换工作流结果为ArticleState失败: {str(conv_e)}")
                        # 尝试部分更新状态
                        for key, value in result.items():
                            if hasattr(state, key):
                                try:
                                    setattr(state, key, value)
                                except Exception:
                                    pass
                else:
                    logger.warning(f"工作流返回非字典类型: {type(result)}")
                
                # 更新最终状态 - 安全地处理
                try:
                    state.update_status(ArticleStatus.COMPLETED)
                except Exception as status_e:
                    logger.error(f"更新状态失败: {str(status_e)}")
                    # 直接设置状态，避免调用方法
                    state.status = ArticleStatus.COMPLETED
                
                # 记录完成时间
                end_time = time.time()
                processing_time = round(end_time - start_time, 2)
                
                state.processing_time = processing_time
                
                logger.info(f"文章生成完成: {article_id}, 耗时: {processing_time}秒")
            except Exception as e:
                logger.error(f"文章生成过程中出错: {str(e)}")
                logger.error(f"错误详情: {traceback.format_exc()}")
                state.add_error("process", f"处理失败: {str(e)}")
                state.update_status(ArticleStatus.ERROR)
                
                # 导出调试信息
                error_file = self.cache_dir / f"error_{article_id}_{int(time.time())}.json"
                try:
                    with error_file.open('w', encoding='utf-8') as f:
                        json.dump(self._prepare_for_serialization(state.to_dict()), f, ensure_ascii=False, indent=2)
                    logger.info(f"错误信息已保存到: {error_file}")
                except Exception as e2:
                    logger.error(f"保存错误信息失败: {str(e2)}")
            
            # 保存最终状态
            self.article_cache[article_id] = state  # 直接更新缓存，不调用保存方法
            
            # 写入文件缓存
            try:
                # 保存到文件，确保可以恢复
                cache_path = self.cache_dir / f"article_{article_id}.json"
                with cache_path.open('w', encoding='utf-8') as f:
                    serializable_state = self._prepare_for_serialization(state.to_dict())
                    json.dump(serializable_state, f, ensure_ascii=False, indent=2)
                logger.info(f"文章状态已保存到文件: {cache_path}")
            except Exception as e:
                logger.error(f"保存文章状态到文件失败: {str(e)}")
                
        except Exception as e:
            logger.error(f"运行文章生成流程失败: {str(e)}")
            logger.error(f"错误详情: {traceback.format_exc()}")
    
    def _prepare_for_serialization(self, data: Any) -> Any:
        """处理数据以确保可以被JSON序列化
        
        Args:
            data: 需要处理的数据
            
        Returns:
            可以被JSON序列化的数据
        """
        # 基本类型直接返回
        if isinstance(data, (str, int, float, bool, type(None))):
            return data
        
        try:
            # 处理枚举类型
            if isinstance(data, Enum):
                return data.value
                
            # 如果对象有to_dict方法，先调用它
            if hasattr(data, "to_dict") and callable(data.to_dict):
                try:
                    dict_data = data.to_dict()
                    return self._prepare_for_serialization(dict_data)
                except Exception as e:
                    logger.error(f"调用to_dict()方法失败: {str(e)}")
                    return str(data)
                    
            # 处理字典
            elif isinstance(data, dict):
                return {
                    key: self._prepare_for_serialization(value)
                    for key, value in data.items()
                }
                
            # 处理列表
            elif isinstance(data, list):
                result = []
                for item in data:
                    try:
                        prepared_item = self._prepare_for_serialization(item)
                        result.append(prepared_item)
                    except Exception as item_e:
                        logger.error(f"处理列表项时出错: {str(item_e)}")
                        result.append(str(item))
                return result
                
            # 处理元组和集合
            elif isinstance(data, (tuple, set)):
                return [self._prepare_for_serialization(item) for item in data]
                
            # 其他类型转换为字符串
            else:
                return str(data)
                
        except Exception as e:
            logger.error(f"序列化过程中出现错误: {str(e)}")
            return f"<序列化错误: {str(e)}>"
            
    async def _execute_tool(self, request: Dict[str, Any], state: ArticleState) -> Any:
        """执行工具请求
        
        Args:
            request: 工具请求
            state: 文章状态
            
        Returns:
            工具执行结果
        """
        tool_name = request.get("tool")
        tool_args = request.get("args", {})
        
        # 根据工具名称执行相应的工具
        if tool_name == "web_scraper":
            return await self.tool_node.execute(
                {"name": "web_scraper", "args": tool_args},
                state
            )
        else:
            raise ValueError(f"未知的工具: {tool_name}")
    # 工作流节点
    async def _start_node(self, state: ArticleState) -> ArticleState:
        """开始节点
        
        Args:
            state: 文章状态
            
        Returns:
            更新后的状态
        """
        try:
            # 确保state是ArticleState类型
            if isinstance(state, dict):
                # 如果是字典，转换为ArticleState对象
                state = ArticleState.from_dict(state)
            
            # 记录开始时间
            state.start_time = time.time()
            
            # 确保工具请求字段存在
            if not hasattr(state, "tool_requests"):
                state.tool_requests = []
            
            # 初始化状态追踪
            self.state_tracker.initialize(state)
            
            # 更新状态为规划阶段而不是内容分析
            state.update_status(ArticleStatus.PLANNING)
            
            # 记录输入信息
            logger.info(f"开始文章处理流程: 主题='{state.topic}', 关键词数量={len(state.keywords) if state.keywords else 0}")
            
            return state
        except Exception as e:
            logger.error(f"初始化文章生成流程失败: {str(e)}")
            if isinstance(state, ArticleState):
                state.add_error("start", f"初始化失败: {str(e)}")
            return state

    async def process_article(self, topic: str = "", keywords: List[str] = None, 
                             selected_direction: str = "", **kwargs) -> Dict[str, Any]:
        """处理文章生成请求
        
        可以直接提供主题、关键词和内容简介
        
        Args:
            topic: 文章主题或标题
            keywords: 关键词列表
            selected_direction: 内容简介或方向
            **kwargs: 额外参数，比如tone、language、target_length等
            
        Returns:
            处理结果字典，包含article_id和状态信息
        """
        try:
            # 创建唯一的文章ID
            article_id = str(uuid.uuid4())
            
            # 记录处理开始
            logger.info(f"开始处理新文章请求: ID={article_id}")
            
            # 初始化文章状态
            state = ArticleState(
                article_id=article_id,
                topic=topic,
                keywords=keywords or [],
                selected_direction=selected_direction,
                **{k: v for k, v in kwargs.items() if hasattr(ArticleState, k)}
            )
            
            # 确保基本数据有效
            if not topic and not keywords and not selected_direction:
                logger.warning("缺少基本输入信息(标题/关键词/内容方向)，文章生成可能不理想")
            
            # 初始化各节点的工具请求字段
            state.search_tool_requests = []
            state.planning_tool_requests = []
            state.writing_tool_requests = []
            state.finalizing_tool_requests = []
           
            # 记录请求详细信息
            logger.info(f"文章处理请求详情: ID={article_id}, 主题='{state.topic}', 关键词={state.keywords}, 内容方向={state.selected_direction}")
            logger.info(f"处理参数: 语调={state.tone}, 语言={state.language}, 目标长度={state.target_length}")
            
            # 直接缓存整个ArticleState对象
            self.article_cache[article_id] = state
            
            # 启动处理流程
            asyncio.create_task(self._run_article_generation(article_id))
            
            return {
                "article_id": article_id,
                "status": "processing",
                "message": "文章生成已开始，可使用article_id查询进度",
                "topic": state.topic,
                "keywords": state.keywords,
                "created_at": datetime.now().isoformat()
            }
        except Exception as e:
            logger.error(f"处理文章请求失败: {str(e)}")
            return {
                "error": f"处理文章请求失败: {str(e)}",
                "status": "error"
            }
    
    async def generate_article(self, article_id: str) -> Dict[str, Any]:
        """获取指定ID的文章内容
        
        Args:
            article_id: 文章ID
            
        Returns:
            文章内容或错误信息
        """
        # 检查缓存
        article = self._get_article(article_id)
        if article:
            return article
        
        # 如果没有找到文章，返回错误
        return {
            "error": "未找到指定ID的文章",
            "article_id": article_id
        }
        
    def _get_article(self, article_id: str) -> Optional[Dict[str, Any]]:
        """从缓存中获取文章
        
        Args:
            article_id: 文章ID
            
        Returns:
            文章数据或None
        """
        try:
            # 先从内存缓存查找
            if article_id in self.article_cache:
                cached_item = self.article_cache[article_id]
                
                # 如果是ArticleState对象，转换为API响应格式
                if isinstance(cached_item, ArticleState):
                    return self._get_article_data(article_id, cached_item)
                
                # 如果是缓存项字典格式 {state: ..., data: ...}
                if isinstance(cached_item, dict) and "data" in cached_item:
                    return cached_item["data"]
                
                # 其他情况尝试序列化
                if isinstance(cached_item, dict):
                    return cached_item
                
                # 记录未知格式
                logger.warning(f"缓存中的项目格式未知: {type(cached_item)}")
        
            # 从文件缓存查找
            article_path = self.cache_dir / f"article_{article_id}.json"
            if article_path.exists():
                try:
                    with article_path.open('r', encoding='utf-8') as f:
                        article_data = json.load(f)
                        # 尝试转换为ArticleState以便提取标准数据
                        try:
                            if "article_id" in article_data:
                                state = ArticleState.from_dict(article_data)
                                result = self._get_article_data(article_id, state)
                                self.article_cache[article_id] = state  # 更新内存缓存
                                return result
                        except Exception as conv_e:
                            logger.warning(f"无法转换文件数据为ArticleState: {str(conv_e)}")
                            
                        # 直接使用文件数据
                        self.article_cache[article_id] = article_data
                        return article_data
                except Exception as e:
                    logger.error(f"读取缓存文章失败: {str(e)}")
        
            return None
        except Exception as e:
            logger.error(f"获取文章数据时出错: {str(e)}")
            return {"error": f"获取文章数据失败: {e.__class__.__name__}",
                    "status": "error"}
        
    def _save_article_to_cache(self, article_id: str, state: Optional[ArticleState] = None) -> None:
        """保存文章到缓存
        
        Args:
            article_id: 文章ID
            state: 文章状态
        """
        try:
            # 如果没有提供状态，从缓存中获取
            if state is None:
                cached_article = self.article_cache.get(article_id)
                if not cached_article or not hasattr(cached_article, "state"):
                    return
                state = cached_article.state
            
            # 提取文章标准数据格式
            article_data = self._get_article_data(article_id, state)
        
            # 保存到缓存
            if state:
                cache_item = {
                    "state": state,
                    "data": article_data
                }
                self.article_cache[article_id] = cache_item
                
                # 记录缓存状态
                cache_size = len(self.article_cache)
                logger.info(f"文章已保存到缓存 [ID={article_id}], 当前缓存大小: {cache_size}")
        except Exception as e:
            # 更安全的错误记录 - 避免直接输出Enum值
            error_message = str(e)
            try:
                if hasattr(e, "__str__") and callable(e.__str__):
                    error_message = e.__str__()
            except:
                error_message = "记录错误时发生未知异常"
            
            logger.error(f"保存文章到缓存失败: {error_message}")
    
    def _get_article_data(self, article_id: str, state: Optional[Any] = None) -> Dict[str, Any]:
        """获取文章数据
        
        Args:
            article_id: 文章ID
            state: 文章状态
            
        Returns:
            文章数据字典
        """
        try:
            # 如果没有提供状态，从缓存中获取
            if state is None:
                cached_article = self.article_cache.get(article_id)
                if not cached_article:
                    return {"id": article_id, "error": "文章未找到"}
                
                if hasattr(cached_article, "data"):
                    return cached_article.data
                
                if hasattr(cached_article, "state"):
                    state = cached_article.state
                else:
                    return {"id": article_id, "error": "缓存格式错误"}
            
            # 准备基本的文章数据结构
            status_value = None
            try:
                # 安全地获取状态值
                if hasattr(state, "status"):
                    if isinstance(state.status, Enum):
                        status_value = state.status.value
                    else:
                        status_value = str(state.status)
                else:
                    status_value = "initialized"
            except Exception as e:
                logger.warning(f"获取状态值失败，使用默认值: {str(e)}")
                status_value = "initialized"
                
            article_data = {
                "id": article_id,
                "created_at": getattr(state, "created_at", int(time.time())),
                "updated_at": int(time.time()),
                "status": status_value,
                "progress": getattr(state, "progress", 0),
                "topic": getattr(state, "topic", ""),
                "keywords": getattr(state, "keywords", []),
                "has_content": {}
            }
            
            # 添加错误信息（如果有）
            if hasattr(state, "errors") and state.errors:
                article_data["errors"] = state.errors
            
            # 添加内容标志
            article_data["has_content"] = {
                "metadata": hasattr(state, "extracted_metadata") and state.extracted_metadata is not None,
                "search_results": hasattr(state, "search_results") and state.search_results is not None 
                                and len(state.search_results) > 0,
                "outline": hasattr(state, "outline") and state.outline is not None,
                "content": hasattr(state, "content") and state.content is not None,
                "analysis_result": hasattr(state, "analysis_result") and state.analysis_result is not None 
                                and len(state.analysis_result) > 0,
                "html": hasattr(state, "html_content") and state.html_content is not None,
                "plain_text": hasattr(state, "plain_text") and state.plain_text is not None
            }
            
            # 添加分析结果
            if hasattr(state, "analysis_result") and state.analysis_result:
                article_data["has_content"]["analysis_result"] = state.analysis_result
                
                # 为兼容旧版API，从analysis_result中提取字段
                keywords = []
                recommended_titles = []
                content_directions = []
                
                for item in state.analysis_result:
                    if isinstance(item, dict):
                        if "keywords" in item and isinstance(item["keywords"], list):
                            keywords.extend(item["keywords"])
                        if "recommended_title" in item:
                            recommended_titles.append(item["recommended_title"])
                        if "content_direction" in item:
                            content_directions.append(item["content_direction"])
                
                # 去重
                keywords = list(dict.fromkeys(keywords))
                recommended_titles = list(dict.fromkeys(recommended_titles))
                content_directions = list(dict.fromkeys(content_directions))
                
                # 填充兼容字段
                article_data["has_content"]["keywords"] = keywords[:5]
                article_data["has_content"]["recommended_titles"] = recommended_titles[:1]
                article_data["has_content"]["content_directions"] = content_directions[:1]
            
            # 添加执行时间
            if hasattr(state, "execution_times") and state.execution_times:
                article_data["execution_times"] = self._prepare_for_serialization(state.execution_times)
                
                # 计算总执行时间
                total_time = sum(state.execution_times.values())
                article_data["total_execution_time"] = round(total_time, 2)
            
            # 添加时间线
            if hasattr(state, "timeline") and state.timeline:
                article_data["timeline"] = self._prepare_for_serialization(state.timeline)
            
            # 添加最终内容
            if hasattr(state, "final_content") and state.final_content:
                article_data["final_content"] = self._prepare_for_serialization(state.final_content)
            elif hasattr(state, "edited_content") and state.edited_content:
                article_data["final_content"] = self._prepare_for_serialization(state.edited_content)
            elif hasattr(state, "article_content") and state.article_content:
                article_data["final_content"] = self._prepare_for_serialization(state.article_content)
            
            return article_data
        except Exception as e:
            logger.error(f"获取文章数据时出错: {str(e)}")
            return {
                "id": article_id,
                "error": f"获取文章数据失败: {e.__class__.__name__}",
                "status": "error"
            }
    

    async def _needs_deeper_search(self, state: ArticleState) -> bool:
        """判断是否需要深度搜索
        
        Args:
            state: 文章状态
            
        Returns:
            是否需要深度搜索
        """
        try:
            self.logger.info("判断是否需要深度搜索")
            
            # 检查搜索轮次限制 (最多3轮搜索)
            if hasattr(state, "search_rounds") and state.search_rounds > 3:
                self.logger.info(f"已达到最大搜索轮次 ({state.search_rounds}/3)，不进行更多搜索")
                return False
            
            # 检查内容评估代理设置的深度搜索标志
            if hasattr(state, "needs_deeper_search") and state.needs_deeper_search:
                self.logger.info("检测到needs_deeper_search标志为True，需要进行深度搜索")
                return True
            
            # 默认不需要深度搜索
            self.logger.info("没有明确的深度搜索标志，默认不进行深度搜索")
            return False
        except Exception as e:
            self.logger.error(f"判断是否需要深度搜索时出错: {str(e)}")
            return False

# Main execution entry
if __name__ == "__main__":
    # Configure logger
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    async def main():
        # Example configuration
        config = {
            "api_keys": {
                "qian_api_key": os.environ.get("QIAN_API_KEY", ""),
                "deepseek_api_key": os.environ.get("DEEPSEEK_API_KEY", "")
            },
            "seo_config": {
                "enabled": True
            },
            "video_config": {
                "enabled": True
            },
            "cache_dir": "./cache"
        }
        
        # Initialize processor
        processor = ArticleProcessor(config)
        
        # Process article - from topic generation
        result1 = await processor.process_article(
            topic="人工智能在中国的发展趋势",
            keywords=["人工智能", "中国", "发展", "趋势", "AI"],
            tone="informative",
            target_length=2000
        )
        
        print("从主题生成文章：")
        print(json.dumps(result1, ensure_ascii=False, indent=2))
        
    
    
    # Run main function
    asyncio.run(main())
