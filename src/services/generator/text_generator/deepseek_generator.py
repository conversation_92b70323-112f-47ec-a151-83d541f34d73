"""
DeepSeek文本生成器 - 使用DeepSeek大语言模型生成文本
"""
import os
import json
import aiohttp
import asyncio
from typing import Dict, Any, List, Optional, Union

from src.services.generator.text_generator.base import (
    BaseTextGenerator,
    TextGenerationConfig,
    TextGenerationResult,
    TextGenerationModel
)
from src.utils.logger import log_info, log_error, CrawlerLogger
from src.config.logging_config import LoggerName

logger = CrawlerLogger().get_logger(LoggerName.API)

class DeepSeekTextGenerator(BaseTextGenerator):
    """DeepSeek文本生成器"""
    
    def __init__(self, api_key: Optional[str] = None):
        """初始化DeepSeek生成器
        
        Args:
            api_key: DeepSeek API密钥
        """
        super().__init__(api_key, "deepseek")
        
        # 使用环境变量获取API密钥（如果未提供）
        if not self.api_key:
            self.api_key = os.getenv("DEEPSEEK_API_KEY", "")
        
        # API端点
        self.endpoint = "https://api.deepseek.com/v1/chat/completions"
        
        # 模型映射
        self.model_mapping = {
            TextGenerationModel.DEEPSEEK_CHAT.value: "deepseek-chat",
            TextGenerationModel.DEEPSEEK_CODER.value: "deepseek-coder"
        }
    
    async def generate(self, config: TextGenerationConfig) -> TextGenerationResult:
        """生成文本
        
        Args:
            config: 文本生成配置
            
        Returns:
            文本生成结果
        """
        if not self._validate_api_key():
            logger.error("未提供有效的DeepSeek API密钥")
            return TextGenerationResult(
                content="错误：未提供有效的API密钥",
                prompt=config.prompt,
                model=config.model.value if config.model else None
            )
        
        # 使用用户提供的API密钥（如果有）
        api_key = config.api_key or self.api_key
        
        # 构建完整提示词
        full_prompt = self._build_full_prompt(config)
        
        # 获取模型名称
        model_name = self._get_model_name(config.model.value)
        
        # 构建请求体
        payload = {
            "model": model_name,
            "messages": [
                {"role": "system", "content": "你是一个专业的内容创作者，能够根据要求生成高质量的文本内容。"},
                {"role": "user", "content": full_prompt}
            ],
            "max_tokens": config.max_tokens,
            "temperature": config.temperature
        }
        
        # 设置请求头
        headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }
        
        try:
            # 发送请求
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    self.endpoint,
                    headers=headers,
                    json=payload
                ) as response:
                    if response.status != 200:
                        error_text = await response.text()
                        logger.error(f"DeepSeek API错误: {response.status} - {error_text}")
                        return TextGenerationResult(
                            content=f"错误：API请求失败，状态码 {response.status}",
                            prompt=full_prompt,
                            model=model_name
                        )
                    
                    # 解析响应
                    response_data = await response.json()
                    
                    # 提取结果
                    choices = response_data.get("choices", [{}])
                    message = choices[0].get("message", {}) if choices else {}
                    generated_text = message.get("content", "")
                    
                    # 获取token计数
                    usage = response_data.get("usage", {})
                    prompt_tokens = usage.get("prompt_tokens", 0)
                    completion_tokens = usage.get("completion_tokens", 0)
                    total_tokens = usage.get("total_tokens", 0)
                    
                    # 创建结果
                    result = TextGenerationResult(
                        content=generated_text,
                        prompt=full_prompt,
                        model=model_name,
                        token_count=total_tokens,
                        metadata={
                            "prompt_tokens": prompt_tokens,
                            "completion_tokens": completion_tokens,
                            "finish_reason": choices[0].get("finish_reason", "") if choices else "",
                            "provider": "deepseek"
                        }
                    )
                    
                    return result
                    
        except Exception as e:
            logger.error(f"DeepSeek API调用失败: {e}")
            return TextGenerationResult(
                content=f"错误：API调用异常 - {str(e)}",
                prompt=full_prompt,
                model=model_name
            )
    
    def _build_full_prompt(self, config: TextGenerationConfig) -> str:
        """构建完整提示词
        
        Args:
            config: 文本生成配置
            
        Returns:
            完整提示词
        """
        # 生成前缀
        prefix = self._generate_prompt_prefix(config)
        
        # 组合提示词
        return f"{prefix}{config.prompt}"
    
    def _get_model_name(self, model_value: str) -> str:
        """获取DeepSeek对应的模型名称
        
        Args:
            model_value: 模型值
            
        Returns:
            DeepSeek API使用的模型名称
        """
        return self.model_mapping.get(model_value, "deepseek-chat") 