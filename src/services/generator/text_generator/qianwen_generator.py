"""
千问文本生成器 - 使用阿里云千问(<PERSON>wen)大语言模型生成文本
"""
import os
import json
import aiohttp
import asyncio
from typing import Dict, Any, List, Optional, Union

from src.services.generator.text_generator.base import (
    BaseTextGenerator,
    TextGenerationConfig,
    TextGenerationResult,
    TextGenerationModel
)
from src.utils.logger import log_info, log_error, CrawlerLogger
from src.config.logging_config import LoggerName

logger = CrawlerLogger().get_logger(LoggerName.API)

class QianwenTextGenerator(BaseTextGenerator):
    """千问文本生成器"""
    
    def __init__(self, api_key: Optional[str] = None):
        """初始化千问生成器
        
        Args:
            api_key: 千问API密钥
        """
        super().__init__(api_key, "qianwen")
        
        # 使用环境变量获取API密钥（如果未提供）
        if not self.api_key:
            self.api_key = os.getenv("QIANWEN_API_KEY", "")
        
        # API端点
        self.endpoint = "https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation"
        
        # 模型映射
        self.model_mapping = {
            TextGenerationModel.QWEN_TURBO.value: "qwen-turbo",
            TextGenerationModel.QWEN_MAX.value: "qwen-max",
            TextGenerationModel.QWEN_PLUS.value: "qwen-plus"
        }
    
    async def generate(self, config: TextGenerationConfig) -> TextGenerationResult:
        """生成文本
        
        Args:
            config: 文本生成配置
            
        Returns:
            文本生成结果
        """
        if not self._validate_api_key():
            logger.error("未提供有效的千问API密钥")
            return TextGenerationResult(
                content="错误：未提供有效的API密钥",
                prompt=config.prompt,
                model=config.model.value if config.model else None
            )
        
        # 使用用户提供的API密钥（如果有）
        api_key = config.api_key or self.api_key
        
        # 构建完整提示词
        full_prompt = self._build_full_prompt(config)
        
        # 获取模型名称
        model_name = self._get_model_name(config.model.value)
        
        # 构建请求体
        payload = {
            "model": model_name,
            "input": {
                "messages": [
                    {"role": "system", "content": "你是一个专业的内容创作者，能够根据要求生成高质量的文本内容。"},
                    {"role": "user", "content": full_prompt}
                ]
            },
            "parameters": {
                "max_tokens": config.max_tokens,
                "temperature": config.temperature,
                "result_format": "message"
            }
        }
        
        # 设置请求头
        headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }
        
        try:
            # 发送请求
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    self.endpoint,
                    headers=headers,
                    json=payload
                ) as response:
                    if response.status != 200:
                        error_text = await response.text()
                        logger.error(f"千问API错误: {response.status} - {error_text}")
                        return TextGenerationResult(
                            content=f"错误：API请求失败，状态码 {response.status}",
                            prompt=full_prompt,
                            model=model_name
                        )
                    
                    # 解析响应
                    response_data = await response.json()
                    
                    # 检查返回状态码
                    if response_data.get("code") != 200:
                        logger.error(f"千问API错误: {response_data.get('code')} - {response_data.get('message')}")
                        return TextGenerationResult(
                            content=f"错误：API请求失败，错误码 {response_data.get('code')}，错误信息：{response_data.get('message')}",
                            prompt=full_prompt,
                            model=model_name
                        )
                    
                    # 提取结果
                    output = response_data.get("output", {})
                    choices = output.get("choices", [{}])
                    message = choices[0].get("message", {}) if choices else {}
                    generated_text = message.get("content", "")
                    
                    # 获取token计数
                    usage = output.get("usage", {})
                    input_tokens = usage.get("input_tokens", 0)
                    output_tokens = usage.get("output_tokens", 0)
                    total_tokens = input_tokens + output_tokens
                    
                    # 创建结果
                    result = TextGenerationResult(
                        content=generated_text,
                        prompt=full_prompt,
                        model=model_name,
                        token_count=total_tokens,
                        metadata={
                            "input_tokens": input_tokens,
                            "output_tokens": output_tokens,
                            "finish_reason": choices[0].get("finish_reason", "") if choices else "",
                            "provider": "qianwen"
                        }
                    )
                    
                    return result
                    
        except Exception as e:
            logger.error(f"千问API调用失败: {e}")
            return TextGenerationResult(
                content=f"错误：API调用异常 - {str(e)}",
                prompt=full_prompt,
                model=model_name
            )
    
    def _build_full_prompt(self, config: TextGenerationConfig) -> str:
        """构建完整提示词
        
        Args:
            config: 文本生成配置
            
        Returns:
            完整提示词
        """
        # 生成前缀
        prefix = self._generate_prompt_prefix(config)
        
        # 组合提示词
        return f"{prefix}{config.prompt}"
    
    def _get_model_name(self, model_value: str) -> str:
        """获取千问对应的模型名称
        
        Args:
            model_value: 模型值
            
        Returns:
            千问API使用的模型名称
        """
        return self.model_mapping.get(model_value, "qwen-turbo") 