"""
LLM适配器 - 为各个Agent提供专门的LLM角色和能力
"""
import logging
from enum import Enum
from typing import Dict, List, Any, Optional, Union

from src.services.generator.text_generator.text_manager import TextManager, TextGenerationProvider
from src.services.generator.text_generator.base import (
    TextGenerationModel, 
    TextGenerationConfig,
    WritingStyle,
    WritingTone,
    ContentType,
    ContentStructure
)
from src.config.api_config import get_qianwen_api_key, get_deepseek_api_key

logger = logging.getLogger("llm_adapter")

class LLMRole(Enum):
    """LLM扮演的角色"""
    PLANNER = "planner"           # 规划者，负责内容结构规划
    CONTENT_PLANNER = "content_planner"  # 内容规划师，负责内容规划和大纲生成
    CONTENT_STRATEGIST = "content_strategist"  # 内容策略师，负责评估内容方向和策略
    WRITER = "writer"             # 写作者，负责内容创作
    CONTENT_WRITER = "content_writer"  # 内容写作者，负责生成高质量文章内容
    EDITOR = "editor"             # 编辑者，负责内容编辑和润色
    SEO_EXPERT = "seo_expert"     # SEO专家，负责SEO优化
    RESEARCHER = "researcher"     # 研究者，负责信息收集和分析
    FINALIZER = "finalizer"       # 终审者，负责最终内容审核
    REFLECTOR = "reflector"       # 反思者，负责流程反思和改进
    SEARCHER = "searcher"         # 搜索者，负责信息搜索策略
    SEARCH_QUERY_GENERATOR = "search_query_generator"  # 搜索查询生成器，负责生成搜索查询
    CONTENT_EVALUATOR = "content_evaluation"  # 内容评估专家，负责评估内容深度和质量

class LLMAdapter:
    """LLM适配器，为不同的Agent提供专门的LLM功能"""
    
    def __init__(self, default_provider: str = "deepseek"):
        """初始化LLM适配器
        
        Args:
            default_provider: 默认LLM提供商，可选值: "deepseek", "qianwen"
        """
        # 从环境变量获取API密钥
        self.qianwen_api_key = get_qianwen_api_key()
        self.deepseek_api_key = get_deepseek_api_key()
        
        # 创建文本管理器
        self.text_manager = TextManager(
            qianwen_api_key=self.qianwen_api_key,
            deepseek_api_key=self.deepseek_api_key
        )
        
        # 设置默认提供商
        self.default_provider = default_provider
        logger.info(f"LLM适配器初始化完成，默认提供商: {default_provider}")
        
        # 角色配置
        self.role_configs = {
            LLMRole.PLANNER: {
                "writing_style": WritingStyle.PROFESSIONAL,
                "writing_tone": WritingTone.AUTHORITATIVE,
                "model": TextGenerationModel.DEEPSEEK_CHAT if default_provider == "deepseek" else TextGenerationModel.QWEN_TURBO,
                "system_prompt": "你是一位专业的内容规划专家，擅长设计结构化的内容大纲和规划写作流程。"
            },
            LLMRole.CONTENT_PLANNER: {
                "writing_style": WritingStyle.PROFESSIONAL,
                "writing_tone": WritingTone.AUTHORITATIVE,
                "model": TextGenerationModel.DEEPSEEK_CHAT if default_provider == "deepseek" else TextGenerationModel.QWEN_TURBO,
                "system_prompt": "你是一位专业的内容规划专家，擅长设计结构化的内容大纲、写作计划和关键词策略，确保文章结构完整并符合SEO要求。"
            },
            LLMRole.CONTENT_STRATEGIST: {
                "writing_style": WritingStyle.PROFESSIONAL,
                "writing_tone": WritingTone.AUTHORITATIVE,
                "model": TextGenerationModel.DEEPSEEK_CHAT if default_provider == "deepseek" else TextGenerationModel.QWEN_TURBO,
                "system_prompt": "你是一位专业的内容策略专家，擅长评估和选择最佳内容方向，确保内容符合目标受众需求并具有高价值。"
            },
            LLMRole.WRITER: {
                "writing_style": WritingStyle.CREATIVE,
                "writing_tone": WritingTone.NEUTRAL,
                "model": TextGenerationModel.DEEPSEEK_CHAT if default_provider == "deepseek" else TextGenerationModel.QWEN_TURBO,
                "system_prompt": "你是一位专业的内容创作者，擅长根据大纲创作高质量、结构清晰、内容丰富的文章。"
            },
            LLMRole.CONTENT_WRITER: {
                "writing_style": WritingStyle.CREATIVE,
                "writing_tone": WritingTone.NEUTRAL,
                "model": TextGenerationModel.DEEPSEEK_CHAT if default_provider == "deepseek" else TextGenerationModel.QWEN_TURBO,
                "system_prompt": "你是一位专业的文章撰写者，擅长遵循大纲生成结构完整的文章，包括引言、章节内容和结论，同时保证内容质量和逻辑性。"
            },
            LLMRole.EDITOR: {
                "writing_style": WritingStyle.PROFESSIONAL,
                "writing_tone": WritingTone.NEUTRAL,
                "model": TextGenerationModel.DEEPSEEK_CHAT if default_provider == "deepseek" else TextGenerationModel.QWEN_TURBO,
                "system_prompt": "你是一位专业的内容编辑，擅长润色文章，修正错误，改善文章流畅度和可读性。"
            },
            LLMRole.SEO_EXPERT: {
                "writing_style": WritingStyle.PROFESSIONAL,
                "writing_tone": WritingTone.AUTHORITATIVE,
                "model": TextGenerationModel.DEEPSEEK_CHAT if default_provider == "deepseek" else TextGenerationModel.QWEN_TURBO,
                "system_prompt": "你是一位SEO优化专家，擅长分析和改进内容以提高搜索引擎排名，同时保持内容质量。"
            },
            LLMRole.RESEARCHER: {
                "writing_style": WritingStyle.ACADEMIC,
                "writing_tone": WritingTone.NEUTRAL,
                "model": TextGenerationModel.DEEPSEEK_CHAT if default_provider == "deepseek" else TextGenerationModel.QWEN_TURBO,
                "system_prompt": "你是一位研究专家，擅长收集、分析和综合信息，提取关键事实和见解。"
            },
            LLMRole.FINALIZER: {
                "writing_style": WritingStyle.PROFESSIONAL,
                "writing_tone": WritingTone.AUTHORITATIVE,
                "model": TextGenerationModel.DEEPSEEK_CHAT if default_provider == "deepseek" else TextGenerationModel.QWEN_TURBO,
                "system_prompt": "你是一位内容审核专家，擅长全面审查文章，确保内容质量、准确性和完整性。"
            },
            LLMRole.REFLECTOR: {
                "writing_style": WritingStyle.PROFESSIONAL,
                "writing_tone": WritingTone.NEUTRAL,
                "model": TextGenerationModel.DEEPSEEK_CHAT if default_provider == "deepseek" else TextGenerationModel.QWEN_TURBO,
                "system_prompt": "你是一位过程分析专家，擅长反思和评估内容创作流程，识别问题并提出改进建议。"
            },
            LLMRole.SEARCHER: {
                "writing_style": WritingStyle.INFORMATIVE,
                "writing_tone": WritingTone.NEUTRAL,
                "model": TextGenerationModel.DEEPSEEK_CHAT if default_provider == "deepseek" else TextGenerationModel.QWEN_TURBO,
                "system_prompt": "你是一位信息搜索专家，擅长设计搜索策略，提取和分析搜索结果，并综合相关信息。"
            },
            LLMRole.SEARCH_QUERY_GENERATOR: {
                "writing_style": WritingStyle.PROFESSIONAL,
                "writing_tone": WritingTone.AUTHORITATIVE,
                "model": TextGenerationModel.DEEPSEEK_CHAT if default_provider == "deepseek" else TextGenerationModel.QWEN_TURBO,
                "system_prompt": "你是一位搜索查询生成专家，擅长生成有效的搜索查询，以帮助用户快速找到所需信息。"
            },
            LLMRole.CONTENT_EVALUATOR: {
                "writing_style": WritingStyle.ANALYTICAL,
                "writing_tone": WritingTone.FORMAL,
                "model": TextGenerationModel.DEEPSEEK_CHAT if default_provider == "deepseek" else TextGenerationModel.QWEN_TURBO,
                "system_prompt": "你是一位内容评估专家，擅长分析文章内容的深度、质量和完整性，识别知识空缺，并提出针对性的搜索建议以补充内容。你能够精确评估内容是否需要进一步搜索和研究，并提供专业的评估结果。"
            }
        }
    
    async def ask_llm(self, 
                     role: Union[str, LLMRole], 
                     prompt: str, 
                     context: Optional[Dict[str, Any]] = None,
                     max_tokens: int = 8000,
                     temperature: float = 0.7,
                     provider: Optional[str] = None,
                     json_format: bool = False) -> str:
        """向LLM提问并获取回答
        
        Args:
            role: LLM角色
            prompt: 提示词
            context: 上下文信息
            max_tokens: 最大生成token数
            temperature: 生成温度
            provider: 提供商，可选值: "deepseek", "qianwen"，如果为None则使用默认提供商
            json_format: 是否输出JSON格式
            
        Returns:
            LLM的回答
        """
        # 转换角色枚举
        if isinstance(role, str):
            try:
                # 处理特殊情况
                if role == "search_query_generator":
                    role = LLMRole.SEARCH_QUERY_GENERATOR
                elif role == "content_evaluation":
                    role = LLMRole.CONTENT_EVALUATOR
                else:
                    # 尝试将字符串转换为枚举值
                    try:
                        role = LLMRole(role)
                    except ValueError:
                        # 如果转换失败，检查是否是大小写问题
                        for enum_role in LLMRole:
                            if enum_role.value.lower() == role.lower():
                                role = enum_role
                                break
                        else:
                            # 如果还是找不到匹配的枚举值，使用默认角色
                            logger.warning(f"未知的LLM角色: {role}，使用默认角色WRITER")
                            role = LLMRole.WRITER
            except Exception as e:
                logger.warning(f"处理LLM角色时出错: {str(e)}，使用默认角色WRITER")
                role = LLMRole.WRITER
        
        # 获取角色配置
        role_config = self.role_configs.get(role, self.role_configs[LLMRole.WRITER])
        
        # 设置提供商
        provider = provider or self.default_provider
        
        # 构建完整提示词
        full_prompt = self._build_full_prompt(role, prompt, context)
        
        try:
            # 调用文本管理器生成内容
            result = await self.text_manager.generate_text(
                prompt=full_prompt,
                model=role_config["model"],
                provider=provider,
                max_tokens=max_tokens,
                temperature=temperature,
                writing_style=role_config["writing_style"],
                writing_tone=role_config["writing_tone"],
                use_cache=True,
                json_format=json_format
            )
            
            return result.content
        except Exception as e:
            logger.error(f"LLM调用失败: {str(e)}")
            return f"LLM调用出错: {str(e)}"
    
    async def generate(self, prompt: str, **kwargs) -> Any:
        """生成文本内容
        
        Args:
            prompt: 提示文本
            **kwargs: 额外参数，可包含role, temperature, max_tokens, provider等
            
        Returns:
            根据json_format参数返回不同格式:
            - 如果json_format=True，返回解析后的JSON内容
            - 否则返回包含生成内容的字典
        """
        try:
            # 从kwargs中提取参数
            role = kwargs.get('role', LLMRole.WRITER)
            temperature = kwargs.get('temperature', 0.5)
            max_tokens = kwargs.get('max_tokens', 8000)
            provider = kwargs.get('provider', self.default_provider)
            json_format = kwargs.get('json_format', False)
            
            # 调用ask_llm方法生成内容
            result = await self.ask_llm(
                prompt=prompt,
                role=role,
                temperature=temperature,
                max_tokens=max_tokens,
                provider=provider,
                json_format=json_format
            )
            
            # 如果json_format为True，尝试解析JSON
            if json_format and isinstance(result, str):
                try:
                    # 尝试直接解析JSON
                    import json
                    return json.loads(result)
                except json.JSONDecodeError:
                    # 尝试从文本中提取JSON
                    import re
                    json_match = re.search(r'```json(.*?)```', result, re.DOTALL)
                    if json_match:
                        try:
                            return json.loads(json_match.group(1).strip())
                        except json.JSONDecodeError:
                            pass
                    
                    # 再尝试匹配大括号包围的内容
                    json_match = re.search(r'({[\s\S]*})', result)
                    if json_match:
                        try:
                            return json.loads(json_match.group(1))
                        except json.JSONDecodeError:
                            pass
                    
                    # 所有解析失败，记录警告并返回原始内容
                    logger.warning("无法解析JSON响应")
            
            # 正常返回
            return result
        except Exception as e:
            logger.error(f"生成内容失败: {str(e)}")
            return {
                'content': f"生成失败: {str(e)}",
                'success': False,
                'model': self.default_provider
            }
    
    async def plan_article_outline(self, 
                                 topic: str, 
                                 keywords: Optional[List[str]] = None, 
                                 sections_count: int = 4,
                                 context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """规划文章大纲
        
        Args:
            topic: 文章主题
            keywords: 关键词列表
            sections_count: 章节数量
            context: 上下文信息
            
        Returns:
            包含大纲结构的字典
        """
        keywords_str = ", ".join(keywords) if keywords else ""
        
        prompt = f"""请为以下主题设计一个详细的文章大纲。

文章主题: {topic}
关键词: {keywords_str}
章节数量: {sections_count}

请提供以下格式的JSON输出:
{{
  "title": "文章标题",
  "introduction": "引言内容概要",
  "sections": [
    {{
      "title": "第一章节标题",
      "content": "章节内容概要",
      "subsections": [
        {{
          "title": "第一子章节标题",
          "content": "子章节内容概要"
        }},
        // 更多子章节...
      ]
    }},
    // 更多章节...
  ],
  "conclusion": "结论内容概要"
}}

每个章节应包含2-3个子章节。确保大纲逻辑连贯，内容全面。
只输出JSON格式的大纲，不要添加任何其他说明。
"""
        
        result = await self.ask_llm(
            role=LLMRole.PLANNER,
            prompt=prompt,
            context=context,
            temperature=0.2  # 使用较低的温度以获得更可控的输出
        )
        
        # 尝试解析JSON
        try:
            import json
            outline = self._extract_json(result)
            return outline
        except json.JSONDecodeError as e:
            logger.error(f"解析大纲JSON失败: {str(e)}")
            # 返回基本大纲
            return self._generate_fallback_outline(topic)
    
    async def write_article_section(self,
                                  topic: str,
                                  section_title: str,
                                  outline: str = "",
                                  keywords: List[str] = None,
                                  word_count: int = 500) -> str:
        """生成文章章节
        
        Args:
            topic: 文章主题
            section_title: 章节标题
            outline: 大纲内容
            keywords: 关键词列表
            word_count: 目标字数
            
        Returns:
            生成的章节内容
        """
        keywords_str = ", ".join(keywords) if keywords else ""
        
        prompt = f"""请为以下文章章节撰写高质量的内容。

文章主题: {topic}
章节标题: {section_title}
目标字数: {word_count}字左右
关键词: {keywords_str}
章节大纲: {outline}

请提供完整、有逻辑性和连贯性的章节内容。内容应该围绕章节标题展开，包含相关的事实、分析和见解。
只需要输出章节的内容部分，不需要输出标题。每个段落之间用空行分隔。
不要输出章节大纲或指导说明，直接输出章节正文内容。
"""
        
        return await self.ask_llm(
            role=LLMRole.WRITER,
            prompt=prompt,
            context={"topic": topic, "section": section_title, "keywords": keywords},
            temperature=0.7,
            max_tokens=word_count * 2
        )
    
    async def write_article_subsection(self,
                                     topic: str,
                                     section_title: str,
                                     subsection_title: str,
                                     outline: str = "",
                                     keywords: List[str] = None,
                                     word_count: int = 300) -> str:
        """生成文章子章节
        
        Args:
            topic: 文章主题
            section_title: 主章节标题
            subsection_title: 子章节标题
            outline: 大纲内容
            keywords: 关键词列表
            word_count: 目标字数
            
        Returns:
            生成的子章节内容
        """
        keywords_str = ", ".join(keywords) if keywords else ""
        
        prompt = f"""请为以下文章子章节撰写高质量的内容。

文章主题: {topic}
主章节: {section_title}
子章节标题: {subsection_title}
目标字数: {word_count}字左右
关键词: {keywords_str}
子章节大纲: {outline}

请提供完整、有逻辑性和连贯性的子章节内容。内容应该紧密围绕子章节标题展开，包含相关的事实、分析和见解。
只需要输出子章节的内容部分，不需要输出标题。每个段落之间用空行分隔。
不要输出章节大纲或指导说明，直接输出子章节正文内容。
"""
        
        return await self.ask_llm(
            role=LLMRole.WRITER,
            prompt=prompt,
            context={"topic": topic, "section": section_title, "subsection": subsection_title, "keywords": keywords},
            temperature=0.7,
            max_tokens=word_count * 2
        )
    
    async def write_introduction(self,
                               topic: str,
                               keywords: List[str] = None,
                               word_count: int = 250) -> str:
        """生成文章引言
        
        Args:
            topic: 文章主题
            keywords: 关键词列表
            word_count: 目标字数
            
        Returns:
            生成的引言内容
        """
        keywords_str = ", ".join(keywords) if keywords else ""
        
        prompt = f"""请为以下主题撰写一篇吸引人的文章引言。

文章主题: {topic}
关键词: {keywords_str}
目标字数: {word_count}字左右

引言应该简明扼要地介绍文章主题，吸引读者注意，并概述文章将要讨论的主要内容。
请确保引言内容有吸引力，让读者想要继续阅读文章。
不要输出指导说明，直接输出引言内容。
"""
        
        return await self.ask_llm(
            role=LLMRole.WRITER,
            prompt=prompt,
            context={"topic": topic, "keywords": keywords},
            temperature=0.7,
            max_tokens=word_count * 2
        )
    
    async def write_conclusion(self,
                            topic: str,
                            section_titles: List[str],
                            word_count: int = 250) -> str:
        """生成文章结论
        
        Args:
            topic: 文章主题
            section_titles: 章节标题列表
            word_count: 目标字数
            
        Returns:
            生成的结论内容
        """
        sections_str = "\n".join([f"- {title}" for title in section_titles])
        
        prompt = f"""请为以下主题撰写一篇有效的文章结论。

文章主题: {topic}
文章章节:
{sections_str}
目标字数: {word_count}字左右

结论应该总结文章的主要观点，强调文章的核心信息，并提供最终的见解或建议。
请确保结论内容有深度，能够给读者留下深刻印象。
不要输出指导说明，直接输出结论内容。
"""
        
        return await self.ask_llm(
            role=LLMRole.WRITER,
            prompt=prompt,
            context={"topic": topic, "sections": section_titles},
            temperature=0.7,
            max_tokens=word_count * 2
        )
    
    async def edit_content(self, 
                         content: str, 
                         instructions: str = "改进文章的流畅度和可读性",
                         context: Optional[Dict[str, Any]] = None) -> str:
        """编辑优化内容
        
        Args:
            content: 待编辑的内容
            instructions: 编辑指示
            context: 上下文信息
            
        Returns:
            编辑后的内容
        """
        prompt = f"""请编辑优化以下内容。

编辑指示: {instructions}

原始内容:
{content}

请直接提供编辑后的完整内容，不要添加任何解释或者标记修改的地方。
保持原文的主要信息和结构，但进行必要的改进以符合编辑指示。
"""
        
        return await self.ask_llm(
            role=LLMRole.EDITOR,
            prompt=prompt,
            context=context,
            temperature=0.5
        )
    
    async def optimize_seo(self, 
                         content: str, 
                         keywords: List[str], 
                         topic: str,
                         context: Optional[Dict[str, Any]] = None) -> str:
        """进行SEO优化
        
        Args:
            content: 原始内容
            keywords: SEO关键词
            topic: 文章主题
            context: 上下文信息
            
        Returns:
            SEO优化后的内容
        """
        keywords_str = ", ".join(keywords)
        
        prompt = f"""请对以下内容进行SEO优化。

文章主题: {topic}
SEO关键词: {keywords_str}

原始内容:
{content}

请对内容进行SEO优化，确保:
1. 关键词自然分布在文章中，避免关键词堆砌
2. 优化标题、小标题和段落开头，使其更有吸引力且包含关键词
3. 改善内容的可读性和参与度
4. 确保内容结构清晰，使用适当的小标题划分
5. 添加有价值的信息，使内容更全面

直接提供优化后的完整内容，不要添加任何解释或者标记修改的地方。
"""
        
        return await self.ask_llm(
            role=LLMRole.SEO_EXPERT,
            prompt=prompt,
            context=context,
            temperature=0.5
        )
    
    async def finalize_content(self, 
                             content: Dict[str, Any], 
                             instructions: str = "确保内容质量和完整性",
                             context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """最终处理内容
        
        Args:
            content: 内容字典
            instructions: 处理指示
            context: 上下文信息
            
        Returns:
            最终处理后的内容
        """
        import json
        
        # 将内容转为JSON字符串
        content_str = json.dumps(content, ensure_ascii=False, indent=2)
        
        prompt = f"""请审核并最终处理以下文章内容。

处理指示: {instructions}

文章内容 (JSON格式):
{content_str}

请审核文章结构和内容，确保质量和完整性，并针对以下方面进行改进:
1. 标题是否吸引人且准确
2. 引言是否能够吸引读者并概述主题
3. 各章节内容是否充分且相关
4. 结论是否有效地总结了内容并首尾呼应
5. 整体结构是否连贯且逻辑清晰

请直接提供最终处理后的完整JSON，保持原有结构，不要添加任何解释。
"""
        
        result = await self.ask_llm(
            role=LLMRole.FINALIZER,
            prompt=prompt,
            context=context,
            temperature=0.3
        )
        
        # 尝试解析JSON
        try:
            finalized_content = self._extract_json(result)
            return finalized_content
        except json.JSONDecodeError:
            logger.error("解析最终内容JSON失败，返回原始内容")
            return content
    
    async def generate_search_strategy(self, 
                                     topic: str, 
                                     keywords: List[str] = None,
                                     context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """生成搜索策略
        
        Args:
            topic: 搜索主题
            keywords: 关键词列表
            context: 上下文信息
            
        Returns:
            搜索策略字典
        """
        keywords_str = ", ".join(keywords) if keywords else ""
        
        prompt = f"""请为以下主题设计详细的搜索策略。

搜索主题: {topic}
关键词: {keywords_str}

请提供以下格式的JSON输出:
{{
  "search_queries": [
    {{
      "query": "搜索查询1",
      "rationale": "使用此查询的原因"
    }},
    // 更多搜索查询...
  ],
  "information_types": [
    "需要搜集的信息类型1",
    "需要搜集的信息类型2",
    // 更多信息类型...
  ],
  "sources_to_prioritize": [
    "应优先考虑的信息源1",
    "应优先考虑的信息源2",
    // 更多信息源...
  ],
  "evaluation_criteria": [
    "评估搜索结果的标准1",
    "评估搜索结果的标准2",
    // 更多评估标准...
  ]
}}

请确保搜索策略全面且针对性强，能够有效收集与主题相关的高质量信息。
只输出JSON格式的搜索策略，不要添加任何其他说明。
"""
        
        result = await self.ask_llm(
            role=LLMRole.SEARCHER,
            prompt=prompt,
            context=context,
            temperature=0.5
        )
        
        # 尝试解析JSON
        try:
            import json
            strategy = self._extract_json(result)
            return strategy
        except json.JSONDecodeError:
            logger.error("解析搜索策略JSON失败")
            # 返回基本搜索策略
            return {
                "search_queries": [
                    {"query": topic, "rationale": "直接使用主题进行搜索"}
                ],
                "information_types": ["概述", "定义", "案例", "数据"],
                "sources_to_prioritize": ["官方网站", "学术文章", "新闻"],
                "evaluation_criteria": ["相关性", "可靠性", "时效性"]
            }
    
    async def analyze_research_materials(self, 
                                       topic: str, 
                                       materials: List[Dict[str, Any]],
                                       context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """分析研究材料
        
        Args:
            topic: 研究主题
            materials: 研究材料列表
            context: 上下文信息
            
        Returns:
            研究分析结果
        """
        import json
        
        # 将材料转为JSON字符串
        materials_str = json.dumps(materials, ensure_ascii=False, indent=2)
        
        prompt = f"""请分析以下与"{topic}"相关的研究材料。

研究材料:
{materials_str}

请提供以下格式的JSON输出:
{{
  "key_findings": [
    "关键发现1",
    "关键发现2",
    // 更多关键发现...
  ],
  "themes": [
    "主题1",
    "主题2",
    // 更多主题...
  ],
  "contradictions": [
    "材料中的矛盾点1",
    "材料中的矛盾点2",
    // 更多矛盾点...
  ],
  "knowledge_gaps": [
    "知识空白1",
    "知识空白2",
    // 更多知识空白...
  ],
  "summary": "材料的综合摘要",
  "recommendations": [
    "基于分析的建议1",
    "基于分析的建议2",
    // 更多建议...
  ]
}}

请确保分析全面、客观且深入，提取材料中的关键信息并识别模式和趋势。
只输出JSON格式的分析结果，不要添加任何其他说明。
"""
        
        result = await self.ask_llm(
            role=LLMRole.RESEARCHER,
            prompt=prompt,
            context=context,
            temperature=0.3,
            max_tokens=3000
        )
        
        # 尝试解析JSON
        try:
            analysis = self._extract_json(result)
            return analysis
        except json.JSONDecodeError:
            logger.error("解析研究分析JSON失败")
            # 返回基本分析结果
            return {
                "key_findings": ["无法从材料中提取关键发现"],
                "themes": ["无法识别主题"],
                "contradictions": [],
                "knowledge_gaps": ["需要更多信息"],
                "summary": f"关于'{topic}'的材料分析不完整",
                "recommendations": ["收集更多信息"]
            }
    
    async def reflect_on_process(self, 
                             process_data: Dict[str, Any],
                             topic: str,
                             context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """反思生成过程
        
        Args:
            process_data: 过程数据
            topic: 文章主题
            context: 上下文信息
            
        Returns:
            反思结果
        """
        import json
        
        # 将过程数据转为JSON字符串
        process_data_str = json.dumps(process_data, ensure_ascii=False, indent=2)
        
        prompt = f"""请对以下关于"{topic}"的内容生成过程进行反思和分析。

过程数据:
{process_data_str}

请提供以下格式的JSON输出:
{{
  "strengths": [
    "过程优势1",
    "过程优势2",
    // 更多优势...
  ],
  "weaknesses": [
    "过程劣势1",
    "过程劣势2",
    // 更多劣势...
  ],
  "key_insights": [
    "关键洞见1",
    "关键洞见2",
    // 更多洞见...
  ],
  "improvement_suggestions": [
    "改进建议1",
    "改进建议2",
    // 更多建议...
  ],
  "summary": "过程反思总结"
}}

请确保反思深入、客观且有建设性，识别过程中的优缺点并提出有价值的改进建议。
只输出JSON格式的反思结果，不要添加任何其他说明。
"""
        
        result = await self.ask_llm(
            role=LLMRole.REFLECTOR,
            prompt=prompt,
            context=context,
            temperature=0.5
        )
        
        # 尝试解析JSON
        try:
            reflection = self._extract_json(result)
            return reflection
        except json.JSONDecodeError:
            logger.error("解析反思JSON失败")
            # 返回基本反思结果
            return {
                "strengths": ["流程按预期执行"],
                "weaknesses": ["解析反思过程出现问题"],
                "key_insights": ["需要改进JSON解析功能"],
                "improvement_suggestions": ["确保生成内容符合预期格式"],
                "summary": "反思过程遇到技术问题，需要进一步完善"
            }
    
    def _build_full_prompt(self, role: LLMRole, prompt: str, context: Optional[Dict[str, Any]] = None) -> str:
        """构建完整提示词
        
        Args:
            role: LLM角色
            prompt: 提示词
            context: 上下文信息
            
        Returns:
            完整提示词
        """
        # 获取角色系统提示词
        role_config = self.role_configs.get(role, self.role_configs[LLMRole.WRITER])
        system_prompt = role_config["system_prompt"]
        
        # 构建上下文信息
        context_str = ""
        if context:
            context_list = []
            context_list.append("上下文信息:")
            for key, value in context.items():
                try:
                    # 防止字符串中的花括号被误解为格式说明符
                    safe_key = str(key).replace("{", "{{").replace("}", "}}")
                    safe_value = str(value).replace("{", "{{").replace("}", "}}")
                    context_list.append(f"- {safe_key}: {safe_value}")
                except Exception as e:
                    logger.warning(f"处理上下文键值对时出错: {str(e)}")
            
            context_list.append("")  # 添加一个空行
            context_str = "\n".join(context_list)
        
        # 使用简单连接而不是复杂的f-string，避免格式说明符问题
        full_prompt = system_prompt + "\n\n"
        if context_str:
            full_prompt += context_str + "\n"
        full_prompt += prompt
        
        return full_prompt
    
    def _extract_json(self, text: str) -> Dict[str, Any]:
        """从文本中提取JSON
        
        Args:
            text: 文本内容
            
        Returns:
            解析后的JSON对象
        """
        import json
        import re
        
        # 尝试直接解析
        try:
            return json.loads(text)
        except json.JSONDecodeError:
            # 尝试提取JSON部分
            json_pattern = r'```(?:json)?\s*([\s\S]*?)```'
            match = re.search(json_pattern, text)
            
            if match:
                try:
                    return json.loads(match.group(1))
                except json.JSONDecodeError:
                    pass
            
            # 寻找最长的花括号对
            try:
                start = text.find('{')
                if start >= 0:
                    # 找到对应的右括号
                    balance = 0
                    for i in range(start, len(text)):
                        if text[i] == '{':
                            balance += 1
                        elif text[i] == '}':
                            balance -= 1
                            if balance == 0:
                                return json.loads(text[start:i+1])
            except:
                pass
            
            # 无法解析，抛出异常
            raise json.JSONDecodeError("无法解析JSON", text, 0)
    
    def _generate_fallback_outline(self, topic: str) -> Dict[str, Any]:
        """生成后备大纲
        
        Args:
            topic: 文章主题
            
        Returns:
            基本大纲
        """
        return {
            "title": topic,
            "introduction": f"介绍{topic}的背景和重要性",
            "sections": [
                {
                    "title": "背景与现状",
                    "content": f"介绍{topic}的发展背景和当前状况",
                    "subsections": [
                        {"title": "历史发展", "content": f"回顾{topic}的发展历程"},
                        {"title": "现状分析", "content": f"分析{topic}的当前状态"}
                    ]
                },
                {
                    "title": "核心分析",
                    "content": f"深入分析{topic}的核心内容",
                    "subsections": [
                        {"title": "关键要素", "content": f"探讨{topic}的关键组成部分"},
                        {"title": "工作原理", "content": f"解释{topic}的运作机制"}
                    ]
                },
                {
                    "title": "实际应用",
                    "content": f"探讨{topic}的实际应用场景",
                    "subsections": [
                        {"title": "应用案例", "content": f"分析{topic}的典型应用案例"},
                        {"title": "实施策略", "content": f"讨论{topic}的最佳实施方法"}
                    ]
                },
                {
                    "title": "未来展望",
                    "content": f"展望{topic}的未来发展趋势",
                    "subsections": [
                        {"title": "发展趋势", "content": f"预测{topic}的未来发展方向"},
                        {"title": "机遇挑战", "content": f"分析{topic}面临的机遇和挑战"}
                    ]
                }
            ],
            "conclusion": f"总结{topic}的重要性和发展前景"
        } 