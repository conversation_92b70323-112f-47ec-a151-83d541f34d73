"""
文本管理器 - 用于管理和协调各种文本生成方式
"""
import os
from typing import Dict, Any, List, Optional, Union, Tuple
from enum import Enum
import json
import uuid
import asyncio
import time
import logging
from datetime import datetime
import re

from src.services.generator.text_generator.base import (
    BaseTextGenerator,
    TextGenerationConfig,
    TextGenerationResult,
    TextGenerationModel,
    ContentType,
    WritingStyle,
    WritingTone,
    ContentStructure,
    HumanizationLevel,
    ContentSource
)
from src.services.generator.text_generator.qianwen_generator import QianwenTextGenerator
from src.services.generator.text_generator.deepseek_generator import DeepSeekTextGenerator
from src.services.generator.text_generator.deepseek_reasoner_generator import DeepSeekReasonerTextGenerator

# 配置日志
logger = logging.getLogger("text_generator")

class TextGenerationProvider(Enum):
    """文本生成提供商"""
    QIANWEN = "qianwen"
    DEEPSEEK = "deepseek"
    DEEPSEEK_REASONER = "deepseek_reasoner"
    
    @staticmethod
    def from_str(provider_str: str) -> 'TextGenerationProvider':
        """从字符串获取提供商枚举
        
        Args:
            provider_str: 提供商名称字符串
            
        Returns:
            对应的提供商枚举，如果不存在则返回QIANWEN
        """
        for provider in TextGenerationProvider:
            if provider.value == provider_str:
                return provider
        return TextGenerationProvider.QIANWEN

class TextManager:
    """文本生成管理器"""
    
    def __init__(self, qianwen_api_key: Optional[str] = None, deepseek_api_key: Optional[str] = None):
        """初始化文本生成管理器
        
        Args:
            qianwen_api_key: 千问API密钥
            deepseek_api_key: DeepSeek API密钥
        """
        self.qianwen_generator = QianwenTextGenerator(qianwen_api_key)
        self.deepseek_generator = DeepSeekTextGenerator(deepseek_api_key)
        self.deepseek_reasoner_generator = DeepSeekReasonerTextGenerator(deepseek_api_key)
        
        # 缓存设置
        self._cache = {}
        self._cache_size = 100  # 最大缓存条目数
        self._cache_expiry = 3600 * 24  # 缓存过期时间（秒）
        self._cache_hits = 0
        self._cache_misses = 0
        
        # 重试设置
        self.max_retries = 3
        self.retry_delay = 2  # 秒
        
        # 进度跟踪
        self.current_task_id = None
        self.task_progress = {}
        
        logger.info("文本生成管理器初始化完成")
    
    async def generate_text(self, 
                          prompt: str, 
                          model: Union[str, TextGenerationModel] = TextGenerationModel.QWEN_TURBO,
                          provider: Union[str, TextGenerationProvider] = TextGenerationProvider.QIANWEN,
                          max_tokens: int = 2000,
                          temperature: float = 0.7,
                          content_type: Optional[ContentType] = None,
                          writing_style: Optional[WritingStyle] = None,
                          writing_tone: Optional[WritingTone] = None,
                          keywords: Optional[List[str]] = None,
                          content_structure: Optional[ContentStructure] = None,
                          humanization_level: Optional[HumanizationLevel] = None,
                          use_cache: bool = True,
                          api_key: Optional[str] = None,
                          json_format: bool = False) -> TextGenerationResult:
        """生成文本
        
        Args:
            prompt: 提示词
            model: 模型
            provider: 提供商
            max_tokens: 最大生成token数
            temperature: 生成温度
            content_type: 内容类型
            writing_style: 写作风格
            writing_tone: 写作语气
            keywords: 关键词列表
            content_structure: 内容结构
            humanization_level: 拟人化程度
            use_cache: 是否使用缓存
            api_key: API密钥
            json_format: 是否返回JSON格式
            
        Returns:
            文本生成结果
        """
        # 进度跟踪
        task_id = str(uuid.uuid4())
        self.current_task_id = task_id
        self.task_progress[task_id] = {
            "status": "started", 
            "progress": 0, 
            "description": "准备生成文本"
        }
        
        # 转换枚举值
        if isinstance(model, str):
            model = TextGenerationModel.from_str(model)
        
        if isinstance(provider, str):
            provider = TextGenerationProvider.from_str(provider)
        
        # 构建配置
        config = TextGenerationConfig(
            prompt=prompt,
            model=model,
            max_tokens=max_tokens,
            temperature=temperature,
            content_type=content_type,
            writing_style=writing_style,
            writing_tone=writing_tone,
            keywords=keywords,
            content_structure=content_structure,
            humanization_level=humanization_level,
            api_key=api_key,
            json_format=json_format
        )
        
        # 检查缓存
        if use_cache:
            cache_key = self._generate_cache_key(config, provider.value)
            cached_result = self._get_from_cache(cache_key)
            if cached_result:
                logger.info(f"从缓存获取文本生成结果 (provider={provider.value}, model={model.value})")
                self._cache_hits += 1
                self.task_progress[task_id] = {"status": "completed", "progress": 100, "description": "从缓存获取结果"}
                return cached_result
            self._cache_misses += 1
        
        self.task_progress[task_id] = {"status": "processing", "progress": 20, "description": "发送请求到API"}
        
        # 选择生成器
        generator = self._get_generator(provider.value)
        if not generator:
            error_msg = f"不支持的提供商: {provider.value}"
            logger.error(error_msg)
            self.task_progress[task_id] = {"status": "failed", "progress": 0, "description": error_msg}
            return TextGenerationResult(
                content=f"错误: {error_msg}",
                prompt=prompt
            )
        
        # 设置重试
        retries = 0
        while retries <= self.max_retries:
            try:
                # 生成文本
                self.task_progress[task_id] = {
                    "status": "processing", 
                    "progress": 40 + retries * 10, 
                    "description": f"尝试生成文本 (尝试 {retries + 1}/{self.max_retries + 1})"
                }
                
                result = await generator.generate(config)
                
                if result.content.startswith("错误:"):
                    if retries < self.max_retries:
                        logger.warning(f"生成文本失败，将进行重试 ({retries + 1}/{self.max_retries}): {result.content}")
                        retries += 1
                        await asyncio.sleep(self.retry_delay * (2 ** retries))  # 指数退避
                        continue
                    else:
                        logger.error(f"生成文本失败，已达到最大重试次数: {result.content}")
                        self.task_progress[task_id] = {"status": "failed", "progress": 0, "description": "达到最大重试次数"}
                        return result
                
                # 缓存结果
                if use_cache:
                    self._add_to_cache(cache_key, result)
                
                self.task_progress[task_id] = {"status": "completed", "progress": 100, "description": "文本生成完成"}
                logger.info(f"成功生成文本 (provider={provider.value}, model={model.value}, tokens={result.token_count})")
                return result
                
            except Exception as e:
                if retries < self.max_retries:
                    logger.warning(f"生成文本时出现异常，将进行重试 ({retries + 1}/{self.max_retries}): {str(e)}")
                    retries += 1
                    await asyncio.sleep(self.retry_delay * (2 ** retries))  # 指数退避
                else:
                    error_msg = f"生成文本失败: {str(e)}"
                    logger.error(error_msg)
                    self.task_progress[task_id] = {"status": "failed", "progress": 0, "description": error_msg}
                    return TextGenerationResult(
                        content=f"错误: {error_msg}",
                        prompt=prompt
                    )
    
    async def generate_article(self,
                              url: Optional[str] = None,
                              title: Optional[str] = None,
                              content: Optional[str] = None, 
                              writing_style: Optional[Union[str, WritingStyle]] = WritingStyle.PROFESSIONAL,
                              writing_tone: Optional[Union[str, WritingTone]] = WritingTone.NEUTRAL,
                              provider: Union[str, TextGenerationProvider] = TextGenerationProvider.QIANWEN,
                              api_key: Optional[str] = None) -> Dict[str, Any]:
        """生成完整文章
        
        Args:
            url: 输入URL（用于内容提取）
            title: 文章标题
            content: 输入内容
            writing_style: 写作风格
            writing_tone: 写作语气
            provider: 提供商
            api_key: API密钥
            
        Returns:
            包含文章和元数据的字典
        """
        # 创建任务ID和跟踪
        task_id = str(uuid.uuid4())
        self.current_task_id = task_id
        self.task_progress[task_id] = {
            "status": "started", 
            "progress": 0, 
            "description": "开始文章生成流程"
        }
        
        try:
            # 1. 内容提取和分析
            self.task_progress[task_id] = {
                "status": "processing", 
                "progress": 10, 
                "description": "提取和分析内容"
            }
            
            extracted_data = {}
            if url:
                extracted_data = await self._extract_content(url)
                logger.info(f"从URL提取内容: {url}")
                
                if not title and "title" in extracted_data:
                    title = extracted_data.get("title")
                    logger.info(f"使用提取的标题: {title}")
                
                if not content and "content" in extracted_data:
                    content = extracted_data.get("content")
            
            # 确保有标题
            if not title:
                title = "未命名文章"
                logger.warning("未提供标题，使用默认标题")
            
            # 2. 生成关键词和内容分析
            self.task_progress[task_id] = {
                "status": "processing", 
                "progress": 20, 
                "description": "分析内容并生成关键词"
            }
            
            analysis_result = await self._analyze_content(title, content, provider, api_key)
            keywords = analysis_result.get("keywords", [])
            emotion = analysis_result.get("emotion", "neutral")
            main_points = analysis_result.get("main_points", [])
            
            logger.info(f"内容分析完成，提取了 {len(keywords)} 个关键词")
            
            # 3. 文章计划
            self.task_progress[task_id] = {
                "status": "processing", 
                "progress": 30, 
                "description": "生成文章大纲"
            }
            
            outline_data = await self._generate_outline(title, keywords, model=self._get_model_for_provider(provider), provider=provider)
            outline = outline_data.get("outline", ["引言", "主要内容", "结论"])
            
            # 如果有更新的关键词，使用它们
            if "keywords" in outline_data and outline_data["keywords"]:
                keywords = outline_data["keywords"]
            
            logger.info(f"文章大纲生成完成，包含 {len(outline)} 个章节")
            
            # 4. 生成引言
            self.task_progress[task_id] = {
                "status": "processing", 
                "progress": 40, 
                "description": "生成文章引言"
            }
            
            introduction = await self._generate_introduction(
                title=title,
                outline=outline,
                keywords=keywords,
                writing_style=writing_style,
                writing_tone=writing_tone,
                model=self._get_model_for_provider(provider),
                provider=provider
            )
            
            # 5. 生成各章节
            self.task_progress[task_id] = {
                "status": "processing", 
                "progress": 50, 
                "description": "生成文章章节"
            }
            
            sections = []
            total_sections = len(outline)
            for i, section_title in enumerate(outline):
                section_progress = int(50 + (i / total_sections) * 30)
                self.task_progress[task_id] = {
                    "status": "processing", 
                    "progress": section_progress, 
                    "description": f"生成章节 {i+1}/{total_sections}: {section_title}"
                }
                
                section_content = await self._generate_section(
                    article_title=title,
                    section_title=section_title,
                    keywords=keywords,
                    writing_style=writing_style,
                    writing_tone=writing_tone,
                    model=self._get_model_for_provider(provider),
                    provider=provider
                )
                
                sections.append({
                    "title": section_title,
                    "content": section_content
                })
            
            # 6. 生成结论
            self.task_progress[task_id] = {
                "status": "processing", 
                "progress": 80, 
                "description": "生成文章结论"
            }
            
            conclusion = await self._generate_conclusion(
                title=title,
                outline=outline,
                keywords=keywords,
                writing_style=writing_style,
                writing_tone=writing_tone,
                model=self._get_model_for_provider(provider),
                provider=provider
            )
            
            # 7. 生成FAQ
            self.task_progress[task_id] = {
                "status": "processing", 
                "progress": 90, 
                "description": "生成常见问题解答"
            }
            
            faqs = await self._generate_faqs(
                title=title,
                content=content,
                keywords=keywords,
                model=self._get_model_for_provider(provider),
                provider=provider
            )
            
            # 8. 组装文章
            self.task_progress[task_id] = {
                "status": "processing", 
                "progress": 95, 
                "description": "组装最终文章"
            }
            
            article = {
                "title": title,
                "keywords": keywords,
                "introduction": introduction,
                "sections": sections,
                "conclusion": conclusion,
                "faqs": faqs,
                "metadata": {
                    "writing_style": writing_style.value if isinstance(writing_style, WritingStyle) else writing_style,
                    "writing_tone": writing_tone.value if isinstance(writing_tone, WritingTone) else writing_tone,
                    "provider": provider.value if isinstance(provider, TextGenerationProvider) else provider,
                    "generated_at": datetime.now().isoformat(),
                    "emotion": emotion,
                    "main_points": main_points,
                    "extracted_from_url": url is not None,
                    "source_url": url,
                    "content_analysis": analysis_result
                }
            }
            
            self.task_progress[task_id] = {
                "status": "completed", 
                "progress": 100, 
                "description": "文章生成完成"
            }
            
            # 记录成功信息
            word_count = len(introduction.split()) + sum(len(s["content"].split()) for s in sections) + len(conclusion.split())
            logger.info(f"文章生成完成: 标题='{title}', 章节数={len(sections)}, 字数≈{word_count}")
            
            return article
        
        except Exception as e:
            error_msg = f"生成文章失败: {str(e)}"
            logger.error(error_msg, exc_info=True)
            self.task_progress[task_id] = {
                "status": "failed", 
                "progress": 0, 
                "description": error_msg
            }
            return {
                "error": error_msg,
                "title": title or "生成失败",
                "keywords": [],
                "introduction": "",
                "sections": [],
                "conclusion": "",
                "faqs": [],
                "metadata": {
                    "error": str(e),
                    "generated_at": datetime.now().isoformat()
                }
            }
    
    async def _extract_content(self, url: str) -> Dict[str, Any]:
        """从URL提取内容
        
        Args:
            url: 要提取内容的URL
            
        Returns:
            包含提取内容的字典
        """
        try:
            logger.info(f"从URL提取内容: {url}")
            
            # 根据URL类型选择不同的提取方式
            if 'mp.weixin.qq.com' in url:
                # 提取微信文章
                from src.services.extractor.wechat_extractor import WechatArticleExtractor
                extractor = WechatArticleExtractor()
                result = await extractor.extract_from_url(url)
                return result
            
            elif 'bilibili.com' in url or 'b23.tv' in url or 'douyin.com' in url or 'iesdouyin.com' in url:
                # 使用统一内容提取和分析接口处理视频
                from src.services.extractor.content_extractor import extract_and_analyze
                result = await extract_and_analyze(url)
                
                if "error" not in result:
                    # 获取分析结果
                    analysis = result.get("analysis", {})
                    
                    return {
                        "title": result.get("title", "视频内容"),
                        "content": result.get("content", ""),
                        "summary": analysis.get("summary", ""),
                        "keywords": analysis.get("keywords", []),
                        "recommended_titles": analysis.get("recommended_titles", []),
                        "content_directions": analysis.get("content_directions", []),
                        "video_data": result,
                        "url": url
                    }
                else:
                    return {
                        "error": result.get("error", "视频内容提取失败"),
                        "title": "提取失败",
                        "content": f"无法从 {url} 提取视频内容",
                        "url": url
                    }
            
            else:
                # 通用网页内容提取
                from langchain_community.document_loaders import WebBaseLoader
                loader = WebBaseLoader(url)
                docs = loader.load()
                return {
                    "title": docs[0].metadata.get("title", "未知标题"),
                    "content": docs[0].page_content,
                    "url": url,
                    "author": docs[0].metadata.get("author", "未知作者"),
                    "publish_time": docs[0].metadata.get("publish_time", "未知时间"),
                }
        
        except Exception as e:
            logger.error(f"从URL提取内容失败: {str(e)}", exc_info=True)
            return {
                "error": f"内容提取失败: {str(e)}",
                "title": "提取失败",
                "content": f"无法从 {url} 提取内容",
                "url": url
            }
    
    async def _analyze_content(self, 
                              title: str, 
                              content: Optional[str], 
                              provider: Union[str, TextGenerationProvider],
                              api_key: Optional[str] = None) -> Dict[str, Any]:
        """分析内容，提取关键词和情感
        
        Args:
            title: 标题
            content: 内容
            provider: 提供商
            api_key: API密钥
            
        Returns:
            分析结果
        """
        try:
            # 如果没有内容，只基于标题分析
            if not content:
                content = title
            
            # 截断过长的内容
            if len(content) > 6000:
                content = content[:6000] + "...(内容已截断)"
            
            prompt = f"""
分析以下文章的标题和内容，提取关键信息：

标题: {title}

内容:
{content}

请提供以下信息:
1. 提取10个最重要的关键词（单词或短语）
2. 分析文章的情感倾向（积极、消极或中性）
3. 总结3-5个主要观点或要点
4. 分析内容的适合目标受众
5. 确定文章的主要主题和次要主题

以JSON格式返回结果，包含以下字段：keywords, emotion, main_points, target_audience, themes。
只返回JSON对象，不要包含额外的解释或文本。
"""
            
            model = self._get_model_for_provider(provider)
            result = await self.generate_text(
                prompt=prompt,
                model=model,
                provider=provider,
                max_tokens=1000,
                temperature=0.3,
                api_key=api_key
            )
            
            try:
                # 解析JSON结果
                analysis = json.loads(result.content)
                return analysis
            except json.JSONDecodeError:
                # 如果解析失败，尝试提取关键词
                import re
                keywords_match = re.search(r'keywords"?\s*:?\s*\[(.*?)\]', result.content)
                keywords = []
                if keywords_match:
                    keywords_str = keywords_match.group(1)
                    keywords = [k.strip().strip('"\'') for k in keywords_str.split(',')]
                
                return {
                    "keywords": keywords,
                    "emotion": "neutral",
                    "main_points": [],
                    "target_audience": "general",
                    "themes": [title]
                }
                
        except Exception as e:
            logger.error(f"内容分析失败: {str(e)}", exc_info=True)
            return {
                "keywords": [w for w in title.split() if len(w) > 3][:5],
                "emotion": "neutral",
                "main_points": [],
                "target_audience": "general",
                "themes": [title]
            }
    
    def get_task_progress(self, task_id: Optional[str] = None) -> Dict[str, Any]:
        """获取任务进度
        
        Args:
            task_id: 任务ID，如果为None则返回当前任务
            
        Returns:
            任务进度信息
        """
        if task_id is None:
            task_id = self.current_task_id
            
        if not task_id or task_id not in self.task_progress:
            return {"status": "unknown", "progress": 0, "description": "未知任务"}
            
        return self.task_progress[task_id]
    
    def get_cache_stats(self) -> Dict[str, int]:
        """获取缓存统计信息
        
        Returns:
            缓存统计
        """
        return {
            "cache_size": len(self._cache),
            "cache_hits": self._cache_hits,
            "cache_misses": self._cache_misses
        }
    
    def clear_cache(self) -> None:
        """清除缓存"""
        self._cache = {}
        logger.info("文本生成缓存已清除")
    
    def _get_generator(self, provider: str) -> Optional[BaseTextGenerator]:
        """获取对应的文本生成器
        
        Args:
            provider: 提供商名称
            
        Returns:
            文本生成器实例
        """
        if provider == TextGenerationProvider.QIANWEN.value:
            return self.qianwen_generator
        elif provider == TextGenerationProvider.DEEPSEEK.value:
            return self.deepseek_generator
        elif provider == TextGenerationProvider.DEEPSEEK_REASONER.value:
            return self.deepseek_reasoner_generator
        else:
            return None
    
    def _get_model_for_provider(self, provider: Union[str, TextGenerationProvider]) -> TextGenerationModel:
        """获取提供商的默认模型
        
        Args:
            provider: 提供商
            
        Returns:
            默认模型
        """
        if isinstance(provider, TextGenerationProvider):
            provider = provider.value
            
        if provider == TextGenerationProvider.QIANWEN.value:
            return TextGenerationModel.QWEN_TURBO
        elif provider == TextGenerationProvider.DEEPSEEK.value:
            return TextGenerationModel.DEEPSEEK_CHAT
        elif provider == TextGenerationProvider.DEEPSEEK_REASONER.value:
            return TextGenerationModel.DEEPSEEK_REASONER
        else:
            return TextGenerationModel.QWEN_TURBO
    
    def _get_from_cache(self, key: str) -> Optional[TextGenerationResult]:
        """从缓存获取结果
        
        Args:
            key: 缓存键
            
        Returns:
            缓存的结果
        """
        if key not in self._cache:
            return None
            
        entry = self._cache[key]
        
        # 检查是否过期
        if time.time() - entry["timestamp"] > self._cache_expiry:
            del self._cache[key]
            return None
            
        return entry["result"]
    
    def _add_to_cache(self, key: str, result: TextGenerationResult) -> None:
        """添加结果到缓存
        
        Args:
            key: 缓存键
            result: 结果
        """
        # 如果缓存已满，删除最旧的条目
        if len(self._cache) >= self._cache_size:
            oldest_key = min(self._cache.keys(), key=lambda k: self._cache[k]["timestamp"])
            del self._cache[oldest_key]
        
        self._cache[key] = {
            "result": result,
            "timestamp": time.time()
        }
    
    def _generate_cache_key(self, config: TextGenerationConfig, provider: str) -> str:
        """生成缓存键
        
        Args:
            config: 文本生成配置
            provider: 提供商
            
        Returns:
            缓存键
        """
        # 使用MD5哈希生成缓存键
        import hashlib
        
        key_parts = [
            provider,
            config.model.value if isinstance(config.model, TextGenerationModel) else str(config.model),
            config.prompt,
            str(config.max_tokens),
            str(config.temperature),
            config.content_type.value if config.content_type else "none",
            config.writing_style.value if config.writing_style else "none",
            config.writing_tone.value if config.writing_tone else "none",
            ",".join(config.keywords or []),
            config.content_structure.value if config.content_structure else "none",
            config.humanization_level.value if config.humanization_level else "none"
        ]
        
        key_string = "|".join(key_parts)
        return hashlib.md5(key_string.encode()).hexdigest()

    async def generate_article_section(self,
                                     section_config: Dict[str, Any],
                                     context: Dict[str, Any],
                                     provider: Union[str, TextGenerationProvider] = TextGenerationProvider.QIANWEN) -> str:
        """生成文章章节
        
        Args:
            section_config: 章节配置
            context: 上下文信息
            provider: 生成提供商
            
        Returns:
            生成的章节内容
        """
        try:
            # 构建章节生成提示词
            prompt = f"""
            请根据以下配置生成文章章节:
            
            章节信息: {json.dumps(section_config, ensure_ascii=False)}
            上下文: {json.dumps(context, ensure_ascii=False)}
            
            要求:
            1. 符合章节的情感基调
            2. 使用指定的写作风格
            3. 注意与上下文的连贯性
            4. 在指定位置预留图片空间
            5. 加入适当的过渡语句
            6. 确保论据充分
            7. 语言自然流畅
            
            请生成章节内容。
            """
            
            result = await self.generate_text(
                prompt=prompt,
                provider=provider,
                content_type=ContentType.ARTICLE_SECTION
            )
            
            return result.content
            
        except Exception as e:
            logger.error(f"章节生成失败: {e}")
            raise

    async def humanize_content(self,
                              content: str,
                              style_guide: Dict[str, Any],
                              provider: Union[str, TextGenerationProvider] = TextGenerationProvider.QIANWEN) -> str:
        """对内容进行拟人化处理
        
        Args:
            content: 原始内容
            style_guide: 风格指南
            provider: 生成提供商
            
        Returns:
            拟人化后的内容
        """
        try:
            prompt = f"""
            请对以下内容进行拟人化处理，使其更加自然、生动和有感染力：
            
            原文：{content}
            
            风格指南：{json.dumps(style_guide, ensure_ascii=False)}
            
            要求：
            1. 保持专业性的同时增加情感色彩
            2. 加入适当的口语化表达
            3. 使用更生动的比喻和修辞
            4. 增加与读者的互动感
            5. 让语言更有温度和个性
            6. 避免机械化和生硬的表达
            7. 保持行文的流畅性和自然度
            
            请返回优化后的内容。
            """
            
            result = await self.generate_text(
                prompt=prompt,
                provider=provider,
                content_type=ContentType.HUMANIZATION
            )
            
            return result.content
            
        except Exception as e:
            logger.error(f"内容拟人化失败: {e}")
            raise

    async def generate_faq(self,
                          content: str,
                          analysis_result: Dict[str, Any],
                          provider: Union[str, TextGenerationProvider] = TextGenerationProvider.QIANWEN) -> List[Dict[str, str]]:
        """生成FAQ问答
        
        Args:
            content: 文章内容
            analysis_result: 分析结果
            provider: 生成提供商
            
        Returns:
            FAQ列表
        """
        try:
            prompt = f"""
            基于以下内容生成高质量的FAQ问答：
            
            文章内容：{content}
            分析结果：{json.dumps(analysis_result, ensure_ascii=False)}
            
            要求：
            1. 问题要切中要害
            2. 回答要简明扼要
            3. 覆盖关键知识点
            4. 体现文章价值
            5. 引导读者思考
            
            请以JSON格式返回FAQ列表，每个FAQ包含question和answer字段。
            """
            
            result = await self.generate_text(
                prompt=prompt,
                provider=provider,
                content_type=ContentType.FAQ
            )
            
            try:
                faq_list = json.loads(result.content)
                return faq_list if isinstance(faq_list, list) else []
            except:
                logger.error("FAQ解析失败")
                return []
            
        except Exception as e:
            logger.error(f"FAQ生成失败: {e}")
            raise

    async def generate_from_video(self, 
                                video_content: Dict[str, Any],
                                writing_style: Optional[Union[str, WritingStyle]] = WritingStyle.PROFESSIONAL,
                                writing_tone: Optional[Union[str, WritingTone]] = WritingTone.NEUTRAL,
                                provider: Union[str, TextGenerationProvider] = TextGenerationProvider.QIANWEN,
                                api_key: Optional[str] = None) -> Dict[str, Any]:
        """基于视频内容生成文章
        
        Args:
            video_content: 视频内容，通常是视频处理器的输出
            writing_style: 写作风格
            writing_tone: 写作语气
            provider: 提供商
            api_key: API密钥
            
        Returns:
            包含文章和元数据的字典
        """
        # 创建任务ID和跟踪
        task_id = str(uuid.uuid4())
        self.current_task_id = task_id
        self.task_progress[task_id] = {
            "status": "started", 
            "progress": 0, 
            "description": "开始基于视频生成文章"
        }
        
        try:
            # 1. 提取视频信息
            video_info = video_content.get("video_info", {})
            video_analysis = video_content.get("analysis", {})
            video_generated_content = video_content.get("generated_content", {})
            
            title = video_info.get("title", "")
            description = video_info.get("description", "")
            topics = video_analysis.get("topics", [])
            key_points = video_analysis.get("key_points", [])
            summary = video_generated_content.get("summary", "")
            
            # 2. 构建文章标题和大纲
            self.task_progress[task_id] = {
                "status": "processing", 
                "progress": 20, 
                "description": "构建文章大纲"
            }
            
            if isinstance(provider, str):
                provider = TextGenerationProvider.from_str(provider)
            
            # 使用适当的模型
            model = self._get_model_for_provider(provider)
            
            # 构建大纲提示词
            outline_prompt = f"""
            请根据以下视频内容生成一篇文章的详细大纲:
            
            视频标题: {title}
            视频描述: {description}
            主要话题: {', '.join(topics) if topics else '无'}
            关键点: 
            {chr(10).join([f"- {point.get('content', '')}" for point in key_points]) if key_points else '无'}
            
            视频概要: {summary}
            
            请输出一个详细的大纲，包括:
            1. 引人注目的文章标题
            2. 5-8个主要章节，每个章节都有一个明确的副标题
            3. 每个章节下2-3个要点
            4. 引言和结论建议
            
            输出格式为JSON，如下:
            ```json
            {
              "title": "文章标题",
              "introduction": "引言概要...",
              "sections": [
                {
                  "title": "第一节标题",
                  "points": ["要点1", "要点2", "要点3"]
                },
                // 更多章节...
              ],
              "conclusion": "结论概要..."
            }
            ```
            """
            
            # 生成大纲
            outline_result = await self.generate_text(
                prompt=outline_prompt,
                model=model,
                provider=provider,
                temperature=0.7,
                api_key=api_key
            )
            
            # 解析大纲JSON
            try:
                outline_text = outline_result.content
                # 提取JSON部分
                json_match = re.search(r'```json(.*?)```', outline_text, re.DOTALL)
                if json_match:
                    outline_json = json_match.group(1)
                else:
                    # 尝试直接解析
                    outline_json = outline_text
                
                outline = json.loads(outline_json.strip())
                
                logger.info(f"成功生成基于视频的文章大纲: {outline.get('title')}")
                
            except Exception as e:
                logger.error(f"解析大纲JSON失败: {str(e)}")
                # 创建一个基本大纲
                outline = {
                    "title": title or "基于视频内容的文章",
                    "introduction": f"本文基于视频《{title}》内容整理而成。",
                    "sections": [
                        {"title": "视频概述", "points": ["视频主要内容", "创作背景", "受众分析"]},
                        {"title": "关键内容解析", "points": ["主要观点", "重要信息", "内容亮点"]}
                    ],
                    "conclusion": "总结视频要点，并给出个人观点。"
                }
                
                # 如果有关键点，添加到大纲
                if key_points and len(key_points) > 0:
                    section_points = []
                    for i, point in enumerate(key_points[:5]):  # 最多取5个关键点
                        section_points.append(point.get("content", f"关键点{i+1}"))
                    
                    outline["sections"].append({
                        "title": "视频关键点详解",
                        "points": section_points
                    })
            
            # 3. 生成文章内容
            self.task_progress[task_id] = {
                "status": "processing", 
                "progress": 40, 
                "description": "生成文章内容"
            }
            
            article_content = {
                "title": outline.get("title", ""),
                "introduction": "",
                "sections": [],
                "conclusion": ""
            }
            
            # 生成引言
            intro_prompt = f"""
            请根据以下大纲为文章写一段引人入胜的引言:
            
            文章标题: {outline.get('title')}
            引言概要: {outline.get('introduction')}
            视频内容: {summary}
            
            要求:
            1. 引言长度为300-500字
            2. 简要介绍文章主题和要点
            3. 吸引读者兴趣，并概述文章结构
            4. 写作风格: {writing_style.value if isinstance(writing_style, WritingStyle) else writing_style}
            5. 写作语气: {writing_tone.value if isinstance(writing_tone, WritingTone) else writing_tone}
            
            不要使用"本文将探讨"、"在这篇文章中"等明显的提示语。直接自然地引入主题。
            """
            
            intro_result = await self.generate_text(
                prompt=intro_prompt,
                model=model,
                provider=provider,
                temperature=0.7,
                api_key=api_key
            )
            
            article_content["introduction"] = intro_result.content
            
            # 生成各章节内容
            section_tasks = []
            for section in outline.get("sections", []):
                section_config = {
                    "title": section.get("title", ""),
                    "points": section.get("points", []),
                    "video_title": title,
                    "video_description": description,
                    "summary": summary
                }
                
                section_context = {
                    "article_title": outline.get("title", ""),
                    "writing_style": writing_style.value if isinstance(writing_style, WritingStyle) else writing_style,
                    "writing_tone": writing_tone.value if isinstance(writing_tone, WritingTone) else writing_tone
                }
                
                task = self.generate_article_section(section_config, section_context, provider)
                section_tasks.append(task)
            
            # 并发生成所有章节
            section_contents = await asyncio.gather(*section_tasks)
            
            for i, section_content in enumerate(section_contents):
                if i < len(outline.get("sections", [])):
                    section = outline["sections"][i]
                    article_content["sections"].append({
                        "title": section.get("title", ""),
                        "content": section_content,
                        "subsections": []
                    })
            
            # 生成结论
            self.task_progress[task_id] = {
                "status": "processing", 
                "progress": 80, 
                "description": "生成文章结论"
            }
            
            conclusion_prompt = f"""
            请根据以下信息为文章写一个有力的结论:
            
            文章标题: {outline.get('title')}
            结论概要: {outline.get('conclusion')}
            视频内容: {summary}
            
            已包含的章节:
            {chr(10).join([f"- {section.get('title', '')}" for section in article_content.get('sections', [])])}
            
            要求:
            1. 结论长度为200-300字
            2. 总结文章的主要观点和要点
            3. 提供一定的洞见或展望
            4. 写作风格: {writing_style.value if isinstance(writing_style, WritingStyle) else writing_style}
            5. 写作语气: {writing_tone.value if isinstance(writing_tone, WritingTone) else writing_tone}
            
            不要使用"总结来说"、"总之"等明显的过渡词，保持自然流畅。
            """
            
            conclusion_result = await self.generate_text(
                prompt=conclusion_prompt,
                model=model,
                provider=provider,
                temperature=0.7,
                api_key=api_key
            )
            
            article_content["conclusion"] = conclusion_result.content
            
            # 4. 完成并返回结果
            self.task_progress[task_id] = {
                "status": "completed", 
                "progress": 100, 
                "description": "文章生成完成"
            }
            
            # 计算总字数
            total_words = (
                len(article_content["introduction"]) +
                sum(len(section["content"]) for section in article_content["sections"]) +
                len(article_content["conclusion"])
            )
            
            result = {
                "article_id": task_id,
                "title": article_content["title"],
                "content": article_content,
                "source": {
                    "type": "video",
                    "video_url": video_content.get("url", ""),
                    "video_title": title,
                    "platform": video_content.get("platform", "unknown")
                },
                "metadata": {
                    "word_count": total_words,
                    "section_count": len(article_content["sections"]),
                    "topics": topics,
                    "writing_style": writing_style.value if isinstance(writing_style, WritingStyle) else writing_style,
                    "writing_tone": writing_tone.value if isinstance(writing_tone, WritingTone) else writing_tone,
                    "generation_time": datetime.now().isoformat()
                }
            }
            
            logger.info(f"成功生成基于视频的文章: {result['title']}, 共{total_words}字")
            
            return result
            
        except Exception as e:
            error_msg = f"基于视频生成文章失败: {str(e)}"
            logger.error(error_msg)
            self.task_progress[task_id] = {
                "status": "failed", 
                "progress": 0, 
                "description": error_msg
            }
            return {
                "article_id": task_id,
                "error": error_msg,
                "status": "failed"
            } 