"""
文本生成器基础模块 - 定义文本生成服务的基本接口和功能
"""
import os
from abc import ABC, abstractmethod
from enum import Enum
from typing import Dict, Any, List, Optional, Union
from dataclasses import dataclass

from src.utils.logger import log_info, log_error, CrawlerLogger
from src.config.logging_config import LoggerName

logger = CrawlerLogger().get_logger(LoggerName.API)

class TextGenerationModel(Enum):
    """文本生成模型选项"""
    GPT_3_5 = "gpt-3.5-turbo"
    GPT_4 = "gpt-4"
    GPT_4_TURBO = "gpt-4-turbo"
    QWEN_TURBO = "qwen-turbo"
    QWEN_MAX = "qwen-max"
    QWEN_PLUS = "qwen-plus"
    DEEPSEEK_CHAT = "deepseek-chat"
    DEEPSEEK_CODER = "deepseek-coder"
    DEEPSEEK_REASONER = "deepseek-reasoner"
    CLAUDE_3_5_BEDROCK = "bedrock/anthropic.claude-3-5-sonnet-20240620-v1:0"
    CLAUDE_3_7_BEDROCK = "bedrock/anthropic.claude-3-7-sonnet-20250219-v1:0"
    GEMINI_PRO = "gemini-pro"
    GEMINI_ULTRA = "gemini-ultra"
    LLAMA_70B = "llama-70b-chat"
    
    @staticmethod
    def from_str(model_str: str) -> 'TextGenerationModel':
        """从字符串获取模型枚举
        
        Args:
            model_str: 模型名称字符串
            
        Returns:
            对应的模型枚举，如果不存在则返回GPT_4
        """
        for model in TextGenerationModel:
            if model.value == model_str:
                return model
        if model_str == "claude-3.5" or model_str == "bedrock-claude-3.5":
            return TextGenerationModel.CLAUDE_3_5_BEDROCK
        return TextGenerationModel.GPT_4

class TextGenerationProvider(Enum):
    """文本生成服务提供商"""
    OPENAI = "openai"       # OpenAI (GPT系列)
    QIANWEN = "qianwen"     # 阿里通义千问
    DEEPSEEK = "deepseek"   # DeepSeek
    ANTHROPIC = "anthropic" # Anthropic (Claude系列)
    GOOGLE = "google"       # Google (Gemini系列)
    META = "meta"           # Meta (Llama系列)
    
    @staticmethod
    def from_str(provider_str: str) -> 'TextGenerationProvider':
        """从字符串获取提供商枚举
        
        Args:
            provider_str: 提供商名称字符串
            
        Returns:
            对应的提供商枚举，如果不存在则返回QIANWEN
        """
        for provider in TextGenerationProvider:
            if provider.value == provider_str:
                return provider
        return TextGenerationProvider.QIANWEN
    
    @staticmethod
    def get_provider_for_model(model: TextGenerationModel) -> 'TextGenerationProvider':
        """根据模型获取对应的提供商
        
        Args:
            model: 模型枚举
            
        Returns:
            对应的提供商枚举
        """
        model_str = model.value.lower()
        if "gpt" in model_str:
            return TextGenerationProvider.OPENAI
        elif "qwen" in model_str:
            return TextGenerationProvider.QIANWEN
        elif "deepseek" in model_str:
            return TextGenerationProvider.DEEPSEEK
        elif "claude" in model_str:
            return TextGenerationProvider.ANTHROPIC
        elif "gemini" in model_str:
            return TextGenerationProvider.GOOGLE
        elif "llama" in model_str:
            return TextGenerationProvider.META
        else:
            return TextGenerationProvider.QIANWEN

class ContentType(Enum):
    """内容类型"""
    ARTICLE = "article"           # 通用文章
    BLOG_POST = "blog_post"       # 博客文章
    KNOWLEDGE = "knowledge"       # 知识类文章
    TUTORIAL = "tutorial"         # 教程
    REVIEW = "review"             # 评测
    NEWS = "news"                 # 新闻
    SOCIAL_POST = "social_post"   # 社交媒体帖子
    PRODUCT_DESC = "product_desc" # 产品描述
    AD_COPY = "ad_copy"           # 广告文案
    EMAIL = "email"               # 电子邮件
    SCRIPT = "script"             # 脚本
    TECH_DOC = "tech_doc"         # 技术文档
    
class WritingStyle(Enum):
    """写作风格"""
    PROFESSIONAL = "professional"   # 专业的
    CASUAL = "casual"               # 随意的
    ACADEMIC = "academic"           # 学术的
    CREATIVE = "creative"           # 创意的
    PERSUASIVE = "persuasive"       # 有说服力的
    INFORMATIVE = "informative"     # 信息性的
    STORYTELLING = "storytelling"   # 讲故事的
    HUMOROUS = "humorous"           # 幽默的
    TECHNICAL = "technical"         # 技术性的
    CONVERSATIONAL = "conversational" # 对话式的
    ANALYTICAL = "analytical"       # 分析型的

class WritingTone(Enum):
    """写作语气"""
    NEUTRAL = "neutral"             # 中性的
    FRIENDLY = "friendly"           # 友好的
    AUTHORITATIVE = "authoritative" # 权威的
    ENTHUSIASTIC = "enthusiastic"   # 热情的
    FORMAL = "formal"               # 正式的
    INFORMAL = "informal"           # 非正式的
    OPTIMISTIC = "optimistic"       # 乐观的
    SERIOUS = "serious"             # 严肃的
    EMPATHETIC = "empathetic"       # 有同理心的
    MOTIVATIONAL = "motivational"   # 激励性的

class ContentSource(Enum):
    """内容来源"""
    DOUYIN = "douyin"        # 抖音视频
    BILIBILI = "bilibili"    # B站视频
    WECHAT = "wechat"        # 微信文章
    XIAOHONGSHU = "xiaohongshu"  # 小红书
    ZHIHU = "zhihu"          # 知乎
    WEBSITE = "website"      # 普通网站
    USER_INPUT = "user_input"  # 用户输入
    GENERATED = "generated"  # 生成的内容

class ContentStructure(Enum):
    """内容结构类型"""
    STANDARD = "standard"       # 标准结构（引言-主体-结论）
    BLOG = "blog"               # 博客结构（介绍/背景-内容-总结-常见问题）
    TUTORIAL = "tutorial"       # 教程结构（介绍-步骤-结果-总结）
    REVIEW = "review"           # 评测结构（介绍-评测标准-正文评测-总结）
    QA = "qa"                   # 问答结构
    LIST = "list"               # 列表结构
    COMPARISON = "comparison"   # 对比结构

class HumanizationLevel(Enum):
    """拟人化程度"""
    NONE = "none"              # 不进行拟人化
    LIGHT = "light"            # 轻度拟人化
    MEDIUM = "medium"          # 中度拟人化
    HEAVY = "heavy"            # 重度拟人化

@dataclass
class TextGenerationConfig:
    """文本生成配置"""
    prompt: str                          # 提示词
    model: Union[str, TextGenerationModel] = TextGenerationModel.QWEN_TURBO  # 默认使用千问
    max_tokens: int = 2000               # 最大生成token数
    temperature: float = 0.7             # 生成温度
    content_type: Optional[ContentType] = None  # 内容类型
    writing_style: Optional[WritingStyle] = None  # 写作风格
    writing_tone: Optional[WritingTone] = None  # 写作语气
    keywords: Optional[List[str]] = None  # 关键词列表
    content_structure: Optional[ContentStructure] = None  # 内容结构
    humanization_level: Optional[HumanizationLevel] = None  # 拟人化程度
    source_type: Optional[ContentSource] = None  # 内容来源类型
    source_content: Optional[str] = None  # 源内容
    api_key: Optional[str] = None        # API密钥
    json_format: bool = False            # 是否返回JSON格式
    
    def __post_init__(self):
        """初始化后处理"""
        # 确保模型是枚举类型
        if isinstance(self.model, str):
            self.model = TextGenerationModel.from_str(self.model)
        
        # 确保内容类型是枚举类型
        if isinstance(self.content_type, str):
            self.content_type = ContentType(self.content_type)
        
        # 确保写作风格是枚举类型
        if isinstance(self.writing_style, str):
            self.writing_style = WritingStyle(self.writing_style)
        
        # 确保写作语气是枚举类型
        if isinstance(self.writing_tone, str):
            self.writing_tone = WritingTone(self.writing_tone)
        
        # 确保内容结构是枚举类型
        if isinstance(self.content_structure, str):
            self.content_structure = ContentStructure(self.content_structure)
            
        # 确保拟人化程度是枚举类型
        if isinstance(self.humanization_level, str):
            self.humanization_level = HumanizationLevel(self.humanization_level)
            
        # 确保内容来源是枚举类型
        if isinstance(self.source_type, str):
            self.source_type = ContentSource(self.source_type)
        
        # 确保关键词是列表类型
        if self.keywords is None:
            self.keywords = []

@dataclass
class TextGenerationResult:
    """文本生成结果"""
    content: str                     # 生成的内容
    prompt: Optional[str] = None     # 使用的提示词
    model: Optional[str] = None      # 使用的模型
    token_count: Optional[int] = None  # 生成的token数量
    metadata: Optional[Dict[str, Any]] = None  # 其他元数据

class BaseTextGenerator(ABC):
    """文本生成器基础类"""
    
    def __init__(self, api_key: Optional[str], provider: Union[str, TextGenerationProvider]):
        """初始化
        
        Args:
            api_key: API密钥
            provider: 提供商名称或枚举
        """
        self.api_key = api_key
        
        # 确保provider是枚举类型
        if isinstance(provider, str):
            self.provider = TextGenerationProvider.from_str(provider)
        else:
            self.provider = provider
    
    @abstractmethod
    async def generate(self, config: TextGenerationConfig) -> TextGenerationResult:
        """生成文本
        
        Args:
            config: 文本生成配置
            
        Returns:
            文本生成结果
        """
        pass
    
    def _validate_api_key(self) -> bool:
        """验证API密钥是否有效
        
        Returns:
            是否有效
        """
        return self.api_key is not None and len(self.api_key) > 0
    
    @staticmethod
    def _generate_prompt_prefix(config: TextGenerationConfig) -> str:
        """生成提示词前缀
        
        Args:
            config: 文本生成配置
            
        Returns:
            提示词前缀
        """
        prefix_parts = []
        
        # 添加内容类型
        if config.content_type:
            prefix_parts.append(f"生成一篇{config.content_type.value}内容。")
        
        # 添加内容结构
        if config.content_structure:
            if config.content_structure == ContentStructure.BLOG:
                prefix_parts.append("使用博客结构：介绍/背景 - 主要内容 - 总结 - 常见问题解答(FAQs)。")
            elif config.content_structure == ContentStructure.TUTORIAL:
                prefix_parts.append("使用教程结构：介绍 - 详细步骤 - 结果展示 - 总结。")
            elif config.content_structure == ContentStructure.REVIEW:
                prefix_parts.append("使用评测结构：介绍 - 评测标准 - 详细评测 - 总结。")
            elif config.content_structure == ContentStructure.QA:
                prefix_parts.append("使用问答结构，以问题和答案的形式组织内容。")
            elif config.content_structure == ContentStructure.LIST:
                prefix_parts.append("使用列表结构，以编号列表的形式组织内容。")
            elif config.content_structure == ContentStructure.COMPARISON:
                prefix_parts.append("使用对比结构，清晰地比较不同选项的优缺点。")
        
        # 添加写作风格
        if config.writing_style:
            prefix_parts.append(f"使用{config.writing_style.value}风格。")
        
        # 添加写作语气
        if config.writing_tone:
            prefix_parts.append(f"采用{config.writing_tone.value}语气。")
        
        # 添加拟人化要求
        if config.humanization_level:
            if config.humanization_level == HumanizationLevel.LIGHT:
                prefix_parts.append("轻度拟人化内容，使其略显个人风格但仍保持专业。")
            elif config.humanization_level == HumanizationLevel.MEDIUM:
                prefix_parts.append("中度拟人化内容，加入个人见解和口语化表达，但不过度。")
            elif config.humanization_level == HumanizationLevel.HEAVY:
                prefix_parts.append("重度拟人化内容，使用强烈的个人风格，口语化表达，感情色彩丰富。")
        
        # 添加关键词
        if config.keywords and len(config.keywords) > 0:
            keywords_str = "、".join(config.keywords)
            prefix_parts.append(f"确保自然地融入以下关键词：{keywords_str}。")
        
        # 添加源内容提示
        if config.source_content and config.source_type:
            prefix_parts.append(f"基于以下{config.source_type.value}平台的内容创作：\n\n---参考内容---\n{config.source_content}\n---参考内容结束---")
        
        # 组合前缀
        if prefix_parts:
            return " ".join(prefix_parts) + "\n\n"
        
        return "" 