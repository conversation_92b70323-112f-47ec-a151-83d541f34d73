"""
文本生成器示例脚本 - 展示TextManager的基本用法和高级功能
"""
import asyncio
import os
from typing import Dict, Any
import json

from src.services.generator.text_generator.text_manager import (
    TextManager, TextGenerationModel, TextGenerationProvider, 
    ContentType, WritingStyle, WritingTone
)
from src.utils.logger import log_info, log_error, CrawlerLogger
from src.config.logging_config import LoggerName

logger = CrawlerLogger().get_logger(LoggerName.API)

async def basic_text_generation():
    """基本文本生成示例"""
    # 初始化TextManager（可以从环境变量获取API密钥，也可以直接传入）
    manager = TextManager(
        qianwen_api_key=os.getenv("QIANWEN_API_KEY"),
        deepseek_api_key=os.getenv("DEEPSEEK_API_KEY")
    )
    
    # 简单文本生成
    prompt = "写一篇关于人工智能的短文，不超过200字"
    result = await manager.generate_text(
        prompt=prompt,
        model=TextGenerationModel.QWEN_TURBO,
        provider=TextGenerationProvider.QIANWEN,
        max_tokens=500,
        temperature=0.7
    )
    
    print("\n===== 基本文本生成 =====")
    print(f"提示词: {prompt}")
    print(f"生成结果: {result.content}")
    print(f"使用的Token: {result.token_count}")
    
    # 使用不同的提供商和模型
    prompt = "解释量子计算的基本原理，使用通俗易懂的语言"
    result = await manager.generate_text(
        prompt=prompt,
        model=TextGenerationModel.DEEPSEEK_CHAT,
        provider=TextGenerationProvider.DEEPSEEK,
        max_tokens=800,
        temperature=0.5
    )
    
    print("\n===== 使用DeepSeek生成 =====")
    print(f"提示词: {prompt}")
    print(f"生成结果: {result.content}")
    print(f"使用的Token: {result.token_count}")
    
    # 缓存状态检查
    cache_stats = manager.get_cache_stats()
    print(f"\n缓存统计: {cache_stats}")

async def styled_text_generation():
    """使用不同风格和语气生成文本"""
    manager = TextManager()
    
    prompt = "介绍中国传统节日春节"
    
    # 学术风格
    result_academic = await manager.generate_text(
        prompt=prompt,
        content_type=ContentType.ARTICLE,
        writing_style=WritingStyle.ACADEMIC,
        writing_tone=WritingTone.FORMAL
    )
    
    # 创意风格
    result_creative = await manager.generate_text(
        prompt=prompt,
        content_type=ContentType.ARTICLE,
        writing_style=WritingStyle.CREATIVE,
        writing_tone=WritingTone.ENTHUSIASTIC
    )
    
    # 对话风格
    result_conversational = await manager.generate_text(
        prompt=prompt,
        content_type=ContentType.ARTICLE,
        writing_style=WritingStyle.CONVERSATIONAL,
        writing_tone=WritingTone.FRIENDLY
    )
    
    print("\n===== 不同风格的文本生成 =====")
    print(f"学术风格: {result_academic.content[:200]}...")
    print(f"创意风格: {result_creative.content[:200]}...")
    print(f"对话风格: {result_conversational.content[:200]}...")

async def article_generation():
    """完整文章生成示例"""
    manager = TextManager()
    
    # 生成一篇关于人工智能的文章
    article = await manager.generate_article(
        title="人工智能如何改变我们的未来",
        writing_style=WritingStyle.INFORMATIVE,
        writing_tone=WritingTone.OPTIMISTIC
    )
    
    print("\n===== 完整文章生成 =====")
    print(f"标题: {article['title']}")
    print(f"关键词: {', '.join(article['keywords'])}")
    print(f"引言: {article['introduction'][:200]}...")
    print(f"章节数: {len(article['sections'])}")
    for i, section in enumerate(article['sections']):
        print(f"  章节{i+1}: {section['title']}")
    print(f"FAQ数量: {len(article['faqs'])}")
    
    # 查看进度追踪
    task_id = manager.current_task_id
    progress = manager.get_task_progress(task_id)
    print(f"\n任务进度: {progress}")

async def content_extraction():
    """从URL提取内容示例"""
    manager = TextManager()
    
    # 从微信文章提取内容
    wechat_url = "https://mp.weixin.qq.com/s/sample_article_id"  # 替换为实际URL
    
    print("\n===== 内容提取 =====")
    print(f"正在从URL提取内容: {wechat_url}")
    
    # 这里可以替换为实际可访问的URL
    # extracted = await manager._extract_content(wechat_url)
    # print(f"提取到的标题: {extracted.get('title')}")
    # print(f"提取到的内容长度: {len(extracted.get('content', ''))}")
    
    print("注意: 此示例需要提供真实的URL才能正常工作")

async def main():
    """运行所有示例"""
    try:
        await basic_text_generation()
        await styled_text_generation()
        await article_generation()
        await content_extraction()
    except Exception as e:
        logger.error(f"运行示例时发生错误: {e}", exc_info=True)

if __name__ == "__main__":
    """运行示例脚本"""
    asyncio.run(main()) 