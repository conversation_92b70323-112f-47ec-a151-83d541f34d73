# 中文SEO优化模块

本模块提供针对中文搜索引擎的搜索引擎优化(SEO)功能，主要支持百度、搜狗、360搜索等中文搜索引擎。

## 功能特点

- **中文SEO分析**：针对中文内容进行SEO分析，包括关键词密度、标题质量、内容结构等
- **智能优化**：自动优化文章标题、内容结构、关键词使用和元数据
- **关键词建议**：基于行业和主题提供相关的热门关键词建议
- **元标签生成**：生成适合中文搜索引擎的元描述和元标签
- **内部链接建议**：提供内部链接优化建议，提高网站权重
- **移动端友好度评估**：评估内容对移动设备的友好程度
- **百度特定优化**：针对百度搜索引擎的特定优化措施

## 模块结构

```
seo/
├── __init__.py           # 模块初始化和导出
├── seo_analyzer.py       # SEO分析功能
├── chinese_seo_optimizer.py  # 中文SEO优化实现
├── seo_manager.py        # SEO管理器，提供统一接口
└── README.md             # 文档说明
```

## 快速开始

### 基本用法

```python
from src.services.generator.seo import SEOManager

# 初始化SEO管理器
seo_manager = SEOManager()

# 分析文章SEO状况
analysis = await seo_manager.analyze_seo(
    title="如何提高网站在百度的排名",
    content="详细的文章内容...",
    keywords=["SEO优化", "百度排名", "网站优化"]
)

# 获取SEO建议
recommendations = await seo_manager.generate_seo_recommendations(
    title="如何提高网站在百度的排名",
    content="详细的文章内容...",
    keywords=["SEO优化", "百度排名", "网站优化"]
)

# 优化文章
result = await seo_manager.optimize_article(
    title="如何提高网站在百度的排名",
    content="详细的文章内容...",
    keywords=["SEO优化", "百度排名", "网站优化"],
    related_urls={"网站优化": "https://example.com/optimization"},
    optimize_for="baidu"
)

# 访问优化结果
optimized_title = result.optimized_title
optimized_content = result.optimized_content
seo_score_before = result.score_before
seo_score_after = result.score_after
```

### 与文章处理器集成

```python
from src.services.generator.article_processor import ArticleProcessor
from src.services.generator.seo import SEOManager

# 初始化SEO管理器
seo_manager = SEOManager()

# 创建文章处理器并传入SEO管理器
processor = ArticleProcessor(
    # 其他参数...
    seo_manager=seo_manager
)

# 创建文章状态，启用SEO优化
article_state = {
    'url': 'https://example.com/article',
    'enable_seo': True,
    'seo_platform': 'baidu',
    'seo_keywords': ['关键词1', '关键词2'],
    'seo_related_urls': {
        '关键词1': 'https://example.com/keyword1',
        '关键词2': 'https://example.com/keyword2'
    }
}

# 处理文章
result = await processor.process_article(article_state)

# 获取SEO优化结果
seo_analysis = result.get('seo_analysis')
seo_recommendations = result.get('seo_recommendations')
seo_meta_tags = result.get('seo_meta_tags')
optimized_article = result.get('final_article')
```

## 高级用法

### 自定义SEO分析器

```python
from src.services.generator.seo import ChineseSEOAnalyzer

# 创建自定义SEO分析器
analyzer = ChineseSEOAnalyzer(
    min_content_length=1000,
    ideal_keyword_density=0.025,
    max_keyword_density=0.04
)

# 执行分析
score = analyzer.analyze(
    title="文章标题",
    content="文章内容...",
    keywords=["关键词1", "关键词2"]
)
```

### 自定义SEO优化器

```python
from src.services.generator.seo import ChineseSEOOptimizer

# 创建自定义SEO优化器
optimizer = ChineseSEOOptimizer()

# 优化文章
result = await optimizer.optimize(
    title="原始标题",
    content="原始内容...",
    keywords=["关键词1", "关键词2"],
    related_urls={"关键词1": "https://example.com/keyword1"},
    platform="baidu"
)
```

## 支持的平台

- 百度 (`baidu`)
- 搜狗 (`sogou`)
- 360搜索 (`360`)
- 神马搜索 (`sm`) - 移动搜索引擎
- 通用优化 (`general`) - 适用于多个搜索引擎的通用优化

## 注意事项

1. 本模块主要针对中文搜索引擎优化，特别是百度
2. 优化建议仅供参考，实际SEO效果还取决于多种外部因素
3. 对于大型文章，建议分段进行优化以获得更好的效果
4. 优化过程中会保留原文的主要内容和意图，仅对结构和表达进行优化

## 未来计划

- 增加针对短视频平台(抖音、快手)的内容SEO优化
- 添加更多行业特定的SEO模板和建议
- 提供更详细的SEO评分报告和可视化展示
- 增强与外部SEO工具的集成能力 