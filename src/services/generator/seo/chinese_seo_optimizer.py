"""
中国SEO优化器 - 针对百度、搜狗等中国搜索引擎的文章优化工具
提供标题优化、关键词优化、内容结构优化等功能
"""
import re
import jieba
import jieba.analyse
from typing import List, Dict, Any, Optional, Tuple
import asyncio
from dataclasses import dataclass, field

# 导入SEO分析器
from .seo_analyzer import ChineseSEOAnalyzer, SEOScore, SEOSuggestion

@dataclass
class SEOOptimizationResult:
    """SEO优化结果数据类"""
    original_title: str  # 原始标题
    optimized_title: str  # 优化后的标题
    original_content: str  # 原始内容
    optimized_content: str  # 优化后的内容
    keywords: List[str]  # 使用的关键词
    metadata: Dict[str, Any] = field(default_factory=dict)  # 元数据
    score_before: Optional[SEOScore] = None  # 优化前评分
    score_after: Optional[SEOScore] = None  # 优化后评分

class ChineseSEOOptimizer:
    """中国SEO优化器类"""
    
    def __init__(self):
        """初始化优化器"""
        self.analyzer = ChineseSEOAnalyzer()
        self.keyword_patterns = self._load_keyword_patterns()
        self.popular_search_terms = self._load_popular_search_terms()
    
    def _load_keyword_patterns(self) -> Dict[str, List[str]]:
        """加载常用关键词模式"""
        # 这里可以从数据库或文件加载，这里简化为直接定义
        return {
            "how_to": ["如何", "怎样", "怎么", "教程", "指南"],
            "comparison": ["对比", "vs", "相比", "哪个好", "优缺点"],
            "review": ["评测", "测评", "点评", "体验", "使用感受"],
            "list": ["排行榜", "清单", "盘点", "必备", "推荐"],
            "guide": ["指南", "攻略", "技巧", "秘诀", "注意事项"]
        }
    
    def _load_popular_search_terms(self) -> Dict[str, List[str]]:
        """加载热门搜索词"""
        # 这里可以从API获取实时热门词，这里简化为直接定义
        return {
            "tech": ["5G", "人工智能", "大模型", "元宇宙", "区块链", "虚拟现实"],
            "finance": ["理财", "投资", "基金", "股票", "房产", "保险"],
            "lifestyle": ["健康", "减肥", "养生", "美食", "旅游", "穿搭"],
            "education": ["考研", "考公", "自考", "留学", "考证", "学习方法"]
        }
    
    def optimize_title(self, title: str, keywords: List[str]) -> str:
        """优化文章标题
        
        Args:
            title: 原始标题
            keywords: 关键词列表
            
        Returns:
            优化后的标题
        """
        # 分析原始标题
        title_analysis = self.analyzer.analyze_title(title, keywords)
        
        # 如果标题已经足够好，直接返回
        if title_analysis.get("score", 0) >= 90:
            return title
        
        # 优化标题
        optimized_title = title
        
        # 1. 确保标题包含主要关键词
        if not title_analysis.get("has_keywords", False) and keywords:
            # 在标题开头添加主要关键词
            main_keyword = keywords[0]
            optimized_title = f"{main_keyword}：{title}"
        
        # 2. 调整标题长度
        if len(optimized_title) < 10:
            # 标题太短，添加描述性内容
            optimized_title = f"{optimized_title}：完整指南与实用技巧"
        elif len(optimized_title) > 30:
            # 标题太长，尝试缩短
            # 移除不必要的修饰词
            for word in ["完全", "非常", "超级", "绝对", "真正"]:
                optimized_title = optimized_title.replace(word, "")
            
            # 如果仍然太长，截断并加省略号
            if len(optimized_title) > 30:
                optimized_title = optimized_title[:27] + "..."
        
        # 3. 添加吸引点击的元素
        if not any(char in optimized_title for char in ["？", "!", "？", "!"]):
            # 尝试将陈述句改为疑问句
            if optimized_title.startswith(tuple(self.keyword_patterns["how_to"])):
                # 已经是"如何"类型的标题
                pass
            elif any(keyword in optimized_title for keyword in keywords):
                # 包含关键词的标题，转为疑问句
                for keyword in keywords:
                    if keyword in optimized_title:
                        optimized_title = optimized_title.replace(
                            keyword, f"{keyword}到底是什么", 1
                        )
                        break
        
        # 4. 确保标题符合百度/搜狗搜索习惯
        # 添加数字/年份使标题更具时效性
        if not any(str(i) in optimized_title for i in range(10)):
            import datetime
            current_year = datetime.datetime.now().year
            optimized_title = f"{current_year}年{optimized_title}"
        
        return optimized_title
    
    def optimize_content_structure(self, content: str) -> str:
        """优化文章结构
        
        Args:
            content: 原始内容
            
        Returns:
            优化后的内容
        """
        # 分析内容结构
        structure_analysis = self.analyzer.analyze_structure(content)
        
        # 如果结构已经足够好，直接返回
        if structure_analysis.get("score", 0) >= 90:
            return content
        
        # 优化内容结构
        lines = content.split("\n")
        optimized_lines = []
        
        # 1. 确保有合适的标题层级
        has_h1 = structure_analysis.get("h1_count", 0) > 0
        has_h2 = structure_analysis.get("h2_count", 0) > 0
        
        if not has_h1 and not content.strip().startswith("# "):
            # 添加一级标题
            if optimized_lines and optimized_lines[0].strip():
                # 使用第一行内容作为标题
                h1_title = optimized_lines[0].strip()
                optimized_lines[0] = f"# {h1_title}"
            else:
                # 创建默认标题
                optimized_lines.insert(0, "# 文章标题")
        
        # 2. 确保合理的段落划分
        paragraph_count = 0
        current_paragraph = ""
        
        for line in lines:
            # 处理标题行
            if line.strip().startswith(("#", "!", "```")):
                # 如果有累积的段落，添加到优化后的内容
                if current_paragraph:
                    optimized_lines.append(current_paragraph)
                    optimized_lines.append("")  # 空行
                    current_paragraph = ""
                    paragraph_count += 1
                
                optimized_lines.append(line)
                continue
            
            # 累积段落内容
            if line.strip():
                if current_paragraph:
                    current_paragraph += " " + line.strip()
                else:
                    current_paragraph = line.strip()
            elif current_paragraph:
                # 空行表示段落结束
                optimized_lines.append(current_paragraph)
                optimized_lines.append("")  # 空行
                current_paragraph = ""
                paragraph_count += 1
            else:
                # 连续空行，只保留一个
                if not optimized_lines or optimized_lines[-1]:
                    optimized_lines.append("")
        
        # 处理最后一个段落
        if current_paragraph:
            optimized_lines.append(current_paragraph)
            paragraph_count += 1
        
        # 3. 如果段落太少，尝试拆分长段落
        if paragraph_count < 3:
            for i in range(len(optimized_lines)):
                line = optimized_lines[i]
                if not line.strip().startswith(("#", "!", "```")) and len(line) > 200:
                    # 尝试在句子边界拆分长段落
                    sentences = re.split(r'([。！？])', line)
                    if len(sentences) > 2:
                        # 重组句子
                        new_paragraphs = []
                        current = ""
                        for j in range(0, len(sentences), 2):
                            if j+1 < len(sentences):
                                sentence = sentences[j] + sentences[j+1]  # 句子+标点
                            else:
                                sentence = sentences[j]
                            
                            if len(current + sentence) > 100:
                                if current:
                                    new_paragraphs.append(current)
                                    current = sentence
                                else:
                                    new_paragraphs.append(sentence)
                            else:
                                current += sentence
                        
                        if current:
                            new_paragraphs.append(current)
                        
                        # 替换原长段落
                        optimized_lines[i:i+1] = new_paragraphs
        
        # 4. 添加小结或总结段落
        if not any("总结" in line or "小结" in line for line in optimized_lines):
            optimized_lines.append("")
            optimized_lines.append("## 总结")
            optimized_lines.append("")
            optimized_lines.append("以上就是关于本主题的全部内容，希望对您有所帮助。如有更多问题，欢迎在评论区留言讨论。")
        
        return "\n".join(optimized_lines)
    
    def optimize_keywords_usage(self, content: str, keywords: List[str]) -> str:
        """优化关键词使用
        
        Args:
            content: 原始内容
            keywords: 关键词列表
            
        Returns:
            优化后的内容
        """
        # 分析关键词使用情况
        keyword_analysis = self.analyzer.analyze_keywords(content, keywords)
        
        # 如果关键词使用已经足够好，直接返回
        if keyword_analysis.get("score", 0) >= 90:
            return content
        
        # 优化关键词使用
        optimized_content = content
        
        # 1. 处理关键词密度过低的情况
        for keyword, density in keyword_analysis.get("density", {}).items():
            if density < 1.0:
                # 在内容中适当增加关键词
                # 寻找合适的插入点（段落开头或结尾）
                paragraphs = re.split(r'\n\s*\n', optimized_content)
                
                # 尝试在每3个段落的开头或结尾添加一次关键词
                insert_count = min(3, len(paragraphs) // 3)
                for i in range(min(insert_count, len(paragraphs))):
                    idx = i * 3  # 每隔3个段落
                    if idx < len(paragraphs):
                        paragraph = paragraphs[idx]
                        
                        # 避免在标题或代码块中插入
                        if paragraph.strip().startswith(("#", "```")):
                            continue
                        
                        sentences = re.split(r'([。！？])', paragraph)
                        if len(sentences) > 2:
                            # 在第一个句子后插入关键词
                            insert_text = f"{sentences[0]}{sentences[1]}关于{keyword}，"
                            modified_paragraph = insert_text + "".join(sentences[2:])
                            paragraphs[idx] = modified_paragraph
                
                optimized_content = "\n\n".join(paragraphs)
        
        # 2. 处理关键词密度过高的情况
        for keyword, density in keyword_analysis.get("density", {}).items():
            if density > 5.0:
                # 使用同义词替换部分关键词，降低密度
                synonyms = self._get_synonyms(keyword)
                if synonyms:
                    # 替换一半的关键词出现
                    occurrences = keyword_analysis.get("counts", {}).get(keyword, 0)
                    to_replace = occurrences // 2
                    
                    # 逐个替换
                    for synonym in synonyms:
                        if to_replace <= 0:
                            break
                        
                        # 替换非标题中的关键词
                        lines = optimized_content.split("\n")
                        for i in range(len(lines)):
                            if not lines[i].strip().startswith("#") and keyword in lines[i]:
                                lines[i] = lines[i].replace(keyword, synonym, 1)
                                to_replace -= 1
                                if to_replace <= 0:
                                    break
                        
                        optimized_content = "\n".join(lines)
        
        # 3. 强化主要关键词在关键位置的出现
        if keywords:
            main_keyword = keywords[0]
            
            # 在第一段添加主关键词（如果没有）
            paragraphs = re.split(r'\n\s*\n', optimized_content)
            first_content_paragraph = ""
            
            for p in paragraphs:
                if p.strip() and not p.strip().startswith(("#", "```", "!")):
                    first_content_paragraph = p
                    break
            
            if first_content_paragraph and main_keyword not in first_content_paragraph:
                idx = paragraphs.index(first_content_paragraph)
                paragraphs[idx] = f"在探讨{main_keyword}相关内容之前，我们先来了解一下基本概念。{first_content_paragraph}"
                optimized_content = "\n\n".join(paragraphs)
        
        return optimized_content
    
    def add_internal_links(self, content: str, keywords: List[str], 
                           related_urls: Dict[str, str] = None) -> str:
        """添加内部链接
        
        Args:
            content: 原始内容
            keywords: 关键词列表
            related_urls: 关键词到URL的映射
            
        Returns:
            添加内部链接后的内容
        """
        if not related_urls:
            return content
        
        # 添加内部链接
        optimized_content = content
        
        # 为每个关键词添加最多一个链接
        for keyword, url in related_urls.items():
            # 检查关键词是否已经是链接的一部分
            link_pattern = rf'\[([^]]*{re.escape(keyword)}[^]]*)\]\([^)]+\)'
            if re.search(link_pattern, optimized_content):
                continue
            
            # 寻找第一次出现的关键词（不在标题、链接或代码块中）
            lines = optimized_content.split("\n")
            for i in range(len(lines)):
                line = lines[i]
                
                # 跳过标题、链接和代码块
                if (line.strip().startswith(("#", "```", "!")) or 
                    "[" in line and "](" in line):
                    continue
                
                # 找到关键词
                if keyword in line:
                    # 替换为链接
                    lines[i] = line.replace(
                        keyword, f"[{keyword}]({url})", 1
                    )
                    break
            
            optimized_content = "\n".join(lines)
        
        return optimized_content
    
    def add_meta_description(self, content: str, keywords: List[str]) -> str:
        """生成meta描述
        
        Args:
            content: 文章内容
            keywords: 关键词列表
            
        Returns:
            meta描述（限制在150字符内）
        """
        # 提取文章第一段作为基础
        paragraphs = re.split(r'\n\s*\n', content)
        first_content_paragraph = ""
        
        for p in paragraphs:
            if p.strip() and not p.strip().startswith(("#", "```", "!")):
                first_content_paragraph = p
                break
        
        if not first_content_paragraph:
            # 没有找到合适的段落，使用关键词创建描述
            return f"关于{', '.join(keywords[:3])}的详细介绍和分析，提供最新信息和专业见解。"
        
        # 提取前150个字符
        description = first_content_paragraph[:150]
        
        # 确保描述包含主要关键词
        if keywords and keywords[0] not in description:
            description = f"{keywords[0]} - {description}"
            # 再次截断以确保不超过150字符
            description = description[:150]
        
        # 如果描述被截断，添加省略号
        if len(description) == 150 and len(first_content_paragraph) > 150:
            # 在最后一个完整句子后截断
            last_punctuation = max(
                description.rfind("。"),
                description.rfind("！"),
                description.rfind("？")
            )
            
            if last_punctuation > 100:
                description = description[:last_punctuation+1]
            else:
                # 在最后一个完整词后截断
                last_space = description.rfind(" ")
                if last_space > 100:
                    description = description[:last_space]
                
                description += "..."
        
        return description
    
    def generate_baidu_meta_tags(self, title: str, description: str,
                                keywords: List[str]) -> Dict[str, str]:
        """生成百度推荐的meta标签
        
        Args:
            title: 页面标题
            description: 页面描述
            keywords: 关键词列表
            
        Returns:
            meta标签字典
        """
        return {
            "title": title,
            "keywords": ",".join(keywords),
            "description": description,
            # 百度特有的meta标签
            "mobile-agent": "format=html5;url=同一网址",
            "applicable-device": "pc,mobile",
            "MobileOptimized": "width",
            "HandheldFriendly": "true"
        }
    
    def _get_synonyms(self, keyword: str) -> List[str]:
        """获取关键词的同义词
        
        Args:
            keyword: 关键词
            
        Returns:
            同义词列表
        """
        # 在实际应用中，可以使用同义词词典或API
        # 这里使用简化的实现
        synonyms = {
            "SEO优化": ["搜索引擎优化", "排名优化", "SEO技术"],
            "百度排名": ["百度搜索排名", "百度收录", "搜索排名"],
            "关键词优化": ["关键词布局", "关键词策略", "核心词优化"],
            "网站优化": ["站点优化", "网站SEO", "站点改进"],
            "移动优化": ["手机端优化", "移动友好性", "手机SEO"],
            "内容优化": ["文章优化", "内容策略", "内容质量提升"],
            "用户体验": ["用户友好度", "使用体验", "交互体验"],
            "转化率": ["转化效果", "行动转化", "营销效果"],
        }
        
        # 查找完全匹配
        if keyword in synonyms:
            return synonyms[keyword]
        
        # 查找部分匹配
        for k, v in synonyms.items():
            if k in keyword or keyword in k:
                return v
        
        return []
    
    async def optimize_article(self, title: str, content: str, 
                         keywords: List[str] = None,
                         related_urls: Dict[str, str] = None) -> SEOOptimizationResult:
        """优化整篇文章的SEO
        
        Args:
            title: 原始标题
            content: 原始内容
            keywords: 关键词列表，如果为None则自动提取
            related_urls: 相关URL字典，用于内部链接
            
        Returns:
            SEO优化结果
        """
        # 如果未提供关键词，则自动提取
        if keywords is None or not keywords:
            # 从标题和内容提取关键词
            text_for_keywords = f"{title} {content}"
            extracted = jieba.analyse.extract_tags(text_for_keywords, topK=5)
            keywords = extracted
        
        # 优化前评分
        score_before = self.analyzer.analyze_seo(title, content, keywords)["score"]
        
        # 1. 优化标题
        optimized_title = self.optimize_title(title, keywords)
        
        # 2. 优化内容结构
        optimized_content = self.optimize_content_structure(content)
        
        # 3. 优化关键词使用
        optimized_content = self.optimize_keywords_usage(optimized_content, keywords)
        
        # 4. 添加内部链接
        if related_urls:
            optimized_content = self.add_internal_links(optimized_content, keywords, related_urls)
        
        # 5. 生成meta描述
        meta_description = self.add_meta_description(optimized_content, keywords)
        
        # 6. 生成百度meta标签
        meta_tags = self.generate_baidu_meta_tags(optimized_title, meta_description, keywords)
        
        # 优化后评分
        score_after = self.analyzer.analyze_seo(optimized_title, optimized_content, keywords)["score"]
        
        # 构建结果
        result = SEOOptimizationResult(
            original_title=title,
            optimized_title=optimized_title,
            original_content=content,
            optimized_content=optimized_content,
            keywords=keywords,
            metadata={
                "meta_description": meta_description,
                "meta_tags": meta_tags,
                "suggestions": score_after.details,
                "optimization_summary": {
                    "title_changed": title != optimized_title,
                    "content_structure_improved": score_after.structure_score > score_before.structure_score,
                    "keyword_usage_improved": score_after.keyword_score > score_before.keyword_score,
                    "readability_improved": score_after.readability_score > score_before.readability_score,
                    "overall_improvement": score_after.overall_score - score_before.overall_score
                }
            },
            score_before=score_before,
            score_after=score_after
        )
        
        return result

if __name__ == "__main__":
    # 测试代码
    optimizer = ChineseSEOOptimizer()
    
    async def test_optimization():
        result = await optimizer.optimize_article(
            title="SEO方法",
            content="SEO是搜索引擎优化的简称。做好SEO可以提高网站排名。\n\n关键词优化是SEO的核心。",
            keywords=["SEO优化", "百度排名", "关键词优化"],
            related_urls={
                "关键词优化": "https://example.com/keyword-optimization",
                "百度排名": "https://example.com/baidu-ranking"
            }
        )
        
        print(f"原始标题: {result.original_title}")
        print(f"优化后标题: {result.optimized_title}")
        print("\n原始内容:")
        print(result.original_content)
        print("\n优化后内容:")
        print(result.optimized_content)
        print(f"\nMeta描述: {result.metadata['meta_description']}")
        print(f"\n优化前评分: {result.score_before.overall_score}")
        print(f"优化后评分: {result.score_after.overall_score}")
        print(f"提升: {result.score_after.overall_score - result.score_before.overall_score:.2f}分")
    
    # 运行测试
    asyncio.run(test_optimization()) 