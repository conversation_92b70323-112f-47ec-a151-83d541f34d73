"""
SEO分析器 - 针对中国搜索引擎优化的内容分析工具
支持百度、搜狗、360搜索等中国主流搜索引擎
"""
import re
import jieba
import jieba.analyse
from typing import List, Dict, Any, Optional, Union
from dataclasses import dataclass

@dataclass
class SEOScore:
    """SEO评分数据类"""
    overall_score: float = 0.0  # 总体评分(0-100)
    keyword_score: float = 0.0  # 关键词评分
    title_score: float = 0.0  # 标题评分
    content_score: float = 0.0  # 内容评分
    readability_score: float = 0.0  # 可读性评分
    structure_score: float = 0.0  # 结构评分
    mobile_score: float = 0.0  # 移动友好性评分
    details: Dict[str, Any] = None  # 详细评分项

    def __post_init__(self):
        if self.details is None:
            self.details = {}

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典以支持JSON序列化
        
        Returns:
            包含SEO评分的字典
        """
        return {
            "overall_score": self.overall_score,
            "keyword_score": self.keyword_score,
            "title_score": self.title_score,
            "content_score": self.content_score,
            "readability_score": self.readability_score,
            "structure_score": self.structure_score,
            "mobile_score": self.mobile_score,
            "details": self.details
        }

@dataclass
class SEOSuggestion:
    """SEO建议数据类"""
    category: str  # 类别(如"标题"、"关键词"等)
    importance: str  # 重要性("高"、"中"、"低")
    issue: str  # 问题描述
    suggestion: str  # 改进建议
    
    def to_dict(self) -> Dict[str, str]:
        """转换为字典以支持JSON序列化
        
        Returns:
            包含SEO建议的字典
        """
        return {
            "category": self.category,
            "importance": self.importance,
            "issue": self.issue,
            "suggestion": self.suggestion
        }

class ChineseSEOAnalyzer:
    """中文SEO分析器类"""
    
    def __init__(self):
        """初始化分析器"""
        # 加载中文停用词
        self.stopwords = self._load_stopwords()
        # 添加自定义词典
        self._add_custom_dict()
    
    def _load_stopwords(self) -> List[str]:
        """加载中文停用词"""
        # 简单的停用词列表，实际使用时可以从文件加载
        return ["的", "了", "和", "是", "在", "有", "与", "这", "那", "你", "我", "他", "她", "它", "们"]
    
    def _add_custom_dict(self):
        """添加自定义词典，以提高分词准确性"""
        # 在实际应用中，可以加载完整的自定义词典
        custom_words = ["搜索引擎优化", "百度权重", "移动友好性", "网站收录", "关键词密度"]
        for word in custom_words:
            jieba.add_word(word)
    
    def extract_keywords_from_content(self, content: str, max_keywords: int = 5) -> List[str]:
        """从内容中提取关键词
        
        Args:
            content: 文章内容
            max_keywords: 最多提取的关键词数量
            
        Returns:
            提取的关键词列表
        """
        if not content:
            return []
        
        # 使用jieba提取关键词
        keywords = jieba.analyse.extract_tags(content, topK=max_keywords)
        return keywords
    
    def analyze_title(self, title: str, keywords: List[str]) -> Dict[str, Any]:
        """分析文章标题
        
        Args:
            title: 文章标题
            keywords: 目标关键词列表
            
        Returns:
            包含标题分析结果的字典
        """
        if not title:
            return {
                "score": 0,
                "length": 0,
                "has_keywords": False,
                "suggestions": ["标题不能为空"]
            }
        
        # 标题长度分析
        title_length = len(title)
        length_score = 0
        length_suggestion = ""
        
        # 百度标题优化建议: 30-60个字符
        if title_length < 10:
            length_score = 30
            length_suggestion = "标题过短，百度建议标题长度在15-30个汉字之间"
        elif 10 <= title_length <= 30:
            length_score = 100
            length_suggestion = "标题长度合适"
        else:
            length_score = 60
            length_suggestion = "标题过长，百度建议标题长度在15-30个汉字之间"
        
        # 关键词出现分析
        keyword_present = False
        keyword_at_beginning = False
        keyword_suggestions = []
        
        # 计算标题中包含的关键词数量
        keywords_included = []
        for keyword in keywords:
            if keyword in title:
                keywords_included.append(keyword)
                keyword_present = True
                if title.startswith(keyword):
                    keyword_at_beginning = True
        
        keyword_score = 0
        if keyword_present:
            keyword_score = 70
            if keyword_at_beginning:
                keyword_score = 100
            if len(keywords_included) > 1:
                keyword_suggestions.append(f"标题包含多个关键词({', '.join(keywords_included)})，可能导致关键词稀释")
        else:
            keyword_suggestions.append("标题中未包含任何目标关键词")
        
        # 特殊字符检查
        special_chars = re.findall(r'[^\w\s\u4e00-\u9fff]', title)
        special_chars_score = 100 - (len(special_chars) * 10)
        special_chars_suggestion = ""
        
        if len(special_chars) > 3:
            special_chars_score = max(special_chars_score, 60)
            special_chars_suggestion = "标题中特殊字符过多，可能影响搜索引擎理解"
        
        # 汇总分析结果
        overall_score = (length_score + keyword_score + special_chars_score) / 3
        
        suggestions = []
        if length_suggestion:
            suggestions.append(length_suggestion)
        if keyword_suggestions:
            suggestions.extend(keyword_suggestions)
        if special_chars_suggestion:
            suggestions.append(special_chars_suggestion)
        
        return {
            "score": overall_score,
            "length": title_length,
            "has_keywords": keyword_present,
            "keywords_included": keywords_included,
            "keyword_at_beginning": keyword_at_beginning,
            "special_chars_count": len(special_chars),
            "suggestions": suggestions
        }
    
    def analyze_keywords(self, content: str, target_keywords: List[str]) -> Dict[str, Any]:
        """分析关键词使用情况
        
        Args:
            content: 文章内容
            target_keywords: 目标关键词列表
            
        Returns:
            包含关键词分析结果的字典
        """
        if not content:
            return {
                "score": 0,
                "density": {},
                "suggestions": ["无内容可分析"]
            }
        
        # 确保target_keywords是有效的字符串列表
        if not target_keywords:
            target_keywords = []
        elif not isinstance(target_keywords, list):
            if isinstance(target_keywords, str):
                target_keywords = [target_keywords]
            else:
                try:
                    target_keywords = [str(target_keywords)]
                except:
                    target_keywords = []
        else:
            valid_keywords = []
            for k in target_keywords:
                if isinstance(k, str) and k:
                    valid_keywords.append(k)
                elif k:
                    try:
                        valid_keywords.append(str(k))
                    except:
                        pass
            target_keywords = valid_keywords
        
        # 使用jieba分词
        words = [word for word in jieba.cut(content) if word not in self.stopwords and len(word) > 1]
        total_words = len(words)
        
        if total_words == 0:
            return {
                "score": 0,
                "density": {},
                "suggestions": ["内容过少，无法分析关键词"]
            }
        
        # 计算目标关键词密度
        keyword_counts = {}
        keyword_density = {}
        
        for keyword in target_keywords:
            # 计算关键词在内容中的出现次数
            count = content.count(keyword)
            keyword_counts[keyword] = count
            
            # 计算关键词密度 (百分比)
            density = (count / total_words) * 100
            keyword_density[keyword] = round(density, 2)
        
        # 提取文章中的TOP关键词
        top_keywords = jieba.analyse.extract_tags(content, topK=10, withWeight=True)
        extracted_keywords = {keyword: round(weight * 100, 2) for keyword, weight in top_keywords}
        
        # 关键词评分和建议
        score = 0
        suggestions = []
        
        # 对每个目标关键词进行评分
        keyword_scores = []
        for keyword, density in keyword_density.items():
            keyword_score = 0
            
            # 百度SEO建议关键词密度在2%-8%之间
            if 0 < density < 1:
                keyword_score = 50
                suggestions.append(f"关键词 '{keyword}' 出现次数过少，密度{density}%，建议增加")
            elif 1 <= density <= 3:
                keyword_score = 100
            elif 3 < density <= 5:
                keyword_score = 80
                suggestions.append(f"关键词 '{keyword}' 密度{density}%，接近过度优化边界")
            else:
                keyword_score = 40
                suggestions.append(f"关键词 '{keyword}' 密度{density}%过高，可能被视为关键词堆砌")
            
            keyword_scores.append(keyword_score)
        
        # 计算平均关键词得分
        if keyword_scores:
            score = sum(keyword_scores) / len(keyword_scores)
        
        # 检查目标关键词是否出现在提取的TOP关键词中
        missing_keywords = []
        for keyword in target_keywords:
            found = False
            for extracted_keyword in extracted_keywords:
                if keyword in extracted_keyword or extracted_keyword in keyword:
                    found = True
                    break
            
            if not found:
                missing_keywords.append(keyword)
        
        if missing_keywords:
            suggestions.append(f"以下关键词在文章中不够突出: {', '.join(missing_keywords)}")
        
        return {
            "score": score,
            "density": keyword_density,
            "counts": keyword_counts,
            "total_words": total_words,
            "extracted_keywords": extracted_keywords,
            "missing_keywords": missing_keywords,
            "suggestions": suggestions
        }
    
    def analyze_content(self, content: str) -> Dict[str, Any]:
        """分析文章内容
        
        Args:
            content: 文章内容
            
        Returns:
            包含内容分析结果的字典
        """
        if not content:
            return {
                "score": 0,
                "length": 0,
                "suggestions": ["无内容可分析"]
            }
        
        # 内容长度分析
        content_length = len(content)
        
        # 段落分析
        paragraphs = re.split(r'\n+', content)
        paragraphs = [p for p in paragraphs if p.strip()]
        
        # 平均段落长度
        avg_paragraph_length = sum(len(p) for p in paragraphs) / len(paragraphs) if paragraphs else 0
        
        # 内容长度评分
        length_score = 0
        length_suggestion = ""
        
        # 百度推荐文章长度至少1000字
        if content_length < 500:
            length_score = 40
            length_suggestion = "内容过短，百度推荐文章长度至少1000字"
        elif 500 <= content_length < 1000:
            length_score = 70
            length_suggestion = "内容略短，百度推荐文章长度至少1000字"
        elif 1000 <= content_length < 2000:
            length_score = 90
            length_suggestion = "内容长度适中"
        else:
            length_score = 100
            length_suggestion = "内容长度充足，有利于SEO"
        
        # 段落结构评分
        paragraph_score = 0
        paragraph_suggestion = ""
        
        if len(paragraphs) < 3:
            paragraph_score = 50
            paragraph_suggestion = "段落数量过少，建议将内容分成更多段落"
        elif avg_paragraph_length > 300:
            paragraph_score = 70
            paragraph_suggestion = "平均段落长度过长，建议将长段落拆分为短段落"
        else:
            paragraph_score = 100
            paragraph_suggestion = "段落结构合理"
        
        # 汇总分析结果
        overall_score = (length_score + paragraph_score) / 2
        
        suggestions = []
        if length_suggestion:
            suggestions.append(length_suggestion)
        if paragraph_suggestion:
            suggestions.append(paragraph_suggestion)
        
        return {
            "score": overall_score,
            "length": content_length,
            "paragraph_count": len(paragraphs),
            "avg_paragraph_length": avg_paragraph_length,
            "suggestions": suggestions
        }
    
    def analyze_structure(self, content: str) -> Dict[str, Any]:
        """分析文章结构
        
        Args:
            content: 文章内容(Markdown格式)
            
        Returns:
            包含结构分析结果的字典
        """
        if not content:
            return {
                "score": 0,
                "has_headings": False,
                "suggestions": ["无内容可分析"]
            }
        
        # 分析Markdown标题
        h1_pattern = r'^# (.+)$'
        h2_pattern = r'^## (.+)$'
        h3_pattern = r'^### (.+)$'
        
        h1_titles = re.findall(h1_pattern, content, re.MULTILINE)
        h2_titles = re.findall(h2_pattern, content, re.MULTILINE)
        h3_titles = re.findall(h3_pattern, content, re.MULTILINE)
        
        has_headings = bool(h1_titles or h2_titles or h3_titles)
        heading_count = len(h1_titles) + len(h2_titles) + len(h3_titles)
        
        # 结构评分
        structure_score = 0
        structure_suggestions = []
        
        if not has_headings:
            structure_score = 30
            structure_suggestions.append("文章缺少标题层级，建议添加h1、h2、h3标题")
        elif not h1_titles:
            structure_score = 60
            structure_suggestions.append("文章缺少h1主标题，每篇文章应有一个h1标题")
        elif len(h1_titles) > 1:
            structure_score = 70
            structure_suggestions.append("文章包含多个h1标题，建议每篇文章只使用一个h1标题")
        elif not h2_titles:
            structure_score = 80
            structure_suggestions.append("文章缺少h2子标题，建议添加h2标题提高结构清晰度")
        else:
            structure_score = 100
            structure_suggestions.append("文章结构层次清晰")
        
        # 分析图片数量
        image_pattern = r'!\[.*?\]\(.*?\)'
        images = re.findall(image_pattern, content)
        image_count = len(images)
        
        image_score = 0
        image_suggestion = ""
        
        # 百度SEO建议：图文结合对排名有利
        if image_count == 0:
            image_score = 50
            image_suggestion = "文章没有图片，建议添加相关图片提高用户体验"
        elif 1 <= image_count <= 2:
            image_score = 80
            image_suggestion = "文章图片数量偏少，建议适当增加"
        elif 3 <= image_count <= 10:
            image_score = 100
            image_suggestion = "文章图片数量适中"
        else:
            image_score = 70
            image_suggestion = "文章图片数量较多，请确保所有图片都与内容相关"
        
        if image_suggestion:
            structure_suggestions.append(image_suggestion)
        
        # 汇总分析结果
        overall_score = (structure_score + image_score) / 2
        
        return {
            "score": overall_score,
            "has_headings": has_headings,
            "heading_count": heading_count,
            "h1_count": len(h1_titles),
            "h2_count": len(h2_titles),
            "h3_count": len(h3_titles),
            "image_count": image_count,
            "suggestions": structure_suggestions
        }
    
    def analyze_readability(self, content: str) -> Dict[str, Any]:
        """分析文章可读性
        
        Args:
            content: 文章内容
            
        Returns:
            包含可读性分析结果的字典
        """
        if not content:
            return {
                "score": 0,
                "suggestions": ["无内容可分析"]
            }
        
        # 分词
        words = list(jieba.cut(content))
        
        # 计算平均词长
        avg_word_length = sum(len(word) for word in words) / len(words) if words else 0
        
        # 计算平均句长
        sentences = re.split(r'[。！？!?.]', content)
        sentences = [s for s in sentences if s.strip()]
        avg_sentence_length = sum(len(s) for s in sentences) / len(sentences) if sentences else 0
        
        # 可读性评分
        readability_score = 0
        readability_suggestions = []
        
        # 句子长度评分
        if avg_sentence_length > 50:
            readability_score += 60
            readability_suggestions.append("平均句子长度过长，建议缩短句子以提高可读性")
        elif 30 <= avg_sentence_length <= 50:
            readability_score += 80
            readability_suggestions.append("句子长度适中，但可以考虑适当缩短一些长句")
        else:
            readability_score += 100
        
        # 专业术语或难词分析（简化版）
        difficult_word_count = sum(1 for word in words if len(word) > 3 and word not in self.stopwords)
        difficult_word_ratio = difficult_word_count / len(words) if words else 0
        
        if difficult_word_ratio > 0.2:
            readability_score = (readability_score + 70) / 2
            readability_suggestions.append("文章包含较多专业术语或长词，建议适当简化以提高可读性")
        
        # 汇总分析结果
        overall_score = readability_score
        
        return {
            "score": overall_score,
            "avg_word_length": avg_word_length,
            "avg_sentence_length": avg_sentence_length,
            "difficult_word_ratio": difficult_word_ratio,
            "suggestions": readability_suggestions
        }
    
    def analyze_seo(self, title: str, content: str, keywords: List[str] = None) -> Dict[str, Any]:
        """综合分析文章SEO情况
        
        Args:
            title: 文章标题
            content: 文章内容(Markdown格式)
            keywords: 目标关键词列表，如果为None则自动提取
            
        Returns:
            包含SEO分析结果的字典
        """
        # 如果未提供关键词，则自动提取
        if keywords is None or not keywords:
            if title and content:
                # 从标题和内容提取关键词
                text_for_keywords = f"{title} {content}"
                extracted = jieba.analyse.extract_tags(text_for_keywords, topK=5)
                keywords = extracted
            else:
                keywords = []
        
        # 确保keywords是字符串列表
        if not isinstance(keywords, list):
            if isinstance(keywords, str):
                keywords = [keywords]
            else:
                try:
                    keywords = [str(keywords)]
                except:
                    keywords = []
        
        # 过滤非字符串元素
        valid_keywords = []
        for k in keywords:
            if isinstance(k, str) and k:
                valid_keywords.append(k)
            elif k:
                try:
                    valid_keywords.append(str(k))
                except:
                    pass
        keywords = valid_keywords
        
        # 各项分析
        title_analysis = self.analyze_title(title, keywords)
        keyword_analysis = self.analyze_keywords(content, keywords)
        content_analysis = self.analyze_content(content)
        structure_analysis = self.analyze_structure(content)
        readability_analysis = self.analyze_readability(content)
        
        # 计算总体评分
        overall_score = (
            title_analysis.get("score", 0) * 0.25 +
            keyword_analysis.get("score", 0) * 0.25 +
            content_analysis.get("score", 0) * 0.2 +
            structure_analysis.get("score", 0) * 0.15 +
            readability_analysis.get("score", 0) * 0.15
        )
        
        # 收集所有建议
        all_suggestions = []
        all_suggestions.extend(title_analysis.get("suggestions", []))
        all_suggestions.extend(keyword_analysis.get("suggestions", []))
        all_suggestions.extend(content_analysis.get("suggestions", []))
        all_suggestions.extend(structure_analysis.get("suggestions", []))
        all_suggestions.extend(readability_analysis.get("suggestions", []))
        
        # 移动友好性建议
        mobile_suggestions = [
            "确保正文字体大小至少为14px，以便在移动设备上阅读",
            "避免使用大型表格，在移动设备上可能需要水平滚动",
            "确保图片响应式展示，自动适应屏幕宽度"
        ]
        
        # 创建SEO评分对象
        seo_score = SEOScore(
            overall_score=overall_score,
            keyword_score=keyword_analysis.get("score", 0),
            title_score=title_analysis.get("score", 0),
            content_score=content_analysis.get("score", 0),
            readability_score=readability_analysis.get("score", 0),
            structure_score=structure_analysis.get("score", 0),
            mobile_score=80.0,  # 假设的移动友好性评分
            details={
                "title_analysis": title_analysis,
                "keyword_analysis": keyword_analysis,
                "content_analysis": content_analysis,
                "structure_analysis": structure_analysis,
                "readability_analysis": readability_analysis,
                "mobile_suggestions": mobile_suggestions
            }
        )
        
        # 返回结果
        return {
            "score": seo_score,
            "keywords": keywords,
            "suggestions": all_suggestions
        }

    def analyze(self, content: Union[str, Dict[str, Any]], title: str = None, keywords: List[str] = None) -> Dict[str, Any]:
        """兼容性方法，用于分析内容的SEO情况
        
        Args:
            content: 文章内容，可以是字符串或结构化内容字典
            title: 文章标题，如果为None则从内容中提取
            keywords: 关键词列表，如果为None则自动提取
            
        Returns:
            包含SEO分析结果的字典，包含得分和建议
        """
        # 确保content是合适的类型
        if not content:
            return {"score": SEOScore(0, 0, 0, 0), "suggestions": [], "keywords": keywords or []}
        
        content_text = ""
        content_title = title
        
        # 处理不同类型的content
        if isinstance(content, dict):
            # 如果标题为None，尝试从content字典中提取
            if not content_title and "title" in content:
                content_title = content.get("title", "")
                
            # 提取全文本内容
            content_text = self._extract_text_from_dict(content)
        elif isinstance(content, str):
            # 如果内容是字符串，直接使用
            content_text = content
            
            # 如果未提供标题，尝试从内容中提取第一行作为标题
            if not content_title:
                lines = content_text.strip().split('\n')
                content_title = lines[0] if lines else ""
        else:
            # 尝试转换为字符串
            try:
                content_text = str(content)
                if not content_title:
                    content_title = "未知标题"
            except:
                return {"score": SEOScore(0, 0, 0, 0), "suggestions": ["无法处理的内容类型"], "keywords": keywords or []}
        
        # 如果未提供关键词，则自动提取
        if not keywords:
            keywords = self.extract_keywords_from_content(content_text)
        else:
            # 确保keywords是字符串列表
            if not isinstance(keywords, list):
                if isinstance(keywords, str):
                    keywords = [keywords]
                else:
                    try:
                        keywords = [str(keywords)]
                    except:
                        keywords = []
            
            # 过滤非字符串元素
            valid_keywords = []
            for k in keywords:
                if isinstance(k, str) and k:
                    valid_keywords.append(k)
                elif k:
                    try:
                        valid_keywords.append(str(k))
                    except:
                        pass
            keywords = valid_keywords
        
        # 调用analyze_seo方法
        return self.analyze_seo(content_title, content_text, keywords)
    
    def _extract_text_from_dict(self, content: Dict[str, Any]) -> str:
        """从结构化内容字典中提取全文本
        
        Args:
            content: 结构化内容字典
            
        Returns:
            提取的全文本
        """
        text_parts = []
        
        # 提取标题
        if "title" in content and content["title"]:
            text_parts.append(str(content["title"]))
            
        # 提取介绍
        if "introduction" in content and content["introduction"]:
            text_parts.append(str(content["introduction"]))
            
        # 提取各节内容
        if "sections" in content and isinstance(content["sections"], list):
            for section in content["sections"]:
                if isinstance(section, dict):
                    if "title" in section and section["title"]:
                        text_parts.append(str(section["title"]))
                    if "content" in section and section["content"]:
                        text_parts.append(str(section["content"]))
                    
                    # 提取子节内容
                    if "subsections" in section and isinstance(section["subsections"], list):
                        for subsection in section["subsections"]:
                            if isinstance(subsection, dict):
                                if "title" in subsection and subsection["title"]:
                                    text_parts.append(str(subsection["title"]))
                                if "content" in subsection and subsection["content"]:
                                    text_parts.append(str(subsection["content"]))
        
        # 提取结论
        if "conclusion" in content and content["conclusion"]:
            text_parts.append(str(content["conclusion"]))
            
        return "\n\n".join(text_parts)

if __name__ == "__main__":
    # 测试代码
    analyzer = ChineseSEOAnalyzer()
    result = analyzer.analyze_seo(
        title="SEO优化指南：如何提高百度排名",
        content="# SEO优化指南：如何提高百度排名\n\n## 什么是SEO\n\nSEO（搜索引擎优化）是通过了解搜索引擎的运作规则来调整网站，以提高网站在搜索引擎中的排名。\n\n## 关键词优化\n\n选择合适的关键词是SEO的基础。关键词应该是用户会搜索的，与您的网站内容相关的词语。\n\n### 长尾关键词\n\n长尾关键词是较长、较具体的关键词短语，通常竞争较小，转化率较高。\n\n## 内容优化\n\n内容为王。提供高质量、原创的内容是提高排名的关键。\n\n![SEO优化图片](https://example.com/seo.jpg)\n\n## 技术优化\n\n网站速度、移动友好性、URL结构等技术因素也会影响SEO效果。",
        keywords=["SEO优化", "百度排名", "关键词优化"]
    )
    
    print(f"总体评分: {result['score'].overall_score}")
    print(f"标题评分: {result['score'].title_score}")
    print(f"关键词评分: {result['score'].keyword_score}")
    print(f"内容评分: {result['score'].content_score}")
    print(f"结构评分: {result['score'].structure_score}")
    print(f"可读性评分: {result['score'].readability_score}")
    print("\n建议:")
    for suggestion in result["suggestions"]:
        print(f"- {suggestion}") 