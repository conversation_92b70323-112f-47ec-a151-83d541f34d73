"""
SEO管理器 - 统一管理中国搜索引擎优化相关的分析和优化功能
支持百度、搜狗、360等中国搜索引擎
"""
import os
import json
import asyncio
from typing import List, Dict, Any, Optional, Union
from dataclasses import dataclass

from .seo_analyzer import ChineseSEOAnalyzer, SEOScore, SEOSuggestion
from .chinese_seo_optimizer import ChineseSEOOptimizer, SEOOptimizationResult

class SEOManager:
    """SEO管理器类 - 提供统一的接口调用SEO分析和优化功能"""
    
    def __init__(self):
        """初始化SEO管理器"""
        self.analyzer = ChineseSEOAnalyzer()
        self.optimizer = ChineseSEOOptimizer()
        self._cache = {}  # 简单的内存缓存
    
    async def analyze_seo(self, title: str, content: str, 
                    keywords: List[str] = None) -> Dict[str, Any]:
        """分析文章的SEO情况
        
        Args:
            title: 文章标题
            content: 文章内容
            keywords: 关键词列表，如果为None则自动提取
            
        Returns:
            SEO分析结果
        """
        # 缓存键
        cache_key = f"analysis_{hash(title)}_{hash(content)}_{hash(str(keywords))}"
        
        # 检查缓存
        if cache_key in self._cache:
            return self._cache[cache_key]
        
        # 执行分析
        result = self.analyzer.analyze_seo(title, content, keywords)
        
        # 缓存结果
        self._cache[cache_key] = result
        
        return result
    
    async def optimize_article(self, title: str, content: str,
                         keywords: List[str] = None,
                         related_urls: Dict[str, str] = None,
                         optimize_for: str = "baidu") -> SEOOptimizationResult:
        """优化文章的SEO
        
        Args:
            title: 文章标题
            content: 文章内容
            keywords: 关键词列表，如果为None则自动提取
            related_urls: 相关URL字典，用于内部链接
            optimize_for: 针对的搜索引擎，默认为"baidu"
            
        Returns:
            SEO优化结果
        """
        # 缓存键
        cache_key = f"optimization_{hash(title)}_{hash(content)}_{hash(str(keywords))}_{optimize_for}"
        
        # 检查缓存
        if cache_key in self._cache:
            return self._cache[cache_key]
        
        # 执行优化
        result = await self.optimizer.optimize_article(
            title=title,
            content=content,
            keywords=keywords,
            related_urls=related_urls
        )
        
        # 缓存结果
        self._cache[cache_key] = result
        
        return result
    
    async def generate_seo_recommendations(self, title: str, content: str,
                                     keywords: List[str] = None) -> List[Dict[str, str]]:
        """生成SEO改进建议
        
        Args:
            title: 文章标题
            content: 文章内容
            keywords: 关键词列表，如果为None则自动提取
            
        Returns:
            SEO建议列表
        """
        # 分析SEO
        analysis = await self.analyze_seo(title, content, keywords)
        score = analysis["score"]
        
        # 收集建议
        recommendations = []
        
        # 标题建议
        if score.title_score < 80:
            title_analysis = score.details["title_analysis"]
            for suggestion in title_analysis.get("suggestions", []):
                recommendations.append({
                    "category": "标题优化",
                    "importance": "高",
                    "suggestion": suggestion
                })
        
        # 关键词建议
        if score.keyword_score < 80:
            keyword_analysis = score.details["keyword_analysis"]
            for suggestion in keyword_analysis.get("suggestions", []):
                recommendations.append({
                    "category": "关键词优化",
                    "importance": "高",
                    "suggestion": suggestion
                })
        
        # 内容建议
        if score.content_score < 80:
            content_analysis = score.details["content_analysis"]
            for suggestion in content_analysis.get("suggestions", []):
                recommendations.append({
                    "category": "内容优化",
                    "importance": "中",
                    "suggestion": suggestion
                })
        
        # 结构建议
        if score.structure_score < 80:
            structure_analysis = score.details["structure_analysis"]
            for suggestion in structure_analysis.get("suggestions", []):
                recommendations.append({
                    "category": "结构优化",
                    "importance": "中",
                    "suggestion": suggestion
                })
        
        # 可读性建议
        if score.readability_score < 80:
            readability_analysis = score.details["readability_analysis"]
            for suggestion in readability_analysis.get("suggestions", []):
                recommendations.append({
                    "category": "可读性优化",
                    "importance": "中",
                    "suggestion": suggestion
                })
        
        # 移动友好性建议
        if score.mobile_score < 80:
            for suggestion in score.details.get("mobile_suggestions", []):
                recommendations.append({
                    "category": "移动友好性",
                    "importance": "中",
                    "suggestion": suggestion
                })
        
        return recommendations
    
    async def generate_meta_tags(self, title: str, content: str,
                           keywords: List[str] = None,
                           platform: str = "all") -> Dict[str, str]:
        """生成SEO元标签
        
        Args:
            title: 文章标题
            content: 文章内容
            keywords: 关键词列表，如果为None则自动提取
            platform: 平台，可选值为"all", "baidu", "sogou", "wechat"等
            
        Returns:
            SEO元标签字典
        """
        # 如果未提供关键词，则自动提取
        if keywords is None or not keywords:
            # 从标题和内容提取关键词
            import jieba.analyse
            text_for_keywords = f"{title} {content}"
            keywords = jieba.analyse.extract_tags(text_for_keywords, topK=5)
        
        # 生成描述
        description = self.optimizer.add_meta_description(content, keywords)
        
        # 基本元标签
        meta_tags = {
            "title": title,
            "keywords": ",".join(keywords),
            "description": description
        }
        
        # 添加平台特定标签
        if platform in ["all", "baidu"]:
            baidu_tags = self.optimizer.generate_baidu_meta_tags(title, description, keywords)
            for key, value in baidu_tags.items():
                if key not in meta_tags:
                    meta_tags[key] = value
        
        # 添加微信平台标签
        if platform in ["all", "wechat"]:
            meta_tags.update({
                "og:title": title,
                "og:description": description,
                "og:type": "article",
                "og:url": "",  # 需要填充实际URL
                "og:image": ""  # 需要填充封面图URL
            })
        
        return meta_tags
    
    async def optimize_title_for_platform(self, title: str, keywords: List[str],
                                    platform: str = "baidu") -> str:
        """针对特定平台优化标题
        
        Args:
            title: 原始标题
            keywords: 关键词列表
            platform: 平台，可选值为"baidu", "sogou", "wechat"等
            
        Returns:
            优化后的标题
        """
        # 不同平台的标题优化策略可能有所不同
        if platform == "baidu":
            # 百度平台标题优化
            return self.optimizer.optimize_title(title, keywords)
        elif platform == "wechat":
            # 微信平台标题优化（更注重吸引力和点击率）
            # 在实际应用中，可以为不同平台实现不同的优化策略
            return title
        else:
            # 默认使用百度优化策略
            return self.optimizer.optimize_title(title, keywords)
    
    async def analyze_keyword_competition(self, keywords: List[str]) -> Dict[str, Dict[str, Any]]:
        """分析关键词竞争度
        
        注意：此功能需要调用外部API，这里仅为示例接口
        
        Args:
            keywords: 关键词列表
            
        Returns:
            关键词竞争度分析结果
        """
        # 在实际应用中，可以调用百度指数API或第三方数据服务
        # 这里仅返回模拟数据
        result = {}
        
        for keyword in keywords:
            result[keyword] = {
                "search_volume": {
                    "baidu": 1000 + len(keyword) * 100,  # 模拟搜索量
                    "sogou": 500 + len(keyword) * 50,
                },
                "competition": 0.5 + len(keyword) * 0.05,  # 模拟竞争度(0-1)
                "difficulty": {
                    "score": min(100, 40 + len(keyword) * 10),  # 模拟难度分数(0-100)
                    "level": "中等"  # 难度级别
                },
                "suggestion": "建议使用" if len(keyword) < 5 else "竞争激烈，慎重使用"
            }
        
        return result
    
    async def get_trending_keywords(self, category: str = "general", 
                             limit: int = 10) -> List[str]:
        """获取热门关键词
        
        注意：此功能需要调用外部API，这里仅为示例接口
        
        Args:
            category: 类别，可选值为"general", "tech", "finance"等
            limit: 返回关键词数量
            
        Returns:
            热门关键词列表
        """
        # 在实际应用中，可以调用百度热搜API或第三方数据服务
        # 这里仅返回模拟数据
        trending_keywords = {
            "general": ["新冠疫情", "世界杯", "经济复苏", "人工智能", "元宇宙", 
                      "数字人民币", "电动汽车", "碳中和", "脱贫攻坚", "乡村振兴"],
            "tech": ["人工智能", "大模型", "5G技术", "元宇宙", "区块链",
                   "量子计算", "物联网", "芯片技术", "云计算", "数据安全"],
            "finance": ["股市行情", "数字货币", "通货膨胀", "利率走势", "房地产市场",
                      "A股市场", "美联储加息", "投资策略", "经济复苏", "银行理财"]
        }
        
        if category in trending_keywords:
            return trending_keywords[category][:limit]
        else:
            return trending_keywords["general"][:limit]

    async def analyze_content(self, title: str, content: str, 
                    keywords: List[str] = None) -> Dict[str, Any]:
        """分析文章内容的SEO情况（兼容方法，调用analyze_seo）
        
        Args:
            title: 文章标题
            content: 文章内容
            keywords: 关键词列表，如果为None则自动提取
            
        Returns:
            SEO分析结果
        """
        # 直接调用analyze_seo方法
        return await self.analyze_seo(title, content, keywords)

if __name__ == "__main__":
    # 测试代码
    seo_manager = SEOManager()
    
    async def test_manager():
        # 测试分析功能
        analysis = await seo_manager.analyze_seo(
            title="SEO优化指南：提高百度排名的实用技巧",
            content="# SEO优化指南\n\nSEO是搜索引擎优化的简称，通过了解搜索引擎的运作规则来调整网站，以提高网站在搜索引擎中的排名。\n\n## 关键词优化\n\n选择合适的关键词是SEO的基础。关键词应该是用户会搜索的，与您的网站内容相关的词语。\n\n## 内容优化\n\n内容为王，提供高质量、原创的内容是提高排名的关键。",
            keywords=["SEO优化", "百度排名", "关键词优化"]
        )
        
        print(f"SEO分析总分: {analysis['score'].overall_score:.2f}")
        
        # 测试优化功能
        result = await seo_manager.optimize_article(
            title="SEO方法",
            content="SEO是搜索引擎优化的简称。做好SEO可以提高网站排名。关键词优化是SEO的核心。",
            keywords=["SEO优化", "百度排名", "关键词优化"],
            related_urls={
                "搜索引擎优化": "https://example.com/seo",
                "关键词优化": "https://example.com/keyword-optimization"
            }
        )
        
        print(f"\n优化前标题: {result.original_title}")
        print(f"优化后标题: {result.optimized_title}")
        print(f"优化前评分: {result.score_before.overall_score:.2f}")
        print(f"优化后评分: {result.score_after.overall_score:.2f}")
        
        # 测试生成SEO建议
        recommendations = await seo_manager.generate_seo_recommendations(
            title="网站建设",
            content="网站建设是指制作网站的过程。",
            keywords=["网站建设", "网站制作"]
        )
        
        print("\nSEO建议:")
        for rec in recommendations:
            print(f"- [{rec['category']}] {rec['suggestion']}")
        
        # 测试生成Meta标签
        meta_tags = await seo_manager.generate_meta_tags(
            title="人工智能应用案例分析",
            content="人工智能在各行各业的应用越来越广泛，本文分析了几个典型案例...",
            keywords=["人工智能", "AI应用", "案例分析"]
        )
        
        print("\nMeta标签:")
        for key, value in meta_tags.items():
            print(f"- {key}: {value}")
        
        # 测试获取热门关键词
        trending = await seo_manager.get_trending_keywords(category="tech", limit=5)
        
        print("\n热门科技关键词:")
        for keyword in trending:
            print(f"- {keyword}")
    
    # 运行测试
    asyncio.run(test_manager()) 