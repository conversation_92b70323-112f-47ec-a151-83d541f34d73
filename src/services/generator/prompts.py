from typing import Dict, List, Any, Optional, Tuple, Union
import json


def planning_search_query_generation_prompt(topic: str, outline: List[Dict[str, Any]], content_direction: str = "", keywords: List[str] = None, article_type: str = None, current_time: Dict[str, Any] = None) -> str:
    """生成搜索查询的提示词

    为了搜集文章素材，需要生成高质量的搜索查询。这个函数通过给LLM提供文章主题、大纲、
    内容方向和关键词等信息，让它生成能够获取全面和相关信息的搜索查询。
    
    Args:
        topic: 文章主题
        outline: 文章大纲列表，每项包含 title 和 content
        content_direction: 选定的内容方向
        keywords: 文章关键词列表
        article_type: 文章类型
        current_time: 当前时间信息字典，包含日期、时间、星期、月份等
        
    Returns:
        生成搜索查询的提示词
    """
    # 防御性编程，确保参数有效
    outline = outline if outline else []
    keywords = keywords if keywords else []
    article_type = article_type or ""
    
    # 处理大纲信息，将大纲转换为易于阅读的格式
    outline_text = ""
    for i, item in enumerate(outline):
        if isinstance(item, dict):
            title = item.get("title", f"章节{i+1}")
            content = item.get("content", "")
            outline_text += f"{i+1}. {title}: {content}\n"
        else:
            outline_text += f"{i+1}. {str(item)}\n"

    # 处理关键词
    keywords_text = ", ".join(keywords) if keywords else "无特定关键词"
    
    # 当前时间相关信息
    time_info_text = ""
    if current_time:
        time_info_text = f"""
当前时间信息：
- 当前日期: {current_time.get('formatted_date', '')}
- 当前时间: {current_time.get('formatted_time', '')}
- 星期: {current_time.get('day_of_week', '')}
- 月份: {current_time.get('month', '')}
- 季节: {current_time.get('season', '')}
"""

    # 基于文章类型推荐不同的搜索查询生成策略
    article_type_prompt = ""
    if article_type:
        if "教程" in article_type or "指南" in article_type or "分析" in article_type:
            article_type_prompt = """
文章类型: 教程/分析型
为教程/分析型文章生成搜索查询时，请特别关注：
1. 详细的方法论和最佳实践指南（包含步骤分解和注意事项）
2. 权威机构发布的技术文档和标准操作流程
3. 专业术语的精确定义和应用场景说明
4. 行业标准、基准数据和评估指标（含量化数据）
5. 成功案例和失败教训的深度分析（包含原因和结果）
"""
        elif "观点" in article_type or "金句" in article_type:
            article_type_prompt = """
文章类型: 议论文/观点型
为议论文/观点型文章生成搜索查询时，请特别关注：
1. 支持论点的权威数据和研究（优先寻找最新统计数据、研究报告和行业白皮书）
2. 反对观点的合理论据（用于构建更有说服力的反驳）
3. 来自认可专家的精确引述（包含专家姓名、职位和出处）
4. 相关政策、法规或行业标准的具体条款和解读
5. 有深度分析的真实案例研究（必须有确切来源和详细背景）
"""
        elif "故事" in article_type or "感触" in article_type or "干货" in article_type:
            article_type_prompt = """
文章类型: 故事/叙事型
为故事/叙事型文章生成搜索查询时，请特别关注：
1. 与主题相关的真实人物和事件的详细背景资料
2. 历史语境和时代背景的准确细节描述
3. 第一手叙述或亲历者访谈记录（包含感受和情感描述）
4. 能增强故事性的场景细节、环境描述和文化习俗
5. 人物的性格特点、言行举止和心理动机的分析资料
"""
        elif "对比" in article_type:
            article_type_prompt = """
文章类型: 对比分析型
为对比分析型文章生成搜索查询时，请特别关注：
1. 各个比较对象的关键特性和技术参数对照表
2. 不同选择在实际应用场景中的优缺点评估
3. 来自专业测评机构的对比测试数据和结论
4. 用户使用体验的多维度反馈和评价
5. 具体成本效益分析和投资回报率计算
"""
        elif "总分总" in article_type:
            article_type_prompt = """
文章类型: 总分总结构
为总分总结构文章生成搜索查询时，请特别关注：
1. 主题的整体概念框架和核心理论依据
2. 各个分论点的专业论证和实证研究
3. 反映不同维度的数据统计和趋势分析
4. 能支持各分论点的具体实例和应用案例
5. 综合性结论和前瞻性展望的专业观点
"""
    
    # 构建最终的提示词
    prompt = f"""作为专业搜索策略专家，请为以下文章计划制定精准的搜索查询方案，确保获取高质量的专业信息和深度素材。输出的搜索查询数组中，每个查询的搜索词尽量是在中国能够搜索到的深度内容，案例和数据要包含比较多的中国本地发生的案例， 可以适当包含国外的案例， 科学依据可以是世界各地的论文或者权威专家的案例。

文章主题: {topic}

{time_info_text}

文章大纲:
{outline_text}

内容方向: {content_direction}

关键词: {keywords_text}

{article_type_prompt}

<think>
思考当前时间与文章主题的关联:
1. 这个主题是否有时效性？是否需要最新的信息？
2. 当前的季节、月份、假日是否与主题相关？
3. 最近的事件或热点是否可以与主题相关联？
4. 搜索时是否应该限定特定的时间范围？如"最近一年"、"2023年以来"等
5. 主题是否涉及历史事件与当前形势的对比？
6. 当前时间是否处于特殊时期(节假日、纪念日等)？这是否对主题有影响？
7. 是否需要搜索主题的历史变化趋势和未来预测？
8. 考虑如何利用当前时间来增加内容的时效性和相关性

思考如何构建深度而全面的搜索策略:
1. 如何超越表面信息，获取真正的深度内容？
2. 如何构建搜索查询，确保覆盖主题的各个重要维度？
3. 什么样的搜索组合能找到专业观点而非浅层观点？
4. 哪些权威来源需要特别关注？
5. 如何平衡通用性搜索与专业性搜索？
</think>

## 搜索策略设计要求
1. 为文章核心内容点设计精准且高效的搜索查询
2. 每个查询必须直指特定信息需求，避免泛泛而搜
3. 优先寻找权威来源的专业内容，确保信息可靠性
4. 查询应当覆盖文章所需的各类信息类型（数据、案例、观点等）
5. 总共生成不超过5个搜索查询，确保每个查询都有明确的价值

## 查询设计要求
每个查询应满足以下标准：
- 使用精确的专业术语和关键概念
- 尽可能包含时间范围限定以确保信息时效性
- 考虑添加权威来源指示词（如研究报告、行业白皮书）
- 适当排除无关信息的过滤词
- 酌情利用搜索运算符提高精准度（如引号、site:等）
- 长度适中，通常3-7个关键词组合效果最佳
- 考虑主题的时效性，优先获取与当前时间({current_time.get('formatted_date', '') if current_time else '今天'})相关的最新信息
- 搜索关键词最后加一个平台文章（比如微信公众号文章， 头条文章）， 常见内容网站微信公众号，今日头条 专业性较强的加上百度百科，知乎， aixiv 对应领域网站文章，生活化的可以加上微博， 豆瓣， 小红书 等等

请以JSON格式返回搜索查询列表:
```json
{{
  "search_queries": [
    "查询1",
    "查询2",
    "查询3",
    ...
  ]
}}
```

请确保生成的查询直接可用于搜索引擎，每个查询都应该精准定位不同的信息需求。优先生成能获取深度、专业、权威内容的查询，而非基础常识信息。查询总数不得超过5个。"""
    
    return prompt

def planning_combined_article_plan_prompt(topic: str, 
                                         keywords: List[str], 
                                         content_directions: Union[List[Union[Dict[str, Any], str]], str], 
                                         recommended_titles: Union[List[str], str] = None,
                                         current_time: Dict[str, Any] = None) -> str:
    """创建文章计划的提示词
    
    生成完整的文章规划提示词，包括文章类型、结构大纲等。
    
    Args:
        topic: 文章主题
        keywords: 关键词列表
        content_directions: 内容方向，可以是字符串或列表
        recommended_titles: 推荐的标题列表或单个标题
        current_time: 当前时间信息字典，包含日期、时间、季节等
        
    Returns:
        文章计划提示词
    """
    # 处理内容方向
    content_direction_text = ""
    if isinstance(content_directions, list):
        if content_directions:
            if isinstance(content_directions[0], dict):
                # 如果是字典列表，提取其中的内容方向
                directions = []
                for item in content_directions:
                    if isinstance(item, dict) and "content_direction" in item:
                        directions.append(item["content_direction"])
                content_direction_text = "\n".join([f"- {d}" for d in directions])
            else:
                # 如果是字符串列表，直接使用
                content_direction_text = "\n".join([f"- {d}" for d in content_directions])
        else:
            content_direction_text = "无特定内容方向"
    else:
        # 如果是单个字符串
        content_direction_text = content_directions

    # 处理推荐标题
    title_text = ""
    if recommended_titles:
        if isinstance(recommended_titles, list):
            if recommended_titles:
                title_text = "\n".join([f"- {t}" for t in recommended_titles])
            else:
                title_text = "无推荐标题"
        else:
            title_text = recommended_titles

    # 处理关键词
    keywords_text = ", ".join(keywords) if keywords else "无特定关键词"
    
    # 当前时间相关信息
    time_info_text = ""
    if current_time:
        time_info_text = f"""
当前时间信息：
- 当前日期: {current_time.get('formatted_date', '')}
- 当前时间: {current_time.get('formatted_time', '')}
- 星期: {current_time.get('day_of_week', '')}
- 月份: {current_time.get('month', '')}
- 季节: {current_time.get('season', '')}
- 临近节日: {current_time.get('upcoming_holiday', '无') if 'upcoming_holiday' in current_time else '无'}
"""

    # 定义模板变量，用于避免在f-string中嵌套过深的问题
    current_date = current_time.get('formatted_date', '今日') if current_time else '今日'
    current_season = current_time.get('season', '当前季节') if current_time else '当前季节'
    
    # 使用三个单引号来避免f-string嵌套过深的问题，以及用+拼接字符串来降低复杂度
    json_template_start = '''{{
  "title": "富有情感和吸引力的标题(10-30字)",
  "article_type": "【必须从以下五种类型中选择一种】：观点+案例+金句、故事+感触+干货、总分总、对比分析、教程/分析型",
  "estimated_word_count": "预计总字数(4000-9000之间)",
  "keywords": ["关键词1", "关键词2", "关键词3", "关键词4", "关键词5"],
  "content_direction": "选择的内容方向",
  "target_audience": "目标读者详细描述",
  "outline": [
    // 对于非教程型文章（流畅型结构），可以包含多个部分：
    {
      "content": "详细描述该部分内容(100-150字)，包括情感点、案例和过渡方式",
      "estimated_length": "该部分预计字数",
      "key_points": ["关键点1", "关键点2", "关键点3"],
      "sensory_elements": ["需要包含的感官元素1", "感官元素2"],
      "references_needed": ["需要引用的资料、案例或金句"]
    }
    
    // 对于教程/分析型, 总分总（章节型结构）， 可以包含多个章节：
    {
      "title": "章节标题",
      "content": "详细的章节内容描述(100-150字)，包含案例、感受和实用价值",
      "estimated_length": "该章节预计字数",
      "key_points": ["关键点1", "关键点2", "关键点3"],
      "sensory_elements": ["需要包含的感官元素1", "感官元素2"],
      "references_needed": ["需要引用的资料、案例或金句"]
    }
  ],
  "cases_and_examples": [
    {
      "case_name": "案例名称",
      "source": "具体来源（必填）",
      "emotional_hook": "案例的情感共鸣点",
      "estimated_length": "该案例预计字数(300-600字)",
      "sensory_details": ["应包含的感官细节1", "感官细节2", "感官细节3"],
      "analysis_points": ["分析要点1", "分析要点2", "分析要点3"]
    }
  ],
  "quotations_to_use": [
    {
      "quote": "推荐引用的诗词、名言或观点",
      "source": "来源",
      "emotional_impact": "这句话的情感冲击力或共鸣点",
      "context": "建议在文章哪个部分使用(具体段落或内容点)"
    }
  ],
  "writing_plan": {
    "tone": "文章语调(温暖、幽默、思考型等)",
    "writing_style": "写作风格(故事型、对话式、沉思型等)",
    "narrative_tricks": ["情感共鸣技巧1", "过渡自然技巧2", "感官描写技巧3"],
    "attention_hooks": ["文章应使用的吸引注意力的技巧1", "技巧2", "技巧3"],
    "personal_elements": ["需要加入的个人经历或感受元素1", "元素2"],
    "audience_connection_points": ["与读者建立情感连接的点1", "连接点2", "连接点3"],
    "pacing_strategy": "文章节奏控制策略(例如：波浪型情感起伏、先舒缓后紧张等)"
  },
  "anti_ai_touches": [
    "列出3-5个特意设计的，使文章更像人类写作的元素，如'在第二部分故意省略了一些细节，增加神秘感'，'在结尾处留下开放性问题'"
  ]
}'''

    # 构建最终提示词
    prompt = f"""作为专业的内容创作策略专家，请为"{topic}"创建一个引人入胜且深度的文章计划，满足3000-5000字的高质量内容要求。

主题: {topic}

{time_info_text}

关键词: {keywords_text}

内容方向:
{content_direction_text}

推荐标题:
{title_text}

【特别提醒】文章类型只能从这五种中选择一种：
1. 观点+案例+金句
2. 故事+感触+干货
3. 总分总
4. 对比分析
5. 教程/分析型
请勿使用其他自创类型，如"观点型文章"、"故事型文章"等。

<think>
深度思考如何像专业作者一样规划文章:

1. 内容深度与读者接受度平衡
   - 如何将专业深度内容转化为读者能轻松理解的表达？
   - 哪些复杂概念需要通过比喻或实例简化？
   - 什么样的知识结构能让读者既有收获又不感到负担？
   - 如何安排信息密度，避免内容过于浅显或过于晦涩？

2. 人性化的结构安排
   - 考虑读者的注意力曲线，哪里需要"钩子"维持兴趣？
   - 如何设计自然的情感波动和思想转折点？
   - 什么地方适合留白或设置悬念，让读者自行思考？
   - 如何避免机械化的叙述模式，增加叙述的不确定性和惊喜感？

3. 时间维度的适度融入
   - 当前时间({current_date})如何微妙地影响内容的呈现方式？
   - 是否有季节性因素({current_season})可以巧妙融入文章的细节？
   - 有哪些时间相关的比喻或引用可以增强共鸣？
   - 时间感知如何影响案例的选择和展示方式？

4. 人类思维特点的模拟
   - 如何在论证过程中展现思考的犹豫和权衡？
   - 在哪些地方可以适当表现出认知偏好或价值判断？
   - 如何在结构中体现非线性思考的特点？
   - 什么样的思维跳跃能让内容更生动而不失逻辑？

5. 不同文章类型的结构特点
   - 【观点+案例+金句】：如何构建引人入胜的论点和有力的论据？哪些案例最能支持论点？
   - 【故事+感触+干货】：如何设计情节曲线？故事中的转折点应该放在哪里？
   - 【总分总】：如何确保分论点既独立又相互支持总论点？
   - 【对比分析】：如何选择最有说服力的对比维度？如何避免刻意对立？
   - 【教程/分析型】：如何确保步骤既清晰又连贯？哪些细节需要特别强调？
</think>

## 深度内容规划要求
为创建一篇具有3000-5000字的高质量长文，请特别关注：

1. 标题必须富有情感张力和点击吸引力，避免平淡直白，同时预示文章的深度和价值
2. 文章必须有架构层次感，从表层到深层逐步展开，满足不同层次读者需求
3. 避免案例拼贴效应：不要堆砌来自不同背景的案例，选择2-3个相关且有共通点的真实案例深入展开，确保案例之间有关联性和连贯性
4. 必须制定清晰的段落规划，每个段落控制在300-600字之间，形成良好的内容节奏
5. 设计3-4个自然的情感共鸣点，在文章关键位置触发读者情感反应，避免机械性地分配情绪
6. 全文必须融入3-5个感官描写元素，增强沉浸感和阅读体验，注重第一人称视角的感受
7. 适当引用2-3条共鸣度高的诗词、名言或金句，提升文化深度和共鸣度
8. 设计1处"意料之外"的内容转折或视角转换，保持读者的持续兴趣，避免完美曲线式的论证
9. 结尾部分必须具有思想升华和行动指引，让读者产生深刻印象并愿意分享
10. 预留一处思考断点或矛盾点，不必完美解决所有问题，允许有未解之谜、复杂情绪或无法定论的讨论，增加真实性

## 防止"AI痕迹"的特别指南
1. 避免"情感彩虹光谱"：不要机械地按固定模式分配情绪，而要让情感自然流动和波动
2. 避免"案例拼贴效应"：不要追求完美均衡的案例分布，应选择角度一致但细节各异的案例
3. 避免"百科全书式堆砌"：每节限制1-2个研究/数据引用，重在深度分析而非广泛收集
4. 避免"逻辑完美曲线"：故意设置1-2处论证跳跃点，允许叙事有起伏和不完美
5. 避免"隐喻密度异常"：选择1-2个核心隐喻贯穿全文，而非短文中混用多种隐喻体系
6. 避免"时间感知混乱"：明确设定案例的时间锚点，避免混合不同时代背景的案例
7. 鼓励表达犹豫和权衡："一方面...但另一方面..."，"我的看法可能不对，但..."
8. 鼓励基于有限信息做出推测："据我所知..."，"从表面看起来..."，"如果没记错的话..."
9. 允许表达带偏向性的观点，不必完全中立公正，可以展现情感倾向

## 不同文章类型的字数分配指南

### 【观点+案例+金句】结构 (4000-9000字)
- 开篇观点引入：400-600字，直接提出明确观点，抓住读者注意力
- 深度案例分析：每个案例600-1000字，集中深入2-3个相关案例，总计2000-4000字
- 每个观点的论述：300-500字/观点，包含具体细节和生动描写
- 设置1-2处思考断点或矛盾思考，表明"这个问题其实并不简单"
- 金句与启示结尾：300-500字，1-2个有力金句和深刻启示
- 过渡段落：每处100-200字，确保文章流畅自然

### 【故事+感触+干货】结构 (3000-5000字)
- 故事铺陈：1500-2500字，集中讲述1个主故事，可辅以1-2个辅助小故事
- 故事细节描写：占故事部分的40%，确保读者身临其境
- 增加情节起伏：必须有1-2个转折点，避免故事发展过于线性和可预测
- 情感共鸣部分：500-800字，从故事中提炼核心感悟，表达真实的复杂情感
- 实用价值分享：800-1200字，提供3-4个可操作的建议或启示
- 收尾与升华：200-400字，回扣主题并给出终极启示，留有思考空间

### 【总分总】结构 (4000-9000字)
- 开头总论：500-800字，点明主题和核心观点，提出1个思考性问题
- 分论点展开：每个分论点700-1200字，精选3-4个分论点，总计2000-3500字
- 每个分论点配有1个深入案例：500-800字/案例，确保案例之间有关联性
- 设置1处转折点或反向思考，如"但我们也应该看到..."
- 结尾总结升华：500-700字，回扣开头并给予更深层次的思考
- 读者互动设计：穿插200-300字，设置1-2个与读者的互动问题

### 【教程/分析型】结构 (4000-9000字)
- 问题背景介绍：400-600字，说明问题的普遍性和重要性
- 核心概念解释：500-800字，确保读者理解基础知识
- 分步骤指导：每个步骤400-600字，选择4-5个关键步骤，总计2000-3000字
- 案例演示：600-1000字，用1个具体、完整的案例展示实际应用
- 预留1处"常见误区"或"注意事项"，指出操作中可能遇到的困难
- 常见问题解答：400-600字，预设并回答3-4个相关问题
- 总结与拓展建议：300-500字，概括要点并给出进阶建议

## 增强人情味和感官体验的具体要求
1. 文章可以包含1-2段个人经历或感悟，每段400-600字，增加真实感和亲近感
2. 使用接地气的语言和口语化表达，如"说实话"、"坦白讲"、"我不得不承认"
3. 每700字左右设置1个感官描写点，包括视觉、听觉、触觉等元素
4. 设置2-3个情感波动点，形成起伏有致的情感曲线，避免平稳的情绪进展
5. 在关键转折处加入反问或设问，增强读者参与感和思考深度
6. 标题和开头的前300字必须具有强烈的情感触发或认知冲突，确保读者继续阅读
7. 增加1-2处主观评价或态度倾向，如"这种做法实在让人心疼"、"说实话我很难认同"

## 输出格式要求
请返回一个有效的JSON对象，不包含任何其他文本或代码块标记：

"""

    # 将json模板添加到提示词中，但不使用f-string嵌套
    prompt += json_template_start

    # 添加JSON格式说明和注意事项
    prompt += """

注意事项:
1. 必须严格按照标准JSON格式输出，确保格式完全正确
2. 不要使用双大括号，只使用标准JSON中的单大括号
3. 所有的键(key)必须用双引号包围
4. 字符串值必须用双引号包围，不要使用单引号
5. 确保大括号和中括号配对正确，检查所有括号是否闭合
6. 对象中的最后一个键值对后面不能有逗号
7. 数组中的最后一个元素后面不能有逗号
8. 文章总字数必须在4000-9000字范围内，outline中各部分字数总和应符合总字数
9. 对于非教程型文章，不要使用明显的章节标题，outline中只描述内容
10. 所有案例必须有关联性和连贯性，不要追求完美均衡的案例分布
11. 文章必须包含至少一处思考断点或矛盾点，避免完美曲线式的论证
12. 文章类型(article_type)字段只能使用指定的5种类型之一：观点+案例+金句、故事+感触+干货、总分总、对比分析、教程/分析型
13. 不要在JSON外部添加任何说明，如"这是我生成的JSON"等文字
14. 不要在JSON中使用注释
15. 在返回JSON之前，请检查确保它是符合标准的有效JSON
"""
    
    return prompt


# ============== WritingAgent提示词 ==============

def writing_article_generation_prompt(topic: str, 
                                      keywords: List[str], 
                                      outline: List[Dict[str, Any]], 
                                      search_content: Union[str, List],
                                      writing_plan: Dict[str, Any] = None,
                                      article_type: str = None,
                                      cases_and_examples: List[Dict[str, Any]] = None,
                                      quotations_to_use: List[Dict[str, Any]] = None,
                                      current_time: Dict[str, Any] = None) -> str:
    """
    生成写作提示，根据计划和搜索内容生成文章
    
    Args:
        topic: 文章主题
        keywords: 关键词列表
        outline: 文章大纲
        search_content: 搜索结果内容，可以是字符串或列表
        writing_plan: 写作计划
        article_type: 文章类型，如"观点+案例+金句"、"故事+感触+干货"、"总分总"、"对比分析"、"教程/分析型"
        cases_and_examples: 要使用的案例和例子列表
        quotations_to_use: 要使用的引用列表
        current_time: 当前时间信息，提供给模型进行时间感知写作
        
    Returns:
        格式化的写作提示
    """
    # 提取和验证输入
    # 转换可能的列表输入到字符串
    search_content_str = ""
    if search_content:
        if isinstance(search_content, list):
            # 如果是列表，连接所有元素
            if all(isinstance(item, str) for item in search_content):
                search_content_str = "\n\n".join(search_content)
            else:
                # 尝试提取每个项中的文本内容
                search_texts = []
                for item in search_content:
                    if isinstance(item, dict) and "content" in item:
                        search_texts.append(str(item["content"]))
                    elif isinstance(item, dict) and "text" in item:
                        search_texts.append(str(item["text"]))
                    else:
                        search_texts.append(str(item))
                search_content_str = "\n\n".join(search_texts)
        else:
            # 如果已经是字符串，直接使用
            search_content_str = str(search_content)
    
    # 格式化案例和引用
    cases_examples_text = ""
    if cases_and_examples and isinstance(cases_and_examples, list) and len(cases_and_examples) > 0:
        cases_examples_text = "\n\n## 可使用的案例和例子\n"
        for i, case in enumerate(cases_and_examples, 1):
            if isinstance(case, dict):
                case_title = case.get("title", f"案例 {i}")
                case_content = case.get("content", "")
                cases_examples_text += f"\n### {case_title}\n{case_content}\n"
            else:
                cases_examples_text += f"\n### 案例 {i}\n{str(case)}\n"
    
    quotations_text = ""
    if quotations_to_use and isinstance(quotations_to_use, list) and len(quotations_to_use) > 0:
        quotations_text = "\n\n## 可使用的引述和金句\n"
        for i, quote in enumerate(quotations_to_use, 1):
            if isinstance(quote, dict):
                quote_text = quote.get("text", "")
                quote_source = quote.get("source", "")
                if quote_source:
                    quotations_text += f"\n- {quote_text} —— {quote_source}\n"
                else:
                    quotations_text += f"\n- {quote_text}\n"
            else:
                quotations_text += f"\n- {str(quote)}\n"
    
    # 格式化大纲
    outline_text = ""
    if outline and isinstance(outline, list) and len(outline) > 0:
        outline_text = "\n\n## 文章大纲\n"
        for i, section in enumerate(outline, 1):
            if isinstance(section, dict):
                section_title = section.get("title", f"章节 {i}")
                section_content = section.get("content", "")
                outline_text += f"\n### {section_title}\n{section_content}\n"
            else:
                outline_text += f"\n### 章节 {i}\n{str(section)}\n"
    
    # 提取写作风格
    writing_style = ""
    tone = ""
    target_audience = ""
    personal_elements = []
    anti_ai_touches = []
    
    if writing_plan:
        writing_style = writing_plan.get("writing_style", "")
        tone = writing_plan.get("tone", "")
        target_audience = writing_plan.get("target_audience", "")
        personal_elements = writing_plan.get("personal_elements", [])
        anti_ai_touches = writing_plan.get("anti_ai_touches", [])
    
    # 根据写作风格和个人元素构建提示
    personal_elements_text = ""
    if personal_elements and isinstance(personal_elements, list) and len(personal_elements) > 0:
        personal_elements_text = "\n\n## 要加入的个人元素和经历\n"
        for element in personal_elements:
            personal_elements_text += f"- {element}\n"

    anti_ai_touches_text = ""
    if anti_ai_touches and isinstance(anti_ai_touches, list) and len(anti_ai_touches) > 0:
        anti_ai_touches_text = "\n\n## 脱离AI痕迹的特别点\n"
        for touch in anti_ai_touches:
            anti_ai_touches_text += f"- {touch}\n"
    
    # 添加通用写作指南
    general_writing_guide = """
## 通用写作指南

### 1. 开头设计
- 以疑问、故事、数据或情景直接开场，抓住读者注意力
- 开场必须富有情感色彩，展现作者独特视角和态度
- 在前300字内必须明确文章核心价值和阅读理由
- 避免开篇堆砌事实或背景，可以适当制造悬念或认知冲突

### 2. 内容深度
- 深入分析而非表面罗列，挖掘现象背后的本质和联系
- 每个核心观点都需要有足够篇幅(600-1500字)展开，而非点到即止
- 使用多维思考，从不同角度分析问题，表现复杂性和辩证性
- 适当表达合理怀疑或批判性思考，避免绝对化表述

### 3. 情感共鸣
- 真实表达情感变化，包括犹豫、惊讶、感动、遗憾等不同情绪
- 使用第一人称"我"分享个人感受和经历，增强亲近感和真实性
- 在叙述、描写中注入情感温度，不做冷静的旁观者
- 设置情感高潮点，引发读者感同身受，配合图景、对话或细节描写

### 4. 逻辑构建
- 观点之间有清晰而自然的过渡，避免生硬跳转
- 适度表现思考过程，如"最初我以为...但深入后发现..."
- 设置合理的矛盾点或悬念，不追求完美无缺的论证
- 适度使用因果推理、类比推理、归纳总结等多种思维方式

### 5. 语言表达
- 使用生动、具体、有画面感的表达，而非抽象概念
- 善用修辞手法(比喻、拟人、夸张等)，点缀文章亮点
- 语言要有节奏感，长短句搭配，制造韵律美
- 使用口语化、接地气的表达，如"说实话"、"坦白讲"等增强亲近感

### 6. 结构节奏
- 文章形成起承转合的自然流动，而非平铺直叙
- 段落长短有变化，有时简短有力，有时从容铺展
- 主次分明，重点部分占据足够篇幅(通常为文章的60-70%)
- 设置1-2处"反转"或"出乎意料"的点，避免过于线性的发展

### 7. 结尾处理
- 回扣开头，形成首尾呼应，给人完整感
- 留有思考空间，不必所有问题都有明确答案
- 提出有深度的问题或展望，拓展思考维度
- 结尾需含有情感升华或价值凝练，给读者深刻印象
"""

    # 添加反AI痕迹指南
    anti_ai_guide = """
## 反AI特征写作技巧

### 1. 打破完美结构
- 故意设置1-2处思路跳跃点，可以用"话说回来"、"不过转念一想"等过渡
- 段落长度有意识地参差不齐，有些段落只有2-3句话，有些则相对较长
- 偶尔使用不完全平行的列举，如点1和点2很相似，但点3却略有不同
- 适当使用插入语和附加说明，制造句式变化和节奏感

### 2. 加入个人化表达
- 使用第一人称视角，加入"坦白讲"、"我不得不承认"等个人口头禅
- 偶尔使用口语化表达，如"这事儿说来话长"、"说实话我也很犯难"
- 加入个人情感判断，如"这种做法真的让人心疼"、"说实话我很难认同"
- 分享具体的个人经历，包含时间、地点、人物等细节，增加可信度

### 3. 展示思考过程
- 表达思考的不确定性，如"我一直在想这个问题，但还没完全想清楚"
- 展示观点的形成过程，如"最初我认为...但后来发现...最终我意识到..."
- 适当表达自我质疑，如"或许我的看法有些片面"、"这只是我的个人理解"
- 使用设问和自问自答，展现思考的内在对话，如"那么问题来了，为什么...?"

### 4. 使用感官与细节描写
- 加入视觉、听觉、触觉等多感官描写，如"刺耳的电话铃声打破了宁静"
- 描述具体的环境细节，增强场景感和代入感
- 使用生动的动作和表情描写，展现人物特征
- 通过具体事物的细节特征，表达抽象概念和情感

### 5. 增加情感波动
- 情感表达有起伏变化，避免单一情绪贯穿全文
- 偶尔表达矛盾心理，如"我既期待又害怕"、"我喜欢但同时也有担忧"
- 允许表达负面情绪或失败经历，增加真实感和共鸣度
- 情感表达与内容进展相结合，而非机械分布的情感点

### 6. 保留不确定性和开放性
- 允许某些问题没有明确答案，如"这个问题可能没有标准答案"
- 承认认知局限，如"基于目前的了解"、"就我所知"、"也许未来会有新发现"
- 对某些观点持保留态度，表达谨慎怀疑
- 结尾处留有开放性思考或未来展望，而非全部封闭

### 7. 使用真实的引用和案例
- 引用真实人物的话语或作品，提供具体出处
- 分享亲身经历或身边真实故事，增加可信度
- 案例描述具体而微观，包含细节、对话和心理活动
- 避免过于完美的案例，允许案例中出现矛盾或遗憾
"""
            
    # 确定文章类型
    type_guide = ""
    article_structure = ""
    output_format = ""
    structure_guide = ""
    
    # 设置默认结构指南
    structure_guide = """
采用自然流畅的段落结构，避免明显的标题分割。使用巧妙的过渡句连接各段落，使文章如行云流水。段落长短应有变化，创造节奏感。避免机械地使用"首先、其次、再次、最后"等过渡词，用更自然的方式连接段落。

### 关键的人性化写作技巧
1. **不完美的表达结构** - 偶尔使用语义重复、冗长表达或简短急促的句子，增加真实感
2. **展示思考过程** - 通过"我想过..."、"刚开始我认为...但后来发现..."等表达思考路径
3. **表达不确定性** - 适当使用"可能"、"或许"、"我猜"，避免绝对化断言
4. **真实情感波动** - 在文章中展现情绪起伏，有时激动、有时冷静、有时疑惑
5. **对话与提问** - 将部分内容设计为与读者的对话，或自问自答的形式
"""
    
    # 更新输出格式：根据文章类型提供不同的输出格式
    if article_type:
        if ("教程/分析型" in article_type) or ("总分总" in article_type):
            # 教程/分析型文章使用章节式结构
            structure_guide = """
## 教程型文章结构安排
1. 整体框架：标题+引言+核心章节+结论，每部分明确分开
2. 引言部分：用问题吸引注意→说明本教程价值→概述将要学到的内容→加入个人经历引发共鸣
3. 核心章节：每个章节都有明确标题→概念解释→步骤说明→实例演示→常见问题
4. 步骤讲解：编号列表呈现→每步都足够具体→重点步骤可加粗突出→添加个人经验或教训
5. 加入挫折体验：分享尝试过程中的失败经历，展示"摸着石头过河"的真实过程
6. 过渡衔接：每个章节开始简要回顾上一章节→结束时预告下一章节内容，注意语言自然
7. 视觉层次：使用标题、副标题、项目符号创建清晰视觉层次，但避免过于整齐划一
8. 总结部分：回顾关键点→强调实际应用→鼓励实践→指引后续学习方向→分享个人观点

### 避免AI痕迹的技巧
1. 制造逻辑小断点：在某一步骤中故意省略一些细节，或表达不确定（"这步可能不是最优的"）
2. 分享特定场景经历：加入自己在特定环境中实践的细节，包括时间、地点、心情变化
3. 避免完美术语：使用"大概"、"差不多"、"看情况"等不精确表达
4. 设置一个"不确定点"：坦言"这个方法我试过几次，效果不太稳定，还在摸索中"
"""
            output_format = """
## 输出格式
你必须严格按照以下JSON格式输出文章，确保没有任何格式错误，不包含任何非法控制字符，确保所有字段都存在：

```json
{
  "title": "有个人风格的教程标题",
  "introduction": "引人入胜的介绍，说明本教程解决的问题和价值，包含个人经历片段",
  "sections": [
    {
      "title": "第一章节标题",
      "content": "章节详细内容，包含步骤说明、示例和提示，以及个人经验或失败教训"
    },
    {
      "title": "第二章节标题",
      "content": "章节详细内容，包含步骤说明、示例和提示，注意与前一章节有自然过渡"
    }
  ],
  "conclusion": "总结要点，分享个人观点与后续建议，留下开放性思考"
}
```

重要提示：
1. 生成的JSON必须严格符合以上格式
2. 不得包含任何特殊控制字符
3. 所有字段必须存在且不能为空
4. JSON中的字符串必须使用双引号
5. 所有内容必须在对应的引号内部
6. 文章总字数应在4000字左右
"""
        else:
            # 其他文章类型（如观点类、故事类等）使用流畅型结构
            output_format = """
## 输出格式
你必须严格按照以下JSON格式输出文章，确保没有任何格式错误，不包含任何非法控制字符，确保所有字段都存在：

```json
{
  "title": "富有情感共鸣的标题",
  "content": "整篇文章的完整内容。应当包含丰富的细节描述、真实的个人情感表达、生动的案例和引用。文章应保持自然流畅的段落结构，不使用明显的标题分割。每个段落至少包含3-5个句子，充分展开观点。在适当位置设置思考断点或情感波动，展现真实思考过程。总体字数应达到4000字左右，确保内容深度和广度。"
}
```

重要提示：
1. 生成的JSON必须严格符合以上格式
2. 不得包含任何特殊控制字符（如制表符、换行符在字符串内部）
3. "title"和"content"字段必须存在且不能为空
4. JSON中的字符串必须使用双引号，不能使用单引号
5. 所有内容必须在对应的引号内部，字符串中的双引号需要转义
6. 文章总字数应在4000字左右
7. 完整内容必须包含在"content"字段中
"""
    else:
        # 默认使用流畅型结构
        output_format = """
## 输出格式
你必须严格按照以下JSON格式输出文章，确保没有任何格式错误，不包含任何非法控制字符，确保所有字段都存在：

```json
{
  "title": "富有情感共鸣的标题",
  "content": "整篇文章的完整内容。应当包含丰富的细节描述、真实的个人情感表达、生动的案例和引用。文章应保持自然流畅的段落结构，不使用明显的标题分割。每个段落至少包含3-5个句子，充分展开观点。在适当位置设置思考断点或情感波动，展现真实思考过程。总体字数应达到4000字左右，确保内容深度和广度。"
}
```

重要提示：
1. 生成的JSON必须严格符合以上格式
2. 不得包含任何特殊控制字符（如制表符、换行符在字符串内部）
3. "title"和"content"字段必须存在且不能为空
4. JSON中的字符串必须使用双引号，不能使用单引号
5. 所有内容必须在对应的引号内部，字符串中的双引号需要转义
6. 文章总字数应在4000字左右
7. 完整内容必须包含在"content"字段中
"""
    
    # 根据文章类型设置不同的指南
    if article_type:
        if "观点+案例+金句" in article_type or "观点" in article_type:
            type_guide = """
## 观点类文章写作指南
1. 开门见山表明观点 - 用简洁有力的语言直接表达核心观点，但可以表现出思考过程
2. 深入少量案例 - 每个观点选择1-2个有关联性的案例深度展开，而非机械罗列多个浅层案例
3. 案例深度剖析 - 不只是讲述案例，更要深入分析案例中的核心要素、关键转折点和深层次原因
4. 适当表达个人观点 - 使用"我认为"、"在我看来"等第一人称表达，展示真实态度
5. 情感共鸣 - 在案例中融入能触动读者情感的细节和转折，让读者产生共鸣
6. 金句点睛 - 在段落结尾或关键位置设计令人印象深刻的金句，起到画龙点睛的作用
7. 语言要接地气 - 避免过于学术化的表达，用日常语言阐述深刻道理，如"说实话"、"老实讲"
8. 对比论证 - 适当用正反对比强化观点的说服力，呈现思考的辩证性
9. 提供独特视角 - 展现对问题的独特思考，不流于表面，挖掘现象背后的本质
10. 结尾留思考 - 以发人深省的问题或启示作结，给读者留下深刻印象，不必解答所有问题

### 避免AI痕迹的特别技巧
1. 表达犹豫与反思 - 使用"我曾经以为...但后来发现..."展示思考的转变过程
2. 加入个人小故事 - 分享与主题相关的个人经历片段，增强真实感与共鸣
3. 语言的不完美性 - 偶尔使用口语化表达或重复强调某些观点，模拟自然说话风格
4. 表达情感倾向 - 不需要对所有观点保持中立，可以表达"这让我很难接受"等情感态度
5. 留下开放性思考 - 文章结尾提出1-2个自己也未完全想清楚的问题，展示思考的开放性

✨ 写作风格提示：写作时想象你在和一个好朋友聊天，分享你的见解和经历。用自然、流畅的语言，避免生硬的专业术语。偶尔加入一些反问，增加与读者的互动感。犹豫和确信并存，既有坚定的核心观点，也有开放的思考空间。
"""
            structure_guide = """
## 观点类文章结构安排
1. 开篇布局(10%篇幅)：可以从一个引人入胜的小故事或疑问开始→提出自己的思考过程→铺垫到核心观点→暗示将要展开的几个分论点
2. 论点展开(70%篇幅)：每个分论点一个或多个段落→每个论点遵循"观点→案例→分析→个人感受→过渡"的内部结构
3. 段落组织：段落长短有变化，不要均匀分布→适当使用短段增强节奏感和强调效果→长段落细致分析
4. 过渡处理：避免用"第一、第二"等机械标记→用问题、感叹或情景自然衔接→引出下一个观点
5. 案例安排：精选少量相关案例深入分析→每个案例都有细节描写和情感温度→避免案例堆砌
6. 情感节奏：设置情绪起伏点→有时兴奋、有时惋惜、有时疑惑→避免情绪曲线过于完美
7. 结尾设计(20%篇幅)：回应开头→综合核心观点→分享个人感悟→以发人深省的问题结束→留下思考余地
"""
        elif "故事+感触+干货" in article_type or "故事" in article_type:
            type_guide = """
## 故事类文章写作指南
1. 精彩开场 - 用悬念、冲突或情感场景开篇，让读者迅速投入故事
2. 视角选择 - 优先使用第一人称"我"或特定主角视角讲述，增强代入感与真实性
3. 生动细节 - 通过人物外貌、动作、对话、环境描写，让故事有画面感，避免过度完美的描述
4. 多层次叙事 - 穿插人物过去、现在的联系，建立立体的人物形象，表现复杂性
5. 情节起伏 - 设置冲突和转折，不要平铺直叙，让故事充满张力，展示解决问题的过程与挣扎
6. 真实情感 - 展现人物复杂的内心世界和情感变化，包括矛盾、犹豫和不确定性
7. 心理描写 - 深入探究人物行为背后的心理变化，尤其是面对困境时的挣扎和成长
8. 感悟要深刻 - 分享从故事中获得的真实感触，避免说教，引发读者共鸣
9. 实用干货 - 提炼出确实能帮助读者的方法、建议或启示，具有实践意义
10. 呼应首尾 - 结尾与开头巧妙呼应，形成完整闭环，但不必完美解答所有问题

### 避免AI痕迹的特别技巧
1. 情绪起伏不平 - 在叙述过程中展现情绪波动，有时欣喜，有时失落，避免情绪曲线过于光滑
2. 加入感官细节 - 描述声音、气味、温度、质感等感官体验，增强沉浸感
3. 非线性叙事 - 适当打破时间顺序，使用倒叙或插叙，增加故事层次感
4. 主观评价插入 - 在讲述过程中加入"说实话当时我很迷茫"等主观评价
5. 设置悬念或疑问 - 保留一些未解之谜或开放性问题，不求事事圆满

✨ 写作风格提示：讲故事时要像和读者面对面聊天一样自然。适当使用第一人称"我"增加亲近感，用"你"直接和读者对话增加共鸣。段落要有长有短，制造节奏感。不要害怕表达真实情感，包括困惑、挣扎或失败的经历。偶尔插入自言自语或反思，增强故事的真实感。
"""
            structure_guide = """
## 故事类文章结构安排
1. 开篇设计(15%篇幅)：以引人入胜的场景或问题开场→自然引入故事背景和人物→暗示故事核心冲突
2. 故事主体(60%篇幅)：设置明确的情节发展线→创造2-3个转折点或冲突→插入对话和内心独白→使用感官描写增强临场感
3. 打破完美叙事：故事中设置1-2处意外或挫折→展示人物面对困境时的真实反应和情绪波动
4. 感悟部分(15%篇幅)：从故事中自然过渡到个人感悟→分享对生活、人性或社会的思考→与读者产生情感共鸣
5. 实用价值(10%篇幅)：提炼实用建议或方法→基于故事经验给出具体可行的行动指南→保持谦逊态度，承认建议可能并非适用所有情况
6. 结尾设计：回扣开头→留下发人深思的余味→可以是一个新问题、一个希望或对未来的展望→避免过于完美的结局

### 故事要素检查表
- 人物是否鲜活且有缺点？(避免完美无瑕的人物形象)
- 情节是否有起伏和转折？(避免线性平滑的发展曲线)
- 是否包含真实的情感冲突和内心挣扎？
- 对话是否自然，反映人物性格和关系？
- 是否有足够的环境和感官描写，让读者身临其境？
- 是否设置了悬念或期待，保持读者兴趣？
"""
        elif "总分总" in article_type:
            type_guide = """
## 总分总结构文章写作指南
1. 引人入胜的总起 - 开篇用简洁有力的语言提出核心观点，可先抛出问题或讲述简短故事引入
2. 明确的分论点 - 将核心观点分解为3-4个清晰的分论点，确保逻辑性和连贯性
3. 论点深度展开 - 每个分论点都有足够篇幅展开，包含案例、分析和个人见解
4. 论证有张力 - 在分论点中设置一些思想碰撞或辩证思考，避免单向度论证
5. 案例有温度 - 每个论点配合生动的案例，案例要有细节和情感温度
6. 过渡要自然 - 分论点之间的过渡要流畅自然，使用问题、感悟或场景变化连接
7. 总结有升华 - 结尾不只是重复前面的内容，而是在更高层次上整合观点，给出更深的洞见或行动建议
8. 逻辑有破点 - 故意在某个分论点中留下小漏洞或未完全解决的问题，表现思考的开放性
9. 表达有个性 - 在适当位置加入个人看法或态度，不必对所有观点保持完全中立
10. 结尾有回响 - 结尾与开头形成呼应，让全文形成完整闭环，又留有思考空间

### 避免AI痕迹的特别技巧
1. 非完美对称性 - 各分论点篇幅不必完全均衡，可以有主次之分
2. 表达思考过程 - 使用"刚开始我以为...但进一步思考后..."等表达思考变化
3. 适当表达情感 - 对某些论点表达更强烈的情感倾向，如"这一点是我最关注的"
4. 引入个人经历 - 在合适位置分享与主题相关的个人故事或见闻
5. 留白与疑问 - 文章不必回答所有问题，可以留下一些开放思考

✨ 写作风格提示：保持文章的骨架清晰，但肌理要丰富多变。避免机械使用"首先、其次、再次、最后"等过渡词，用更自然的方式连接段落。段落长短有变化，短段强调重点，长段深入分析。适当使用反问和设问增加互动感。在分析中融入真实情感和个人体验，增强共鸣。
"""
            structure_guide = """
## 总分总文章结构安排
1. 开篇总论(15%篇幅)：以简短故事、现象或问题引入→提出自己的思考过程→点明核心观点→概述将要展开的分论点
2. 分论点展开(70%篇幅)：
   - 每个分论点占15-25%篇幅，不必完全平均
   - 主要分论点可以篇幅更长，次要分论点可以略短
   - 分论点内部结构：论点陈述→案例支撑→深入分析→个人见解→自然过渡
3. 展开特点：
   - 各分论点之间要有内在联系，避免完全割裂
   - 设置1-2处转折或辩证思考，如"然而，我们也要看到..."
   - 适当表达对某些观点的情感倾向
   - 案例之间要有联系，避免完全无关联的案例拼贴
4. 结尾总结(15%篇幅)：
   - 回应开头提出的问题或现象
   - 在更高层次上整合前面的分论点
   - 提出更深层次的思考或行动建议
   - 可以设置一个思考性问题，让读者带着思考离开

### 分论点展开的变化技巧
- 第一个分论点：可以从共识或基础认知出发，相对简单明了
- 中间分论点：深入分析，可包含一些争议性观点或辩证思考
- 最后一个分论点：可以是最有洞察力的部分，或对前面论点的升华
- 过渡方式多样化：问题引导、情景转换、对比联系等，避免程式化

### 总分总结构的深度提升技巧
- 在分论点之间设置内在联系，而非简单并列
- 每个分论点都有明确的论证路径，不是单纯的观点陈述
- 通过真实案例支撑观点，案例有细节和温度
- 在各分论点中融入个人观察和体验，增强真实感
- 结尾不只重复前面内容，而是升华思考层次
"""
        elif "对比分析" in article_type:
            type_guide = """
## 对比分析类文章写作指南
1. 鲜明的对比主题 - 明确定义要对比的两个或多个对象，确保它们有可比性
2. 个人视角切入 - 从个人视角或经历引入对比话题，增强代入感和真实性
3. 多维度比较 - 设定3-5个关键维度进行系统比较，避免简单化或片面比较
4. 论证有深度 - 对每个维度的比较都有深入分析，不停留在表面现象
5. 案例要生动 - 用具体案例支撑对比论点，案例要有细节和情感温度
6. 对比有平衡 - 认可每个对比对象的优缺点，避免完全偏向一方
7. 表达有态度 - 最终基于对比给出自己的倾向性观点和理由，但承认选择可能因个人需求而异
8. 情感有起伏 - 在对比过程中表达不同情绪，有时赞赏，有时惋惜，有时矛盾
9. 结尾有思考 - 超越简单的"二选一"，提供更深层次的思考或综合方案
10. 视角有转换 - 尝试从不同角度看待对比对象，增加立体感

### 避免AI痕迹的特别技巧
1. 非绝对判断 - 避免绝对性结论，使用"对我而言"、"就我所知"等限定表达
2. 承认信息不足 - 在某些对比点上坦言"这方面我了解有限"，展示信息的有限性
3. 表达偏好变化 - 分享自己对比对象的偏好如何随时间或情境而变化
4. 加入场景感受 - 描述在特定场景中使用或体验对比对象的感受和细节
5. 主观评价明确 - 不掩饰对某方面明显的喜好或不满，表达真实情感

✨ 写作风格提示：避免完全客观中立的百科全书式比较，注入个人体验和情感倾向。使用"我曾经...但后来发现..."等表达，展示认知的变化过程。在重要比较点使用生动的类比或比喻，增强可读性。承认某些判断可能受个人偏好影响，增加真实感。
"""
            structure_guide = """
## 对比分析文章结构安排
1. 开篇设计(15%篇幅)：
   - 以个人经历或疑问引入对比主题
   - 明确定义对比对象及其重要性
   - 简述对比的主要维度和个人关注点
   - 可以分享最初的直觉判断或困惑

2. 对比主体(70%篇幅)：
   - 按3-5个关键维度进行系统对比
   - 每个维度的对比可以有不同结构和长度，不必完全对称
   - 对比格式多样化：可以一个维度一个维度对比，也可以先全面介绍A再介绍B
   - 在对比过程中穿插个人经历、感受和思考变化
   
3. 对比维度设计：
   - 从基础/直观维度开始，逐渐深入到复杂/抽象维度
   - 在某个维度上可以有意制造悬念或转折
   - 适当加入意外发现或对预期的颠覆
   - 允许在某个维度上表达困惑或矛盾感受
   
4. 结论部分(15%篇幅)：
   - 综合前面的对比重点
   - 表达个人倾向和理由，但承认选择可能因人而异
   - 提出超越简单对立的整合思考或使用建议
   - 回应开头的疑问或困惑，展示认知变化
   - 可以提出未来发展趋势的思考

### 对比文章的特别技巧
- 非完美对称：对比不必完全平衡，可以对某方面给予更多关注
- 自我定位：明确说明自己使用场景和需求，解释为何某些维度对你更重要
- 评价多元化：不同维度可以有不同的评价方式，有时用数字，有时用描述性语言
- 故事穿插：在对比过程中穿插使用体验的小故事，增加生动感
- 矛盾允许：允许前后评价存在一定矛盾，展示思考的复杂性
"""
        elif "教程/分析型" in article_type:
            type_guide = """
## 教程/分析型文章写作指南
1. 解决问题开场 - 开篇说明这篇教程能解决什么问题，满足什么需求
2. 步骤详细清晰 - 每个步骤都要足够具体，初学者一看就能操作
3. 步骤原理解析 - 不仅告诉"怎么做"，还要解释"为什么这样做"，帮助读者理解背后的逻辑
4. 多角度展示技术 - 从基础原理、实际应用、常见问题等多角度讲解技术要点
5. 个人经验分享 - 加入你实践时的心得、技巧和踩过的坑
6. 分享实践感悟 - 讲述你使用这些方法时的思考过程和情感体验
7. 生活化比喻 - 用日常生活中熟悉的事物解释复杂概念
8. 预判问题 - 提前解答读者可能遇到的困惑和常见问题
9. 深入分析问题根源 - 不只是提供解决方案，更要帮助读者理解问题产生的原因
10. 鼓励支持 - 在关键步骤后加入鼓励性语言，给读者信心
11. 进阶技巧 - 在基础内容之外，提供一些进阶小技巧
12. 互动邀请 - 鼓励读者分享他们的实践结果或提出疑问

### A避免AI痕迹的特别技巧
1. 分享具体失败经验 - 坦白分享"我第一次尝试时做错了什么"，展示学习过程中的真实挫折
2. 承认方法局限性 - 明确指出"这种方法不适用于什么情况"，避免万能解决方案的假象
3. 添加个人习惯表达 - 使用独特的表达方式或术语解释，如"我习惯称这个步骤为'防火墙'"
4. 描述特定环境影响 - 提及"在我的旧电脑上这一步特别慢"等环境因素，增加真实感
5. 情感反应真实化 - 描述面对复杂问题时的真实感受，如"看到这个错误信息我当时真绝望"

✨ 写作风格提示：教程不必是冷冰冰的指令列表。用轻松友好的语气，就像你在手把手教一个朋友。可以加入幽默元素缓解学习压力。使用"我们"而不是"你应该"，营造共同学习的氛围。承认某些步骤可能有难度，表现出对读者的理解和支持。
"""
            # 教程型文章的结构指南已在前面设置
            structure_guide = """
## 教程/分析型文章结构安排
1. 开篇设计(10-15%篇幅)：
   - 以读者痛点或实际问题开场
   - 明确说明本教程将解决什么问题
   - 分享个人与该问题的经历或故事
   - 简要说明完成该教程需要的准备和预期收获

2. 基础概念讲解(15-20%篇幅)：
   - 确保读者了解必要的背景知识
   - 用生活化比喻解释复杂概念
   - 避免专业术语堆砌，必要时给出通俗解释
   - 分享为什么理解这些概念很重要的个人观点

3. 步骤详解(40-50%篇幅)：
   - 每个步骤都要足够具体，包含操作细节
   - 解释每一步背后的原理和目的
   - 提供可能遇到的问题和解决方法
   - 加入个人经验和技巧，如"我一般会多测试几次这一步"
   - 在关键步骤使用具体场景举例，增强理解

4. 实例演示(15-20%篇幅)：
   - 通过1-2个完整案例展示整个流程
   - 案例应贴近读者实际情况
   - 包含"思考过程"，而非仅展示结果
   - 可以展示一些尝试中的"失败案例"，增加真实感

5. 总结与扩展(10-15%篇幅)：
   - 回顾关键步骤和要点
   - 提供进阶应用的方向和建议
   - 分享个人使用该方法的长期收获
   - 鼓励读者实践，并根据自身需求调整

### 特别实用性增强技巧
- 适当使用提示框标注重要警告或快捷技巧
- 设置"常见错误"部分，帮助读者避坑
- 在步骤之间设置检查点，帮助读者确认是否正确完成
- 提供实际场景下的变通方案，而不仅是理想条件下的操作
- 加入故障排除指南，帮助读者解决可能遇到的问题
"""
    else:
        # 默认使用流畅型结构的指南
        type_guide = """
## 通用文章写作指南
1. 开篇引人入胜 - 用生动的场景、有价值的问题或意外的观点吸引读者
2. 内容有深度 - 不要停留在表面，深入挖掘主题，提供独特视角
3. 多维度剖析主题 - 从历史演变、核心原理、实际应用、未来趋势等多角度分析主题
4. 案例具体详实 - 精选1-2个相关案例深入分析，不要浅尝辄止地罗列多个案例
5. 案例深度解析 - 不只是讲述案例，更要分析案例中的关键要素和内在逻辑
6. 情感要丰富真实 - 在适当位置表达真实情感，包括犹豫、矛盾和不确定性
7. 结构要清晰但不机械 - 虽不明显分段，但内在逻辑需清晰，避免过于完美的结构
8. 语言要亲切自然 - 如同与朋友对话，适当使用口语化表达，避免过于学术化
9. 结尾有深意但留白 - 不必完美解答所有问题，给读者留下思考空间和回味

### 避免AI痕迹的特别技巧
1. 打破完美对称 - 段落长短有明显变化，部分内容可以展开更多，部分可以简略
2. 表达认知变化 - 使用"起初我以为...后来才明白..."等表现思考变化的过程
3. 加入具体场景描述 - 描述特定时间地点的个人经历，增加真实感和代入感 
4. 情感表达有波动 - 对不同内容有不同的情感反应，避免情感曲线过于平滑
5. 保留一些思考悬念 - 不必解决文章中提出的所有问题，可以留下开放性思考

✨ 写作风格提示：想象你正在咖啡厅与一个朋友分享你的思考和经历。用自然流畅的语言，适当加入情感波动和生活化细节，让读者感受到真实的人在说话。不必过度修饰，真实比完美更有吸引力。在关键点使用"说实话"、"坦白讲"等口语表达，增强亲近感。
"""
        structure_guide = """
## 通用文章结构安排
1. 开篇设计(15%篇幅)：
   - 以引人入胜的方式开场（故事、问题或现象）
   - 分享与主题相关的个人经历或困惑
   - 自然过渡到文章核心主题
   - 暗示文章将要展开的方向，但不必过于明确

2. 内容展开(70%篇幅)：
   - 按内在逻辑顺序展开论述，而非简单并列
   - 每个主要观点配以具体案例和深入分析
   - 观点间设置自然过渡，避免机械标记
   - 在展开过程中表达思考变化和情感波动
   - 适当设置1-2处转折或反思点

3. 段落特点：
   - 段落长短有明显变化
   - 重要观点用简短有力的段落表达
   - 复杂分析用较长段落展开
   - 偶尔使用极短段落（1-2句）制造强调效果
   - 段落之间有自然流动，不生硬切换

4. 结尾设计(15%篇幅)：
   - 自然过渡到总结阶段，避免突兀
   - 提炼文章核心观点，但不机械重复
   - 分享个人最终思考或感悟
   - 与开头形成某种呼应
   - 提出一个值得继续思考的问题，保留一定余味

### 写作细节建议
- 加入1-2处感官描写，让读者身临其境
- 设置2-3个情感波动点，避免情绪线过于平滑
- 适当使用反问或设问，增加与读者的互动感
- 在关键点分享个人观点或态度，展示真实性
- 承认某些问题的复杂性，避免过于简单化
"""
    
    # 构建完整的提示
    # Markdown格式的提示内容
    prompt = f"""作为一名拥有20年写作经验的专业撰稿人，请按照以下指南为主题"{topic}"创作一篇内容丰富、结构合理、感情真挚的高质量文章。

## 基本信息
主题: {topic}
关键词: {', '.join(keywords) if keywords else ''}
文章类型: {article_type if article_type else '请根据主题特点选择最合适的类型'}
写作风格: {writing_style if writing_style else '真诚自然、情感丰富、深度思考'}
语调: {tone if tone else '真实亲切、有思考深度'}
目标受众: {target_audience if target_audience else '对该主题有兴趣的普通读者'}
{f"当前时间: {current_time['formatted_date']} {current_time['formatted_time']} {current_time['day_of_week_cn']}" if current_time else ''}
{f"当前季节: {current_time['season']}" if current_time and 'season' in current_time else ''}

{general_writing_guide}

{anti_ai_guide}

{type_guide}

{structure_guide}

{outline_text}

## <思考>搜索内容分析
请先对搜索内容进行深入思考和分析：
1. 哪些信息与主题和大纲最相关？哪些可以直接采用？
2. 搜索内容中是否有矛盾观点？如何辩证处理这些不同观点？
3. 有哪些事实、数据或案例可以支持论点？
4. 搜索内容中有哪些独特视角或深刻洞见？
5. 哪些内容可能是过时的或不太相关的？应当舍弃哪些内容？
6. 如何根据当前时间背景对内容进行适当调整？
7. 如何将这些信息与我的个人观点和经验结合？

我应该以批判性思维审视搜索内容，既不盲目采信也不全盘否定，而是提取有价值的信息融入我的写作中。
</思考>

# 搜索内容
{search_content_str}

{cases_examples_text}

{quotations_text}

## <思考>写作规划
在开始写作前，我需要：
1. 确定清晰的主线和论点发展路径
2. 设计引人入胜的开头和有力的结尾
3. 规划情感起伏点，避免平铺直叙
4. 安排故事、案例或数据在何处插入
5. 考虑如何自然地融入关键词，而非生硬堆砌
6. 设计1-2处思考跳跃点，让文章更有真实感
7. 注意时间背景与当前时间({current_time['formatted_date'] if current_time else '当前日期'})的一致性

我将确保内容既有深度又有共鸣，既有逻辑分析又有情感温度。
</思考>

# 个性化和反ai味
{personal_elements_text}

{anti_ai_touches_text}

## 输出格式
{output_format}

## 重要提醒
1. 确保内容原创、真实、有深度，避免空洞说教和表面总结。最后文章字数4000字左右
2. 文章必须具有强烈的人文气息和个人风格，避免机械化和公式化
3. 每个段落至少包含3-5个句子，观点层层递进，保持逻辑连贯
4. 适当加入个人经历、观点和情感，增强真实感和亲近感
5. 文中必须自然融入所有关键词，不可生硬堆砌
6. 适度使用生动的比喻、贴切的引用和鲜活的案例，增强表现力
7. 注重细节描写和场景再现，让读者身临其境
8. 结尾要有深度思考或情感升华，给读者留下深刻印象
9. 全文语言表达要自然流畅，避免过于正式或学术化的表达
10. 确保有自己独特的见解和思考，而不仅仅是已有观点的复述
11. 文章具体真实事件时间背景要和搜索内容的事件最新的时间背景保持一致，也就是全篇文章的写作时间背景要符合{current_time['formatted_date'] if current_time else '当前日期'}的时间背景
"""
    return prompt


# ============== FinalizingAgent提示词 ==============

def finalizing_edit_prompt(draft_content: str,
                           article_type: str = None,
                           edit_focus: str = None,
                           current_time: Dict[str, Any] = None) -> str:
    """
    最终润色文章的提示词
    
    Args:
        draft_content: 文章草稿内容
        article_type: 文章类型
        edit_focus: 编辑重点，可以是"情感共鸣"、"结构流畅"、"语言生动"等
        current_time: 当前时间信息，包含年、月、日等
        
    Returns:
        格式化的提示词
    """
    
    # 添加时间信息
    time_info = ""
    if current_time and isinstance(current_time, dict):
        year = current_time.get("year", "")
        month = current_time.get("month", "")
        day = current_time.get("day", "")
        season = current_time.get("season", "")
        
        time_parts = []
        if year:
            time_parts.append(f"当前年份: {year}")
        if month:
            time_parts.append(f"当前月份: {month}")
        if day:
            time_parts.append(f"当前日期: {day}")
        if season:
            time_parts.append(f"当前季节: {season}")
        
        if time_parts:
            time_info = "时间背景:\n" + "\n".join(time_parts) + "\n\n"
    
    # 处理编辑重点
    edit_focus_guide = ""
    if edit_focus:
        if "情感" in edit_focus or "共鸣" in edit_focus:
            edit_focus_guide = """
【情感共鸣增强指南】
1. 从个人视角真实分享感受，如"昨晚我躺在床上时，突然想起那件往事，鼻子一酸..."
2. 加入微妙复杂的情绪矛盾，如"我既害怕失败，又隐隐期待挑战"
3. 描述身体的微小反应，如"听到这个消息，我的手指不自觉地攥紧了咖啡杯"
4. 融入真实生活场景中的小失误和不完美，如"我手忙脚乱地翻找钱包，差点把咖啡打翻"
5. 插入不完美但真实的对话片段，包含停顿、重复和口头禅，如"就是...怎么说呢...那种感觉很难形容"
6. 设置情感转折点，但避免过于戏剧化的情感波动，保持自然起伏
"""
        elif "结构" in edit_focus or "流畅" in edit_focus:
            edit_focus_guide = """
【结构流畅优化指南】
1. 避免过于完美的结构和明显的框架痕迹，适当保留一些思维的跳跃性
2. 用生活化的事件或环境变化作为自然过渡，如"窗外的雨停了，我的思绪也慢慢清晰起来"
3. 打破线性叙事，适当使用插叙和倒叙，更符合人类思维方式
4. 减少"首先、其次、最后"等机械过渡词，用意象或情绪连接段落
5. 保留一些看似随意但自然的跑题和联想，后面再自然地拉回主题
6. 段落长度有变化，避免过于规则的段落分布，符合自然书写习惯
"""
        elif "语言" in edit_focus or "生动" in edit_focus:
            edit_focus_guide = """
【语言生动提升指南】
1. 增加口语化和地域特色表达，如"老话说得好：宁可信其有..."或"在我们老家，这叫做..."
2. 适度使用不完整句和语气词，如"说实话，真没想到会这样。算了，过去的事就让它过去吧"
3. 加入个人特色的口头禅或思考方式，保持全文的语言风格一致性
4. 使用感官细节描述，让读者仿佛置身其中，如"咖啡的苦涩和木质香气在舌尖交织"
5. 运用具体的时间和地点锚点，如"去年深秋的那个周二下午"而非模糊的"前段时间"
6. 在描述专业概念时加入生活化比喻，如"这个算法就像我奶奶织毛衣的针法"
"""
        elif "深度" in edit_focus or "价值" in edit_focus:
            edit_focus_guide = """
【内容深度提升指南】
1. 加入个人对议题的真实困惑和疑问，表达思考过程中的不确定性
2. 分享一个深入的个人经历或案例，而非多个浅层案例的拼凑
3. 避免完美的论证和绝对结论，保留一些开放性思考和未解问题
4. 展现对立观点之间的真实张力，避免刻意平衡的假中立
5. 分享具体的"犯错经历"和从中获得的洞见，增加真实性和说服力
6. 在结尾提出一个真正有深度的问题，而非完美的解决方案
"""
    
    # 反AI特征指南 - 新增
    anti_ai_traits_guide = """
## 反AI特征指南

人工智能生成的文本通常有以下特征，请刻意避免：

1. 【情感彩虹光谱】 - AI文本常按固定模式分配情绪（如悲伤→希望→震惊→感动），形成"情感全覆盖"
   * 避免策略：情感波动应源于具体事件，而非刻意安排；保持主要情绪基调，添加微妙变化而非剧烈转换
   * 正确示例："我对这件事的愤怒，直到现在提起来还是没完全消退，虽然我知道应该放下了"

2. 【案例拼贴效应】 - AI文本常堆砌不同年龄、职业、背景的案例，形成"完美样本分布"
   * 避免策略：聚焦1-2个深入、具体的案例或经历，提供丰富细节；避免"代表性案例"堆砌
   * 正确示例："我同学王磊的经历让我印象深刻，他32岁辞职创业那年..."（而非"从60岁老人到90后年轻人都有类似经历"）

3. 【百科全书式堆砌】 - AI文本常引用过多研究机构、专家观点和精确数据
   * 避免策略：减少权威引用数量，增加个人视角；数据使用应有误差和估计感
   * 正确示例："根据我看到的一项研究，大约三成的人有这种倾向"（而非"哈佛大学2023年研究显示，32.7%的人..."）

4. 【逻辑完美曲线】 - AI文本常呈现完美的事件发展或论证过程，缺乏真实的波折
   * 避免策略：加入意外、失误和计划外的转折；保留一些"没道理但确实发生了"的元素
   * 正确示例："原本以为这样做会成功，结果完全相反，反而让事情更复杂了"

5. 【隐喻密度异常】 - AI文本常在短段落内使用过多不同维度的比喻
   * 避免策略：选择1-2个核心隐喻并深入发展；避免"比喻轰炸"
   * 正确示例：选择"登山"作为核心隐喻，然后围绕"陡坡"、"氧气稀薄"、"山顶风景"等相关意象展开

6. 【时间感知混乱】 - AI文本常混合使用不同时代背景的案例和技术
   * 避免策略：建立清晰的时间锚点；确保案例与所处时代的技术和社会背景一致
   * 正确示例："2015年智能手机刚普及那会儿"（而非模糊的"近年来"）
"""
    
    # 思考：第一人称转化和人性化策略
    first_person_guide = """
<思考>
## 自媒体博主第一人称策略

### 1. 身份定位：专业内容观察者和价值传递者
* 主要角色：信息整合者、观察分析者、价值解读者（而非单纯的个人经历分享者）
* 核心使命：为读者提供有价值的信息、观点和启发
* 叙述视角：以"我了解到"、"我观察到"、"我研究发现"为主，个人经历为辅

### 2. 内容价值导向的第一人称使用策略：
* 【信息传递】优先用"我了解到"、"据我观察"引入有价值的信息和观点
* 【专业解读】用"以我的理解"、"在我看来"对复杂信息进行解读和分析
* 【适度个人化】仅在能增强内容价值时分享个人经历，如"我曾经遇到过类似情况"
* 【引用权威】用"我了解到专家认为"、"我接触过的业内人士表示"增加可信度
* 【避免流水账】杜绝无价值的个人日常（如"我昨天去哪里"、"我今天看到什么"）

### 3. 案例与观点的平衡使用：
* 【控制案例密度】全文最多2-3个核心案例，每个案例后必须有深度分析
* 【真实性优先】优先使用公开可查的真实案例和人物，避免虚构身边人
* 【价值服务】每个案例都必须为核心观点服务，而非为了增加故事性
* 【观点主导】确保个人观点和分析占文章主体，案例只是支撑

### 4. 专业性与人情味的平衡：
* 在保持专业可信的同时，适当展现思考过程和困惑
* 承认认知局限："这方面我了解有限，但基于现有信息..."
* 展现学习态度："通过深入了解，我发现..."
* 避免装腔作势，保持谦逊和诚实的态度
</思考>
"""
    
    # 思考：共鸣与价值增强策略
    value_guide = """
<思考>
## 增强读者共鸣和文章价值思考
1. 如何预判读者可能有的疑惑和反对意见？
   - 读者对哪些观点最容易产生质疑？
   - 我如何提前回应这些潜在质疑？
   - 哪些论点需要更充分的论证或案例支持？

2. 文章哪些部分最能引发读者共鸣和情感共识？
   - 哪些普遍经历或情感是大多数读者能认同的？
   - 如何将这些共鸣点与文章主题自然连接？
   - 如何在保持专业性的同时增加情感连接？

3. 如何将专业知识转化为实用建议和可行动的步骤？
   - 读者可以如何将所学知识应用到日常生活中？
   - 哪些复杂概念需要通过实例或类比简化？
   - 如何给予具体可行的建议而非空洞理论？

4. 如何通过展现自己的脆弱性和成长过程，让读者认同自己？
   - 我可以分享哪些失败或挫折经历？
   - 如何展现自己的学习和成长历程？
   - 如何通过自己的真实经历增强文章说服力？

5. 如何在结论部分留下开放性思考，鼓励读者继续探索？
   - 哪些问题值得读者进一步思考？
   - 如何避免简单化结论而保持问题的复杂性？
   - 如何平衡给出见解与留空间给读者思考之间的关系？

6. 如何对复杂问题保持诚实的不确定性，而不是假装有完美答案？
   - 哪些领域我需要承认认知局限？
   - 如何坦诚地表达"我不确定"但不减损文章权威性？
   - 如何区分"确定的事实"和"个人观点/猜测"？

7. 如何平衡教育价值与娱乐价值，让文章既有深度又有趣味？
   - 如何在严肃话题中加入轻松元素？
   - 如何使用幽默但不失专业和尊重？
   - 如何避免过度娱乐化而失去深度？
</思考>
"""

    # 内容保持与丰富指南 - 更新
    content_preservation_guide = """
## 内容保持与丰富指南

请重点注意以下事项：
1. 【原文信息保护增强】原文的核心数据、观点、案例必须完整保留，个人化改写是增强表达而非替代内容，增强原文内容
2. 【价值优先原则】作为自媒体博主，确保每段内容都有明确价值输出（信息/知识/情感/实用），避免纯粹的个人流水账
3. 【真实可信平衡】在增加人情味的同时，避免编造过多不存在的身边人物，优先使用真实经历和公开信息
4. 【神似优先原则】重点体现中国人的思维方式和经历背景，而非刻意堆砌本土元素；让读者自然感受到"这是一个在中国生活的人的观察和思考"
5. 【保留全部内容】不要删减原文的关键观点和论证，但可以调整表达方式和组织结构
6. 【打破完美结构】适当打乱过于工整的结构，增加一些思维跳跃和关联，更像人类自然思考
7. 【个人化改写】加入"我"的视角和真实感受，用第一人称讲述部分内容，增加亲近感，但是对于具体的重要的真实事件和观点以及大家公认的事实，适当保留原来主人公的名字并且转述内容）
8. 【不完美案例】保持案例完整性，但添加一些小失败、意外和挫折，避免过于完美的成功故事
9. 【思考过程外显】展示思考的不确定性和转折，如"一开始我以为...但后来发现..."
10. 【真实生活细节】加入日常生活中的小细节、不便和妥协，增加真实感
11. 【主观立场保留】保持适度的偏见和倾向性，避免过度中立和平衡
12. 【语言多样化】使用不同长度的句子，混合正式与口语表达，偶尔使用方言或流行语
13. 【感官体验丰富】加入声音、气味、触感等感官细节，让内容更有沉浸感
"""
    
    # 根据文章类型调整润色重点
    article_type_guide = ""
    if article_type:
        # 确保article_type是字符串
        article_type = str(article_type).lower()
        
        if "观点" in article_type or "议论" in article_type or "案例" in article_type or "金句" in article_type:
            article_type_guide = """
【观点/议论型文章润色重点】
1. 避免"客观理性"的假面具，展示真实的情感投入和态度
2. 观点可以有偏向性，不必刻意平衡或中立，人类本来就有偏见
3. 加入个人经历作为观点源起，如"去年我经历的一件事彻底改变了我的看法..."
4. 案例应有灰度和复杂性，避免非黑即白的简单案例
5. 承认观点的局限性，如"这种方法或许不适用于所有情况，尤其是..."
6. 适当保留一些逻辑跳跃，展现思考过程中的直觉和灵感
7. 在阐述重要观点前可以有犹豫和反思，如"我反复思考这个问题，甚至一度改变主意..."
8. 金句应源于个人体悟，带有独特视角，而非大众共识的改写
"""
        elif "故事" in article_type or "叙事" in article_type or "感触" in article_type or "干货" in article_type:
            article_type_guide = """
【故事/叙事型文章润色重点】
1. 打破完美的情节发展，加入意外、失误和不在预期中的转折
2. 人物应有缺点和矛盾心理，避免完美无缺的角色塑造
3. 从特定角色视角讲述故事，带入其独特感受和认知局限
4. 对话应包含犹豫、重复和口头禅，如"那个...就是那个...你知道我的意思吧？"
5. 描述场景时加入感官细节和微妙氛围，如"咖啡厅里弥漫着一股微妙的焦虑，或许是因为窗外的阴云"
6. 时间线可以有跳跃和闪回，符合人类记忆的自然流动
7. 情感反应应有生理表现，如"听到这个消息，我的胃猛地一沉"
8. 结尾可以有开放性和未解决的问题，避免过于完美的收尾
"""
        elif "总分总" in article_type:
            article_type_guide = """
【总分总结构文章润色重点】
1. "总"部分应有个人视角和独特切入点，而非通用开场白
2. "分"部分的论点可以有主次和偏重，不必均匀分布篇幅
3. 过渡应自然流畅，避免机械的"第一点...第二点..."
4. 各分论点间可有思维跳跃和关联，展现思考的发散性
5. 论证过程中展示一些思考的曲折，如"这一点我曾有过不同看法..."
6. 结尾的"总"部分应有新的思考深度，而非简单重复开头
7. 适当加入个人困惑和未解答的问题，避免给出完美答案
8. 使用类比和生活化比喻解释抽象概念，增加亲近感
"""
        elif "分析" in article_type or "教程" in article_type:
            article_type_guide = """
【分析/教程型文章润色重点】
1. 以个人经验和尝试失败开始，如"我第一次尝试这个方法时，把整个系统搞崩溃了..."
2. 承认方法的局限性和适用条件，避免"万能解决方案"的口吻
3. 加入使用过程中可能遇到的常见问题和解决思路
4. 技术分析中融入生活化比喻，帮助理解复杂概念
5. 步骤指导应包含常见错误提醒和个人经验技巧
6. 结论部分应包含个人使用感受和主观评价
7. 适当使用口语化表达和行业术语，展现专业背景
8. 加入一些意料之外但实用的小技巧，如"这个小窍门是我无意中发现的..."
"""
        elif "对比" in article_type:
            article_type_guide = """
【对比分析型文章润色重点】
1. 明确表达个人偏好和立场，不需要做出完全中立的假象
2. 比较维度可以有主次和偏重，不必刻意平衡各点篇幅
3. 举例时用亲身经历或真实案例，添加细节和情境
4. 承认自己的认知局限，如"对于A的技术细节我了解有限，但就用户体验而言..."
5. 展现认知过程中的变化，如"最初我更偏向A，但使用一段时间后发现B其实更适合我"
6. 结论可以有条件性和个性化，避免放之四海而皆准的评价
7. 加入一些出乎意料的对比维度，反映个人独特关注点
8. 适当使用户外语和类比解释专业概念
"""
    
    # 根据文章类型设置输出格式指南
    output_format_guide = ""
    if article_type:
        if ("教程/分析型" in article_type) or ("总分总" in article_type):
            # 教程/分析型， 总分总文章使用章节式结构
            output_format_guide = """
## 输出格式
请严格按照以下结构输出润色后的文章：

{
  "title": "有情感色彩的标题，可以是问句或包含个人态度",
  "introduction": "以第一人称讲述者角度开头的引人入胜介绍，以'我听说...'或'最近我了解到...'等方式自然引入话题，可融入当前时间背景，建立叙述者身份，至少300字",
  "sections": [
    {
      "title": "第一章节标题，可以是问句或有情感色彩",
      "content": "以第一人称讲述者角度撰写，区分'讲述他人故事'、'转述专业观点'和'分享个人感受'三种模式，同时自然融入时间背景信息，每章节至少800字"
    },
    {
      "title": "第二章节标题，可以与上章节形成呼应或转折",
      "content": "继续以第一人称讲述者角度，注意使用时间锚点增强叙事真实感，保持叙述者身份的一致性"
    }
  ],
  "conclusion": "作为讲述者总结要点，包含对所讲内容的个人感悟和思考，可结合当前时间背景提出对未来的展望，以开放式问题结尾邀请读者参与讨论"
}
"""
        else:
            # 其他文章类型使用流畅型结构
            output_format_guide = """
## 输出格式
请严格按照以下结构输出润色后的文章：

{
  "title": "有情感张力和个人色彩的标题",
  "content": "以第一人称讲述者角度撰写的完整文章内容，使用自然流畅的段落结构，不分章节。清晰区分'讲述他人故事'、'转述专业观点'和'分享个人感受'三种模式，适当融入当前的时间、季节等信息增强时效性和真实感。文章应像一个专业内容分享者在讲述自己了解和观察到的故事与见解，同时保持对时间背景的自然呼应。总内容至少在4000字以上。"
}
"""
    else:
        # 默认使用流畅型结构
        output_format_guide = """
## 输出格式
请严格按照以下结构输出润色后的文章：

{
  "title": "有情感张力和个人色彩的标题",
  "content": "以第一人称讲述者角度撰写的完整文章内容，使用自然流畅的段落结构，不分章节。清晰区分'讲述他人故事'、'转述专业观点'和'分享个人感受'三种模式，适当融入当前的时间、季节等信息增强时效性和真实感。文章应像一个专业内容分享者在讲述自己了解和观察到的故事与见解，同时保持对时间背景的自然呼应。总内容至少在4000字以上。"
}
"""

    # 增加真实叙事角度指南 - 新增
    narrative_perspective_guide = """
## 真实叙事角度指南

在创作叙事性内容时，不同角度的选择对人性化表达至关重要：

### 第一人称叙述
1. 主角视角 - 以"我"的身份直接讲述：
   * 加入感官体验：不只是"我看到了风景"，而是"风穿过树叶的沙沙声让我想起小时候老家的夏天"
   * 展示内心独白：加入犹豫、自我质疑、突发奇想的念头
   * 表现认知局限：承认"当时我并不理解他的选择，直到多年后才恍然大悟"
   * 使用个人习惯语：每个人说话有固定习惯，确保全文一致性

2. 旁观者视角 - 以"我"作为故事见证者：
   * 加入个人评论："说实话，看到李明那样选择，我心里其实挺失望的"
   * 表达困惑和猜测："我至今不明白他为什么那样做，或许是因为..."
   * 承认观察死角："那天晚上发生的事，我只听到了一部分，剩下的是我猜的"

### 第二人称叙述
1. 以"你"作为叙述者：
   * 使用"你"作为叙述者，直接与读者对话
   * 加入个人评论："说实话，看到李明那样选择，我心里其实挺失望的"
   * 表达困惑和猜测："我至今不明白他为什么那样做，或许是因为..."
   * 承认观察死角："那天晚上发生的事，我只听到了一部分，剩下的是我猜的"

### 第三人称叙述
1. 受限视角 - 聚焦特定角色感受：
   * 深入特定角色心理："张伟摩挲着杯沿，脑海中闪过母亲的叮嘱，但他已决定不再听从"
   * 使用角色特有表达："'算啦算啦'，王阿姨摆摆手，这是她四十年来解决问题的万能句式"
   * 保持视角一致性：不要随意切换被聚焦的角色

2. 全知视角 - 适度克制的上帝视角：
   * 有选择地展示信息："他们谁都没注意到角落里的那个信封，而那正是整个故事的转折点"
   * 保留一些悬念和未知："关于那天的决定，李明表面云淡风轻，内心却翻江倒海，只是没人知道具体原因"
   * 避免过度全知：保留一些无法解释的行为和动机

### 关键技巧
1. 视角限制原则 - 真实人类写作不会无所不知：
   * 正确："据小区保安老刘回忆，那天张先生确实神色慌张地出门了"
   * 错误："张先生内心充满愧疚，王先生对此一无所知，而李先生则早有察觉"（过度全知）

2. 情感真实性原则 - 情感表达应有具体来源：
   * 正确："看到那封信的第一行，她的手开始发抖，咖啡撒了一桌子"
   * 错误："她感到无比震惊、痛苦、愤怒和绝望"（情感堆砌无具体触发）

3. 视角衔接自然 - 视角切换应有明确标记：
   * 正确："这是李芳的想法。而在城市另一端，张明正有着完全不同的打算。"
   * 错误：不加过渡突然从一个人物跳到另一个人物
"""
    
    # 人类写作特征指南 - 新增
    human_writing_traits_guide = """
## 人类写作特征指南

### 1. 思维跳跃与联想
* 加入偶尔离题的自然联想，如"这让我想起小时候..."
* 有时突然回到前面提过的话题："对了，刚才说到那个问题..."
* 在正式分析中插入突发的个人感受："说实话，每次看到这种数据我都挺沮丧"

### 2. 语言的不完美性
* 使用不完全定义的模糊表达："某种程度上"、"差不多"、"感觉像是..."
* 偶尔使用口语化的夸张表达："简直要累死我了"、"绝对是史上最难的决定"
* 承认表达困难："这个很难用语言描述清楚，有点像..."
* 适当重复强调关键点，而非完全避免重复

### 3. 场景与细节描写
1. 感官描写：通过视觉、听觉、触觉、嗅觉、味觉的细节描写唤起读者感官记忆
2. 微表情捕捉：描述细微的表情变化，如"嘴角微微上扬"、"眉头不自觉地皱起"
3. 环境氛围营造：通过光影、温度、声音等描述创造情绪氛围
4. 细节铺陈：用小细节反映大情感，如"他的眼镜上有一道浅浅的划痕"
5. 动作与反应：通过自然的身体反应展示情感，如"我不自觉地攥紧了拳头"

### 4. 情感表达的真实性
* 情感有具体来源而非突然出现："那个结果出来后，我坐在电脑前发了很久的呆"
* 情感的渐进变化："开始只是觉得不对劲，后来越想越觉得有问题"
* 复杂矛盾的情感："心里明白这是对的，但就是接受不了"
* 通过行为暗示情感："我发现自己在无意识地避开那个话题"
* 避免过度戏剧化：真实的情感往往是细微而持续的

### 5. 经历表达的可信度
* 用细节体现真实经历："我记得那天的天气，因为..."
* 承认记忆的不完美："具体时间记不太清了，但大概是..."
* 展现当时的认知局限："那时候我还不懂这些，以为..."
* 包含失误和尴尬："结果我理解错了意思，闹了个笑话"
* 避免过于完美的回忆：真实的经历都有模糊和不确定的部分
"""
    
    # 新增中国本土视角指南
    china_perspective_guide = """
## 中国本土化表达指南（神似优先）

重点不是刻意添加中国元素，而是自然体现中国人的思维方式和经历背景：

### 1. 【经历可信度】- 核心原则
* 表达时自然流露出"我在这个环境下生活过"的感觉，表现我自己拥有深厚的相关背景
* 避免刻意的标签化描述，用细节体现真实经历
* 展现对中国社会现象的深度理解，而非表面观察

### 2. 【思维方式的自然体现】
* 中国人特有的含蓄表达："不太好说"、"多少有点"、"怎么说呢"
* 集体主义思维：更多用"我们"而非"我"，关注社会影响
* 关系导向思维：考虑人情、面子、关系平衡等因素
* 经验主义倾向：更倾向于用经历验证道理，而非纯理论

### 3. 【自然的生活细节】
* 适当具体中国元素，而是通过生活方式体现背景
* 时间观念：提到工作节奏、生活压力时的中国特色理解
* 社会认知：对教育、工作、家庭关系的理解方式
* 价值判断：体现中国人的价值观和优先级排序

### 4. 【语言习惯的内化】
* 自然的停顿和转折："这个嘛..."、"说起来也是"
* 委婉的表达方式：避免过于直接的批评或赞美
* 情感的克制表达：不会过于外露，但有真实的内在情感
* 经验分享的口吻：更像在和朋友聊天分享，而非正式发言

### 5. 【避免的刻意化表现】
* 不要故意堆砌"共享单车"、"微信支付"等标志性元素，需要提及的时候自然表达就好了
* 不要使用过时或生硬的网络流行语， 要符合当下的潮流
* 不要刻意对比"中国vs外国"来强调本土身份
* 不要为了本土化而本土化，一切以自然真实为准

关键是让读者感觉"这就是一个在中国生活的人在分享他的观察和思考"，而不是"这个人在努力证明自己是中国人"。
"""
    
    prompt = f"""请对以下文章进行人性化/自媒体博主化润色， 不要减少原文内容/改变原文含义，合理增强表达，使其更加自然、真实，像真正的人类自媒体博主创作的作品，字数应该在4000-6000字以上。

## 需要润色的文章内容
{draft_content}

<思考>
## 润色前整体思考
核心思考 ： 原文哪里需要转换为自媒体博主化表达， 哪里需要增强表达， 哪里需要保持原文内容，哪里需要去除ai味


1. 自媒体博主第一人称叙述策略选择：
   - 我应该以什么身份/角色作为第一人称叙述者？(研究者？亲历者？分享者等)
   - 对于文章中的不同部分，我应该如何切换"讲述他人故事"、"转述专业观点"和"分享个人感受"？
   - 如何确保叙述者身份在全文保持一致性和可信度？

2. 内容真实性与时效性增强：
   - 文章中哪些观点或事实需要与当前时间背景相关联？
   - 如何通过加入具体的时间信息增强文章的真实感和时效性？
   - 如何避免文章感觉像是"任何时候都可能写的"而缺乏时间锚点？

3. 人物观点与案例规划：
   - 哪些核心观点适合通过引用真实人物来支持？
   - 不同类型人物如何搭配使用（学者、企业家、艺术家、普通人等）？
   - 如何确保每个引用和案例都服务于我的核心观点，而非简单罗列？
   - 如何在引用观点后自然过渡到自己的思考和分析？
   - 如何避免过多引用导致文章缺乏个人色彩和原创性？
4. 文章主题与时间背景的关联性：
   - 今天是{current_time.get('year', '')}年{current_time.get('month', '')}月{current_time.get('day', '')}日，当前季节是{current_time.get('season', '')}。
   - 文章内容与当前时间有什么自然的连接点？
   - 有哪些时事热点或季节特点可以自然融入？

</思考>


## 第一人称本土化视角要求
{first_person_guide}

{china_perspective_guide}

## 去ai味润色指南
{anti_ai_traits_guide}

{narrative_perspective_guide}

{human_writing_traits_guide}


## 润色重点方向
{value_guide}

{article_type_guide}

{edit_focus_guide}

{content_preservation_guide}







    

## 核心润色要求
1. 自媒体博主价值导向策略：
   - 【价值优先】每段内容必须有明确的信息、知识或启发价值，避免无意义的个人流水账
   - 【原文保护】完整保留原文的核心数据、观点、研究结果，个人化表达仅为增强效果
   - 【专业可信】保持作为内容创作者的专业性，避免过度娱乐化或情绪化
   - 【读者导向】始终考虑"读者能从这里获得什么价值？"，如无明确价值则重新组织

2. 第一人称叙述策略（适度使用）：
   - 优先使用"我了解到"、"据我观察"、"我研究发现"等信息导向表达
   - 适度使用"我读过的"、"我接触过"等专业性表达增加可信度
   - 谨慎使用个人经历，仅在能为主题提供价值时使用
   - 避免"我昨天看到"、"我路过时发现"等无价值的个人日常描述

3. 案例使用规范：
   - 全文限制使用1-2个核心案例，必须高度相关且有深度
   - 优先使用公开可查的真实案例和人物，避免虚构身边人
   - 每个案例后必须有至少2段的深度分析和观点阐述
   - 确保案例服务于核心观点，而非为增加故事性

4. 语言真实化：
   - 混合使用长短句，偶尔使用不完整句和口语化表达
   - 适当展现思考的不确定性："这方面我了解有限，但..."
   - 承认认知局限，避免装腔作势的专家口吻
   - 使用生动的比喻和感官细节，但避免过度修饰

5. 人性化表达（真实的人的特质）：
   - 【感官体验】适当加入视觉、听觉、触觉等感官细节："那种闷热让人心烦意乱"
   - 【情感倾向】表现个人喜好和偏见："说实话，我对这种做法一直有些反感"
   - 【情绪波动】展现真实的情绪变化："听到这个消息时，我心里咯噔一下"
   - 【社会关系】体现在社会中的位置和关系："作为一个普通的上班族"、"和大多数同龄人一样"
   - 【生活状态】反映当下的生活状态和处境："最近工作比较忙，但还是忍不住关注这个话题"
   - 【价值冲突】展现内心的矛盾和纠结："理智告诉我应该这样，但情感上很难接受"

6. 结构自然化：
   - 打破过于工整的段落结构，增加思维跳跃和关联
   - 时间信息的自然融入，增强时效性和真实感
   - 适当保留逻辑跳跃，展现人类思考的自然性
   - 情感变化应有明确触发事件，避免机械轮换

7. 内容质量保证：
   - 确保文章总字数在4000-6000字以上
   - 避免AI常见特征：情感彩虹光谱、案例拼贴、百科全书式堆砌等
   - 保持适度的主观性和偏向性，不必完全中立
   - 结尾留下开放性思考，鼓励读者参与讨论

   

<思考>
## 润色完成前的最终思考

0. 自媒体博主价值传递检查：
   - 每段内容是否都有明确的信息、知识或情感价值输出？
   - 原文的核心数据、观点、研究结果是否完整保留并正确表达？
   - 个人化改写是否真正增强表达效果，而非削弱原有内容？
   - 作为内容创作者的专业性和可信度是否得到体现？
   - 真实经历分享是否与主题高度相关，避免为故事性而偏离核心？
   - 是否充分考虑了目标读者的信息需求和认知水平？
   - 文章是否避免了纯粹的个人流水账，真正传递了价值？

1. 叙述者角色一致性检查：
   - 我选择了什么样的第一人称叙述者身份？这个身份在全文中是否一致？
   - 叙述者的知识背景、经历和语言风格是否在各个部分保持一致？
   - 是否有些段落的表达方式或视角与选定的叙述者身份不符？
   - 各种转述他人观点的表达是否符合叙述者身份的认知能力和信息渠道？

2. 时间信息一致性检查：
   - 文章中提到的各种时间信息是否相互矛盾？
   - 对过去事件的描述与当前时间背景是否自洽？
   - 是否有足够的时间锚点让读者感知文章的时效性？
   - 叙述中的季节、气候、节日等时间标记是否与文章整体时间背景一致？

3. 内容真实性与可信度检查：
   - 第一人称叙述中对他人观点的转述是否合理可信？
   - 叙述中的个人经历是否自然、有细节、易于相信？
   - 专业观点的引用是否符合叙述者的身份和渠道？
   - 文中是否有过于完美的叙述结构或不自然的情感发展？

4. 引用与案例平衡检查：
    -是否没有目的的虚构了过多朋友，同学或者亲人的案例
   - 引用的人物是可信，是否有相关真实名字人物的故事或者观点（比如: 专家学者、企业家、文化人物、普通人等）？
   - 每个引用和案例是否都服务于文章的核心观点和思考？
   - 是否在每个引用后都有自己的见解和思考，避免简单罗列？
   - 引用和个人观点之间的比例是否合适，保持文章的原创性？
   - 是否避免了过度使用名人名言而缺乏个人色彩？
   
5. 案例使用频率与布局检查：
   - 全文是否仅在关键处使用了2-3个具体案例，而非每段都有？
   - 每个案例后是否有至少2段的个人观点、思考或分析？
   - 文章是否包含纯观点/分析段落，完全不含案例？
   - 是否避免了虚构案例的使用，或在使用时明确标记为假设？
   - 是否避免了连续使用多个案例而缺乏个人见解的情况？

6. 人性化表达检查：
   - 文章是否有真实的感官体验和生活细节，而非过于抽象？
   - 是否表现出了个人的情感倾向和偏好，避免过度中性？
   - 情绪变化是否自然真实，有具体的触发原因？
   - 是否体现了作者在社会中的真实位置和关系背景？
   - 是否展现了内心的矛盾冲突，避免过于完美的理性？
   - 整体是否让人感觉这是一个有血有肉的真实的人在表达？
</思考>

请确保文章在保留原有全部内容的同时，适当优化增加内容,完全摆脱AI生成的痕迹，使其读起来就像一个生活在中国的真实人物在分享故事思考和感受。特别注意避免AI常见的"情感彩虹光谱"、"案例拼贴效应"、"百科全书式堆砌"、"逻辑完美曲线"和"隐喻密度异常"等特征， 以及摩挲/量子等普通人不经常使用的词语。
全文应该在4000-6000字以上。
{output_format_guide}"""
    
    return prompt

def content_evaluation_prompt(content: str, topic: str, keywords: List[str], outline: List[Dict[str, Any]] = None, evaluation_round: int = 1, current_time: Dict[str, Any] = None) -> str:
    """
    生成内容评估的提示词，用于评估文章内容深度和质量，并查找需要补充的关键内容
    
    Args:
        content: 格式化的文章内容
        topic: 文章主题
        keywords: 关键词列表
        outline: 文章大纲
        evaluation_round: 当前评估轮次
        current_time: 当前时间信息，包含年、月、日等
        
    Returns:
        格式化的提示词
    """
    # 确保content不为空
    if not content or not isinstance(content, str):
        content = str(content) if content else "文章内容为空"
    
    # 确保topic不为空
    if not topic or not isinstance(topic, str):
        topic = str(topic) if topic else "未指定主题"
        
    # 确保keywords是有效的列表
    if not keywords or not isinstance(keywords, list):
        try:
            if isinstance(keywords, str):
                keywords = [keywords]
            elif keywords:
                keywords = list(keywords)
            else:
                keywords = []
        except:
            keywords = []
    
    # 过滤有效关键词并转为字符串
    keywords_str = ", ".join([str(k) for k in keywords if k]) if keywords else ""
    
    # 确保评估轮次有效
    try:
        evaluation_round = int(evaluation_round)
        if evaluation_round < 1:
            evaluation_round = 1
        elif evaluation_round > 3:
            evaluation_round = 3
    except:
        evaluation_round = 1
    
    # 添加时间信息
    time_info = ""
    if current_time and isinstance(current_time, dict):
        year = current_time.get("year", "")
        month = current_time.get("month", "")
        day = current_time.get("day", "")
        season = current_time.get("season", "")
        
        time_parts = []
        if year:
            time_parts.append(f"年份: {year}")
        if month:
            time_parts.append(f"月份: {month}")
        if day:
            time_parts.append(f"日期: {day}")
        if season:
            time_parts.append(f"季节: {season}")
        
        if time_parts:
            time_info = "当前时间: " + ", ".join(time_parts) + "\n"
    
    # 格式化大纲信息（如果有）
    outline_text = ""
    if outline and isinstance(outline, list) and len(outline) > 0:
        try:
            outline_text = "文章大纲:\n"
            for i, section in enumerate(outline):
                if isinstance(section, dict):
                    if "title" in section:
                        title = section.get("title", f"章节{i+1}")
                        outline_text += f"- {title}\n"
                    elif "content" in section:
                        content_desc = section.get("content", "")[:50]
                        outline_text += f"- 段落{i+1}: {content_desc}...\n"
                    
                    # 添加关键点（如果有）
                    key_points = section.get("key_points", [])
                    if key_points and isinstance(key_points, list):
                        for point in key_points:
                            outline_text += f"  * {point}\n"
                elif isinstance(section, str):
                    outline_text += f"- {section}\n"
            
            outline_text = f"\n{outline_text}\n"
        except:
            outline_text = ""
    
    # 预先定义JSON模板部分，避免在f-string中使用复杂结构
    json_template = """```json
{
  "needs_deeper_search": true/false,
  "knowledge_gaps": [
    "最需要补充的理论依据/上下文/故事1",
    "最需要补充的理论依据/上下文/故事2（如有必要）"
  ],
  "search_queries": [
    "针对性搜索查询1",
    "针对性搜索查询2"
  ],
  "gap_importance": [
    {
      "gap": "知识空缺1",
      "type": "理论依据/故事/上下文",
      "reason": "为什么这个补充内容能显著提升文章质量",
      "insertion_point": "应在文章哪个部分插入这一内容"
    }
  ],
  "content_quality_assessment": {
    "depth_score": 1-10,
    "story_quality": 1-10,
    "completeness": 1-10,
    "overall_score": 1-10
  },
  "ai_traits_assessment": {
    "emotional_spectrum_score": 1-10,
    "narrative_authenticity_score": 1-10,
    "case_diversity_score": 1-10,
    "metaphor_coherence_score": 1-10,
    "data_credibility_score": 1-10
  },
  "humanizing_suggestions": [
    "如何让文章更接近人类作者风格的具体建议1",
    "如何让文章更接近人类作者风格的具体建议2"
  ],
  "improvement_suggestion": "对于如何整合补充内容的具体建议"
}
```"""
    return f"""作为一位专业的内容质量与真实性评估专家，请对以下文章进行深度、全面的评估，优先识别和修正常见的AI生成痕迹，同时找出最需要补充的内容，使文章更真实、更深入。

文章主题: {topic}
关键词: {keywords_str}
{time_info}{outline_text}

## 当前文章内容:
---
{content}
---

## 思考：评估文章缺乏深度的关键点
请先深入思考文章当前存在的主要深度缺失点：
1. 这篇文章缺少哪些关键的专业理论支撑？
2. 文章中哪些观点或论述缺乏充分的证据或案例？
3. 相比专业人士，这篇文章缺少哪些行业内的深度洞察？
4. 从中文读者的角度，文章缺少哪些本地化的案例或参考？
5. 文中是否缺少对复杂问题的辩证分析，例如各种观点的对比或批判性思考？
6. 如果没有在文章中标明当前年份，文中的信息是否已过时？需要更新哪些内容？

## AI生成特征检测
首先评估文章中的AI生成痕迹，重点关注：

1. "情感彩虹光谱"问题:
   - 是否存在过多情感维度的机械化覆盖（如悲伤→希望→震惊→感动的固定模式）
   - 情感转换是否自然，是否有突兀的情绪跳跃
   - 是否缺乏真实的情感波动与复杂性

2. "案例拼贴效应"问题:
   - 案例是否过多且涵盖范围过广（如同时出现各种年龄、职业、国家的案例）
   - 是否有"完美抽样分布"（如老年代表、青年代表、专家代表、普通人代表）
   - 案例细节是否真实可信，还是模糊且缺乏真实感

3. "百科全书式"数据堆砌:
   - 是否引用过多研究机构、专家观点和精确数据
   - 引用的研究数据是否存在内部矛盾或不合理的精确性
   - 专业术语使用是否自然，还是生硬堆砌

4. 时间线与逻辑一致性:
   - 案例中的时间点是否合理，是否存在技术、社会背景的时代错位
   - 因果关系是否严密，还是存在逻辑跳跃
   - 是否存在"完美曲线"的成长或变化路径，缺乏真实的波折

5. 隐喻系统的连贯性:
   - 是否使用过多不相关的隐喻体系，而非围绕核心隐喻深入发展
   - 比喻是否过度精确或机械化，缺乏模糊性和灵活性
   - 是否存在隐喻密度异常（每千字比喻数过多）

## 思考：评估缺失的人类独特视角
1. 文章缺少哪些只有人类才能提供的独特经验和观察？
2. 当前内容中，哪些部分最缺乏个人经历的细节和真实感？
3. 哪些内容可以通过添加作者个人故事、挫折或领悟来增强亲和力？
4. 文章中哪些观点缺少情感投入或个人立场？
5. 文章是否缺少生活化的语言和表达方式？
6. 哪些部分需要添加更多主观评价、犹豫或不确定性来体现人类思维特点？

## 内容深度评估
在识别AI特征后，评估内容深度：

1. 深度分析：
   - 是否提供了独特视角，而非表面化的共识
   - 论点是否有充分的论据支持，而非简单陈述
   - 是否深入探讨了主题的复杂性和矛盾性

2. 故事真实性：
   - 案例是否具有细节的真实感和复杂性
   - 人物描写是否立体，有个性特征而非类型化
   - 情感表达是否有深度和微妙变化

3. 内容连贯性：
   - 各部分是否有机衔接，还是机械分段
   - 是否有统一的思想主线和情感基调
   - 结论是否来自内容的自然延伸，而非突兀的价值判断

## 思考：搜索补充内容策略
1. 我们应该专注搜索哪些理论或原创研究来补充文章深度？
2. 需要查找哪些中国本土的真实案例或故事来增强文章的本地关联性？
3. 有哪些领域专家的权威观点可以引用来增强文章可信度？
4. 应该搜索哪些具体的数据或研究报告来支持文章的核心论点？
5. 需要寻找哪些生活化的例子或比喻来使抽象概念更容易理解？
6. 文章缺少哪些历史背景或发展脉络的信息？

## 需要聚焦的补充内容
基于AI特征识别、内容深度评估和思考过程，最多识别3-4个最需要补充的内容：
- 优先选择能让文章更贴近人类创作特质的内容
- 聚焦能显著增加真实感和深度的元素
- 具体明确，避免泛泛而谈
- 提供精准的搜索方向（查询的搜索词尽量是在中国能够搜索到的深度内容，案例和数据要包含比较多的中国本地发生的案例， 可以适当包含国外的案例， 科学依据可以是世界各地的论文或者权威专家的案例）
- 确保搜索关键词不包含时间
- 搜索关键词最后加一个平台文章（比如公众号文章， 头条文章）， 比如常见内容网站微信公众号，今日头条 专业性较强的加上百度百科， 知乎， aixiv， 知网，  对应领域网站文章，生活化的可以加上微博， 豆瓣， 小红书 等等

## 输出格式要求
必须严格按照以下JSON格式返回您的评估结果，确保没有任何格式错误：

{json_template}

重要提示：
1. 严格按照示例JSON格式返回结果，尤其注意数组的完整性
2. 对于布尔值使用true或false而非字符串
3. 数字评分必须是整数值
4. 最多只提供1-2个最关键的补充内容建议
5. 评估应同时注重内容质量和人类创作特征
6. 搜索查询必须精准定位所需信息
7. 人性化建议应具体可行，避免模板化语言
8. 不要在JSON外部添加额外的注释或说明
9. 所有字段必须填写完整，不得省略任何字段

如果文章已经非常全面深入，overall_score达到9.5以上，且不存在明显的AI生成特征，可以建议不需要进一步补充。"""

def content_enhancement_prompt(existing_content: Dict[str, Any], 
                               search_content: str, 
                               knowledge_gaps: List[str] = None,
                               humanizing_suggestions: List[str] = None,
                               improvement_suggestion: str = "",
                               topic: str = "",
                               keywords: List[str] = None,
                               article_type: str = None,
                               current_time: Dict[str, Any] = None) -> str:
    """
    生成内容增强的提示词，用于基于新搜索结果增强现有文章
    
    Args:
        existing_content: 现有文章内容字典或字符串
        search_content: 新搜索到的内容
        knowledge_gaps: 之前识别的知识空缺列表
        humanizing_suggestions: 人性化建议列表
        improvement_suggestion: 改进建议
        topic: 文章主题
        keywords: 关键词列表
        article_type: 文章类型，指定为5种标准类型之一
        current_time: 当前时间信息
        
    Returns:
        格式化的提示词
    """
    # 格式化现有内容
    existing_content_formatted = ""
    
    # 确保类型安全，转换为字符串或处理字典
    if existing_content is None:
        existing_content_formatted = "当前文章内容为空"
    elif isinstance(existing_content, dict):
        try:
            # 标题
            if "title" in existing_content and existing_content["title"]:
                existing_content_formatted += f"# {existing_content['title']}\n\n"
            
            # 正文内容(新格式)
            if "content" in existing_content and existing_content["content"]:
                existing_content_formatted += f"{existing_content['content']}\n\n"
                
            # 引言(旧格式)
            elif "introduction" in existing_content and existing_content["introduction"]:
                existing_content_formatted += f"{existing_content['introduction']}\n\n"
            
                # 章节
                if "sections" in existing_content and isinstance(existing_content["sections"], list):
                    for section in existing_content["sections"]:
                        if isinstance(section, dict):
                            section_title = section.get("title", "")
                            section_content = section.get("content", "")
                            if section_title:
                                existing_content_formatted += f"## {section_title}\n\n"
                            if section_content:
                                existing_content_formatted += f"{section_content}\n\n"
                
                # 结论
                if "conclusion" in existing_content and existing_content["conclusion"]:
                    existing_content_formatted += f"## 结论\n\n{existing_content['conclusion']}\n\n"
                
            # 如果格式化后内容为空，尝试直接字符串化字典
            if not existing_content_formatted.strip():
                existing_content_formatted = json.dumps(existing_content, ensure_ascii=False, indent=2)
        except Exception as e:
            # 如果解析字典失败，转为字符串
            existing_content_formatted = str(existing_content)
    elif isinstance(existing_content, str):
        # 已经是字符串，直接使用
        existing_content_formatted = existing_content
    else:
        # 其他类型，转换为字符串
        try:
            existing_content_formatted = str(existing_content)
        except:
            existing_content_formatted = "无法解析的内容格式"
    
    # 确保现有内容不为空
    if not existing_content_formatted.strip():
        existing_content_formatted = "当前文章内容为空"
    
    # 格式化搜索内容
    if not search_content or not isinstance(search_content, str):
        try:
            search_content = str(search_content) if search_content else "没有新的搜索内容"
        except:
            search_content = "无法解析的搜索内容"
    
    # 格式化知识空缺
    gaps_text = ""
    if knowledge_gaps:
        try:
            if isinstance(knowledge_gaps, list) and len(knowledge_gaps) > 0:
                # 过滤空值并确保都是字符串
                valid_gaps = [str(gap) for gap in knowledge_gaps if gap]
                if valid_gaps:
                    gaps_text = "需要解决的知识空缺:\n" + "\n".join([f"- {gap}" for gap in valid_gaps])
            elif isinstance(knowledge_gaps, str) and knowledge_gaps.strip():
                gaps_text = f"需要解决的知识空缺:\n- {knowledge_gaps}"
        except:
            gaps_text = ""
    
    # 修复变量valid_suggestions未定义的问题
    humanizing_suggestions_text = ""
    if humanizing_suggestions:
        try:
            if isinstance(humanizing_suggestions, list) and len(humanizing_suggestions) > 0:
                # 过滤空值并确保都是字符串
                valid_suggestions = [str(suggestion) for suggestion in humanizing_suggestions if suggestion]
                if valid_suggestions:
                    humanizing_suggestions_text = "需要改进的方面:\n" + "\n".join([f"- {suggestion}" for suggestion in valid_suggestions])
            elif isinstance(humanizing_suggestions, str) and humanizing_suggestions.strip():
                humanizing_suggestions_text = f"需要改进的方面:\n- {humanizing_suggestions}"
        except:
            humanizing_suggestions_text = ""
    
    # 格式化改进建议
    improvement_text = ""
    if improvement_suggestion and isinstance(improvement_suggestion, str) and improvement_suggestion.strip():
        improvement_text = f"整体改进建议:\n{improvement_suggestion}"
    
    # 格式化关键词
    keywords_text = ""
    if keywords:
        try:
            if isinstance(keywords, list) and len(keywords) > 0:
                # 过滤空值并确保都是字符串
                valid_keywords = [str(kw) for kw in keywords if kw]
                if valid_keywords:
                    keywords_text = "关键词: " + ", ".join(valid_keywords)
            elif isinstance(keywords, str) and keywords.strip():
                keywords_text = f"关键词: {keywords}"
        except:
            keywords_text = ""
    
    # 格式化主题
    if not topic or not isinstance(topic, str):
        try:
            topic = str(topic) if topic else "未指定主题"
        except:
            topic = "未指定主题"
    
    # 根据文章类型提供特定的增强指南
    type_specific_guide = ""
    output_format_guide = ""
    style_guide = ""
    
    # 格式化时间信息
    time_info_text = ""
    if current_time and isinstance(current_time, dict):
        time_parts = []
        if 'formatted_date' in current_time:
            time_parts.append(f"当前日期: {current_time['formatted_date']}")
        if 'formatted_time' in current_time:
            time_parts.append(f"当前时间: {current_time['formatted_time']}")
        if 'day_of_week_cn' in current_time:
            time_parts.append(f"星期: {current_time['day_of_week_cn']}")
        if 'season' in current_time:
            time_parts.append(f"季节: {current_time['season']}")
        
        if time_parts:
            time_info_text = "当前时间信息:\n" + "\n".join(time_parts)
    
    # 强化版的写作风格指南 - 移除AI味道
    style_guide = """
## 写作风格指南 - 让文章更具人性与真实感

### 语言与表达
1. 接地气表达：使用日常对话中的自然表达，避免过于完美的句式和词汇
2. 句式多样性：混合使用长短句，有节奏感的变化，避免机械性的句式平衡
3. 真实情感：表达真实、有温度的情感，包括细微的犹豫、矛盾和困惑
4. 个性化声音：建立独特的叙述语气，与作者身份和主题相符
5. 避免过度解释：人类不会解释显而易见的事情，保留一些需要读者自行理解的空间

### 叙事技巧
1. 不完美的细节：加入生活中的小瑕疵和意外，如"手忙脚乱地翻找钱包"、"差点把咖啡打翻"
2. 感官描写：通过味道、声音、触感等多感官细节增强真实感
3. 时间锚点：使用特定的时间标记（如"去年冬天"、"午后三点"），而非模糊的"最近"
4. 地域特色：融入特定地点的地标、方言或文化习惯，增强场景真实感
5. 具体化抽象概念：用具体例子和场景替代抽象论述

### 结构与连贯性
1. 非线性叙事：适当使用插叙、倒叙等手法，避免过于平直的叙事线
2. 自然过渡：使用事件、情感或思考的自然延伸作为段落过渡，避免机械的过渡词
3. 适度跳跃：人类思维常有跳跃，保留一些思维"跳跃"，但确保读者能跟上
4. 重复与变奏：自然地重复关键概念，每次增加新的层次，而非简单重复
5. 留白艺术：不必解释所有细节，给读者思考和想象的空间

### 避免AI特征
1. 避免数据堆砌：减少过多准确数字和百分比的使用，特别是在叙事型内容中
2. 控制引用密度：适度引用专家观点和研究，避免过度堆砌权威来源
3. 减少完美平衡：避免刻意平衡正反两面观点，可以有倾向性
4. 避免过度并列：减少"一方面...另一方面"的对称结构
5. 情感节奏自然：情感起伏应有节奏，避免每段都有情感高潮

### 增加真实性的技巧
1. 设置场景：使用具体地点、时间、天气等背景元素建立场景
2. 真实对话：加入口头禅、停顿、重复等自然对话特征
3. 主观评价：适当加入个人主观判断和偏好
4. 文化引用：使用与目标读者相关的流行文化、典故或熟悉的比喻
5. 个人经历：适当分享看似随意但相关的个人经历，增强亲近感
"""
    
    # 根据文章类型设置不同的输出格式指南
    if article_type:
        if ("教程/分析型" in article_type) or ("总分总" in article_type):
            # 教程/分析型文章使用章节式结构
            output_format_guide = """
## 输出格式
请严格按照以下结构输出增强后的文章：

{
  "title": "清晰简洁的教程标题",
  "introduction": "引人入胜的介绍，说明本教程解决的问题和价值",
  "sections": [
    {
      "title": "第一章节标题",
      "content": "章节详细内容，包含步骤说明、示例和提示"
    },
    {
      "title": "第二章节标题",
      "content": "章节详细内容，包含步骤说明、示例和提示"
    }
  ],
  "conclusion": "总结要点，并提供后续学习建议"
}
"""
        else:
            # 其他文章类型使用流畅型结构
            output_format_guide = """
## 输出格式
请严格按照以下结构输出增强后的文章：

{
  "title": "富有情感共鸣的标题",
  "content": "增强后的完整文章内容，使用自然流畅的段落结构，不分章节，总内容至少在4000字以上"
}
"""
    else:
        # 默认使用流畅型结构
        output_format_guide = """
## 输出格式
请严格按照以下结构输出增强后的文章：

{
  "title": "富有情感共鸣的标题",
  "content": "增强后的完整文章内容，使用自然流畅的段落结构，不分章节，总内容至少在4000字以上"
}
"""
    
    if article_type:
        if "观点+案例+金句" in article_type or "观点" in article_type:
            type_specific_guide = """
## 观点类文章增强重点
1. 鲜明立场：表达清晰、有个性的观点，不要过于中立或模棱两可
2. 具体案例：使用有细节、有温度的真实案例，包括人物背景、场景描述和情感变化
3. 不完美逻辑：适当保留一些思考过程和转折，避免过于完美的逻辑推导
4. 个人色彩：融入作者自身经历或感受，增加真实感和信服力
5. 对比手法：通过鲜明对比强化论点，但避免过于刻意的二元对立
6. 适度争议：不怕引起一些争议，敢于表达前沿或非主流观点
7. 金句设计：在关键点设置有力金句，但避免过多而显得刻意
8. 情感共鸣：触动读者情感，但不做过度煽情

加强技巧：
- 开头设置悬念或冲突，如"第一次见到他时，我完全不相信他所说的话..."
- 在案例中加入细微失误或意外，增加真实感
- 使用地域特色和时间锚点（如"去年深圳的一个闷热午后"）
- 加入一些思考性的提问，展示思维过程
- 结尾不求完美解决所有问题，可以留下开放性思考
"""
        elif "故事+感触+干货" in article_type or "故事" in article_type:
            type_specific_guide = """
## 故事类文章增强重点
1. 叙事节奏：创造自然的起伏和张力，避免过于均匀的情节分布
2. 人物真实性：塑造有矛盾、有缺点的立体角色，避免完美无缺的人物
3. 场景细节：加入视觉、听觉、嗅觉等多感官细节，创造沉浸感
4. 对话真实化：使用口语、停顿、方言等元素，避免过于工整的对话
5. 情感复杂性：展现情感的多层次和矛盾，不简单化处理人物感受
6. 偶然性：加入一些计划外的意外和巧合，增加故事的不可预测性
7. 个人反思：分享真实、深刻的感悟，包括疑惑和未解问题
8. 实用启示：从故事中提炼真正有价值的经验，而非简单道德说教

增强技巧：
- 使用回忆片段打破线性叙事，如"那让我想起五年前的一个决定..."
- 加入特定场景的环境描写（街道声音、室内温度、食物气味等）
- 使用具体地名和真实场所，增加可信度
- 在对话中加入重复、更正、口头禅等自然元素
- 结尾与开头形成呼应，但避免过于完美的闭环
"""
        elif "总分总" in article_type:
            type_specific_guide = """
## 总分总结构文章增强重点
1. 个性化开场：以独特的个人经历或观察引入主题，避免宽泛的"近年来"式开头
2. 分论点层次感：确保各分论点既独立成章又有内在联系
3. 案例深入度：深入挖掘少量案例，而非浅尝辄止地罗列多个例子
4. 适度争议：不回避争议点，展示思考的深度和广度
5. 转折与连接：运用自然的思维流转而非机械的数字标记连接各部分
6. 立体证据：混合使用数据、案例、比喻和类比等多种论证方法
7. 平衡专业与通俗：在保持专业深度的同时确保表达通俗易懂
8. 实用价值：提供读者可直接应用的建议或启示

增强技巧：
- 使用第一人称引入个人思考过程，如"起初我以为...但后来发现..."
- 在关键论点后添加简短的自问自答，模拟思维探索过程
- 加入"思想实验"，引导读者参与思考
- 使用生活化比喻解释复杂概念
- 结尾既总结核心观点，又开放新的思考方向
"""
        elif "对比分析" in article_type:
            type_specific_guide = """
## 对比分析文章增强重点
1. 对比维度的自然展开：选择3-5个关键维度，但避免机械化的逐点比较，应像自然聊天般展开
2. 非完美平衡的观点：允许偏向某一方的观点，避免刻意的对称性和过度平衡的比较
3. 真实体验为核心：分享自己真实经历过的使用体验，包括犹豫、失误和意外发现
4. 情感波动的真实表达：坦诚表达偏好和失望，例如"第一次用产品A时，我确实被它华丽的界面吸引，但三天后就开始厌烦了"
5. 非线性的比较节奏：不要整齐划一地比较每个维度，可在某些点上深入展开，略过不重要的方面
6. 具体化的使用场景：描述特定环境下的实际使用情况，例如"在地铁拥挤的早高峰，产品A的单手操作明显更实用"
7. 主观判断与自我矛盾：展现人类思考的复杂性，例如"理性上我知道B更高效，但我就是更喜欢A的设计语言"
8. 时间维度的对比：展示对比对象随时间变化的表现，避免静态比较

增强技巧：
- 融入个人习惯和偏好，比如"作为一个经常忘记充电的人，我更倾向于电池续航的方案"
- 使用具体数字而非完美比例，例如"使用17天后"而非"半个月左右"
- 加入突发状况的反应，如"当A系统在演示会议上突然崩溃时，我意识到备选方案的重要性"
- 在关键观点前表达犹豫，如"我反复思考后才勉强得出这个结论..."
- 承认知识盲区，例如"关于技术架构这块，我了解有限，但从用户体验来说..."
- 使用独特的个人化表达，如"这让我想起祖父常说的一句话..."
"""
        elif "教程/分析" in article_type:
            type_specific_guide = """
## 教程/分析型文章增强重点
1. 真实挫折的坦诚分享：详述自己学习过程中遇到的真实困难，例如"第一次尝试这个技术时，我连续三天都卡在配置环境上"
2. 不完美的解决方案：承认某些步骤的局限性，例如"这种方法解决了80%的问题，但对于极端情况仍显不足"
3. 技术迭代的时间锚点：提供具体技术版本和时间节点，例如"截至2023年6月的React 18版本中..."
4. 非线性学习路径：分享探索过程中的弯路和意外收获，避免过于顺畅的进程描述
5. 地域化的实操差异：提及不同地区使用该技术的差异，例如"在国内网络环境下，你可能需要额外配置..."
6. 成本与收益的真实评估：坦诚讨论时间成本、学习曲线和可能的收益
7. 适用场景的具体限定：明确说明方法适用的具体场景，以及不适用的情况
8. 个性化的问题解决习惯：展示你独特的思考方式和解决问题的路径

增强技巧：
- 使用口头化表达描述技术过程，如"卡住的时候，我通常会先检查一下日志，然后google相关报错—反正死不了人嘛"
- 加入特定时段的感受，如"午夜两点仍在调试时，我几乎想砸电脑"
- 使用非完美的修辞表达，例如"这块其实挺绕的，我自己也是反复看了好几遍才理解"
- 描述意外的中断和回归，如"这项目本来去年就该完成，结果因为各种原因拖到现在"
- 加入跨领域的个人理解，如"这个架构设计让我想起中国园林的层层递进"
- 提及特定环境下的小技巧，如"我在星巴克编程时发现，这个框架在网络不稳定时特别容易出问题"
- 承认难以解释的偏好，如"虽然新版本功能更多，但我就是习惯了旧界面的工作流"
"""
    # 新增内容增强指导
    content_enhancement_guidelines = """
## 内容深度增强指南

### 知识深度提升
1. 避免表面化处理：不仅解释"是什么"，更要探讨"为什么"和"如何"
2. 多层次分析：从现象→原因→机制→影响→应对的多层次分析
3. 辩证思考：呈现事物的复杂性和矛盾性，避免简单化处理
4. 时间纵深：增加历史背景和发展脉络，建立纵向理解
5. 跨领域关联：建立不同知识领域之间的联系，形成网状认知

### 人性化深度
1. 价值观探讨：深入探讨相关的价值观冲突和伦理考量
2. 情感复杂性：展示复杂、矛盾的情感状态，避免单一情绪描述
3. 认知差异：呈现不同群体或个体的认知差异和理解角度
4. 文化语境：考虑中文文化/语境,文化背景对主题理解的影响
5. 共情视角：从不同立场理解同一问题，展现同理心

### 结构性深度
1. 问题层层深入：从表层问题引向深层问题，形成"问题树"
2. 故事嵌套：在主线叙事中嵌入支线故事，丰富内容层次
3. 意义递进：从个人意义→社会意义→哲学意义的递进式探讨
4. 矛盾展开：通过设置和解决矛盾推动内容发展
5. 开放式结构：结尾既有总结又有新问题，形成思考螺旋
"""
    
    # 针对报告中常见问题的改进指南
    common_issues_guide = """
## 常见问题改进指南

### 避免AI特征显现
1. 数据合理化：减少过多的精确数字和百分比，必要时使用"约"、"大约"等词语
2. 案例真实性：选择2-3个深入案例，而非遍历式覆盖多个浅层案例
3. 引用本地化：优先使用与目标读者文化相关的研究和专家
4. 隐喻一致性：在全文使用相关联的隐喻系统，避免跨维度混用
5. 情感节奏自然化：避免按段落机械安排情感高潮

### 加强内容可信度
1. 时空一致性：确保案例的时间线与技术发展、社会背景相符
2. 细节锚定：使用具体地点、时间、人物和场景，避免模糊描述
3. 适度专业性：在保持可读性的同时加入适当专业细节，展示领域了解
4. 经验传递：分享看似"小众"但实用的经验和窍门
5. 非完美结论：允许一些问题保持开放，反映真实世界的复杂性

### 增加文化相关性
1. 本土案例：使用目标读者熟悉的本土案例和场景
2. 文化符号：融入特定文化的习俗、表达方式和价值观
3. 时代特征：反映当前社会热点和时代特色
4. 群体认同：考虑目标读者群体的共同经历和认同感
5. 语言风格：使用符合主题和读者群体的语言风格和表达方式
"""
    
    # 构建完整的提示词
    prompt = f"""# 内容增强与人性化任务

## 任务说明
你的任务是基于新搜索到的内容，对现有文章进行增强和人性化处理，使其更加深入、真实和有吸引力。

## 主题与关键词
主题: {topic}
{keywords_text}
{time_info_text}

## 现有文章内容
{existing_content_formatted}

## <思考>搜索内容分析
请先对新搜索到的内容进行深入思考和分析：
1. 哪些新信息能填补现有文章的知识空缺？
2. 搜索内容中有哪些可以丰富现有观点的事实、数据或案例？
3. 是否有与现有内容相矛盾的观点？如何辩证处理？
4. 新搜索到的内容中有哪些独特视角或深刻洞见？
5. 哪些内容可能过时或不够相关？应当舍弃什么？
6. 如何根据当前时间背景({current_time['formatted_date'] if current_time and 'formatted_date' in current_time else '当前日期'})对内容进行适当调整？

我应该以批判性思维审视搜索内容，提取有价值的信息来增强文章深度和广度。
</思考>

## 新搜索内容
{search_content}

## 需要改进的方面
{gaps_text}

{humanizing_suggestions_text}

{improvement_text}

## <思考>内容优化策略
基于现有文章和新搜索内容，我需要：
1. 识别现有文章的结构和逻辑薄弱点
2. 寻找知识深度不足或者论证/表达深度不够的部分
3. 确定情感共鸣和个人色彩不足的段落
4. 发现论述不完整或缺乏支持论据的观点
5. 确认需要更新的时间敏感信息
6. 找出可以增加案例或细节描写的部分
7.  思考怎么依据现有搜索内容，增加文章的深度，丰富度和独特的人类生活体验

我将保持文章的核心观点和风格，同时显著提升内容质量、深度和真实感。
</思考>

{style_guide}

{content_enhancement_guidelines}

{common_issues_guide}

{type_specific_guide}


## 增强要求
1. 保持原有文章的核心观点和结构框架
2. 融入新搜索内容中的有价值信息，填补知识空缺
3. 加强文章的真实感、情感表达和共鸣度
4. 确保内容深度、逻辑性和连贯性
5. 使用适合主题的语言风格和叙事手法
6. 避免明显的AI生成特征
7. 保持文章类型特征，但更加自然和人性化
8. 总字数保持在4000-6000字之间
9. 确保文章时间背景符合当前日期({current_time['formatted_date'] if current_time and 'formatted_date' in current_time else '今日'})的时间环境


## 输出格式

必须严格按照以下JSON格式输出文章，确保没有任何格式错误，不包含任何非法控制字符，确保所有字段都存在：
{output_format_guide}"""
    
    return prompt
