"""
文章生成相关的数据模型
"""
import uuid
from enum import Enum
from typing import Dict, List, Optional, Any, Union
from pydantic import BaseModel, Field

# 章节数据
class SubsectionData(BaseModel):
    """子章节数据"""
    id: str = Field(default_factory=lambda: str(uuid.uuid4()), description="子章节ID")
    title: str = Field(default="", description="子章节标题")
    description: str = Field(default="", description="子章节描述")
    
    model_config = {"arbitrary_types_allowed": True}

class SectionData(BaseModel):
    """章节数据"""
    id: str = Field(default_factory=lambda: str(uuid.uuid4()), description="章节ID")
    title: str = Field(default="", description="章节标题")
    description: str = Field(default="", description="章节描述")
    subsections: List[SubsectionData] = Field(default_factory=list, description="子章节")
    
    model_config = {"arbitrary_types_allowed": True}

class ContentQualityScore(BaseModel):
    """内容质量评分"""
    overall: float = Field(default=0.0, description="总体质量评分")
    introduction: float = Field(default=0.0, description="引言质量评分")
    sections: float = Field(default=0.0, description="章节质量评分")
    conclusion: float = Field(default=0.0, description="结论质量评分")
    
    model_config = {"arbitrary_types_allowed": True}

class ImageGenerationInfo(BaseModel):
    """图片生成信息"""
    url: str = Field(default="", description="图片URL")
    prompt: str = Field(default="", description="生成提示词")
    section_id: Optional[str] = Field(default=None, description="关联章节ID")
    width: int = Field(default=512, description="图片宽度")
    height: int = Field(default=512, description="图片高度")
    generated: bool = Field(default=False, description="是否已生成")
    generation_time: float = Field(default=0.0, description="生成时间(秒)")
    
    model_config = {"arbitrary_types_allowed": True}

class FormattedContent(BaseModel):
    """格式化内容"""
    html: str = Field(default="", description="HTML格式")
    markdown: str = Field(default="", description="Markdown格式")
    text: str = Field(default="", description="纯文本格式")
    
    model_config = {"arbitrary_types_allowed": True}

class SubsectionWithContent(SubsectionData):
    """带内容的子章节"""
    content: str = Field(default="", description="子章节内容")
    
    model_config = {"arbitrary_types_allowed": True}

class SectionWithContent(SectionData):
    """带内容的章节"""
    content: str = Field(default="", description="章节内容")
    subsections: List[SubsectionWithContent] = Field(default_factory=list, description="带内容的子章节")
    
    model_config = {"arbitrary_types_allowed": True}

# Agent数据模型
class PlanningAgentData(BaseModel):
    """规划Agent数据"""
    planning_time: float = Field(default=0.0, description="规划时间(秒)")
    outline_quality: float = Field(default=0.0, description="大纲质量评分")
    writing_plan: Dict[str, Any] = Field(default_factory=dict, description="写作计划")
    
    model_config = {"arbitrary_types_allowed": True}

class WriteAgentData(BaseModel):
    """写作Agent数据"""
    generation_time: float = Field(default=0.0, description="生成时间(秒)")
    words_count: int = Field(default=0, description="总字数")
    readability_score: float = Field(default=0.0, description="可读性评分")
    content_quality_score: ContentQualityScore = Field(default_factory=ContentQualityScore, description="内容质量评分")
    
    model_config = {"arbitrary_types_allowed": True}

class SEOAgentData(BaseModel):
    """SEO Agent数据"""
    optimization_time: float = Field(default=0.0, description="优化时间(秒)")
    seo_score: float = Field(default=0.0, description="SEO评分")
    keyword_density: Dict[str, float] = Field(default_factory=dict, description="关键词密度")
    improvements: List[str] = Field(default_factory=list, description="改进项")
    
    model_config = {"arbitrary_types_allowed": True}

class ImageAgentData(BaseModel):
    """图片生成Agent数据"""
    generation_time: float = Field(default=0.0, description="生成时间(秒)")
    images_count: int = Field(default=0, description="图片数量")
    image_quality_score: float = Field(default=0.0, description="图片质量评分")
    image_generation_details: Dict[str, Any] = Field(default_factory=dict, description="生成细节")
    
    model_config = {"arbitrary_types_allowed": True}

class EditAgentData(BaseModel):
    """编辑Agent数据"""
    editing_time: float = Field(default=0.0, description="编辑时间(秒)")
    improvement_score: float = Field(default=0.0, description="改进评分")
    content_changes: Dict[str, Any] = Field(default_factory=dict, description="内容变化统计")
    words_count: int = Field(default=0, description="编辑后总字数")
    readability_score: float = Field(default=0.0, description="编辑后可读性评分")
    content_quality_score: ContentQualityScore = Field(default_factory=ContentQualityScore, description="编辑后内容质量评分")
    
    model_config = {"arbitrary_types_allowed": True}

class ReflectionData(BaseModel):
    """反思数据"""
    strengths: List[str] = Field(default_factory=list, description="过程优势")
    weaknesses: List[str] = Field(default_factory=list, description="过程劣势")
    key_insights: List[str] = Field(default_factory=list, description="关键洞见")
    improvement_suggestions: List[str] = Field(default_factory=list, description="改进建议")
    summary: str = Field(default="", description="过程反思总结")
    reflection_time: float = Field(default=0.0, description="反思时间(秒)")
    
    model_config = {"arbitrary_types_allowed": True}

class SearchData(BaseModel):
    """搜索数据"""
    strategy: Dict[str, Any] = Field(default_factory=dict, description="搜索策略")
    results: List[Dict[str, Any]] = Field(default_factory=list, description="搜索结果")
    analysis: Dict[str, Any] = Field(default_factory=dict, description="结果分析")
    search_time: float = Field(default=0.0, description="搜索时间(秒)")
    
    model_config = {"arbitrary_types_allowed": True}

class ArticleGenerationState(BaseModel):
    """
    文章生成状态
    """
    article_id: str = Field(default_factory=lambda: str(uuid.uuid4()), description="文章ID")
    status: str = Field(default="初始化", description="当前状态")
    topic: str = Field(default="", description="文章主题")
    keywords: Optional[List[str]] = Field(default=None, description="关键词列表")
    url: Optional[str] = Field(default=None, description="原始URL")
    images: Optional[Dict[str, ImageGenerationInfo]] = Field(default=None, description="生成的图片信息")
    sections: List[Union[SectionData, SectionWithContent]] = Field(default_factory=list, description="文章章节")
    introduction: Optional[str] = Field(default=None, description="引言")
    conclusion: Optional[str] = Field(default=None, description="结论")
    final_content: Optional[str] = Field(default=None, description="最终内容(JSON格式)")
    formatted_content: Optional[FormattedContent] = Field(default=None, description="格式化内容")
    
    # 各个Agent的数据
    planning_agent_data: Optional[PlanningAgentData] = Field(default=None, description="规划Agent数据")
    write_agent_data: Optional[WriteAgentData] = Field(default=None, description="写作Agent数据")
    seo_agent_data: Optional[SEOAgentData] = Field(default=None, description="SEO Agent数据")
    image_agent_data: Optional[ImageAgentData] = Field(default=None, description="图片生成Agent数据")
    
    # 新增Agent数据
    edit_agent_data: Optional[EditAgentData] = Field(default=None, description="编辑Agent数据")
    reflection_data: Optional[ReflectionData] = Field(default=None, description="反思数据")
    search_data: Optional[SearchData] = Field(default=None, description="搜索数据")
    
    model_config = {"arbitrary_types_allowed": True}
    
    def copy(self, deep=False):
        """
        复制当前状态
        
        Args:
            deep: 是否深拷贝
            
        Returns:
            拷贝后的对象
        """
        if deep:
            import copy
            return copy.deepcopy(self)
        # 使用Pydantic v2 API进行浅拷贝
        return self.model_copy() 