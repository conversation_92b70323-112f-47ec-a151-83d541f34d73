from typing import Dict, List, Any, Optional, Union, Tuple, Set, cast
import time
import json
import uuid
from dataclasses import dataclass, field, asdict, fields
from enum import Enum
from datetime import datetime
from copy import deepcopy

class ArticleStatus(str, Enum):
    """文章状态枚举"""
    
    # 初始状态
    INITIALIZED = "initialized"
    
    # 处理中的状态
    PROCESSING = "processing"
    
    PLANNING = "planning"
    PLAN_COMPLETED = "plan_completed"
    SEARCHING = "searching"
    SEARCH_COMPLETED = "search_completed"
    WRITING = "writing"
    WRITING_COMPLETED = "writing_completed"
    EVALUATING = "evaluating"  # 新增内容评估状态
    EDITING = "editing"
    FINALIZING = "finalizing"
    GENERATING_IMAGES = "generating_images"
    
    # 完成状态
    TOPIC_ANALYZED = "topic_analyzed"
    PLANNING_COMPLETED = "planning_completed"
    CONTENT_WRITTEN = "content_written"
    IMAGE_GENERATION_COMPLETED = "image_generation_completed"
    FINALIZED = "finalized"
    
    # 结束状态
    COMPLETED = "completed"
    FAILED = "failed"
    ERROR = "error"
    
    def __str__(self) -> str:
        """返回状态的字符串值，用于安全地转换枚举为字符串"""
        return self.value
        
    @classmethod
    def from_str(cls, status_str: str) -> 'ArticleStatus':
        """从字符串安全地转换为枚举值
        
        Args:
            status_str: 状态字符串
            
        Returns:
            对应的ArticleStatus枚举值，如果找不到则返回INITIALIZED
        """
        try:
            return cls(status_str)
        except ValueError:
            # 找不到对应枚举值，返回默认值
            return cls.INITIALIZED

    @classmethod
    def is_processing(cls, status):
        """检查状态是否为处理中"""
        processing_statuses = [
            cls.PROCESSING,
            cls.CONTENT_ANALYSIS,
            cls.CONTENT_EXTRACTED,
            cls.PLANNING,
            cls.PLAN_COMPLETED,
            cls.SEARCHING,
            cls.SEARCH_COMPLETED,
            cls.WRITING,
            cls.WRITING_COMPLETED,
            cls.EVALUATING,  # 新增内容评估状态
            cls.EDITING,
            cls.FINALIZING,
            cls.HUMAN_FEEDBACK  # 新增人类反馈状态
        ]
        return status in processing_statuses

@dataclass
class ArticleState:
    """文章状态类，用于在工作流中传递状态"""
    
    # 基本信息
    article_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    status: ArticleStatus = ArticleStatus.INITIALIZED
    start_time: float = field(default_factory=time.time)
    last_updated: float = field(default_factory=time.time)
    
    # 输入参数
    topic: str = ""
    keywords: List[str] = field(default_factory=list)
    tone: str = "professional"
    language: str = "zh-CN"
    target_length: int = 3500
    selected_direction: str = ""  # 用户选择的内容方向
    
    # URL相关
    
    
    # 内容相关
    extracted_content: Dict[str, Any] = field(default_factory=dict)
    extracted_metadata: Dict[str, Any] = field(default_factory=dict)  # 提取的元数据
    extracted_images: List[str] = field(default_factory=list)  # 从内容中提取的图片URLs
   
    article_plan: Dict[str, Any] = field(default_factory=dict)
    outline: List[Dict[str, Any]] = field(default_factory=list)  # 文章大纲
    writing_plan: Dict[str, Any] = field(default_factory=dict)  # 写作计划
    article_content: Dict[str, Any] = field(default_factory=dict)  # 文章内容
    edited_content: Dict[str, Any] = field(default_factory=dict)  # 编辑后的内容
    draft_content: Dict[str, Any] = field(default_factory=dict)  # 草稿内容
    
    
    seo_optimized_content: Dict[str, Any] = field(default_factory=dict)  # SEO优化后的内容
    final_content: Dict[str, Any] = field(default_factory=dict)  # 最终内容
    formatted_content: Dict[str, Any] = field(default_factory=dict)  # 格式化后的内容
    final_metadata: Dict[str, Any] = field(default_factory=dict)  # 最终元数据
    meta_description: str = ""  # 元描述，用于SEO
    
    # 分析结果
    analysis_result: List[Dict[str, Any]] = field(default_factory=list)  # 分析结果列表，每个项目包含keywords、recommended_title和content_direction
    
    # 搜索结果
    search_results: List[Dict[str, Any]] = field(default_factory=list)  # 搜索到的参考资料
    search_queries: List[str] = field(default_factory=list)  # 生成的搜索查询
    
    # 多次搜索相关字段
    search_rounds: int = 0  # 搜索轮次计数，从1开始
    deeper_search_queries: List[str] = field(default_factory=list)  # 深度搜索的查询
    deeper_search_reasons: List[str] = field(default_factory=list)  # 深度搜索的原因
    knowledge_gaps: List[str] = field(default_factory=list)  # 知识空缺列表

    humanizing_suggestions: List[str] = field(default_factory=list)  # 如何让文章更接近人类作者风格的具体建议
    improvement_suggestion: str = ""  # 对于如何整合补充内容的具体建议

    needs_deeper_search: bool = False  # 是否需要深度搜索的标志
    evaluation_rounds: int = 0  # 评估轮次计数
    all_search_results: List[Dict[str, Any]] = field(default_factory=list)  # 所有轮次的搜索结果
    search_history: List[Dict[str, Any]] = field(default_factory=list)  # 搜索历史记录，包含每轮搜索的查询和结果
    accumulated_search_content: str = ""  # 累积的格式化搜索内容
    
    # 人类反馈相关
    human_feedback: Dict[str, Any] = field(default_factory=dict)  # 用户提供的反馈数据
    feedback_history: List[Dict[str, Any]] = field(default_factory=list)  # 历史反馈记录
    
    # FAQ相关
    faqs: List[Dict[str, str]] = field(default_factory=list)  # 常见问题及答案列表
    
    # 完成状态
    completion_status: Dict[str, bool] = field(default_factory=dict)  # 各阶段完成状态
    
    # SEO相关
    seo_metadata: Dict[str, Any] = field(default_factory=dict)
    seo_score: Dict[str, Any] = field(default_factory=dict)
    seo_suggestions: List[Dict[str, Any]] = field(default_factory=list)
    
    # 工具相关
    tools_used: List[str] = field(default_factory=list)
    tool_results: Dict[str, Any] = field(default_factory=dict)
    
    # 错误处理
    errors: List[Dict[str, Any]] = field(default_factory=list)
    warnings: List[str] = field(default_factory=list)
    
    # 反思相关字段 - 向后兼容保留，但不再主动使用
    reflections: List[Dict[str, Any]] = field(default_factory=list)  # 向后兼容，不再主动使用
    improvement_suggestions: List[str] = field(default_factory=list)  # 向后兼容，不再主动使用
    reflection: Dict[str, Any] = field(default_factory=dict)  # 向后兼容，不再主动使用
    
    # 工具请求和结果
    tool_requests: List[Dict[str, Any]] = field(default_factory=list)
    
    # 节点输出，用于跟踪每个节点的详细执行结果
    node_outputs: Dict[str, Any] = field(default_factory=dict)
    
    # 执行时间记录
    execution_times: Dict[str, float] = field(default_factory=dict)  # 各阶段执行时间
    
    def update_status(self, status: Union[ArticleStatus, str]) -> None:
        """更新状态
        
        Args:
            status: 新状态，可以是ArticleStatus枚举值或字符串
        """
        try:
            # 如果传入的是字符串，尝试转换为枚举值
            if isinstance(status, str):
                try:
                    self.status = ArticleStatus(status)
                except ValueError:
                    # 如果转换失败，记录一个警告，但仍然设置状态
                    if hasattr(self, "add_warning"):
                        self.add_warning(f"未知状态值: {status}，将使用字符串形式")
                    self.status = status  # 使用字符串形式
            else:
                # 枚举值直接设置
                self.status = status
            
            # 更新最后修改时间
            self.last_updated = time.time()
        except Exception as e:
            # 捕获所有可能的异常，确保状态更新不会失败
            if hasattr(self, "add_error"):
                self.add_error("status_update", f"状态更新失败: {str(e)}")
    
    def add_error(self, step: str, error_msg: str, error_details: Any = None) -> None:
        """添加错误
        
        Args:
            step: 发生错误的步骤
            error_msg: 错误消息
            error_details: 错误详情
        """
        # 确保错误消息不是枚举值
        if isinstance(error_msg, Enum):
            error_msg = str(error_msg.value)
        
        self.errors.append({
            "timestamp": time.time(),
            "step": step,
            "message": error_msg,
            "details": error_details
        })
        
        # 如果有太多错误，可能需要将状态设为失败
        if len(self.errors) >= 5:
            self.update_status(ArticleStatus.FAILED)
    
    def add_warning(self, warning_msg: str) -> None:
        """添加警告"""
        self.warnings.append(warning_msg)
    
    def add_tool_result(self, tool_name: str, result: Any) -> None:
        """添加工具使用结果"""
        if tool_name not in self.tools_used:
            self.tools_used.append(tool_name)
        
        if tool_name not in self.tool_results:
            self.tool_results[tool_name] = []
        
        self.tool_results[tool_name].append({
            "timestamp": time.time(),
            "result": result
        })
    
    def add_reflection(self, phase: str, content: str, metrics: Dict[str, Any] = None) -> None:
        """添加反思"""
        self.reflections.append({
            "timestamp": time.time(),
            "phase": phase,
            "content": content,
            "metrics": metrics or {}
        })
    
    def add_tool_request(self, tool_name: str, params: Dict[str, Any]) -> Dict[str, Any]:
        """添加工具请求"""
        request_id = str(uuid.uuid4())
        request = {
            "id": request_id,
            "timestamp": time.time(),
            "tool": tool_name,
            "params": params,
            "status": "pending"
        }
        self.tool_requests.append(request)
        return request
    
    def update_tool_request(self, request_id: str, status: str, result: Any = None) -> None:
        """更新工具请求状态"""
        for request in self.tool_requests:
            if request["id"] == request_id:
                request["status"] = status
                if result is not None:
                    request["result"] = result
                request["updated_at"] = time.time()
                break
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return asdict(self)
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ArticleState':
        """从字典创建实例
        
        Args:
            data: 包含状态数据的字典
            
        Returns:
            ArticleState实例
        """
        # 过滤掉不在类定义中的字段
        valid_fields = {f.name for f in fields(cls)}
        filtered_data = {k: v for k, v in data.items() if k in valid_fields}
        
        return cls(**filtered_data)
    
    def copy(self, deep: bool = False) -> 'ArticleState':
        """创建当前对象的副本
        
        Args:
            deep: 是否深拷贝
            
        Returns:
            ArticleState的副本
        """
        data = self.to_dict()
        if deep:
            data = deepcopy(data)
            
        return ArticleState.from_dict(data)
    
    def update_from(self, other: 'ArticleState') -> None:
        """从另一个ArticleState实例更新当前实例
        
        Args:
            other: 另一个ArticleState实例
        """
        if not isinstance(other, ArticleState):
            raise ValueError("只能从ArticleState实例更新")
            
        # 获取所有字段，不包括内置方法
        fields = [f for f in dir(other) if not f.startswith('_') and not callable(getattr(other, f))]
        
        # 更新所有非None的值
        for field_name in fields:
            other_value = getattr(other, field_name)
            if other_value is not None:
                setattr(self, field_name, deepcopy(other_value))
                
        # 更新最后修改时间
        self.last_updated = time.time() 