"""
抖音热榜任务实现
"""

from typing import Any, Dict, Optional, List
from sqlalchemy.orm import Session
import time

from src.services.crawlers.tasks.base import BaseCrawlerTask, TaskPriority, TaskResult
from src.services.crawlers.tasks.douhot_async_crawler import DouhotAsyncCrawler
from src.domain import DouhotBillboardVideoRepository, DouhotBillboardTopicRepository, DouhotBillboardSearchRepository
from src.utils.logger import log_info, log_warning, log_error


class BatchProcessor:
    """批量处理器"""
    def __init__(self, batch_size: int = 100):
        self.batch_size = batch_size
        self.current_batch: List[Any] = []
        
    def add(self, item: Any):
        """添加项目到批次"""
        self.current_batch.append(item)
        return len(self.current_batch) >= self.batch_size
        
    def get_batch(self) -> List[Any]:
        """获取当前批次并清空"""
        batch = self.current_batch[:]
        self.current_batch = []
        return batch
        
    def remaining_items(self) -> List[Any]:
        """获取剩余项目"""
        return self.get_batch()


class VideoBillboardTask(BaseCrawlerTask):
    """视频榜单任务
    考虑到分页不确定的原因
    按照sub_type、date_window、tag1、tag2拆分任务
    而不是拆分页那么细
    """
    
    def __init__(
        self,
        task_id: str,
        crawler: DouhotAsyncCrawler,
        sub_type: int,
        date_window: int,
        tag1: int,
        tag2: int,
        page_size: int = 50,
        page_upper_limit: int = 20,
        batch_size: int = 100,
        priority: TaskPriority = TaskPriority.MEDIUM
    ):
        super().__init__(task_id, priority)
        self.crawler = crawler
        self.sub_type = sub_type
        self.date_window = date_window
        self.page_size = page_size
        self.tag1 = tag1
        self.tag2 = tag2
        self.page_upper_limit = page_upper_limit
        self.batch_size = batch_size
        
    async def execute(self, db: Session, **kwargs) -> TaskResult:
        try:
            # 获取视频榜单数据
            data, task_msg = await self.crawler.fetch_video_billboard(
                sub_type=self.sub_type,
                date_window=self.date_window,
                tag1=self.tag1,
                tag2=self.tag2,
                page_size=self.page_size,
                page_upper_limit=self.page_upper_limit
            )
            if not data:
                return TaskResult(success=True, result=[])
            if task_msg:
                return TaskResult(success=False, error=task_msg)          
        except Exception as e:
            log_error("VideoBillboardTask Result Failure", 
                        f"任务({self.sub_type}-{self.date_window}-{self.tag1}-{self.tag2})视频榜单任务, msg: {task_msg}")
            return TaskResult(success=False, error=str(e))

        # 批量保存到数据库
        start_time = time.time()
        processor = BatchProcessor(self.batch_size)
        repo = DouhotBillboardVideoRepository(db)
        saved_count = 0
        error_count = 0

        try:
            for item in data:
                if processor.add(item):
                    # 处理当前批次
                    batch = processor.get_batch()
                    try:
                        for video in batch:
                            await repo.upsert_video(video)
                        db.commit()
                        saved_count += len(batch)
                    except Exception as e:
                        db.rollback()
                        error_count += len(batch)
                        log_error(
                            "VideoBillboardTask Batch Error",
                            "批量保存视频榜数据失败",
                            error=str(e),
                            batch_size=len(batch)
                        )

            # 处理剩余项目
            remaining = processor.remaining_items()
            if remaining:
                try:
                    for video in remaining:
                        await repo.upsert_video(video)
                    db.commit()
                    saved_count += len(remaining)
                except Exception as e:
                    db.rollback()
                    error_count += len(remaining)
                    log_error(
                        "VideoBillboardTask Batch Error",
                        "批量保存视频榜数据失败",
                        error=str(e),
                        batch_size=len(remaining)
                    )

            execution_time = time.time() - start_time
            log_info("VideoBillboardTask Result Success", 
                    f"任务({self.sub_type}-{self.date_window}-{self.tag1}-{self.tag2})视频榜单任务完成",
                    total=len(data),
                    saved=saved_count,
                    failed=error_count,
                    execution_time=f"{execution_time:.2f}s")

            return TaskResult(
                success=True, 
                result={
                    "task_id": self.task_id,
                    "total": len(data),
                    "saved": saved_count,
                    "failed": error_count,
                    "execution_time": execution_time
                }
            )

        except Exception as e:
            log_error("VideoBillboardTask Result Failure", 
                    f"任务({self.sub_type}-{self.date_window}-{self.tag1}-{self.tag2})视频榜单任务异常",
                    error=str(e))
            return TaskResult(success=False, error=str(e))


class ChallengeBillboardTask(BaseCrawlerTask):
    """话题榜单任务"""
    
    def __init__(
        self,
        task_id: str,
        crawler: DouhotAsyncCrawler,
        sub_type: int,
        date_window: int,
        tag1: int,
        tag2: int,
        page_size: int = 50,
        page_upper_limit: int = 20,
        batch_size: int = 100,
        priority: TaskPriority = TaskPriority.MEDIUM
    ):
        super().__init__(task_id, priority)
        self.crawler = crawler
        self.sub_type = sub_type
        self.date_window = date_window
        self.tag1 = tag1
        self.tag2 = tag2
        self.page_size = page_size
        self.page_upper_limit = page_upper_limit
        self.batch_size = batch_size
        
    async def execute(self, db: Session, **kwargs) -> TaskResult:
        try:
            # 获取话题榜单数据
            data, task_msg = await self.crawler.fetch_challenge_billboard(
                sub_type=self.sub_type,
                date_window=self.date_window,
                tag1=self.tag1,
                tag2=self.tag2,
                page_size=self.page_size,
                page_upper_limit=self.page_upper_limit
            )
            if not data:
                return TaskResult(success=True, result=[])
            if task_msg:
                return TaskResult(success=False, error=task_msg)          
        except Exception as e:
            log_error("ChallengeBillboardTask Result Failure", 
                        f"任务({self.sub_type}-{self.date_window}-{self.tag1}-{self.tag2})话题榜单任务, msg: {task_msg}")
            return TaskResult(success=False, error=str(e))

        # 批量保存到数据库
        start_time = time.time()
        processor = BatchProcessor(self.batch_size)
        repo = DouhotBillboardTopicRepository(db)
        saved_count = 0
        error_count = 0

        try:
            for item in data:
                if processor.add(item):
                    # 处理当前批次
                    batch = processor.get_batch()
                    try:
                        for topic in batch:
                            await repo.upsert_topic(topic)
                        db.commit()
                        saved_count += len(batch)
                    except Exception as e:
                        db.rollback()
                        error_count += len(batch)
                        log_error(
                            "ChallengeBillboardTask Batch Error",
                            "批量保存话题榜数据失败",
                            error=str(e),
                            batch_size=len(batch)
                        )

            # 处理剩余项目
            remaining = processor.remaining_items()
            if remaining:
                try:
                    for topic in remaining:
                        await repo.upsert_topic(topic)
                    db.commit()
                    saved_count += len(remaining)
                except Exception as e:
                    db.rollback()
                    error_count += len(remaining)
                    log_error(
                        "ChallengeBillboardTask Batch Error",
                        "批量保存话题榜数据失败",
                        error=str(e),
                        batch_size=len(remaining)
                    )

            execution_time = time.time() - start_time
            log_info("ChallengeBillboardTask Result Success", 
                    f"任务({self.sub_type}-{self.date_window}-{self.tag1}-{self.tag2})话题榜单任务完成",
                    total=len(data),
                    saved=saved_count,
                    failed=error_count,
                    execution_time=f"{execution_time:.2f}s")

            return TaskResult(
                success=True, 
                result={
                    "task_id": self.task_id,
                    "total": len(data),
                    "saved": saved_count,
                    "failed": error_count,
                    "execution_time": execution_time
                }
            )

        except Exception as e:
            log_error("ChallengeBillboardTask Result Failure", 
                    f"任务({self.sub_type}-{self.date_window}-{self.tag1}-{self.tag2})话题榜单任务异常",
                    error=str(e))
            return TaskResult(success=False, error=str(e))


class SearchBillboardTask(BaseCrawlerTask):
    """搜索榜单任务"""
    
    def __init__(
        self,
        task_id: str,
        crawler: DouhotAsyncCrawler,
        sub_type: str,
        date_window: str,
        page_size: int = 50,
        page_upper_limit: int = 20,
        batch_size: int = 100,
        priority: TaskPriority = TaskPriority.MEDIUM
    ):
        super().__init__(task_id, priority)
        self.crawler = crawler
        self.sub_type = sub_type
        self.date_window = date_window
        self.page_size = page_size
        self.page_upper_limit = page_upper_limit
        self.batch_size = batch_size
        
    async def execute(self, db: Session, **kwargs) -> TaskResult:
        try:
            # 获取搜索榜单数据
            data, task_msg = await self.crawler.fetch_search_billboard(
                sub_type=self.sub_type,
                date_window=self.date_window,
                page_size=self.page_size,
                page_upper_limit=self.page_upper_limit
            )
            if not data:
                return TaskResult(success=True, result=[])
            if task_msg:
                return TaskResult(success=False, error=task_msg)          
        except Exception as e:
            log_error("SearchBillboardTask Result Failure", 
                        f"任务({self.sub_type}-{self.date_window})搜索榜单任务, msg: {task_msg}")
            return TaskResult(success=False, error=str(e))

        # 批量保存到数据库
        start_time = time.time()
        processor = BatchProcessor(self.batch_size)
        repo = DouhotBillboardSearchRepository(db)
        saved_count = 0
        error_count = 0

        try:
            for item in data:
                if processor.add(item):
                    # 处理当前批次
                    batch = processor.get_batch()
                    try:
                        for search in batch:
                            await repo.upsert_search(search)
                        db.commit()
                        saved_count += len(batch)
                    except Exception as e:
                        db.rollback()
                        error_count += len(batch)
                        log_error(
                            "SearchBillboardTask Batch Error",
                            "批量保存搜索榜数据失败",
                            error=str(e),
                            batch_size=len(batch)
                        )

            # 处理剩余项目
            remaining = processor.remaining_items()
            if remaining:
                try:
                    for search in remaining:
                        await repo.upsert_search(search)
                    db.commit()
                    saved_count += len(remaining)
                except Exception as e:
                    db.rollback()
                    error_count += len(remaining)
                    log_error(
                        "SearchBillboardTask Batch Error",
                        "批量保存搜索榜数据失败",
                        error=str(e),
                        batch_size=len(remaining)
                    )

            execution_time = time.time() - start_time
            log_info("SearchBillboardTask Result Success", 
                    f"任务({self.sub_type}-{self.date_window})搜索榜单任务完成",
                    total=len(data),
                    saved=saved_count,
                    failed=error_count,
                    execution_time=f"{execution_time:.2f}s")

            return TaskResult(
                success=True, 
                result={
                    "task_id": self.task_id,
                    "total": len(data),
                    "saved": saved_count,
                    "failed": error_count,
                    "execution_time": execution_time
                }
            )

        except Exception as e:
            log_error("SearchBillboardTask Result Failure", 
                    f"任务({self.sub_type}-{self.date_window})搜索榜单任务异常",
                    error=str(e))
            return TaskResult(success=False, error=str(e))
