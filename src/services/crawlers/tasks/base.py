"""
爬虫任务基础类和任务管理器
"""

from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional, TypeVar, Generic, Tuple
from enum import Enum
from dataclasses import dataclass
import asyncio
import time
from datetime import datetime
import logging
from sqlalchemy.orm import Session
from src.utils.logger import log_info, log_warning, log_error

T = TypeVar('T')

class TaskStatus(Enum):
    """任务状态"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    RETRYING = "retrying"
    CANCELLED = "cancelled"

class TaskPriority(Enum):
    """任务优先级"""
    HIGH = 0
    MEDIUM = 1
    LOW = 2

@dataclass
class TaskResult(Generic[T]):
    """任务执行结果"""
    success: bool
    result: Optional[T] = None
    error: Optional[Exception] = None
    retry_count: int = 0
    execution_time: float = 0.0

@dataclass
class TaskStats:
    """任务统计"""
    total_tasks: int = 0
    completed_tasks: int = 0
    failed_tasks: int = 0
    pending_tasks: int = 0
    running_tasks: int = 0
    cancelled_tasks: int = 0
    total_execution_time: float = 0.0
    total_retry_count: int = 0
    
    @property
    def success_rate(self) -> float:
        completed = self.completed_tasks
        total = completed + self.failed_tasks
        return completed / total if total > 0 else 0.0
    
    @property
    def average_execution_time(self) -> float:
        completed = self.completed_tasks
        return self.total_execution_time / completed if completed > 0 else 0.0
    
    def __str__(self) -> str:
        return (
            f"总任务数: {self.total_tasks}, "
            f"完成: {self.completed_tasks}, "
            f"失败: {self.failed_tasks}, "
            f"等待中: {self.pending_tasks}, "
            f"运行中: {self.running_tasks}, "
            f"已取消: {self.cancelled_tasks}, "
            f"成功率: {self.success_rate:.2%}, "
            f"平均执行时间: {self.average_execution_time:.2f}s"
        )

class BaseCrawlerTask(ABC):
    """爬虫任务基类"""
    
    def __init__(self, task_id: str, priority: TaskPriority = TaskPriority.MEDIUM):
        self.task_id = task_id
        self.priority = priority
        self.status = TaskStatus.PENDING
        self.result: Optional[TaskResult] = None
        self.created_at = datetime.now()
        self.started_at: Optional[datetime] = None
        self.completed_at: Optional[datetime] = None
        self._cancelled = False
        
    @abstractmethod
    async def execute(self, db: Session, **kwargs) -> TaskResult:
        """执行任务"""
        pass
    
    def cancel(self):
        """取消任务"""
        self._cancelled = True
        self.status = TaskStatus.CANCELLED

class TaskQueue:
    """任务队列管理器"""
    
    def __init__(
        self,
        max_concurrent_tasks: int = 10,
        max_retries: int = 3,
        retry_delay: float = 1.0,
        batch_size: int = 50,
        logger: Optional[logging.Logger] = None
    ):
        self._queue: asyncio.PriorityQueue = asyncio.PriorityQueue()
        self._semaphore = asyncio.Semaphore(max_concurrent_tasks)
        self._tasks: Dict[str, BaseCrawlerTask] = {}
        self._max_retries = max_retries
        self._retry_delay = retry_delay
        self._batch_size = batch_size
        self._logger = logger or logging.getLogger(__name__)
        self._running = False
        self._workers: List[asyncio.Task] = []
        self._stats = TaskStats()
        
    async def add_task(self, task: BaseCrawlerTask) -> None:
        """添加任务到队列"""
        if task.task_id in self._tasks:
            raise ValueError(f"Task with id {task.task_id} already exists")
            
        self._tasks[task.task_id] = task
        self._stats.total_tasks += 1
        self._stats.pending_tasks += 1
        await self._queue.put((task.priority.value, task.task_id))
        log_info("TaskQueue", f"任务 {task.task_id} 已添加到队列，优先级 {task.priority.name}")

    async def start(self, db: Session, num_workers: int = 3):
        """启动任务队列处理"""
        if self._running:
            return
            
        self._running = True
        self._workers = [
            asyncio.create_task(self._worker(db, i))
            for i in range(num_workers)
        ]
        log_info("TaskQueue", f"启动 {num_workers} 个工作协程")

    async def stop(self):
        """停止任务队列处理"""
        self._running = False
        for worker in self._workers:
            worker.cancel()
        await asyncio.gather(*self._workers, return_exceptions=True)
        self._workers.clear()
        log_info("TaskQueue", "任务队列已停止")

    async def _worker(self, db: Session, worker_id: int):
        """工作协程"""
        log_info("TaskQueue", f"工作协程 {worker_id} 已启动")
        
        while self._running:
            try:
                # 批量获取任务
                batch = []
                try:
                    for _ in range(self._batch_size):
                        _, task_id = await self._queue.get()
                        task = self._tasks[task_id]
                        if not task._cancelled:
                            batch.append((task_id, task))
                except asyncio.QueueEmpty:
                    pass
                
                if not batch:
                    await asyncio.sleep(0.1)  # 避免空轮询
                    continue
                
                # 并发执行批量任务
                async with self._semaphore:
                    tasks = []
                    for task_id, task in batch:
                        self._stats.pending_tasks -= 1
                        self._stats.running_tasks += 1
                        task.status = TaskStatus.RUNNING
                        task.started_at = datetime.now()
                        tasks.append(self._execute_task(task, db))
                    
                    results = await asyncio.gather(*tasks, return_exceptions=True)
                    
                    # 处理结果
                    for (task_id, task), result in zip(batch, results):
                        if isinstance(result, Exception):
                            task.status = TaskStatus.FAILED
                            task.result = TaskResult(success=False, error=result)
                            self._stats.failed_tasks += 1
                        else:
                            task.result = result
                            if result.success:
                                task.status = TaskStatus.COMPLETED
                                self._stats.completed_tasks += 1
                            else:
                                task.status = TaskStatus.FAILED
                                self._stats.failed_tasks += 1
                        
                        self._stats.running_tasks -= 1
                        task.completed_at = datetime.now()
                        self._queue.task_done()
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                log_error("TaskQueue", f"工作协程 {worker_id} 错误: {str(e)}")
                continue
        
        log_info("TaskQueue", f"工作协程 {worker_id} 已停止")

    async def _execute_task(self, task: BaseCrawlerTask, db: Session) -> TaskResult:
        """执行单个任务"""
        start_time = time.time()
        retry_count = 0
        last_error = None
        
        while retry_count <= self._max_retries and not task._cancelled:
            try:
                result = await task.execute(db)
                if result.success:
                    execution_time = time.time() - start_time
                    result.execution_time = execution_time
                    result.retry_count = retry_count
                    self._stats.total_execution_time += execution_time
                    self._stats.total_retry_count += retry_count
                    return result
                
                raise Exception(result.error)
                
            except Exception as e:
                last_error = e
                retry_count += 1
                if retry_count <= self._max_retries:
                    task.status = TaskStatus.RETRYING
                    delay = self._retry_delay * (2 ** (retry_count - 1))  # 指数退避
                    log_warning("TaskQueue", 
                              f"任务 {task.task_id} 失败，正在重试 ({retry_count}/{self._max_retries})",
                              error=str(e),
                              retry_delay=delay)
                    await asyncio.sleep(delay)
        
        execution_time = time.time() - start_time
        self._stats.total_execution_time += execution_time
        self._stats.total_retry_count += retry_count
        
        return TaskResult(
            success=False,
            error=last_error,
            retry_count=retry_count,
            execution_time=execution_time
        )

    async def wait_for_tasks(self):
        """等待所有任务完成"""
        await self._queue.join()

    def get_queue_size(self) -> int:
        """获取队列中的任务数量"""
        return self._queue.qsize()

    def get_active_tasks(self) -> List[str]:
        """获取正在执行的任务ID列表"""
        return [
            task_id
            for task_id, task in self._tasks.items()
            if task.status == TaskStatus.RUNNING
        ]

    def get_task_statistics(self) -> Dict[str, Any]:
        """获取任务统计信息"""
        return {
            "total_tasks": self._stats.total_tasks,
            "completed_tasks": self._stats.completed_tasks,
            "failed_tasks": self._stats.failed_tasks,
            "pending_tasks": self._stats.pending_tasks,
            "running_tasks": self._stats.running_tasks,
            "cancelled_tasks": self._stats.cancelled_tasks,
            "success_rate": f"{self._stats.success_rate:.2%}",
            "average_execution_time": f"{self._stats.average_execution_time:.2f}s",
            "total_retry_count": self._stats.total_retry_count
        }
