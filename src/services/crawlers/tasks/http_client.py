"""
异步HTTP客户端
"""

import aiohttp
import asyncio
from typing import Any, Dict, Optional
import json
import time
from dataclasses import dataclass, field
from src.utils.logger import log_info, log_warning, log_error

@dataclass
class RequestStats:
    """请求统计"""
    total_requests: int = 0
    successful_requests: int = 0
    failed_requests: int = 0
    total_retry_count: int = 0
    total_request_time: float = 0.0
    
    def add_request(self, success: bool, retry_count: int, request_time: float):
        self.total_requests += 1
        if success:
            self.successful_requests += 1
        else:
            self.failed_requests += 1
        self.total_retry_count += retry_count
        self.total_request_time += request_time
        
    @property
    def success_rate(self) -> float:
        return self.successful_requests / self.total_requests if self.total_requests > 0 else 0.0
    
    @property
    def average_request_time(self) -> float:
        return self.total_request_time / self.total_requests if self.total_requests > 0 else 0.0
    
    def __str__(self) -> str:
        return (
            f"总请求数: {self.total_requests}, "
            f"成功率: {self.success_rate:.2%}, "
            f"平均请求时间: {self.average_request_time:.2f}s, "
            f"总重试次数: {self.total_retry_count}"
        )

class AsyncHttpClient:
    """异步HTTP客户端"""
    
    def __init__(
        self,
        base_url: str,
        cookies: Optional[str] = None,
        timeout: int = 60,
        max_retries: int = 3,
        retry_delay: float = 1.0,
        max_concurrent_requests: int = 10,
        connector_limit_per_host: int = 30
    ):
        self.cookies = cookies
        self.base_url = base_url
        self.timeout = timeout
        self.max_retries = max_retries
        self.retry_delay = retry_delay
        self.max_concurrent_requests = max_concurrent_requests
        self.connector_limit_per_host = connector_limit_per_host
        self._session: Optional[aiohttp.ClientSession] = None
        self._semaphore = asyncio.Semaphore(max_concurrent_requests)
        self.stats = RequestStats()
        
    async def __aenter__(self):
        await self.create_session()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.close()
        
    async def create_session(self):
        """创建会话"""
        if not self._session:
            headers = {
                "authority": self.base_url,
                "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.114 Safari/537.36",
                "Accept": "application/json, text/plain, */*",
                "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
                "Cookie": self.cookies if self.cookies else ""
            }
            
            # 使用TCPConnector配置连接池
            connector = aiohttp.TCPConnector(
                limit_per_host=self.connector_limit_per_host,
                force_close=False,
                enable_cleanup_closed=True
            )
            
            self._session = aiohttp.ClientSession(
                headers=headers,
                timeout=aiohttp.ClientTimeout(total=self.timeout),
                connector=connector
            )
            
    async def close(self):
        """关闭会话"""
        if self._session:
            if not self._session.closed:
                await self._session.close()
            self._session = None
            
    async def request(
        self,
        method: str,
        url: str,
        params: Optional[Dict] = None,
        data: Optional[Dict] = None,
        headers: Optional[Dict] = None
    ) -> Dict[str, Any]:
        """发送请求"""
        if not self._session:
            await self.create_session()
            
        retries = 0
        last_error = None
        start_time = time.time()
        success = False
        
        async with self._semaphore:  # 使用信号量控制并发
            while retries <= self.max_retries:
                try:
                    async with self._session.request(
                        method=method,
                        url=url,
                        params=params,
                        json=data,
                        headers=headers
                    ) as response:
                        response.raise_for_status()
                        result = await response.json()
                        success = True
                        return result
                        
                except aiohttp.ClientError as e:
                    last_error = e
                    retries += 1
                    if retries <= self.max_retries:
                        # 使用指数退避策略
                        delay = self.retry_delay * (2 ** (retries - 1))
                        log_warning("AsyncHttpClient", 
                                  f"请求失败，正在重试 ({retries}/{self.max_retries})",
                                  url=url,
                                  error=str(e),
                                  retry_delay=delay)
                        await asyncio.sleep(delay)
                        continue
                    raise last_error
                finally:
                    request_time = time.time() - start_time
                    self.stats.add_request(success, retries, request_time)
                
    async def get(self, url: str, params: Optional[Dict] = None, **kwargs) -> Dict[str, Any]:
        """GET请求"""
        return await self.request("GET", url, params=params, **kwargs)
        
    async def post(self, url: str, data: Optional[Dict] = None, **kwargs) -> Dict[str, Any]:
        """POST请求"""
        return await self.request("POST", url, data=data, **kwargs)
        
    def get_stats(self) -> str:
        """获取请求统计信息"""
        return str(self.stats)
