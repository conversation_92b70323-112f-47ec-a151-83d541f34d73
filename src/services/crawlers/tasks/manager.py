"""
任务管理器和任务工厂
"""

import uuid
import asyncio
from typing import Dict, List, Any, Type
from sqlalchemy.orm import Session

from .base import BaseCrawlerTask, TaskQueue, TaskPriority
from .douhot_tasks import (
    VideoBillboardTask,
    ChallengeBillboardTask,
    SearchBillboardTask
)
from .douyin_tasks import (
    DouyinHotSearchTask
)
from .douhot_async_crawler import (
    DouhotAsyncCrawler
)
from .douyin_async_crawler import (
    DouyinAsyncCrawler
)
from src.utils.logger import log_info

class TaskFactory:
    """任务工厂"""
    
    @staticmethod
    def create_douhot_video_billboard_task(
        crawler: DouhotAsyncCrawler,
        sub_type: int,
        date_window: int,
        tag1: int,
        tag2: int,
        page_size: int = 50,
        page_upper_limit: int = 20
    ) -> BaseCrawlerTask:
        """创建单个视频榜单任务"""
        task_id = str(uuid.uuid4())
        return VideoBillboardTask(
            task_id=task_id,
            crawler=crawler,
            sub_type=sub_type,
            date_window=date_window,
            tag1=tag1,
            tag2=tag2,
            page_size=page_size,
            page_upper_limit=page_upper_limit
        )
        
    @staticmethod
    def create_douhot_challenge_billboard_task(
        crawler: DouhotAsyncCrawler,
        sub_type: int,
        date_window: int,
        tag1: int,
        tag2: int,
        page_size: int = 50,
        page_upper_limit: int = 20
    ) -> BaseCrawlerTask:
        """创建单个话题榜单任务"""
        task_id = str(uuid.uuid4())
        return ChallengeBillboardTask(
            task_id=task_id,
            crawler=crawler,
            sub_type=sub_type,
            date_window=date_window,
            tag1=tag1,
            tag2=tag2,
            page_size=page_size,
            page_upper_limit=page_upper_limit
        )
        
    @staticmethod
    def create_douhot_search_billboard_task(
        crawler: DouhotAsyncCrawler,
        sub_type: str,
        date_window: str,
        page_size: int = 50,
        page_upper_limit: int = 20
    ) -> BaseCrawlerTask:
        """创建单个搜索榜单任务"""
        task_id = str(uuid.uuid4())
        return SearchBillboardTask(
            task_id=task_id,
            crawler=crawler,
            sub_type=sub_type,
            date_window=date_window,
            page_size=page_size,
            page_upper_limit=page_upper_limit
        )

    @staticmethod
    def create_douyin_hot_search_task(
        crawler: DouyinAsyncCrawler,
        sub_type: str
    ) -> BaseCrawlerTask:
        """创建单个搜索榜单任务"""
        task_id = str(uuid.uuid4())
        return DouyinHotSearchTask(
            task_id=task_id,
            crawler=crawler,
            sub_type=sub_type
        )


class CrawlerTaskManager:
    """爬虫任务管理器"""
    
    def __init__(self, max_concurrent_tasks: int = 5):
        self.max_concurrent_tasks = max_concurrent_tasks
        self.tasks: List[BaseCrawlerTask] = []
        self.completed_tasks: List[BaseCrawlerTask] = []
        self.failed_tasks: List[BaseCrawlerTask] = []
        self.semaphore = asyncio.Semaphore(max_concurrent_tasks)
        
    async def schedule_tasks(self, tasks: List[BaseCrawlerTask]):
        """调度任务"""
        self.tasks.extend(tasks)
        
    async def run_task(self, task: BaseCrawlerTask, db: Session):
        """运行单个任务"""
        async with self.semaphore:
            try:
                result = await task.execute(db)
                if result.success:
                    self.completed_tasks.append(task)
                else:
                    self.failed_tasks.append(task)
                return result
            except Exception as e:
                self.failed_tasks.append(task)
                return None
                
    async def run_tasks(self, db: Session):
        """运行所有任务"""
        tasks = [self.run_task(task, db) for task in self.tasks]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        return results
        
    def get_task_statistics(self) -> Dict[str, Any]:
        """获取任务统计"""
        total_tasks = len(self.tasks)
        completed_tasks = len(self.completed_tasks)
        failed_tasks = len(self.failed_tasks)
        
        return {
            "total_tasks": total_tasks,
            "completed_tasks": completed_tasks,
            "failed_tasks": failed_tasks,
            "success_rate": (completed_tasks / total_tasks * 100) if total_tasks > 0 else 0,
            "average_execution_time": 0  # TODO: 实现执行时间统计
        }
