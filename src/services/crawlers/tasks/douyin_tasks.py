from typing import Any
from sqlalchemy.orm import Session
import time

from src.services.crawlers.tasks.base import BaseCrawlerTask, TaskPriority, TaskResult
from src.services.crawlers.tasks.douyin_async_crawler import DouyinAsyncCrawler
from src.domain.douyin_hot_search import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>earch, DouyinHotSearchRepository
from src.utils.logger import log_info, log_warning, log_error


class DouyinHotSearchTask(BaseCrawlerTask):
    def __init__(
        self,
        task_id: str,
        crawler: DouyinAsyncCrawler,  # 这里替换为实际的DouyinCrawler类型
        sub_type: str,
        priority: TaskPriority = TaskPriority.MEDIUM
    ):
        super().__init__(task_id, priority)
        self.crawler = crawler
        self.sub_type = sub_type
    
    async def execute(self, db: Session, **kwargs) -> None:
        try:
            datas, task_msg = await self.crawler.fetch_hot_search_list(self.sub_type)
            if not datas:
                return TaskResult(success=True, result=[])
            if task_msg:
                return TaskResult(success=False, error=task_msg)
        except Exception as e:
            log_error("DouyinHotSearchTask", f"获取视频榜单失败: {str(e)}")
            return TaskResult(success=False, error=str(e))
        repo = DouyinHotSearchRepository(db)
        try:
            for item in datas:
                await repo.upsert(item)
            db.commit() 
        except Exception as e:
            db.rollback()
            log_error("DouyinHotSearchTask", f"批量保存视频榜数据失败: {str(e)}")
            return TaskResult(success=False, error=str(e))
        return TaskResult(success=True, result=datas)
