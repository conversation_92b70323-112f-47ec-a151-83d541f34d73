"""
抖音热榜异步爬虫实现
"""

from typing import Dict, List, Optional, Any
import asyncio
from datetime import datetime, timedelta

from src.utils.logger import log_error, log_warning, log_info
from .http_client import AsyncHttpClient

class DouyinAsyncCrawler:
    """抖音热榜异步爬虫"""
    
    def __init__(
        self,
        cookies: Optional[str] = None
    ):        
        # 视频榜单
        self.douyin_client = AsyncHttpClient(
            base_url="www.douyin.com",
            cookies=""
        )
        self.douyin_hot_search_url="https://aweme.snssdk.com/aweme/v1/hot/search/list/"
        
    async def __aenter__(self):
        await asyncio.gather(
            self.douyin_client.create_session(),
        )
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await asyncio.gather(
            self.douyin_client.close()
        )
        
    async def fetch_hot_search_list(
                self,
                sub_type: str
    ) -> List[Dict[str, Any]]:
        """获取抖音热搜榜单数据"""
        datas = []
        params = {
            'device_platform': 'android',
            'version_name': '13.2.0',
            'version_code': '130200',
            'aid': '1128'
        }
        try: 
            raw_data = await self.douyin_client.get(self.douyin_hot_search_url, params=params)
            items = raw_data.get("data", {}).get("word_list", [])
            for item in items:
                word = item.get("word", "")
                if len(word) == 0: continue
                datas.append({
                    "board": sub_type,
                    "article_detail_count": item.get("article_detail_count", 0),
                    "aweme_infos": item.get("aweme_infos", []),
                    "can_extend_detail": item.get("can_extend_detail", False),
                    "discuss_video_count": item.get("discuss_video_count", 0),
                    "display_style": item.get("display_style", 0),
                    "drift_info": item.get("drift_info", {}),
                    "event_time": item.get("event_time", 0),
                    "group_id": item.get("group_id", ""),
                    "hot_value": item.get("hot_value", 0),
                    "hotlist_param": item.get("hotlist_param", ""),
                    "label": item.get("label", 0),
                    "label_url": item.get("label_url", ""),
                    "position": item.get("position", 0),
                    "related_words": item.get("related_words", []),
                    "sentence_id": item.get("sentence_id", ""),
                    "sentence_tag": item.get("sentence_tag", 0),
                    "video_count": item.get("video_count", 0),
                    "view_count": item.get("view_count", 0),
                    "word": word,
                    "word_cover": item.get("word_cover", {}),
                    "word_sub_board": item.get("word_sub_board", 0),
                    "word_type": item.get("word_type", 0),                    
                    "url": "https://www.douyin.com/hot/"+item.get("sentence_id", "")
                })
            return datas, None
            
        except Exception as e:
            print(f"获取视频榜单失败: {str(e)}")
            log_error("DouyinAsyncCrawler", f"获取视频榜单失败: {str(e)}")
            return [], f"获取视频榜单失败: {str(e)}"
            
   