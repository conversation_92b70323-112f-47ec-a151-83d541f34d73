"""
抖音热榜异步爬虫实现
"""

from typing import Dict, List, Optional, Any
import asyncio
from datetime import datetime, timedelta

from src.utils.logger import log_error, log_warning, log_info
from .http_client import AsyncHttpClient

class DouhotAsyncCrawler:
    """抖音热榜异步爬虫"""
    
    def __init__(
        self,
        cookies: Optional[str] = None
    ):        
        # 视频榜单
        self.video_client = AsyncHttpClient(
            base_url="douhot.douyin.com",
            cookies=cookies
        )
        
        # 话题榜单
        self.challenge_client = AsyncHttpClient(
            base_url="douhot.douyin.com",
            cookies=cookies
        )
        
        # # 搜索榜单
        self.search_client = AsyncHttpClient(
            base_url="douhot.douyin.com",
            cookies=cookies
        )
        self.video_billboard_post_url=f"https://douhot.douyin.com/douhot/v1/material/video_billboard"
        self.topic_billboard_post_url=f"https://douhot.douyin.com/douhot/v1/material/challenge_billboard"
        self.search_billboard_post_url=f"https://douhot.douyin.com/douhot/v1/dashboard/hot_search/query_list"
        
    async def __aenter__(self):
        await asyncio.gather(
            self.video_client.create_session(),
            self.challenge_client.create_session(),
            self.search_client.create_session()
        )
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await asyncio.gather(
            self.video_client.close(),
            self.challenge_client.close(),
            self.search_client.close()
        )
        
    async def fetch_video_billboard(
                self,
                sub_type : int,
                date_window : int,
                tag1 : int,
                tag2 : int,
                page_size : int,
                page_upper_limit : int
    ) -> List[Dict[str, Any]]:
        """获取视频榜单数据"""
        try:
            page = 1 #先开始第一页
            videos = []
            while True:
                if page and page > page_upper_limit:
                    break
                # 请求体
                params = {
                    "msToken": "",
                    "X-Bogus": "",
                    "_signature": ""
                }
                payload = {
                        "sub_type": sub_type,
                        "date_window": date_window,
                        "page": page,
                        "page_size": page_size,
                        "tag_version": "v2",
                        "tags": [
                                    {
                                        "value": tag1,
                                        "children": [
                                            {
                                                "value": tag2
                                            }
                                        ]
                                    }
                                ]
                    }
            
                raw_data = await self.video_client.post(
                    self.video_billboard_post_url,
                    data=payload,
                    params=params
                )
                items = raw_data.get("data", {}).get("objs", [])
                for item in items:
                    item_id = str(item.get("item_id", ""))
                    if len(item_id) == 0:
                        continue
                    video = {
                        "video_tag1": tag1,
                        "video_tag2": tag2   ,
                        "video_tag3": 0,
                        "billboard_type": sub_type,
                        "date_window": date_window,
                        "item_id": item_id,
                        "item_title": item.get("item_title", ""),
                        "item_cover_url": item.get("item_cover_url", ""),
                        "item_duration": int(item.get("item_duration", 0)),
                        "nick_name": item.get("nick_name", ""),
                        "avatar_url": item.get("avatar_url", ""),
                        "fans_cnt": int(item.get("fans_cnt", 0)),
                        "play_cnt": int(item.get("play_cnt", 0)),
                        "publish_time": datetime.fromtimestamp(item.get("publish_time", 0)),
                        "score": int(item.get("score", 0)),
                        "item_url": item.get("item_url", ""),
                        "like_cnt": int(item.get("like_cnt", 0)),
                        "follow_cnt": int(item.get("follow_cnt", 0)),
                        "follow_rate": float(item.get("follow_rate", 0)),
                        "like_rate": float(item.get("like_rate", 0)),
                        "media_type": int(item.get("media_type", 0)),
                        "favorite_id": item.get("favorite_id", ""),
                        "is_favorite": bool(item.get("is_favorite", False)),
                        "image_cnt": int(item.get("image_cnt", 0))
                    }
                    videos.append(video)

                # 判断是否继续读取下一页：还存在下一页
                pagination = raw_data.get("data", {})
                total_size = pagination.get("page", {}).get("total", 0)
                page_size = pagination.get("page", {}).get("page_size", 1)
                current_page = pagination.get("page", {}).get("page", 1)
                if current_page < total_size // page_size + 1:
                    page = current_page + 1
                else:
                    break
                
            return videos, None
            
        except Exception as e:
            log_error(f"获取视频榜单失败: {str(e)}")
            return [], f"获取视频榜单失败: {str(e)}"
            
    async def fetch_challenge_billboard(
                self,
                sub_type : int,
                date_window : int,
                tag1 : int,
                tag2 : int,
                page_size : int,
                page_upper_limit : int
    ) -> List[Dict[str, Any]]:
        """获取话题榜单数据"""
        try:
            page = 1 #先开始第一页
            topics = []
            while True:
                if page and page > page_upper_limit:
                    break
                # 请求体
                params = {
                    "msToken": "",
                    "X-Bogus": "",
                    "_signature": ""
                }
                payload = {
                        "sub_type": sub_type,
                        "date_window": date_window,
                        "page": page,
                        "page_size": page_size,
                        "tag_version": "v2",
                        "tags": [
                                    {
                                        "value": tag1,
                                        "children": [
                                            {
                                                "value": tag2
                                            }
                                        ]
                                    }
                                ]
                    }
            
                raw_data = await self.challenge_client.post(
                    self.topic_billboard_post_url,
                    data=payload,
                    params=params
                )
                items = raw_data.get("data", {}).get("objs", [])
                for item in items:
                    challenge_id = str(item.get("challenge_id", ""))
                    if len(challenge_id) == 0:
                        continue
                    topic = {
                        "topic_tag1": tag1,
                        "topic_tag2": tag2,
                        "topic_tag3": 0,
                        "billboard_type": sub_type,
                        "date_window": date_window,
                        "challenge_id": challenge_id,
                        "challenge_name": item.get("challenge_name", ""),
                        "play_cnt": int(item.get("play_cnt", 0)),
                        "publish_cnt": int(item.get("publish_cnt", 0)),
                        "cover_url": item.get("cover_url", ""),
                        "score": int(item.get("score", 0)),
                        "avg_play_cnt": int(item.get("avg_play_cnt", 0)),
                        "create_time": int(item.get("create_time", 0)),
                        "origin_trend_str": item.get("origin_trend_str", ""),
                        "trends": item.get("trends", []),
                        "challenge_type": int(item.get("challenge_type", 0)),
                        "item_list": item.get("item_list", []),
                        "is_favorite": bool(item.get("is_favorite", False)),
                        "is_recommend": bool(item.get("is_recommend", False)),
                        "show_rank": int(item.get("show_rank", 0)),
                        "real_rank": int(item.get("real_rank", 0)),
                        "origin_rank": int(item.get("origin_rank", 0)),
                        "related_event": item.get("related_event")
                    }
                    topics.append(topic)

                # 判断是否继续读取下一页：还存在下一页
                pagination = raw_data.get("data", {})
                total_size = pagination.get("page", {}).get("total", 0)
                page_size = pagination.get("page", {}).get("page_size", 1)
                current_page = pagination.get("page", {}).get("page", 1)
                if current_page < total_size // page_size + 1:
                    page = current_page + 1
                else:
                    break
            return topics, None
            
        except Exception as e:
            log_error("douhot_crawler fetch_challenge_billboard", f"获取话题榜单失败: {str(e)}")
            return [], f"获取话题榜单失败: {str(e)}"
            
    async def fetch_search_billboard(
        self,
        sub_type: str,
        date_window: str,
        page_size: int = 50,
        page_upper_limit: int = 20
    ) -> List[Dict[str, Any]]:
        """获取搜索榜单数据"""
        try:
            page = 1 #先开始第一页
            searches = []
            while True:
                if page and page > page_upper_limit:
                    log_info("douhot_crawler fetch_search_billboard",
                             "达到最大页数限制",
                             max_pages=page_upper_limit
                    )
                    break
                # 请求体
                params = {
                    "msToken": "",
                    "X-Bogus": "",
                    "_signature": ""
                }
                payload = {
                    "sub_type": sub_type,
                    "date_window": date_window,
                    "page_num": page,
                    "page_size": page_size,
                }
            
                raw_data = await self.search_client.post(
                    self.search_billboard_post_url,
                    data=payload,
                    params=params
                )
                items = raw_data.get("data", {}).get("search_list", [])
                # print(f">>>>>>>{items}")
                for item in items:
                    keyword = item.get("key_word", "").strip()
                    if len(keyword) == 0:  # Skip empty keywords
                        continue
                        
                    search = {
                        "billboard_type": sub_type,
                        "date_window": date_window,
                        "keyword": keyword,
                        "search_score": int(item.get("search_score", 0)),
                        "trends": item.get("trends", [])
                    }
                    searches.append(search)

                # 判断是否继续读取下一页：还存在下一页
                pagination = raw_data.get("data", {})
                total_size = pagination.get("total_count", 0)
                page_size = pagination.get("page_size", 1)
                current_page = pagination.get("page_num", 1)
                if current_page < total_size // page_size + 1:
                    page = current_page + 1
                else:
                    log_info("douhot_crawler fetch_search_billboard",
                             "超出最大数据页数",
                             max_pages=current_page
                    )
                    break
            return searches, None

        except Exception as e:
            log_error("douhot_crawler fetch_search_billboard", f"获取搜索榜单失败: {str(e)}")
            return [], f"获取搜索榜单失败: {str(e)}"
            
