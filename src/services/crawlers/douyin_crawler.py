"""抖音热榜爬虫"""
# 标准库导入
import os
import logging
import json
import contextlib
import re
from typing import List, Dict, Any, Optional
from datetime import datetime
from pathlib import Path

# 第三方库导入
import requests
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry
from sqlalchemy import create_engine
from sqlalchemy.orm import Session, sessionmaker

# 项目内导入
from src.domain.base import Base
from src.domain.hot_item import HotItemCreate, HotItemRepository
from src.config.settings import settings

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class DouyinCrawler:
    """抖音热榜爬虫"""
    
    def __init__(self, init_db: bool = True):
        """初始化爬虫
        
        Args:
            init_db: 是否初始化数据库连接，测试时可以设为False
        """
        if init_db:
            # 创建数据库连接
            self.engine = create_engine(settings.MYSQL_URL)
            self.SessionLocal = sessionmaker(bind=self.engine)
            
            # 确保数据库表存在
            Base.metadata.create_all(self.engine)
        
        # API配置
        self.base_url = "https://www.douyin.com"
        self.api_url = f"{self.base_url}/aweme/v1/web/hot/search/list/?device_platform=webapp&aid=6383&channel=channel_pc_web&detail_list=1"
        self.cookie_url = f"{self.base_url}/passport/general/login_guiding_strategy/?aid=6383"
        
        # 重试配置
        self.retries = Retry(
            total=3,
            backoff_factor=1,
            status_forcelist=[k for k in range(400, 600)]
        )

        # 请求头配置
        self.headers = {
            "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) "
                         "AppleWebKit/537.36 (KHTML, like Gecko) "
                         "Chrome/117.0.0.0 Safari/537.36",
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,*/*;q=0.8",
            "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
        }
    
    @contextlib.contextmanager
    def request_session(self):
        """创建请求会话"""
        s = requests.session()
        try:
            s.headers.update(self.headers)
            s.mount("http://", HTTPAdapter(max_retries=self.retries))
            s.mount("https://", HTTPAdapter(max_retries=self.retries))
            yield s
        finally:
            s.close()

    def get_cookie(self) -> str:
        """获取Cookie"""
        cookie_data = ""
        try:
            with self.request_session() as s:
                resp = s.get(self.cookie_url)
                regex = re.compile(r"passport_csrf_token=(.*?); Path=/;")
                result = re.match(regex, resp.headers["Set-Cookie"])
                if result:
                    cookie_data = result.group(1)
                return cookie_data
        except:
            logger.exception("获取抖音cookie失败")
        return cookie_data

    def get_raw_data(self, cookie: str) -> dict:
        """获取原始数据"""
        ret = {}
        try:
            with self.request_session() as s:
                s.headers.update({"Cookie": f"passport_csrf_token={cookie}"})
                resp = s.get(self.api_url)
                ret = resp.json()
        except:
            logger.exception("获取数据失败")
        return ret

    def clean_raw_data(self, raw_data: dict) -> List[Dict[str, Any]]:
        """清洗原始数据"""
        ret = []
        for item in raw_data["data"]["word_list"]:
            ret.append({
                "title": item["word"],
                "article_detail_count": item["article_detail_count"],
                "can_extend_detail": item["can_extend_detail"],
                "discuss_video_count": item["discuss_video_count"],
                "display_style": item["display_style"],
                "event_time": datetime.fromtimestamp(item["event_time"]).isoformat() if item.get("event_time") else None,
                "group_id": item["group_id"],
                "hot_value": item["hot_value"],
                "hotlist_param": item["hotlist_param"],
                "label": item.get("label", ""),
                "label_url": item.get("label_url", ""),
                "max_rank": item["max_rank"],
                "position": item["position"],
                "sentence_id": item["sentence_id"],
                "sentence_tag": item.get("sentence_tag", ""),
                "video_count": item["video_count"],
                "word": item["word"],
                "word_cover": item.get("word_cover", {}),
                "word_type": item["word_type"],
                "url": f"https://www.douyin.com/hot/{item['sentence_id']}"
            })
        return ret

    def get_db(self) -> Session:
        """获取数据库会话"""
        db = self.SessionLocal()
        try:
            return db
        except:
            db.close()
            raise

    async def get_trending_list(self) -> list:
        """获取热榜数据"""
        try:
            # 获取Cookie
            cookie = self.get_cookie()
            if not cookie:
                raise Exception("获取Cookie失败")

            # 获取原始数据
            raw_data = self.get_raw_data(cookie)
            if not raw_data:
                raise Exception("获取原始数据失败")

            # 清洗数据
            clean_data = self.clean_raw_data(raw_data)
            
            return clean_data

        except Exception as e:
            logger.error(f"获取热榜失败: {str(e)}")
            return []

    def save_items(self, items: List[Dict[str, Any]]) -> int:
        """保存数据到数据库
        
        Args:
            items: 要保存的数据列表
            
        Returns:
            成功保存的数据条数
        """
        if not items or not hasattr(self, 'engine'):
            return 0
            
        db = self.get_db()
        try:
            # 转换为HotItemCreate对象
            hot_items = []
            for item in items:
                hot_item = HotItemCreate(
                    title=item["title"],
                    url=item["url"],
                    sentence_id=item["sentence_id"],
                    hot_value=item["hot_value"],
                    article_detail_count=item["article_detail_count"],
                    can_extend_detail=item["can_extend_detail"],
                    discuss_video_count=item["discuss_video_count"],
                    display_style=item["display_style"],
                    event_time=item["event_time"],
                    group_id=item["group_id"],
                    hotlist_param=item["hotlist_param"],
                    label=item["label"],
                    label_url=item["label_url"],
                    max_rank=item["max_rank"],
                    position=item["position"],
                    sentence_tag=item["sentence_tag"],
                    video_count=item["video_count"],
                    word=item["word"],
                    word_cover=item["word_cover"],
                    word_type=item["word_type"]
                )
                hot_items.append(hot_item)

            repository = HotItemRepository(db)
            # 过滤已存在的数据
            new_items = []
            for item in hot_items:
                if not repository.get_by_sentence_id(item.sentence_id):
                    new_items.append(item)
            
            # 保存新数据
            if new_items:
                repository.create_many(new_items)
                logger.info(f"成功保存{len(new_items)}条热榜数据")
                return len(new_items)
            return 0
        finally:
            db.close()

    def save_test_data(self, items: List[Dict[str, Any]]):
        """保存测试数据到文件"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            save_path = Path(__file__).parent.parent / "data" / f"douyin_hot_test_{timestamp}.json"
            save_path.parent.mkdir(parents=True, exist_ok=True)
            with open(save_path, 'w', encoding='utf-8') as f:
                json.dump({
                    "update_time": datetime.now().isoformat(),
                    "data": items
                }, f, ensure_ascii=False, indent=2)
            logger.info(f"测试数据已保存到: {save_path}")
        except:
            logger.exception("保存测试数据失败")


# 测试代码
if __name__ == "__main__":
    import asyncio
    import pprint
    
    async def test_crawler():
        crawler = DouyinCrawler(init_db=True)  # 测试模式不初始化数据库
        
        print("测试获取Cookie:")
        cookie = crawler.get_cookie()
        print(f"Cookie: {cookie}\n")
        
        print("测试获取清洗后的热榜数据:")
        hot_list = await crawler.get_trending_list()
        print(f"获取到 {len(hot_list)} 条热榜数据:")
        pprint.pprint(hot_list[:3])
        print()
        
        print("保存测试数据到文件:")
        crawler.save_test_data(hot_list)
    
    # 运行测试
    asyncio.run(test_crawler())
