douhot_category_mapping = {
    # 一级分类 (first_tag) -> 二级分类 (second_tag)
    628: {
        "name": "美食",
        "sub_categories": {
            62808: "品酒教学",
            62804: "美食教程",
            62806: "美食知识",
            62803: "美食测评",
            62802: "美食展示",
            62801: "美食探店",
            62811: "美食其它",
            62807: "饮食文化",
            62805: "吃播",
            62810: "美食营销"
        }
    },
    629: {
        "name": "旅行",
        "sub_categories": {
            62905: "旅行风景",
            62913: "旅行记录",
            62912: "旅行推荐与攻略",
            62908: "户外生活",
            62906: "旅行机构与营销",
            62907: "酒店/民宿",
            62904: "旅行日常",
            62909: "旅行其他",
            62911: "旅行营销",
            62903: "旅行文化",
            62910: "旅行见闻",
            62902: "旅行攻略",
            62901: "旅行推荐"
        }
    },
    634: {
        "name": "休闲娱乐",
        "sub_categories": {
            63407: "逛街",
            63412: "现场演出",
            63410: "传统休闲",
            63406: "演出二创&资讯",
            63411: "运动玩乐",
            63414: "采摘/农家乐",
            63404: "实操技巧",
            63413: "保健养生",
            63408: "休娱其他",
            63402: "IP测评&科普",
            63409: "推理桌游",
            63401: "探店攻略/评测",
            63405: "实拍&实录"
        }
    },
    624: {
        "name": "文化",
        "sub_categories": {
            62401: "传统文化",
            62415: "武术",
            62402: "人文",
            62408: "传统建筑",
            62407: "传统美术",
            62413: "戏曲",
            62417: "山歌",
            62405: "博物馆",
            62403: "人文综合",
            62414: "传统手工艺",
            62406: "书法",
            62412: "曲艺",
            62410: "非遗综合",
            62404: "品牌历史",
            62419: "其他传统文化",
            62416: "古董文玩",
            62411: "茶道",
            62409: "民俗",
            62418: "游艺与杂技",
            62420: "传统服饰"
        }
    }
}

# video_billboard_tag_first_type= {"美食": 2, 
#                                 "旅行": 2, "休闲娱乐": 2, "文化": 2, "舞蹈": 2, 
#                                 "教育校园": 2, "公益": 2, "艺术": 2, "时尚": 2, "旅行": 2, 
#                                 "三农": 2, "汽车": 2, "二次元": 2, "体育": 2, "动物": 2, 
#                                 "医疗健康": 2, "社会时政": 2, "音乐": 2, "随拍": 2, "电视剧": 2, 
#                                 "摄影摄像": 2, "生活家居": 2, "情感": 2, "人文社科": 2, "电影": 2, 
#                                 "科普": 2, "明星": 2, "财经": 2, "职场": 2, "科技": 2, "母婴": 2, 
#                                 "综艺": 2, "生活记录": 2, "亲子": 2, "法律": 2, "剧情": 2, "游戏": 2}

douhot_date_window= {1: "近1小时", 24: "近1天", 72: "近3天", 168: "近7天"}
douhot_page_size = 50

# 视频榜：https://douhot.douyin.com/douhot/v1/material/video_billboard
'''
{
    "page": 1,
    "page_size": 50,
    "date_window": 24,
    "sub_type": 1001,
    "tag_version": "v2",
    "tags": [
        {
            "value": 624,
            "children": [
                {
                    "value": 62401
                },
                {
                    "value": 62415
                }
            ]
        }
    ]
}
'''
video_billboard_sub_type= {1001: "视频总榜", 1002: "低粉爆款", 1003: "高完播", 1004: "高涨粉", 1005: "高点赞"}


# 话题榜：https://douhot.douyin.com/douhot/v1/material/challenge_billboard
'''
{
    "page": 1,
    "page_size": 50,
    "date_window": 24,
    "sub_type": 2001,
    "tag_version": "v2",
    "tags": [
        {
            "value": 628,
            "children": [
                {
                    "value": 62808
                }
            ]
        }
    ]
}
'''
challenge_billboard_sub_type= {2001:"话题总榜", 2002: "热度飙升的话题榜"}


# 搜索榜：https://douhot.douyin.com/douhot/v1/dashboard/hot_search/query_list
'''
{
    "page_num": 1,
    "page_size": 50,
    "date_window": 24,
    "sub_type": 3001
}
'''
hot_search_query_list_sub_type= {3001: "搜索总榜", 3002: "热度飙升的搜索榜"}