"""
抖音热榜爬虫启动器
"""

import asyncio
from typing import List, Dict, Any, Optional
from sqlalchemy.orm import Session
from datetime import datetime
import itertools

from src.services.crawlers.tasks.douhot_async_crawler import DouhotAsyncCrawler
from src.services.crawlers.tasks.douyin_async_crawler import DouyinAsyncCrawler
from src.services.crawlers.tasks.manager import TaskFactory, CrawlerTaskManager
from src.services.crawlers.douhot_define import (
    video_billboard_sub_type,
    challenge_billboard_sub_type,
    hot_search_query_list_sub_type,
    douhot_date_window,
    douhot_page_size,
    douhot_category_mapping
)
from src.utils.logger import log_info, log_error

class CrawlerRunner:
    """抖音热榜爬虫运行器"""
    
    def __init__(
        self,
        cookies: Optional[str] = None,
        max_concurrent_tasks: int = 50
    ):
        self.douhot_crawler = DouhotAsyncCrawler(
            cookies=cookies
        )
        self.douyin_crawler = DouyinAsyncCrawler(
            cookies=cookies
        )
        self.task_manager = CrawlerTaskManager(
            max_concurrent_tasks=max_concurrent_tasks
        )
        
    async def create_douhot_video_tasks(self) -> None:
        """创建视频榜单任务"""
        tasks = []
        combinations = itertools.product(list(video_billboard_sub_type.keys()), list(douhot_date_window.keys()))
        for sub_type, date_window in combinations:
            for tag1_id, tag_dict in douhot_category_mapping.items():
                tag1_name = tag_dict.get("name","")
                tag2_dict = tag_dict.get("sub_categories",{})
                if len(tag1_name) == 0 or len(tag2_dict) == 0:continue
                for tag2_id, tag2_name in tag2_dict.items():
                    task = TaskFactory.create_douhot_video_billboard_task(
                        crawler=self.douhot_crawler,
                        sub_type=sub_type,
                        date_window=date_window,
                        tag1=tag1_id,
                        tag2=tag2_id,
                        page_size=douhot_page_size,
                        page_upper_limit=20
                    )
                    tasks.append(task)
                    log_info("douhot_runner create_video_tasks", f"增加({tag1_name}-{tag2_name})视频榜单任务")
                    
        await self.task_manager.schedule_tasks(tasks)
        log_info("douhot_runner create_video_tasks", f"创建了 {len(tasks)} 个视频榜单任务")
        
    async def create_douhot_challenge_tasks(self) -> None:
        """创建话题榜单任务"""
        tasks = []
        combinations = itertools.product(list(challenge_billboard_sub_type.keys()), list(douhot_date_window.keys()))
        for sub_type, date_window in combinations:
            for tag1_id, tag_dict in douhot_category_mapping.items():
                tag1_name = tag_dict.get("name","")
                tag2_dict = tag_dict.get("sub_categories",{})
                if len(tag1_name) == 0 or len(tag2_dict) == 0:continue
                for tag2_id, tag2_name in tag2_dict.items():
                    task = TaskFactory.create_douhot_challenge_billboard_task(
                        crawler=self.douhot_crawler,
                        sub_type=sub_type,
                        date_window=date_window,
                        tag1=tag1_id,
                        tag2=tag2_id,
                        page_size=douhot_page_size,
                        page_upper_limit=20
                    )
                    tasks.append(task)
                    log_info("douhot_runner create_challenge_tasks", f"增加({tag1_name}-{tag2_name})话题榜单任务")
                    
        await self.task_manager.schedule_tasks(tasks)
        log_info("douhot_runner create_challenge_tasks", f"创建了 {len(tasks)} 个话题榜单任务")
        
    async def create_douhot_search_tasks(self) -> None:
        """创建搜索榜单任务"""
        tasks = []
        combinations = itertools.product(list(hot_search_query_list_sub_type.keys()), list(douhot_date_window.keys()))
        for sub_type, date_window in combinations:
            task = TaskFactory.create_douhot_search_billboard_task(
                crawler=self.douhot_crawler,
                sub_type=sub_type,
                date_window=date_window,
                page_size=douhot_page_size,
                page_upper_limit=20
            )
            tasks.append(task)
                    
        await self.task_manager.schedule_tasks(tasks)
        log_info("douhot_runner", f"创建了 {len(tasks)} 个搜索榜单任务")

    async def create_douyin_hot_search_tasks(self) -> None:
        """创建搜索榜单任务"""
        tasks = []
        task = TaskFactory.create_douyin_hot_search_task(
            crawler=self.douyin_crawler,
            sub_type='热搜榜'
        )
        tasks.append(task)
                    
        await self.task_manager.schedule_tasks(tasks)
        log_info("douhot_runner", f"创建了 {len(tasks)} 个搜索榜单任务")
        
    async def run(self, db: Session) -> None:
        """运行所有类型的爬虫任务（视频、话题、搜索）"""
        try:
            # 创建所有任务
            await asyncio.gather(
                self.create_video_tasks(),
                self.create_challenge_tasks(),
                self.create_search_tasks()
            )
            
            # 运行任务
            async with self.douhot_crawler:
                await self.task_manager.run_tasks(db)
                
            # 输出统计信息
            stats = self.task_manager.get_task_statistics()
            log_info(
                "douhot_runner",
                "任务执行完成",
                total_tasks=stats["total_tasks"],
                completed_tasks=stats["completed_tasks"],
                failed_tasks=stats["failed_tasks"],
                success_rate=f"{stats['success_rate']:.2f}%"
            )
            
        except Exception as e:
            log_error("douhot_runner", f"运行失败: {str(e)}")
            raise
        finally:
            # 确保在任务完成或发生异常时都能关闭数据库连接
            if db:
                db.close()

    async def run_douhot_video_tasks(self, db: Session) -> None:
        """运行视频榜单爬虫任务"""
        try:
            # 创建所有任务
            await asyncio.gather(
                self.create_douhot_video_tasks()
            )
            
            # 运行任务
            async with self.douhot_crawler:
                await self.task_manager.run_tasks(db)
                
            # 输出统计信息
            stats = self.task_manager.get_task_statistics()
            log_info(
                "douhot_runner",
                "任务执行完成",
                total_tasks=stats["total_tasks"],
                completed_tasks=stats["completed_tasks"],
                failed_tasks=stats["failed_tasks"],
                success_rate=f"{stats['success_rate']:.2f}%"
            )
            
        except Exception as e:
            log_error("douhot_runner", f"运行失败: {str(e)}")
            raise
        finally:
            # 确保在任务完成或发生异常时都能关闭数据库连接
            if db:
                db.close()

    async def run_douhot_challenge_tasks(self, db: Session) -> None:
        """运行视频榜单爬虫任务"""
        try:
            # 创建所有任务
            await asyncio.gather(
                self.create_douhot_challenge_tasks()
            )
            
            # 运行任务
            async with self.douhot_crawler:
                await self.task_manager.run_tasks(db)
                
            # 输出统计信息
            stats = self.task_manager.get_task_statistics()
            log_info(
                "douhot_runner",
                "任务执行完成",
                total_tasks=stats["total_tasks"],
                completed_tasks=stats["completed_tasks"],
                failed_tasks=stats["failed_tasks"],
                success_rate=f"{stats['success_rate']:.2f}%"
            )
            
        except Exception as e:
            log_error("douhot_runner", f"运行失败: {str(e)}")
            raise
        finally:
            # 确保在任务完成或发生异常时都能关闭数据库连接
            if db:
                db.close()
    
    async def run_douhot_search_tasks(self, db: Session) -> None:
        """运行视频榜单爬虫任务"""
        try:
            # 创建所有任务
            await asyncio.gather(
                self.create_douhot_search_tasks()
            )
            
            # 运行任务
            async with self.douhot_crawler:
                await self.task_manager.run_tasks(db)
                
            # 输出统计信息
            stats = self.task_manager.get_task_statistics()
            log_info(
                "douhot_runner",
                "任务执行完成",
                total_tasks=stats["total_tasks"],
                completed_tasks=stats["completed_tasks"],
                failed_tasks=stats["failed_tasks"],
                success_rate=f"{stats['success_rate']:.2f}%"
            )
            
        except Exception as e:
            log_error("douhot_runner", f"运行失败: {str(e)}")
            raise
        finally:
            # 确保在任务完成或发生异常时都能关闭数据库连接
            if db:
                db.close()

    async def run_douyin_hot_search_tasks(self, db: Session) -> None:
        """运行视频榜单爬虫任务"""
        try:
            # 创建所有任务
            await asyncio.gather(
                self.create_douyin_hot_search_tasks()
            )
            
            # 运行任务
            async with self.douyin_crawler:
                await self.task_manager.run_tasks(db)
                
            # 输出统计信息
            stats = self.task_manager.get_task_statistics()
            log_info(
                "douhot_runner",
                "任务执行完成",
                total_tasks=stats["total_tasks"],
                completed_tasks=stats["completed_tasks"],
                failed_tasks=stats["failed_tasks"],
                success_rate=f"{stats['success_rate']:.2f}%"
            )
            
        except Exception as e:
            log_error("douhot_runner", f"运行失败: {str(e)}")
            raise
        finally:
            # 确保在任务完成或发生异常时都能关闭数据库连接
            if db:
                db.close()