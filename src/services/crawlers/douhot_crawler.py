"""抖音热榜爬虫"""
import logging
import contextlib
import re
import requests
from datetime import datetime
from typing import Dict, Any, List, Optional
import asyncio
import aiohttp
import json
from sqlalchemy.orm import Session

from src.utils.logger import log_info, log_error, log_warning, log_execution_time, CrawlerLogger
from src.domain import DouhotBillboardVideoRepository, DouhotBillboardTopicRepository, DouhotBillboardSearchRepository
from .douhot_define import (video_billboard_sub_type, 
                            challenge_billboard_sub_type, 
                            hot_search_query_list_sub_type,
                            douhot_date_window,
                            douhot_page_size,
                            douhot_category_mapping)

class DouhotCrawler:
    """抖音热榜爬虫"""
    
    def __init__(self, cookies: str):
        """初始化爬虫"""
        self.cookies = cookies
        self.headers = {
            "authority": "douhot.douyin.com",
            "accept": "application/json, text/plain, */*",
            "accept-language": "zh-C<PERSON>,zh;q=0.9",
            "content-type": "application/json",
            "origin": "https://douhot.douyin.com",
            "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) "
                         "AppleWebKit/537.36 (KHTML, like Gecko) "
                         "Chrome/120.0.0.0 Safari/537.36",
            "cookie": self.cookies
        }
        # self.video_billboard_url = "https://www.douyin.com/aweme/v1/web/hot/search/billboard/"
        # self.topic_billboard_url = "https://www.douyin.com/aweme/v1/web/hot/topic/billboard/"
        # self.search_billboard_url = "https://www.douyin.com/aweme/v1/web/hot/word/billboard/"
        self.video_billboard_post_url=f"https://douhot.douyin.com/douhot/v1/material/video_billboard"
        self.topic_billboard_post_url=f"https://douhot.douyin.com/douhot/v1/material/challenge_billboard"
        self.search_billboard_post_url=f"https://douhot.douyin.com/douhot/v1/dashboard/hot_search/query_list"
        self.logger = CrawlerLogger().get_logger("douhot_crawler")

    @log_execution_time("douhot_crawler")
    def get_raw_data(
        self,
        url_type: str = "video",
        sub_type: int = 1001,
        date_window: int = 24,
        page: int = 1,
        page_size: int = 50,
        tag_version: str = "v2",
        tag1: int = 0,
        tag2: int = 0,
    ) -> Dict[str, Any]:
        """获取抖音热榜数据"""
        data = {}
        request_id = f"{url_type}_{page}"
        
        # 请求体
        params = {
            "msToken": "",
            "X-Bogus": "",
            "_signature": ""
        }
        payload = {}
        
        # 根据类型选择不同的URL和payload
        try:
            if url_type == "video":
                url = self.video_billboard_post_url
                payload = {
                    "sub_type": sub_type,
                    "date_window": date_window,
                    "page": page,
                    "page_size": page_size,
                    "tag_version": tag_version
                }
            elif url_type == "topic":
                url = self.topic_billboard_post_url
                payload = {
                    "sub_type": 2001,
                    "date_window": date_window,
                    "page": page,
                    "page_size": page_size,
                    "tag_version": tag_version
                }
            elif url_type == "search":
                url = self.search_billboard_post_url
                payload = {
                    "sub_type": 3001,
                    "date_window": date_window,
                    "page_num": page,
                    "page_size": page_size,
                }
            else:
                raise ValueError(f"不支持的URL类型: {url_type}")

            if tag1 != 0 and tag2 != 0:
                payload["tags"] = [
                                    {
                                        "value": tag1,
                                        "children": [
                                            {
                                                "value": tag2
                                            }
                                        ]
                                    }
                                ]

            log_info("douhot_crawler", 
                    f"开始请求{url_type}榜数据",
                    request_id=request_id,
                    url=url,
                    payload=payload
            )

            response = requests.post(
                url=url,
                params=params,
                headers=self.headers,
                json=payload,
                timeout=10
            )

            if response.status_code == 200:
                resp_data = response.json()
                print(f">>>>>>>>>response: {resp_data}")
                if resp_data.get("code") == 0:
                    data = resp_data
                    # 记录分页信息
                    pagination = resp_data.get("data", {})
                    if url_type in ["video", "topic"]:
                        total = pagination.get("page", {}).get("total", 0)
                        page_size = pagination.get("page", {}).get("page_size", 1)
                        current_page = pagination.get("page", {}).get("page", 1)
                    elif url_type in ["search"]:
                        total = pagination.get("total_count", 0)
                        page_size = pagination.get("page_size", 1)
                        current_page = pagination.get("page_num", 1)
                    else:
                        raise ValueError(f"不支持的URL类型: {url_type}")

                    
                    log_info("douhot_crawler",
                            f"成功获取{url_type}榜数据",
                            request_id=request_id,
                            total=total,
                            current_page=current_page,
                            page_size=page_size
                    )
                else:
                    log_error(
                        "douhot_crawler",
                        "API请求失败",
                        request_id=request_id,
                        code=resp_data.get("code"),
                        error_msg=resp_data.get("msg"),
                        response=resp_data
                    )
            else:
                log_error("douhot_crawler",
                         f"HTTP请求失败",
                         request_id=request_id,
                         status_code=response.status_code,
                         response_text=response.text
                )
        except Exception as e:
            log_error(
                "douhot_crawler",
                "请求处理异常",
                error=str(e),
                request_id=request_id
            )
            raise
        return data

    @log_execution_time("douhot_crawler")
    async def fetch_all_pages(
        self,
        url_type: str,
        sub_type: int = 1001,
        date_window: int = 24,
        page_size: int = 50,
        tag_version: str = "v2",
        tag1: int = 0,
        tag2: int = 0,
        max_pages: int = 2,
        request_interval: float = 1.0
    ) -> List[Any]:
        """获取所有页面的数据"""
        all_items = []
        page = 1
        task_id = f"{url_type}_{int(datetime.now().timestamp())}"
        
        log_info("douhot_crawler",
                 f"开始批量获取{url_type}榜数据",
                 task_id=task_id,
                 max_pages=max_pages,
                 request_interval=request_interval
        )
        
        try:
            while True:
                if max_pages and page > max_pages:
                    log_info("douhot_crawler",
                            f"达到最大页数限制",
                            task_id=task_id,
                            max_pages=max_pages
                    )
                    break

                # 获取当前页数据
                raw_data = self.get_raw_data(
                    url_type=url_type,
                    sub_type=sub_type,
                    date_window=date_window,
                    page=page,
                    page_size=page_size,
                    tag_version=tag_version,
                    tag1=tag1,
                    tag2=tag2
                )
                
                if not raw_data:
                    log_error("douhot_crawler",
                             "响应数据为空",
                             task_id=task_id,
                             page=page
                    )
                    break
                    
                # 解析数据
                try:
                    if url_type == "video":
                        items = self.parse_video_billboard_data(raw_data, sub_type=sub_type, date_window=date_window, tag_id1=tag1, tag_id2=tag2)
                    elif url_type == "topic":
                        items = self.parse_topic_billboard_data(raw_data, sub_type=sub_type, date_window=date_window, tag_id1=tag1, tag_id2=tag2)
                    else:  # search
                        items = self.parse_search_billboard_data(raw_data, sub_type=sub_type, date_window=date_window)
                except Exception as e:
                    log_error("douhot_crawler",
                             "解析数据异常",
                             error=str(e),
                             task_id=task_id,
                             page=page
                    )
                    raise
                    
                if not items:
                    log_error("douhot_crawler",
                              "页面无数据",
                              task_id=task_id,
                              page=page,
                              raw_data=raw_data
                    )
                    break
                    
                all_items.extend(items)
                
                # 检查是否还有下一页
                # if url_type == "search":
                #     pagination = raw_data.get("data", {})
                # else:
                #     pagination = raw_data.get("data", {}).get("page", {})
                
                pagination = raw_data.get("data", {})
                if url_type in ["video", "topic"]:
                    total_size = pagination.get("page", {}).get("total", 0)
                    page_size = pagination.get("page", {}).get("page_size", 1)
                    current_page = pagination.get("page", {}).get("page", 1)
                elif url_type in ["search"]:
                    total_size = pagination.get("total_count", 0)
                    page_size = pagination.get("page_size", 1)
                    current_page = pagination.get("page_num", 1)
                else:
                    raise ValueError(f"不支持的URL类型: {url_type}")
                
                if current_page * page_size >= total_size:
                    log_info("douhot_crawler",
                            f"已到达最后一页",
                            task_id=task_id,
                            total_size=total_size,
                            current_page=current_page
                    )
                    break
                    
                page += 1
                await asyncio.sleep(request_interval)
                
            log_info("douhot_crawler",
                     f"批量获取数据完成",
                     task_id=task_id,
                     total_items=len(all_items)
            )
            
        except Exception as e:
            log_error("douhot_crawler",
                     "批量获取数据异常",
                     error=str(e),
                     task_id=task_id
            )
            raise
            
        return all_items

    def parse_video_billboard_data(self, raw_data: dict, **kwargs) -> List[Dict[str, Any]]:
        """解析视频榜数据"""
        videos = []
        try:
            items = raw_data.get("data", {}).get("objs", [])
            for item in items:
                item_id = str(item.get("item_id", ""))
                if len(item_id) == 0:
                    continue
                video = {
                    "video_tag1": kwargs.get("tag1", 0),
                    "video_tag2": kwargs.get("tag2", 0),
                    "video_tag3": kwargs.get("tag3", 0),
                    "billboard_type": kwargs.get("sub_type", 0),
                    "date_window": kwargs.get("date_window", 0),
                    "item_id": item_id,
                    "item_title": item.get("item_title", ""),
                    "item_cover_url": item.get("item_cover_url", ""),
                    "item_duration": int(item.get("item_duration", 0)),
                    "nick_name": item.get("nick_name", ""),
                    "avatar_url": item.get("avatar_url", ""),
                    "fans_cnt": int(item.get("fans_cnt", 0)),
                    "play_cnt": int(item.get("play_cnt", 0)),
                    "publish_time": datetime.fromtimestamp(item.get("publish_time", 0)),
                    "score": int(item.get("score", 0)),
                    "item_url": item.get("item_url", ""),
                    "like_cnt": int(item.get("like_cnt", 0)),
                    "follow_cnt": int(item.get("follow_cnt", 0)),
                    "follow_rate": float(item.get("follow_rate", 0)),
                    "like_rate": float(item.get("like_rate", 0)),
                    "media_type": int(item.get("media_type", 0)),
                    "favorite_id": item.get("favorite_id", ""),
                    "is_favorite": bool(item.get("is_favorite", False)),
                    "image_cnt": int(item.get("image_cnt", 0))
                }
                videos.append(video)
        except Exception as e:
            log_error("douhot_crawler",
                     "解析视频榜数据异常",
                     error=str(e)
            )
            raise
        return videos

    def parse_topic_billboard_data(self, raw_data: dict, **kwargs) -> List[Dict[str, Any]]:
        """解析话题榜数据"""
        topics = []
        try:
            items = raw_data.get("data", {}).get("objs", [])
            for item in items:
                challenge_id = str(item.get("challenge_id", ""))
                if len(challenge_id) == 0:
                    continue
                topic = {
                    "topic_tag1": kwargs.get("tag1", 0),
                    "topic_tag2": kwargs.get("tag2", 0),
                    "topic_tag3": kwargs.get("tag3", 0),
                    "billboard_type": kwargs.get("sub_type", 0),
                    "date_window": kwargs.get("date_window", 0),
                    "challenge_id": challenge_id,
                    "challenge_name": item.get("challenge_name", ""),
                    "play_cnt": int(item.get("play_cnt", 0)),
                    "publish_cnt": int(item.get("publish_cnt", 0)),
                    "cover_url": item.get("cover_url", ""),
                    "score": int(item.get("score", 0)),
                    "avg_play_cnt": int(item.get("avg_play_cnt", 0)),
                    "create_time": int(item.get("create_time", 0)),
                    "origin_trend_str": item.get("origin_trend_str", ""),
                    "trends": item.get("trends", []),
                    "challenge_type": int(item.get("challenge_type", 0)),
                    "item_list": item.get("item_list", []),
                    "is_favorite": bool(item.get("is_favorite", False)),
                    "is_recommend": bool(item.get("is_recommend", False)),
                    "show_rank": int(item.get("show_rank", 0)),
                    "real_rank": int(item.get("real_rank", 0)),
                    "origin_rank": int(item.get("origin_rank", 0)),
                    "related_event": item.get("related_event")
                }
                topics.append(topic)
        except Exception as e:
            log_error("douhot_crawler",
                     "解析话题榜数据异常",
                     error=str(e)
            )
            raise
        return topics

    def parse_search_billboard_data(self, raw_data: dict, **kwargs) -> List[Dict[str, Any]]:
        """解析搜索榜数据"""
        searches = []
        try:
            items = raw_data.get("data", {}).get("search_list", [])
            if not items:
                log_info("douhot_crawler", "搜索榜数据为空")
                return searches

            for item in items:
                keyword = item.get("key_word", "").strip()
                if not keyword:  # Skip empty keywords
                    continue
                    
                search = {
                    "billboard_type": kwargs.get("sub_type", 0),
                    "date_window": kwargs.get("date_window", 0),
                    "keyword": keyword,
                    "search_score": int(item.get("search_score", 0)),
                    "trends": item.get("trends", [])
                }
                searches.append(search)
        except Exception as e:
            log_error("douhot_crawler",
                     "解析搜索榜数据异常",
                     error=str(e)
            )
            raise
        return searches

    async def fetch_video_billboard(self, db: Session) -> List[Any]:
        """抓取视频榜数据"""
        # 获取所有页面的数据
        import itertools
        combinations = itertools.product(list(video_billboard_sub_type.keys()), list(douhot_date_window.keys()))
        fetched_data = []
        for sub_type, date_window in combinations:
            for tag1_id, tag_dict in douhot_category_mapping.items():
                tag1_name = tag_dict.get("name","")
                tag2_dict = tag_dict.get("sub_categories",{})
                if len(tag1_name) == 0 or len(tag2_dict) == 0:continue
                for tag2_id, tag2_name in tag2_dict.items():
                    try:
                        videos = await self.fetch_all_pages("video", sub_type=sub_type, 
                                                            date_window=date_window, 
                                                            tag1=tag1_id, tag2=tag2_id)
                        if not videos:
                            log_warning(
                                "douhot_crawler",
                                f"""未获取到{tag1_name}-{tag2_name}话题榜数据, \
                                榜类型:{video_billboard_sub_type.get(sub_type, 'unkonwn')},\
                                时间窗口:{douhot_date_window.get(date_window, 'unkonwn')}"""
                            )
                            continue
                        fetched_data.extend(videos)
                    except Exception as e:
                        log_warning(
                            "douhot_crawler",
                            f"""获取话题榜数据异常, \
                            榜类型:{video_billboard_sub_type.get(sub_type, 'unkonwn')},\
                            时间窗口:{douhot_date_window.get(date_window, 'unkonwn')}""",
                            error=str(e)
                        )
                        continue
            
        # 保存到数据库
        try:
            repo = DouhotBillboardVideoRepository(db)
            saved_videos = []
            
            for video_data in fetched_data:
                # 检查是否已存在
                await repo.upsert_video(video_data)
                saved_videos.append(video_data)
            
            db.commit()
            log_info("douhot_crawler",
                        f"成功保存视频数据",
                        total_items=len(saved_videos)
            )
            return saved_videos  
        except Exception as e:
            log_error(
                "douhot_crawler",
                "抓取视频榜数据至数据库异常",
                error=str(e)
            )
            db.rollback() 
            return []     
        finally:
            db.close()

    async def fetch_topic_billboard(self, db: Session) -> List[Any]:
        """抓取话题榜数据"""
        import itertools
        combinations = itertools.product(list(challenge_billboard_sub_type.keys()), list(douhot_date_window.keys()))
        fetched_data = []
        for sub_type, date_window in combinations:
            for tag1_id, tag_dict in douhot_category_mapping.items():
                tag1_name = tag_dict.get("name","")
                tag2_dict = tag_dict.get("sub_categories",{})
                if len(tag1_name) == 0 or len(tag2_dict) == 0:continue
                for tag2_id, tag2_name in tag2_dict.items():
                    try:
                        topics = await self.fetch_all_pages("topic", sub_type=sub_type, 
                                                            date_window=date_window, 
                                                            tag1=tag1_id, tag2=tag2_id)
                        if not topics:
                            log_warning(
                                "douhot_crawler",
                                f"""未获取到{tag1_name}-{tag2_name}话题榜数据, \
                                榜类型:{challenge_billboard_sub_type.get(sub_type, 'unkonwn')},\
                                时间窗口:{douhot_date_window.get(date_window, 'unkonwn')}"""
                            )
                            continue
                        fetched_data.extend(topics)
                    except Exception as e:
                        log_warning(
                            "douhot_crawler",
                            f"""获取话题榜数据异常, \
                            榜类型:{challenge_billboard_sub_type.get(sub_type, 'unkonwn')},\
                            时间窗口:{douhot_date_window.get(date_window, 'unkonwn')}""",
                            error=str(e)
                        )
                        continue

        # 保存到数据库
        try:
            repo = DouhotBillboardTopicRepository(db)
            saved_videos = []
            
            for data in fetched_data:
                # 检查是否已存在
                await repo.upsert_topic(data)
                saved_videos.append(data)
            
            db.commit()
            log_info("douhot_crawler",
                        f"成功保存话题数据",
                        total_items=len(saved_videos)
            )
            return saved_videos  
        except Exception as e:
            log_error(
                "douhot_crawler",
                "抓取话题榜数据至数据库异常",
                error=str(e)
            )
            db.rollback() 
            return []     
        finally:
            db.close()

    async def fetch_search_billboard(self, db: Session) -> List[Any]:
        """抓取搜索榜数据"""
        import itertools
        combinations = itertools.product(list(hot_search_query_list_sub_type.keys()), list(douhot_date_window.keys()))
        fetched_data = []
        for sub_type, date_window in combinations:
            try:
                searches = await self.fetch_all_pages("search", sub_type=sub_type, date_window=date_window)
                if not searches:
                    log_warning(
                        "douhot_crawler",
                        f"""未获取到搜索榜数据, \
                        榜类型:{hot_search_query_list_sub_type.get(sub_type, 'unkonwn')},\
                        时间窗口:{douhot_date_window.get(date_window, 'unkonwn')}"""
                    )
                    continue
                fetched_data.extend(searches)
            except Exception as e:
                log_warning(
                    "douhot_crawler",
                    f"""抓取搜索榜数据异常, \
                    榜类型:{hot_search_query_list_sub_type.get(sub_type, 'unkonwn')},\
                    时间窗口:{douhot_date_window.get(date_window, 'unkonwn')}""",
                    error=str(e)
                )
                continue

        try:            
            # 保存到数据库
            repo = DouhotBillboardSearchRepository(db)
            saved_searches = []
            
            for data in fetched_data:
                # 检查是否已存在
                await repo.upsert_search(data)
                saved_searches.append(data)
            
            db.commit()
            log_info("douhot_crawler",
                     f"成功保存搜索数据",
                     total_items=len(saved_searches)
            )
            
            return saved_searches

        except Exception as e:
            log_error(
                "douhot_crawler",
                "抓取搜索榜数据异常",
                error=str(e)
            )
            db.rollback()
            return []
        finally:
            db.close()