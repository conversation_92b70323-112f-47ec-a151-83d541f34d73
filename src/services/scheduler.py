"""热榜调度器服务"""
# 标准库导入
import logging

# 第三方库导入
from apscheduler.schedulers.background import BackgroundScheduler
from apscheduler.triggers.interval import IntervalTrigger

# 项目内导入
from src.services.crawlers import DouyinCrawler

logger = logging.getLogger(__name__)

class HotListScheduler:
    """热榜调度器"""
    
    def __init__(self):
        """初始化调度器"""
        self.scheduler = BackgroundScheduler()
        self.is_running = False
        self.crawler = DouyinCrawler(init_db=True)
    
    def crawl_task(self):
        """爬虫任务"""
        try:
            # 执行爬虫任务并保存到数据库
            items = self.crawler.crawl(save_to_db=True)
            logger.info(f"抖音爬虫获取到 {len(items)} 条数据")
        except Exception as e:
            logger.error(f"抖音爬虫执行失败: {str(e)}")
    
    def start(self, interval: int = 300):
        """启动调度器
        
        Args:
            interval: 爬虫间隔（秒）
        """
        if not self.is_running:
            # 添加定时任务
            self.scheduler.add_job(
                self.crawl_task,
                trigger=IntervalTrigger(seconds=interval),
                id="douyin_crawler",
                name="抖音热榜爬虫",
                replace_existing=True
            )
            
            # 启动调度器
            self.scheduler.start()
            self.is_running = True
            logger.info(f"调度器已启动，爬虫间隔为 {interval} 秒")
    
    def stop(self):
        """停止调度器"""
        if self.is_running:
            self.scheduler.shutdown()
            self.is_running = False
            logger.info("调度器已停止")