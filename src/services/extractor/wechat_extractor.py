"""
公众号文章内容提取模块。
提供从公众号文章URL中提取标题、正文、图片等内容的功能。
"""
import re
import requests
from bs4 import BeautifulSoup
from typing import Dict, List, Optional, Any
import logging
import time
import asyncio
import pkg_resources

# 导入DrissionPage相关模块
from DrissionPage import ChromiumPage, ChromiumOptions

logger = logging.getLogger(__name__)

# 检查DrissionPage版本
try:
    drission_version = pkg_resources.get_distribution("DrissionPage").version
    logger.info(f"DrissionPage版本: {drission_version}")
    DRISSION_VERSION = tuple(map(int, drission_version.split('.')))
except Exception as e:
    logger.warning(f"无法获取DrissionPage版本: {e}")
    DRISSION_VERSION = (0, 0, 0)  # 默认版本

class WechatArticleExtractor:
    """微信公众号文章内容提取器"""
    
    def __init__(self, use_dynamic=False, min_content_length=100, max_retries=4):
        """
        初始化微信公众号文章提取器
        
        Args:
            use_dynamic: 是否使用动态加载方式提取内容
            min_content_length: 最小内容长度要求，默认100字符
            max_retries: 最大重试次数，默认4次
        """
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
        self.use_dynamic = use_dynamic
        self.min_content_length = min_content_length
        self.max_retries = max_retries
        self._page = None  # 动态页面对象
    
    async def extract_from_url(self, url: str) -> Dict[str, Any]:
        """
        从微信公众号文章URL中提取内容，包含重试机制
        
        Args:
            url: 微信公众号文章URL
            
        Returns:
            包含文章标题、作者、发布时间、正文内容等信息的字典
        """
        retry_count = 0
        last_error = None
        
        while retry_count < self.max_retries:
            try:
                logger.info(f"尝试提取文章内容 (第{retry_count + 1}次尝试): {url}")
                
                # 添加重试延迟
                if retry_count > 0:
                    delay = 2 + retry_count  # 2, 3, 4秒延迟
                    logger.info(f"等待 {delay} 秒后重试...")
                    await asyncio.sleep(delay)
                
                # 执行提取
                if self.use_dynamic:
                    result = self._extract_dynamic(url)
                else:
                    try:
                        response = requests.get(url, headers=self.headers, timeout=10)
                        response.raise_for_status()
                        result = self._parse_html(response.text, url)
                    except requests.RequestException as e:
                        logger.error(f"提取文章内容时发生请求错误: {e}")
                        result = {"error": f"提取文章内容失败: {str(e)}"}
                
                # 检查结果
                if result and not result.get("error"):
                    content_length = len(result.get("content", ""))
                    logger.info(f"提取到内容长度: {content_length} 字符")
                    
                    if content_length >= self.min_content_length:
                        logger.info(f"内容长度满足要求 (>= {self.min_content_length})")
                        result["content_length"] = content_length
                        result["retry_count"] = retry_count
                        return result
                    else:
                        logger.warning(f"内容长度不足 ({content_length} < {self.min_content_length})，将重试")
                        last_error = f"内容长度不足: {content_length} < {self.min_content_length}"
                else:
                    last_error = result.get("error", "提取失败") if result else "提取失败"
                    logger.warning(f"提取失败: {last_error}")
                
            except Exception as e:
                last_error = str(e)
                logger.error(f"提取过程中发生异常: {e}")
            
            retry_count += 1
        
        # 所有重试都失败了
        logger.error(f"经过 {self.max_retries} 次尝试后仍无法提取足够内容，最后错误: {last_error}")
        return {
            "error": f"提取失败，经过 {self.max_retries} 次重试: {last_error}",
            "retry_count": retry_count,
            "url": url
        }
    
    def _extract_dynamic(self, url: str) -> Dict[str, Any]:
        """
        使用DrissionPage动态加载方式提取内容
        
        Args:
            url: 文章URL
            
        Returns:
            包含文章信息的字典
        """
        try:
            # 初始化ChromiumPage
            if self._page is None:
                options = ChromiumOptions()
                # 根据不同版本设置无头模式
                if DRISSION_VERSION >= (4, 0, 0):
                    options.headless = True  # 4.0.0及以上版本
                else:
                    try:
                        options.set_headless(True)  # 旧版本
                    except AttributeError:
                        logger.warning("无法设置无头模式，请升级DrissionPage到最新版本")
                self._page = ChromiumPage(options=options)
            
            # 访问URL
            self._page.get(url)
            
            # 等待页面加载完成
            time.sleep(2)  # 等待动态内容加载
            
            # 提取标题
            title = self._extract_title_dynamic()
            
            # 提取作者和公众号名称
            author, account_name = self._extract_author_and_account_dynamic()
            
            # 提取发布时间
            publish_time = self._extract_publish_time_dynamic()
            
            # 提取正文内容
            content = self._extract_content_dynamic()
            
            # 提取图片URL列表
            images = self._extract_images_dynamic()
            
            return {
                "url": url,
                "title": title,
                "author": author,
                "account_name": account_name,
                "publish_time": publish_time,
                "content": content,
                "images": images
            }
        except Exception as e:
            logger.error(f"使用动态加载方式提取文章内容时发生错误: {e}")
            return {"error": f"提取文章内容失败: {str(e)}"}
        finally:
            # 不关闭浏览器，以便复用
            pass
    
    def close(self):
        """关闭动态页面对象"""
        if self._page is not None:
            self._page.quit()
            self._page = None
    
    def _extract_title_dynamic(self) -> str:
        """使用动态加载方式提取文章标题"""
        try:
            # 尝试获取标题元素 - 多种可能的选择器
            selectors = [
                '.rich_media_title',
                'h1',
                'h2.rich_media_title',
                '#activity-name',
                '.title',
                'title'  # 网页标题也可能包含文章标题
            ]
            
            for selector in selectors:
                title_elem = self._page.ele(selector)
                if title_elem and title_elem.text.strip():
                    return title_elem.text.strip()
            
            # 尝试从页面标题中提取
            page_title = self._page.title
            if page_title and page_title.strip() and page_title != "微信公众平台":
                return page_title.strip()
            
            # 记录更详细的调试信息
            logger.warning(f"无法找到文章标题，页面URL: {self._page.url}")
            
            # 最后的备选方案
            return "未找到标题"
        except Exception as e:
            logger.warning(f"提取标题时发生错误: {e}")
            return "未找到标题"
    
    def _extract_author_and_account_dynamic(self) -> tuple:
        """使用动态加载方式提取作者和公众号名称"""
        author = "未知作者"
        account_name = "未知公众号"
        
        try:
            # 尝试提取作者
            author_elem = self._page.ele('.rich_media_meta_text')
            if author_elem:
                author = author_elem.text.strip()
            
            # 尝试提取公众号名称
            account_elem = self._page.ele('#js_name')
            if account_elem:
                account_name = account_elem.text.strip()
            
            return author, account_name
        except Exception as e:
            logger.warning(f"提取作者和公众号名称时发生错误: {e}")
            return author, account_name
    
    def _extract_publish_time_dynamic(self) -> str:
        """使用动态加载方式提取发布时间"""
        try:
            # 尝试获取发布时间
            time_elem = self._page.ele('#publish_time')
            if time_elem:
                return time_elem.text.strip()
            
            # 备用方法
            time_elem = self._page.ele('.rich_media_meta_text')
            if time_elem:
                return time_elem.text.strip()
            
            return "未知时间"
        except Exception as e:
            logger.warning(f"提取发布时间时发生错误: {e}")
            return "未知时间"
    
    def _extract_content_dynamic(self) -> str:
        """使用动态加载方式提取正文内容"""
        try:
            # 获取正文容器
            content_elem = self._page.ele('#js_content')
            if not content_elem:
                return "未找到正文内容"
            
            # 获取纯文本内容
            text = content_elem.text.strip()
            
            # 清理文本（移除多余空白行等）
            text = re.sub(r'\n{3,}', '\n\n', text)
            
            return text
        except Exception as e:
            logger.warning(f"提取正文内容时发生错误: {e}")
            return "未找到正文内容"
    
    def _extract_images_dynamic(self) -> List[str]:
        """使用动态加载方式提取文章中的图片URL"""
        images = []
        try:
            # 获取正文容器中的所有图片 - 多种可能的选择器
            content_selectors = ['#js_content', '.rich_media_content', 'article', '.article-content', 'body']
            
            for selector in content_selectors:
                content_elem = self._page.ele(selector)
                if content_elem:
                    # 获取所有img标签
                    img_elems = content_elem.eles('img')
                    for img in img_elems:
                        # 尝试获取图片URL - 微信文章图片通常使用data-src存储懒加载URL
                        src = img.attr('data-src') or img.attr('src') or img.attr('data-original')
                        if src and src.startswith(('http://', 'https://')):
                            if src not in images:  # 避免重复
                                images.append(src)
                    
                    # 如果已经找到图片，就不再继续查找
                    if images:
                        break
            
            # 如果正文中没找到图片，试着在整个页面中找
            if not images:
                img_elems = self._page.eles('img')
                for img in img_elems:
                    src = img.attr('data-src') or img.attr('src') or img.attr('data-original')
                    if src and src.startswith(('http://', 'https://')):
                        if src not in images:  # 避免重复
                            images.append(src)
            
            # 记录调试信息
            if not images:
                logger.warning(f"未能提取到图片，页面URL: {self._page.url}")
            else:
                logger.info(f"成功提取到 {len(images)} 张图片")
                
            return images
        except Exception as e:
            logger.warning(f"提取图片URL时发生错误: {e}")
            return images
    
    def _parse_html(self, html_content: str, url: str) -> Dict[str, Any]:
        """
        解析HTML内容，提取文章信息
        
        Args:
            html_content: HTML内容
            url: 原始URL，用于记录
            
        Returns:
            包含文章信息的字典
        """
        soup = BeautifulSoup(html_content, 'html.parser')
        
        # 提取标题
        title = self._extract_title(soup)
        
        # 提取作者和公众号名称
        author, account_name = self._extract_author_and_account(soup)
        
        # 提取发布时间
        publish_time = self._extract_publish_time(soup)
        
        # 提取正文内容
        content = self._extract_content(soup)
        
        # 提取图片URL列表
        images = self._extract_images(soup)
        
        return {
            "url": url,
            "title": title,
            "author": author,
            "account_name": account_name,
            "publish_time": publish_time,
            "content": content,
            "images": images
        }
    
    def _extract_title(self, soup: BeautifulSoup) -> str:
        """提取文章标题"""
        title_tag = soup.find('h1', class_='rich_media_title') or soup.find('h1')
        if title_tag:
            return title_tag.get_text(strip=True)
        return "未找到标题"
    
    def _extract_author_and_account(self, soup: BeautifulSoup) -> tuple:
        """提取作者和公众号名称"""
        author = "未知作者"
        account_name = "未知公众号"
        
        # 尝试提取作者
        author_tag = soup.find('span', class_='rich_media_meta_text')
        if author_tag:
            author = author_tag.get_text(strip=True)
        
        # 尝试提取公众号名称
        account_tag = soup.find('a', class_='wx_tap_link')
        if account_tag:
            account_name = account_tag.get_text(strip=True)
        
        return author, account_name
    
    def _extract_publish_time(self, soup: BeautifulSoup) -> str:
        """提取发布时间"""
        time_tag = soup.find('em', class_='rich_media_meta_text')
        if time_tag:
            return time_tag.get_text(strip=True)
        return "未知时间"
    
    def _extract_content(self, soup: BeautifulSoup) -> str:
        """提取正文内容"""
        content_div = soup.find('div', class_='rich_media_content')
        if not content_div:
            return "未找到正文内容"
        
        # 移除脚本和样式元素
        for script in content_div(["script", "style"]):
            script.decompose()
        
        # 获取纯文本内容
        text = content_div.get_text(separator='\n', strip=True)
        
        # 清理文本（移除多余空白行等）
        text = re.sub(r'\n{3,}', '\n\n', text)
        
        return text
    
    def _extract_images(self, soup: BeautifulSoup) -> List[str]:
        """提取文章中的图片URL"""
        images = []
        content_div = soup.find('div', class_='rich_media_content')
        if content_div:
            for img in content_div.find_all('img'):
                src = img.get('data-src') or img.get('src')
                if src:
                    images.append(src)
        return images


class GeneralArticleExtractor:
    """通用网页文章内容提取器"""
    
    def __init__(self, use_dynamic=False):
        """
        初始化通用网页文章提取器
        
        Args:
            use_dynamic: 是否使用动态加载方式提取内容
        """
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
        self.use_dynamic = use_dynamic
        self._page = None  # 动态页面对象
    
    async def extract_from_url(self, url: str) -> Dict[str, Any]:
        """
        从一般网页文章URL中提取内容
        
        Args:
            url: 文章URL
            
        Returns:
            包含文章信息的字典
        """
        if self.use_dynamic:
            return self._extract_dynamic(url)
        else:
            try:
                response = requests.get(url, headers=self.headers, timeout=10)
                response.raise_for_status()
                return self._parse_html(response.text, url)
            except requests.RequestException as e:
                logger.error(f"提取文章内容时发生请求错误: {e}")
                return {"error": f"提取文章内容失败: {str(e)}"}
    
    def _extract_dynamic(self, url: str) -> Dict[str, Any]:
        """
        使用DrissionPage动态加载方式提取内容
        
        Args:
            url: 文章URL
            
        Returns:
            包含文章信息的字典
        """
        try:
            # 初始化ChromiumPage
            if self._page is None:
                options = ChromiumOptions()
                # 根据不同版本设置无头模式
                if DRISSION_VERSION >= (4, 0, 0):
                    options.headless = True  # 4.0.0及以上版本
                else:
                    try:
                        options.set_headless(True)  # 旧版本
                    except AttributeError:
                        logger.warning("无法设置无头模式，请升级DrissionPage到最新版本")
                self._page = ChromiumPage(options=options)
            
            # 访问URL
            self._page.get(url)
            
            # 等待页面加载完成
            time.sleep(2)  # 等待动态内容加载
            
            # 提取标题
            title = self._extract_title_dynamic()
            
            # 提取正文内容
            content = self._extract_main_content_dynamic()
            
            # 提取图片
            images = self._extract_images_dynamic()
            
            return {
                "url": url,
                "title": title,
                "content": content,
                "images": images
            }
        except Exception as e:
            logger.error(f"使用动态加载方式提取文章内容时发生错误: {e}")
            return {"error": f"提取文章内容失败: {str(e)}"}
        finally:
            # 不关闭浏览器，以便复用
            pass
    
    def close(self):
        """关闭动态页面对象"""
        if self._page is not None:
            self._page.quit()
            self._page = None
    
    def _extract_title_dynamic(self) -> str:
        """使用动态加载方式提取文章标题"""
        try:
            # 尝试获取标题元素 - 多种可能的选择器
            selectors = [
                '.rich_media_title',
                'h1',
                'h2.rich_media_title',
                '#activity-name',
                '.title',
                'title'  # 网页标题也可能包含文章标题
            ]
            
            for selector in selectors:
                title_elem = self._page.ele(selector)
                if title_elem and title_elem.text.strip():
                    return title_elem.text.strip()
            
            # 尝试从页面标题中提取
            page_title = self._page.title
            if page_title and page_title.strip() and page_title != "微信公众平台":
                return page_title.strip()
            
            # 记录更详细的调试信息
            logger.warning(f"无法找到文章标题，页面URL: {self._page.url}")
            
            # 最后的备选方案
            return "未找到标题"
        except Exception as e:
            logger.warning(f"提取标题时发生错误: {e}")
            return "未找到标题"
    
    def _extract_main_content_dynamic(self) -> str:
        """
        使用动态加载方式提取正文内容
        使用启发式算法查找可能的内容容器
        """
        try:
            # 尝试常见的内容容器选择器
            content_selectors = [
                'article',
                'div.content', 'div.article', 'div.post',
                'div[class*="content"]', 'div[class*="article"]',
                'div[id*="content"]', 'div[id*="article"]'
            ]
            
            # 尝试查找内容容器
            content_text = ""
            for selector in content_selectors:
                content_elem = self._page.ele(selector)
                if content_elem:
                    content_text = content_elem.text.strip()
                    if len(content_text) > 200:  # 如果内容足够长，认为找到了正文
                        break
            
            # 如果没有找到内容容器，尝试获取所有段落
            if not content_text:
                paragraphs = []
                p_elems = self._page.eles('p')
                for p in p_elems:
                    p_text = p.text.strip()
                    if len(p_text) > 50:  # 只收集长度超过50的段落
                        paragraphs.append(p_text)
                content_text = '\n\n'.join(paragraphs)
            
            # 清理文本
            content_text = re.sub(r'\n{3,}', '\n\n', content_text)
            
            return content_text
        except Exception as e:
            logger.warning(f"提取正文内容时发生错误: {e}")
            return "未找到正文内容"
    
    def _extract_images_dynamic(self) -> List[str]:
        """使用动态加载方式提取文章中的图片URL"""
        images = []
        try:
            # 获取正文容器中的所有图片 - 多种可能的选择器
            content_selectors = ['#js_content', '.rich_media_content', 'article', '.article-content', 'body']
            
            for selector in content_selectors:
                content_elem = self._page.ele(selector)
                if content_elem:
                    # 获取所有img标签
                    img_elems = content_elem.eles('img')
                    for img in img_elems:
                        # 尝试获取图片URL - 微信文章图片通常使用data-src存储懒加载URL
                        src = img.attr('data-src') or img.attr('src') or img.attr('data-original')
                        if src and src.startswith(('http://', 'https://')):
                            if src not in images:  # 避免重复
                                images.append(src)
                    
                    # 如果已经找到图片，就不再继续查找
                    if images:
                        break
            
            # 如果正文中没找到图片，试着在整个页面中找
            if not images:
                img_elems = self._page.eles('img')
                for img in img_elems:
                    src = img.attr('data-src') or img.attr('src') or img.attr('data-original')
                    if src and src.startswith(('http://', 'https://')):
                        if src not in images:  # 避免重复
                            images.append(src)
            
            # 记录调试信息
            if not images:
                logger.warning(f"未能提取到图片，页面URL: {self._page.url}")
            else:
                logger.info(f"成功提取到 {len(images)} 张图片")
                
            return images
        except Exception as e:
            logger.warning(f"提取图片URL时发生错误: {e}")
            return images
    
    def _parse_html(self, html_content: str, url: str) -> Dict[str, Any]:
        """解析HTML内容，提取文章信息"""
        soup = BeautifulSoup(html_content, 'html.parser')
        
        # 提取标题
        title = self._extract_title(soup)
        
        # 提取正文内容（使用启发式算法）
        content = self._extract_main_content(soup)
        
        # 提取图片
        images = self._extract_images(soup)
        
        return {
            "url": url,
            "title": title,
            "content": content,
            "images": images
        }
    
    def _extract_title(self, soup: BeautifulSoup) -> str:
        """提取文章标题"""
        title_tag = soup.find('title')
        if title_tag:
            return title_tag.get_text(strip=True)
        
        h1_tag = soup.find('h1')
        if h1_tag:
            return h1_tag.get_text(strip=True)
        
        return "未找到标题"
    
    def _extract_main_content(self, soup: BeautifulSoup) -> str:
        """
        使用启发式算法提取正文内容
        这里使用简单的方法，实际应用中可能需要更复杂的算法
        """
        # 尝试查找常见的文章内容容器
        content_candidates = [
            soup.find('article'),
            soup.find('div', class_=lambda c: c and ('content' in c.lower() or 'article' in c.lower())),
            soup.find('div', id=lambda i: i and ('content' in i.lower() or 'article' in i.lower()))
        ]
        
        # 使用第一个找到的有效容器
        content_element = next((e for e in content_candidates if e is not None), None)
        
        if not content_element:
            # 如果没有找到明确的内容容器，使用所有段落文本
            paragraphs = soup.find_all('p')
            return '\n\n'.join(p.get_text(strip=True) for p in paragraphs if len(p.get_text(strip=True)) > 50)
        
        # 移除脚本和样式元素
        for script in content_element(["script", "style"]):
            script.decompose()
        
        # 获取纯文本内容
        text = content_element.get_text(separator='\n', strip=True)
        
        # 清理文本
        text = re.sub(r'\n{3,}', '\n\n', text)
        
        return text
    
    def _extract_images(self, soup: BeautifulSoup) -> List[str]:
        """提取文章中的图片URL"""
        images = []
        content_element = soup.find('article') or soup.find('div', class_=lambda c: c and ('content' in c.lower()))
        
        if content_element:
            for img in content_element.find_all('img'):
                src = img.get('src')
                if src and not src.startswith('data:'):
                    images.append(src)
        
        return images


def create_extractor(url: str, use_dynamic: bool = False) -> Any:
    """
    根据URL创建合适的提取器
    
    Args:
        url: 文章URL
        use_dynamic: 是否使用动态加载方式提取内容
        
    Returns:
        适合该URL的提取器实例
    """
    if 'mp.weixin.qq.com' in url:
        return WechatArticleExtractor(use_dynamic=use_dynamic)
    else:
        return GeneralArticleExtractor(use_dynamic=use_dynamic)


async def extract_article(url: str, use_dynamic: bool = False) -> Dict[str, Any]:
    """
    从URL中提取文章内容
    
    Args:
        url: 文章URL
        use_dynamic: 是否使用动态加载方式提取内容
        
    Returns:
        包含文章信息的字典
    """
    extractor = create_extractor(url, use_dynamic)
    try:
        return await extractor.extract_from_url(url)
    finally:
        # 如果使用动态加载，确保关闭浏览器
        if use_dynamic and hasattr(extractor, 'close'):
            extractor.close()
