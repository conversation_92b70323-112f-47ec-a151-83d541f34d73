"""
统一内容分析模块 - 整合文章和视频的分析功能，提供统一接口
"""
from typing import Dict, Any, List, Optional, Union
import os
import time
import json
import asyncio
import logging
from urllib.parse import urlparse

from src.services.extractor.ai_analyzer import ArticleAnalyzer, analyze_article
from src.services.extractor.video_analyzer import VideoAnalyzer
from src.utils.logger import log_info, log_error, CrawlerLogger, log_warning
from src.config.logging_config import LoggerName
from src.config.api_config import get_deepseek_api_key

logger = CrawlerLogger().get_logger(LoggerName.API)

class ContentType:
    """内容类型枚举"""
    ARTICLE = "article"
    VIDEO = "video"
    UNKNOWN = "unknown"

class UnifiedAnalyzer:
    """统一内容分析器，支持文章和视频分析"""
    
    def __init__(self, article_api_key: Optional[str] = None, video_api_key: Optional[str] = None):
        """
        初始化统一分析器
        
        Args:
            article_api_key: 文章分析API密钥
            video_api_key: 视频分析API密钥(已废弃，保留参数是为了兼容性)
        """
        self.article_api_key = article_api_key or get_deepseek_api_key()
        # 视频分析也使用DeepSeek API
        self.video_api_key = self.article_api_key
        
        # 初始化各类分析器
        self.article_analyzer = ArticleAnalyzer(api_key=self.article_api_key)
        self.video_analyzer = VideoAnalyzer(api_key=self.article_api_key)
    
    async def analyze_content(self, extraction_result: Dict[str, Any]) -> Dict[str, Any]:
        """
        分析提取的内容（自动判断类型）
        
        Args:
            extraction_result: 内容提取结果
            
        Returns:
            分析结果
        """
        try:
            # 检查提取结果是否有错误
            if "error" in extraction_result:
                log_error(LoggerName.API, "内容分析跳过 - 提取过程中已有错误", 
                         error=extraction_result.get("error", "未知错误"))
                return extraction_result
            
            # 检查提取结果是否为空
            if not extraction_result:
                log_error(LoggerName.API, "内容分析跳过 - 提取结果为空")
                return {
                    "error": "提取结果为空，无法进行分析",
                    "content_type": ContentType.UNKNOWN
                }
            
            # 判断内容类型
            content_type = self._detect_content_type(extraction_result)
            
            # 根据类型选择合适的分析器
            if content_type == ContentType.ARTICLE:
                return await self.analyze_article(extraction_result)
            elif content_type == ContentType.VIDEO:
                return await self.analyze_video(extraction_result)
            else:
                log_error(LoggerName.API, "内容分析失败 - 无法确定内容类型")
                return {
                    **extraction_result,
                    "error": "无法确定内容类型，分析失败",
                    "content_type": ContentType.UNKNOWN
                }
                
        except Exception as e:
            log_error(LoggerName.API, "统一内容分析失败", 
                     error_type=type(e).__name__,
                     error_message=str(e))
            
            return {
                **extraction_result,
                "error": f"内容分析失败: {str(e)}"
            }
    
    def _detect_content_type(self, extraction_result: Dict[str, Any]) -> str:
        """
        检测内容类型
        
        Args:
            extraction_result: 内容提取结果
            
        Returns:
            内容类型
        """
        # 优先使用显式设置的类型
        if "content_type" in extraction_result:
            return extraction_result["content_type"]
        
        # 根据内容特征判断
        if "content" in extraction_result and extraction_result["content"]:
            return ContentType.ARTICLE
        
        if any(key in extraction_result for key in ["transcript", "subtitle", "video_url"]):
            return ContentType.VIDEO
        
        # 根据源URL判断
        if "source_url" in extraction_result:
            url = extraction_result["source_url"]
            parsed_url = urlparse(url)
            domain = parsed_url.netloc.lower()
            
            video_domains = ["bilibili.com", "b23.tv", "douyin.com", "youtube.com", "youtu.be"]
            article_domains = ["mp.weixin.qq.com", "zhihu.com", "toutiao.com"]
            
            for d in video_domains:
                if d in domain:
                    return ContentType.VIDEO
            
            for d in article_domains:
                if d in domain:
                    return ContentType.ARTICLE
        
        # 无法确定类型
        return ContentType.UNKNOWN
    
    async def analyze_article(self, extraction_result: Dict[str, Any]) -> Dict[str, Any]:
        """
        分析文章内容
        
        Args:
            extraction_result: 文章提取结果
            
        Returns:
            分析结果
        """
        try:
            # 记录开始时间
            start_time = time.time()
            title = extraction_result.get("title", "")
            content = extraction_result.get("content", "")
            
            log_info(LoggerName.API, "开始分析文章", title=title)
            
            # 参数验证
            if not title or not content:
                log_error(LoggerName.API, "文章分析失败", reason="提取的文章标题或内容为空", title=title)
                return {
                    **extraction_result, 
                    "error": "提取的文章标题或内容为空",
                    "content_type": ContentType.ARTICLE
                }
            
            # 检查是否已有分析结果
            if "analysis_result" in extraction_result and extraction_result["analysis_result"]:
                analysis_result = extraction_result["analysis_result"]
            else:
                # 分析文章 - 最多重试2次
                analysis_result = None
                retry_errors = []
                
                for attempt in range(3):  # 初始尝试 + 最多2次重试
                    try:
                        logger.info(f"调用文章分析器 (尝试 {attempt+1}/3)，标题长度: {len(title)}, 内容长度: {len(content)}")
                        analysis_result = analyze_article(title, content, self.article_api_key)
                        
                        # 检查结果是否有效
                        if self._is_valid_analysis_result(analysis_result):
                            break
                        
                        logger.warning(f"文章分析返回结果无效，尝试 {attempt+1}/3")
                        retry_errors.append("无效结果")
                        
                    except Exception as e:
                        error_msg = f"{type(e).__name__}: {str(e)}"
                        log_error(LoggerName.API, "文章分析API调用失败", error_message=error_msg, attempt=attempt+1)
                        retry_errors.append(error_msg)
                
                # 如果所有尝试都失败
                if not analysis_result or not self._is_valid_analysis_result(analysis_result):
                    if retry_errors:
                        error_detail = f"分析失败(尝试3次): {'; '.join(retry_errors[:3])}"
                        log_error(LoggerName.API, error_detail)
                        
                    # 使用基本提取作为后备
                    logger.warning("使用基本关键词提取作为后备方案")
                    analysis_result = [{
                        "keywords": self._extract_basic_keywords(title, content),
                        "recommended_titles": [title] if title else ["文章分析"],
                        "content_directions": ["提供关于主题的深入分析和见解"]
                    }]
            
            # 标准化并合并结果
            standardized_result = self._standardize_article_result(analysis_result)
            result = {
                **extraction_result,
                "analysis_result": standardized_result,
                "content_type": ContentType.ARTICLE,
                "processing_time": round(time.time() - start_time, 2)
            }
            
            # 修复：standardized_result是列表而不是字典
            keywords_count = 0
            titles_count = 0
            if standardized_result and isinstance(standardized_result, list):
                # 计算所有项目中的关键词总数
                keywords_count = sum(len(item.get("keywords", [])) for item in standardized_result if isinstance(item, dict))
                # 计算所有项目中的标题总数
                titles_count = sum(1 for item in standardized_result if isinstance(item, dict) and item.get("recommended_title"))
            
            log_info(LoggerName.API, "文章分析完成", 
                    title=title,
                    processing_time=result["processing_time"],
                    keywords_count=keywords_count,
                    titles_count=titles_count)
            
            return result
            
        except Exception as e:
            log_error(LoggerName.API, "文章分析失败", 
                     title=extraction_result.get("title", ""),
                     error_type=type(e).__name__,
                     error_message=str(e))
            
            return {
                **extraction_result,
                "error": f"文章分析失败: {str(e)}",
                "content_type": ContentType.ARTICLE
            }
            
    def _is_valid_analysis_result(self, result) -> bool:
        """检查分析结果是否有效"""
        if not result:
            return False
            
        if isinstance(result, list):
            return any(
                isinstance(item, dict) and (
                    (item.get("keywords") and len(item["keywords"]) > 0) or
                    (item.get("recommended_titles") and len(item["recommended_titles"]) > 0)
                )
                for item in result
            )
            
        if isinstance(result, dict):
            return (
                (result.get("keywords") and len(result["keywords"]) > 0) or
                (result.get("recommended_titles") and len(result["recommended_titles"]) > 0)
            )
            
        return False
    
    def _extract_basic_keywords(self, title: str, content: str) -> List[str]:
        """从标题和内容中提取基本关键词"""
        import re
        from collections import Counter
        
        # 合并标题和内容前300个字符
        text = f"{title} {content[:300]}"
        
        # 移除标点符号和特殊字符
        text = re.sub(r'[^\w\s]', ' ', text)
        
        # 提取中英文单词
        words = re.findall(r'[a-zA-Z\u4e00-\u9fa5]{2,}', text)
        
        # 计数并获取频率最高的单词
        counter = Counter(words)
        
        # 返回前10个最常见的单词作为关键词
        return [word for word, _ in counter.most_common(10)]
    
    def _standardize_article_result(self, analysis_result: Any) -> List[Dict[str, Any]]:
        """
        标准化文章分析结果为结构化格式
        
        Args:
            analysis_result: 原始分析结果
            
        Returns:
            标准化的分析结果列表，每一项都包含keywords, recommended_title, content_direction
        """
        # 初始化结果
        structured_results = []
        
        # 分析结果可能是列表或字典
        if isinstance(analysis_result, list):
            logger.info(f"标准化列表格式的分析结果，长度：{len(analysis_result)}")
            
            # 检查列表中的项目是否已经是结构化格式
            is_structured_format = all(
                isinstance(item, dict) and "keywords" in item and 
                "recommended_title" in item and "content_direction" in item
                for item in analysis_result if isinstance(item, dict)
            )
            
            if is_structured_format:
                logger.info("分析结果已经是结构化格式，直接使用")
                structured_results = analysis_result
            else:
                # 处理非标准格式，尝试构建结构化结果
                for item in analysis_result:
                    if isinstance(item, dict):
                        # 提取关键词
                        keywords = []
                        if "keywords" in item and isinstance(item["keywords"], list):
                            keywords = item.get("keywords", [])[:5]  # 限制为最多5个关键词
                                
                            # 提取标题
                            recommended_title = ""
                            if "recommended_title" in item:
                                recommended_title = item["recommended_title"]
                            elif "recommended_titles" in item and isinstance(item["recommended_titles"], list) and item["recommended_titles"]:
                                recommended_title = item["recommended_titles"][0]
                            elif "title" in item:
                                recommended_title = item["title"]
                                    
                            # 提取内容方向
                            content_direction = ""
                            if "content_direction" in item:
                                content_direction = item["content_direction"]
                            elif "content_directions" in item and isinstance(item["content_directions"], list) and item["content_directions"]:
                                content_direction = item["content_directions"][0]
                            elif "direction" in item:
                                content_direction = item["direction"]
                                    
                            # 确保至少有一个字段有内容
                            if keywords or recommended_title or content_direction:
                                structured_item = {
                                    "keywords": keywords,
                                    "recommended_title": recommended_title,
                                    "content_direction": content_direction
                                }
                                structured_results.append(structured_item)
            
        # 如果分析结果是字典格式，转换为单个项目的列表
        elif isinstance(analysis_result, dict):
            logger.info("标准化字典格式的分析结果")
            
            # 检查是否已经是结构化格式
            if "keywords" in analysis_result and "recommended_title" in analysis_result and "content_direction" in analysis_result:
                structured_results = [analysis_result]
            else:
                # 提取关键词
                keywords = []
                if "keywords" in analysis_result and isinstance(analysis_result["keywords"], list):
                    keywords = analysis_result.get("keywords", [])[:5]
                    
                # 提取标题（从复数形式字段或其他可能的字段）
                recommended_title = ""
                if "recommended_title" in analysis_result:
                    recommended_title = analysis_result["recommended_title"]
                elif "recommended_titles" in analysis_result and isinstance(analysis_result["recommended_titles"], list) and analysis_result["recommended_titles"]:
                    recommended_title = analysis_result["recommended_titles"][0]
                elif "title" in analysis_result:
                    recommended_title = analysis_result["title"]
                        
                # 提取内容方向（从复数形式字段或其他可能的字段）
                content_direction = ""
                if "content_direction" in analysis_result:
                    content_direction = analysis_result["content_direction"]
                elif "content_directions" in analysis_result and isinstance(analysis_result["content_directions"], list) and analysis_result["content_directions"]:
                    content_direction = analysis_result["content_directions"][0]
                elif "direction" in analysis_result:
                    content_direction = analysis_result["direction"]
                        
                # 创建结构化项
                structured_item = {
                    "keywords": keywords,
                    "recommended_title": recommended_title,
                    "content_direction": content_direction
                }
                structured_results.append(structured_item)
        
        # 如果仍然没有结构化结果，创建一个兼容的基本结果
        if not structured_results:
            logger.warning("无法从分析结果中提取结构化数据，创建基本兼容结果")
            
            # 创建一个基本的结构化项
            structured_results.append({
                "keywords": [],
                "recommended_title": "",
                "content_direction": ""
            })
        
        # 清理并验证每个结构化项中的数据
        for item in structured_results:
            # 确保keywords是列表
            if "keywords" in item:
                if not isinstance(item["keywords"], list):
                    item["keywords"] = [str(item["keywords"])]
                    # 限制关键词数量
                    item["keywords"] = item["keywords"][:5] 
                else:
                    # 已经是列表，只需限制长度
                    item["keywords"] = item["keywords"][:5]
            else:
                item["keywords"] = []
            
            # 确保字符串字段是有效的字符串
            for string_field in ["recommended_title", "content_direction"]:
                if string_field in item:
                    if not isinstance(item[string_field], str):
                        item[string_field] = str(item[string_field])
                else:
                    item[string_field] = ""
        
        logger.info(f"标准化完成：共{len(structured_results)}个结构化项目")
        
        return structured_results
    
    def _standardize_video_result(self, analysis_result: Any, extraction_result: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        标准化视频分析结果
        
        Args:
            analysis_result: 原始分析结果
            extraction_result: 提取的视频内容结果
            
        Returns:
            标准化的分析结果列表，每项包含keywords、recommended_title和content_direction
        """
        # 初始化结果列表
        structured_results = []
        
        try:
            # 如果分析结果已经是列表格式，检查每个项目的结构
            if isinstance(analysis_result, list):
                logger.info(f"标准化视频分析结果，列表长度: {len(analysis_result)}")
                
                # 如果列表中的项目已经具有正确的结构，直接使用
                has_correct_format = all(
                    isinstance(item, dict) and 
                    "keywords" in item and 
                    "recommended_title" in item and 
                    "content_direction" in item
                    for item in analysis_result if isinstance(item, dict)
                )
                
                if has_correct_format:
                    logger.info("视频分析结果已经是规范格式，直接使用")
                    # 更改：确保使用所有正确格式的项目，而不是覆盖structured_results
                    structured_results = analysis_result
                    logger.info(f"保留了 {len(structured_results)} 条推荐思路")
                else:
                    # 标准化每个项目
                    for item in analysis_result:
                        if isinstance(item, dict):
                            structured_item = self._convert_video_item_to_structured(item, extraction_result)
                            if structured_item:
                                structured_results.append(structured_item)
                    logger.info(f"标准化处理后得到 {len(structured_results)} 条推荐思路")
            # 如果是字典格式，转换为单项的列表
            elif isinstance(analysis_result, dict):
                logger.info("标准化字典格式的视频分析结果")
                structured_item = self._convert_video_item_to_structured(analysis_result, extraction_result)
                if structured_item:
                    structured_results.append(structured_item)
            
            # 如果没有有效的结构化结果，创建一个基本项
            if not structured_results:
                logger.warning("无法从视频分析结果中提取有效数据，创建基本项")
                structured_results.append({
                    "keywords": [],
                    "recommended_title": extraction_result.get("title", "视频内容分析"),
                    "content_direction": "基于视频内容的分析"
                })
            
            logger.info(f"视频分析标准化完成，共{len(structured_results)}个项目")
            return structured_results
            
        except Exception as e:
            log_error(LoggerName.API, "标准化视频分析结果出错", 
                     error_type=type(e).__name__,
                     error_message=str(e),
                     title=extraction_result.get("title", "未知视频"))
            
            # 创建一个基本的结构化项目
            return [{
                "keywords": [],
                "recommended_title": extraction_result.get("title", "视频内容分析"),
                "content_direction": "视频内容分析失败，请重试"
            }]
    
    def _convert_video_item_to_structured(self, item: Dict[str, Any], extraction_result: Dict[str, Any]) -> Dict[str, Any]:
        """转换视频分析项为标准结构化格式"""
        # 提取关键词
        keywords = []
        if "keywords" in item and isinstance(item["keywords"], list):
            keywords = item["keywords"][:5]  # 限制最多5个关键词
        
        # 提取标题
        recommended_title = ""
        if "recommended_title" in item:
            recommended_title = item["recommended_title"]
        elif "title" in item:
            recommended_title = item["title"]
        elif "summary" in item:
            # 如果没有标题但有摘要，使用摘要前部分作为标题
            summary = item.get("summary", "")
            recommended_title = summary[:30] + ("..." if len(summary) > 30 else "")
        else:
            # 如果没有标题，使用视频标题
            recommended_title = extraction_result.get("title", "视频内容")
        
        # 提取内容方向
        content_direction = ""
        if "content_direction" in item:
            content_direction = item["content_direction"]
        elif "direction" in item:
            content_direction = item["direction"]
        elif "summary" in item:
            content_direction = item["summary"]
        elif "topics" in item and isinstance(item["topics"], list) and item["topics"]:
            content_direction = f"关于{item['topics'][0]}的分析与洞察"
        else:
            content_direction = f"关于{recommended_title}的深度分析"
        
        # 创建结构化项
        return {
            "keywords": keywords,
            "recommended_title": recommended_title,
            "content_direction": content_direction
        }
    
    async def analyze_video(self, extraction_result: Dict[str, Any]) -> Dict[str, Any]:
        """
        分析视频内容
        
        Args:
            extraction_result: 视频提取结果
            
        Returns:
            分析结果
        """
        try:
            # 记录开始时间
            start_time = time.time()
            log_info(LoggerName.API, "开始分析视频", 
                    title=extraction_result.get("title", ""))
            
            # 提取已有分析结果（如果有）
            if "analysis_result" in extraction_result:
                analysis_result = extraction_result["analysis_result"]
            else:
                # 分析视频
                analysis_result = await self.video_analyzer.analyze_video(extraction_result)
            
            # 新增：记录分析结果的类型和长度
            if isinstance(analysis_result, list):
                logger.info(f"视频分析返回列表结果，长度：{len(analysis_result)}")
            elif isinstance(analysis_result, dict):
                logger.info("视频分析返回字典结果")
            else:
                logger.info(f"视频分析返回其他类型结果：{type(analysis_result)}")
                
            # 检查分析结果是否为空或无效
            if not analysis_result:
                log_warning(LoggerName.API, "视频分析返回空结果，创建基本分析项")
                analysis_result = [{
                    "keywords": [],
                    "recommended_title": extraction_result.get("title", "视频内容分析"),
                    "content_direction": "基于视频内容的分析"
                }]
            
            # 确保分析结果是列表格式
            if not isinstance(analysis_result, list):
                analysis_result = [analysis_result]
                
            # 处理分析结果（标准化格式）
            standardized_result = self._standardize_video_result(analysis_result, extraction_result)
            
            # 新增：记录所有标准化项目的标题
            if standardized_result and isinstance(standardized_result, list):
                titles = [item.get('recommended_title', 'No title') for item in standardized_result if isinstance(item, dict)]
                logger.info(f"标准化后的推荐思路标题列表: {titles}")
            
            # 合并结果
            result = {
                **extraction_result,
                "analysis_result": standardized_result,
                "content_type": ContentType.VIDEO,
                "processing_time": round(time.time() - start_time, 2)
            }
            
            # 计算关键词和标题的数量
            keywords_count = 0
            titles_count = 0
            if standardized_result and isinstance(standardized_result, list):
                # 计算所有项目中的关键词总数
                keywords_count = sum(len(item.get("keywords", [])) for item in standardized_result if isinstance(item, dict))
                # 计算推荐标题数量
                titles_count = sum(1 for item in standardized_result if isinstance(item, dict) and item.get("recommended_title"))
            
            log_info(LoggerName.API, "视频分析完成", 
                    title=extraction_result.get("title", ""),
                    processing_time=result["processing_time"],
                    keywords_count=keywords_count,
                    titles_count=titles_count)
            
            return result
            
        except Exception as e:
            log_error(LoggerName.API, "视频分析失败", 
                     title=extraction_result.get("title", ""),
                     error_type=type(e).__name__,
                     error_message=str(e))
            
            return {
                **extraction_result,
                "error": f"视频分析失败: {str(e)}",
                "content_type": ContentType.VIDEO
            }

# 便捷函数
async def analyze_from_extraction_result(extraction_result: Dict[str, Any], 
                                        article_api_key: Optional[str] = None, 
                                        video_api_key: Optional[str] = None) -> Dict[str, Any]:
    """
    从提取结果中分析内容（自动判断类型）
    
    Args:
        extraction_result: 内容提取结果
        article_api_key: 文章分析API密钥
        video_api_key: 视频分析API密钥
        
    Returns:
        分析结果
    """
    analyzer = UnifiedAnalyzer(article_api_key=article_api_key, video_api_key=video_api_key)
    return await analyzer.analyze_content(extraction_result) 

async def analyze_text_content(title: str, content: str, 
                             article_api_key: Optional[str] = None) -> Dict[str, Any]:
    """
    分析纯文本内容（无需提取）
    
    Args:
        title: 文本标题
        content: 文本内容
        article_api_key: 文章分析API密钥
        
    Returns:
        分析结果
    """
    try:
        # 构建模拟的提取结果
        extraction_result = {
            "title": title,
            "content": content,
            "content_type": ContentType.ARTICLE,
            "source_url": "",
            "processed_at": time.time()
        }
        
        # 调用统一分析器
        analyzer = UnifiedAnalyzer(article_api_key=article_api_key)
        return await analyzer.analyze_content(extraction_result)
    except Exception as e:
        log_error(LoggerName.API, "文本内容分析失败", 
                 error_type=type(e).__name__,
                 error_message=str(e),
                 title=title)
        
        return {
            "error": f"文本内容分析失败: {str(e)}",
            "title": title
        } 