"""
AI文章分析模块。
使用DeepSeek模型分析文章内容，提取关键词并推荐相似文章标题。
"""
import os
import json
import logging
import time
import datetime
from typing import Dict, List, Any, Optional
from openai import OpenAI
from dataclasses import dataclass
import re
import json_repair  # 添加json_repair库导入

logger = logging.getLogger(__name__)

@dataclass
class ModelOptions:
    """模型配置选项"""
    max_tokens: int = 2000
    temperature: float = 0.7
    top_p: float = 0.9

@dataclass
class Config:
    """API配置"""
    api_key: str
    model: str = "deepseek-chat"

class ArticleAnalyzer:
    """文章分析器，提取关键词并推荐相似标题"""
    
    def __init__(self, api_key: Optional[str] = None):
        """
        初始化文章分析器
        
        Args:
            api_key: DeepSeek API密钥，如果为None则从环境变量获取
        """
        self.api_key = api_key or os.environ.get("DEEPSEEK_API_KEY", "")
        if not self.api_key:
            logger.warning("未设置DeepSeek API密钥，请设置DEEPSEEK_API_KEY环境变量或在初始化时提供")
        
        self.config = Config(api_key=self.api_key)
        self.options = ModelOptions()
        
    def _get_current_time_info(self) -> Dict[str, Any]:
        """
        获取当前时间信息
        
        Returns:
            Dict[str, Any]: 包含当前时间信息的字典
        """
        now = datetime.datetime.now()
        
        # 判断季节
        month = now.month
        if 3 <= month <= 5:
            season = "春季"
        elif 6 <= month <= 8:
            season = "夏季"
        elif 9 <= month <= 11:
            season = "秋季"
        else:
            season = "冬季"
            
        # 星期几
        weekday_map = {
            0: "星期一",
            1: "星期二",
            2: "星期三", 
            3: "星期四",
            4: "星期五",
            5: "星期六",
            6: "星期日"
        }
        weekday = weekday_map[now.weekday()]
        
        return {
            "date": now.strftime("%Y-%m-%d"),
            "time": now.strftime("%H:%M:%S"),
            "year": now.year,
            "month": now.month,
            "day": now.day,
            "weekday": weekday,
            "season": season,
            "timestamp": time.time()
        }
    
    def analyze_article(self, title: str, content: str) -> Dict[str, Any]:
        """
        分析文章内容，提取关键词并推荐相似标题
        
        Args:
            title: 文章标题
            content: 文章内容
            
        Returns:
            包含关键词和推荐标题的字典
        """
        try:
            # 检查输入参数
            if not title or not title.strip():
                logger.warning("分析文章内容时标题为空")
                return {
                    "error": "文章标题为空",
                    "keywords": [],
                    "recommended_titles": [],
                    "content_directions": []
                }
                
            if not content or not content.strip():
                logger.warning("分析文章内容时内容为空")
                return {
                    "error": "文章内容为空",
                    "keywords": [],
                    "recommended_titles": [],
                    "content_directions": []
                }
            
            # 准备分析提示词，添加时间信息
            prompt = self._prepare_analysis_prompt(title, content)
            
            # 调用模型获取分析结果
            result = self._call_model(prompt)
            
            # 解析模型返回的结果
            parsed_result = self._parse_analysis_result(result)
            
            return parsed_result
        except json.JSONDecodeError as e:
            logger.error(f"分析文章内容时JSON解析错误: {e}")
            return {
                "error": f"JSON解析错误: {e}",
                "keywords": [],
                "recommended_titles": [],
                "content_directions": []
            }
        except ValueError as e:
            logger.error(f"分析文章内容时值错误: {e}")
            return {
                "error": f"值错误: {e}",
                "keywords": [],
                "recommended_titles": [],
                "content_directions": []
            }
        except Exception as e:
            logger.error(f"分析文章内容时发生未知错误: {str(e)}", exc_info=True)
            return {
                "error": f"内容分析失败: {str(e)}",
                "keywords": [],
                "recommended_titles": [],
                "content_directions": []
            }
    
    def _prepare_analysis_prompt(self, title: str, content: str) -> str:
        """
        准备分析提示词
        
        Args:
            title: 文章标题
            content: 文章内容
            
        Returns:
            格式化的提示词
        """
        # 截断过长的内容
        if len(content) > 6000:
            content = content[:6000] + "...(内容已截断)"
        
        # 获取当前时间信息
        time_info = self._get_current_time_info()
        logger.info(f"分析时间: {time_info['date']} {time_info['time']}, {time_info['season']} {time_info['weekday']}")
        
        return f"""
            当前时间信息：
            - 日期: {time_info['date']}
            - 时间: {time_info['time']}
            - 季节: {time_info['season']}
            - 星期: {time_info['weekday']}
            
            文章标题：{title}

            文章内容：
            {content}
            
            请考虑当前时间因素（{time_info['season']}、{time_info['weekday']}），可以在推荐中适当体现时令或当前热点。
            """
    
    def _call_model(self, prompt: str) -> str:
        """
        调用DeepSeek模型
        
        Args:
            prompt: 提示词
            
        Returns:
            模型返回的文本
        """
        sys_prompt = """
# Role：热点文章分析与思路推荐专家  

## Background：  
用户已经获取了一篇热点文章的标题和正文内容，希望对其进行深度分析，并生成10条不同的文章思路。每条思路需包含标题、5个最能概括文章内容的关键词，以及一句话概述文章方向。如果内容包含英文，请先将其翻译为中文再进行分析。

## Attention：  
- 如果提供的文章包含英文内容，请先将其翻译为中文，再进行后续分析。
- 文章分析需提取核心信息，确保推荐的文章思路具有代表性和创意。  
- 每条思路需涵盖 **标题、关键词（5个）、文章摘要描述（简明扼要）**。  
- 推荐的一组关键词需有部分与提供的文章内容相关，每组关键词能够表述文章的方向。  
- 每组关键词应该思维发散，从能够从提供的热点文章为圆心发散，扩散提供更多的写文思路。
- 提供的每组思路或者文章思路要具有明显的差异性，不同得到的文章同质性非常高。
- 文章方向应逻辑清晰，能够为进一步创作提供明确指引。  
- 输出格式需规范，便于用户快速阅读和选择合适的思路。  

## Profile：  
- Author: pp  
- Version: 2.1  
- Language: 中文  
- Description: 你是一名专业的热点文章分析与创作思路专家，擅长从文本中提炼关键信息，并提供清晰、有创意的文章方向建议。  

### Skills:  
- 英文内容翻译能力，能够准确将英文文章翻译为中文。
- 高效提取文章核心信息，精准提炼关键词。  
- 具备内容创作思维，能够提供多角度的文章思路。  
- 逻辑清晰，能确保输出的思路方向明确且富有创意。  
- 具备热点敏感度，能结合时事趋势推荐符合当前热点的文章方向。  

## Goals:  
- 对英文内容进行准确翻译。
- 分析输入的热点文章，提取其核心主题和主要信息点。  
- 生成10条不同的文章思路，每条包含 **标题、关键词（5个）、文章方向描述**。  
- 确保关键词精准概括文章内容，文章方向简明扼要。  
- 输出格式清晰，方便用户快速理解和使用。  

## Constrains:  
- 关键词数量严格限定为5个，需精准、完整概括推荐文章核心内容。  
- 文章方向描述需简短直接，避免冗长或模糊表达。  
- 输出的10条思路需涵盖不同角度，避免过度重复。  
- 所有推荐思路需符合逻辑，不得生成与文章主题完全无关的内容。  

## Workflow:  
1. **翻译与解析文章内容**：  
- 如有英文内容，先将其翻译为中文。
- 读取用户提供的热点文章标题和正文。  
- 提取文章的核心主题、主要观点和关键事件。  
2. **生成关键词**：  
- 识别文章中的高频词汇、核心概念和关键人物/事件。  
- 归纳整理出 **5个最能概括文章内容的关键词**。  
3. **构思文章思路**：  
- 从不同角度扩展文章主题，形成10条不同的文章方向。  
- 确保思路涵盖多个维度，如**社会影响、历史背景、未来趋势、争议点、精神价值、世界观、人物性格、深度解析等**。  
4. **格式化输出**：  
- 以 **参考示例** 方式输出10条文章思路，每条包含：  
    - **标题**（精准概括主题）  
    - **关键词**（5个核心概念）  
    - **文章方向**（一句话说明文章主要探讨方向）  
5. **检查与优化**：  
- 确保10条思路逻辑清晰、不重复、方向明确。  
- 确保关键词准确且具有代表性。  

## OutputFormat:  
[
    {
        "keywords": ["关键词1", "关键词2", ...，"关键词5"],
        "recommended_titles": "标题",
        "content_directions": "一句话概括"
    },
    {
        "keywords": ["关键词1", "关键词2", ...，"关键词5"],
        "recommended_titles": "标题",
        "content_directions": "一句话概括"
    },
    {
        "keywords": ["关键词1", "关键词2", ...，"关键词5"],
        "recommended_titles": "标题",
        "content_directions": "一句话概括"
    }
]
        """
        try:
            client = OpenAI(
                api_key=self.config.api_key,
                base_url=os.getenv('DEEPSEEK_API_BASE', 'https://api.deepseek.com')
            )
            
            response = client.chat.completions.create(
                model=self.config.model,
                messages=[
                    {"role": "system", "content": sys_prompt},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=self.options.max_tokens,
                temperature=self.options.temperature,
                top_p=self.options.top_p
            )
            
            return response.choices[0].message.content
        except Exception as e:
            logger.error(f"调用DeepSeek模型时发生错误: {e}")
            raise
    
    def _parse_analysis_result(self, result: str) -> Dict[str, Any]:
        """
        解析模型返回的分析结果
        
        Args:
            result: 模型返回的文本
            
        Returns:
            解析后的结果字典
        """
        if not result or len(result.strip()) < 10:
            logger.warning("模型返回结果为空或过短")
            return [{
                "keywords": [],
                "recommended_title": "",
                "content_direction": ""
            }]
            
        logger.info(f"开始解析分析结果，长度：{len(result)}，预览：{result[:100]}...")
        
        # 保存原始输入，用于调试
        raw_result = result
        
        # 提取所有可能的关键词，作为最后的备选
        all_potential_keywords = re.findall(r'["\']([\w\s\u4e00-\u9fa5]+)["\']', result)
        backup_keywords = [kw for kw in all_potential_keywords if len(kw) > 1 and not re.match(r'^[\d\s]+$', kw)][:10]
        
        # 首先清理结果文本
        # 1. 移除Markdown代码块标记
        result = re.sub(r'^```json\s*', '', result)
        result = re.sub(r'\s*```$', '', result)
        result = re.sub(r'^```\s*', '', result)
        result = re.sub(r'\s*```$', '', result)
        
        # 2. 清理带引导说明的文本
        # 例如："以下是10条文章思路："
        intro_match = re.search(r'^.*?(?=\[)', result)
        if intro_match and intro_match.group().strip():
            logger.info(f"移除介绍文本：{intro_match.group().strip()}")
            result = result[intro_match.end():].strip()
        
        # 3. 尝试提取最外层的JSON数组
        if result.startswith('[') and ']' in result:
            # 找到匹配的结束括号
            bracket_count = 0
            end_pos = -1
            
            for i, char in enumerate(result):
                if char == '[':
                    bracket_count += 1
                elif char == ']':
                    bracket_count -= 1
                    if bracket_count == 0:
                        end_pos = i + 1
                        break
            
            if end_pos > 0:
                result = result[:end_pos]
        
        # 4. 使用json_repair尝试修复JSON格式
        try:
            logger.info("尝试使用json_repair修复JSON")
            repaired_json = json_repair.repair_json(result)
            data = json.loads(repaired_json)
            
            if isinstance(data, list):
                logger.info(f"成功解析JSON数组，包含{len(data)}个项目")
                # 标准化结果
                formatted_results = []
                for item in data:
                    if isinstance(item, dict):
                        formatted_item = self._normalize_item(item)
                        if any(formatted_item.values()):
                            formatted_results.append(formatted_item)
                
                if formatted_results:
                    return formatted_results
            elif isinstance(data, dict):
                logger.info("成功解析单个JSON对象")
                formatted_item = self._normalize_item(data)
                if any(formatted_item.values()):
                    return [formatted_item]
                    
        except Exception as e:
            logger.warning(f"json_repair修复失败: {str(e)}")
        
        # 5. 尝试手动构建对象
  
    def _normalize_item(self, item: Dict[str, Any]) -> Dict[str, Any]:
        """标准化数据项的格式"""
        # 提取关键词
        keywords = []
        if "keywords" in item and isinstance(item["keywords"], list):
            keywords = [k for k in item["keywords"] if isinstance(k, str)]
        
        # 处理标题
        recommended_title = ""
        for field in ["recommended_title", "recommended_titles", "title", "titles"]:
            if field in item:
                value = item[field]
                if isinstance(value, list) and value:
                    recommended_title = str(value[0])
                elif value:
                    recommended_title = str(value)
                if recommended_title:
                    break
        
        # 处理内容方向
        content_direction = ""
        for field in ["content_direction", "content_directions", "direction", "directions"]:
            if field in item:
                value = item[field]
                if isinstance(value, list) and value:
                    content_direction = str(value[0])
                elif value:
                    content_direction = str(value)
                if content_direction:
                    break
        
        return {
            "keywords": keywords,
            "recommended_title": recommended_title,
            "content_direction": content_direction
        }

def analyze_article(title: str, content: str, api_key: Optional[str] = None) -> List[Dict[str, Any]]:
    """
    分析文章内容，提取关键词并推荐相似标题
    
    Args:
        title: 文章标题
        content: 文章内容
        api_key: DeepSeek API密钥，如果为None则从环境变量获取
        
    Returns:
        包含关键词和推荐标题的字典列表
    """
    # 输入参数校验
    if not title and not content:
        logger.warning("文章标题和内容都为空")
        return [{
            "keywords": ["空文章"],
            "recommended_title": "未提供内容",
            "content_direction": "无法分析空内容"
        }]
    
    logger.info(f"分析文章：标题长度={len(title)}, 内容长度={len(content)}")
    
    # 如果内容过长，截取前5000个字符
    if len(content) > 5000:
        content = content[:5000] + "..."
    
    # 创建分析器
    analyzer = ArticleAnalyzer(api_key)
    
    # 添加重试逻辑
    max_retries, retry_count, last_error = 2, 0, None
    
    while retry_count <= max_retries:
        try:
            # 调用DeepSeek模型
            result = analyzer.analyze_article(title, content)
            
            # 检查结果
            if _has_valid_content(result):
                return result if isinstance(result, list) else [result]
            
            # 结果为空，增加重试次数
            logger.warning(f"分析结果为空，重试 {retry_count+1}/{max_retries+1}")
            retry_count += 1
            time.sleep(1)  # 重试前短暂等待
            
        except Exception as e:
            last_error = str(e)
            logger.error(f"分析失败: {last_error}, 重试 {retry_count+1}/{max_retries+1}")
            retry_count += 1
            time.sleep(1)  # 重试前短暂等待
    
    # 所有重试都失败，使用基本提取
    logger.warning("所有分析尝试都失败，使用基本关键词提取")
    keywords = _extract_keywords_from_text(title + " " + (content[:300] if content else ""))
    
    return [{
        "keywords": keywords,
        "recommended_title": title if title else "文章分析",
        "content_direction": "提供关于主题的深入分析和见解",
        "error": last_error
    }]

def _has_valid_content(result) -> bool:
    """检查分析结果是否包含有效内容"""
    if not result:
        return False
    
    if isinstance(result, list):
        return any(
            isinstance(item, dict) and (
                item.get("keywords") or 
                item.get("recommended_titles") or 
                item.get("recommended_title") or
                item.get("content_directions") or
                item.get("content_direction")
            ) for item in result
        )
    
    if isinstance(result, dict):
        return bool(
            result.get("keywords") or 
            result.get("recommended_titles") or 
            result.get("recommended_title") or
            result.get("content_directions") or
            result.get("content_direction")
        )
    
    return False

def _extract_keywords_from_text(text: str) -> List[str]:
    """从文本中提取关键词"""
    from collections import Counter
    
    # 分词和清理
    words = re.findall(r'[a-zA-Z\u4e00-\u9fa5]{2,}', text)
    
    # 统计词频
    counter = Counter(words)
    
    # 返回前10个最常见的词
    return [word for word, _ in counter.most_common(10)]

def analyze_from_extraction_result(extraction_result: Dict[str, Any], api_key: Optional[str] = None) -> Dict[str, Any]:
    """
    从提取结果中分析文章
    
    Args:
        extraction_result: 文章提取结果
        api_key: DeepSeek API密钥，如果为None则从环境变量获取
        
    Returns:
        包含原始提取结果和分析结果的字典
    """
    if "error" in extraction_result:
        return extraction_result
    
    title = extraction_result.get("title", "")
    content = extraction_result.get("content", "")
    
    if not title or not content:
        return {**extraction_result, "error": "提取的文章标题或内容为空"}
    
    analysis_result = analyze_article(title, content, api_key)
    # print(analysis_result)
    
    # 合并提取结果和分析结果
    return {**extraction_result, "analysis_result": analysis_result}
