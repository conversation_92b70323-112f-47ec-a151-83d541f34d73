"""
视频分析器 - 分析视频内容并提取关键信息
"""
from typing import Dict, Any, List, Optional, Union
import re
import json
import time
import asyncio
import datetime
from langchain_openai import ChatOpenAI
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.output_parsers import StrOutputParser
import jieba
import json_repair  # 添加json_repair库

from src.utils.logger import log_info, log_error, log_warning, CrawlerLogger
from src.config.logging_config import LoggerName
from src.config.api_config import get_deepseek_api_key

logger = CrawlerLogger().get_logger(LoggerName.API)

class VideoAnalyzer:
    """视频内容分析器"""
    
    def __init__(self, api_key: Optional[str] = None, model: str = "deepseek-chat"):
        """初始化视频分析器
        
        Args:
            api_key: 可选的DeepSeek API密钥
            model: 使用的DeepSeek模型
        """
        self.api_key = api_key or get_deepseek_api_key()
        # 使用ChatOpenAI但配置DeepSeek的API地址
        self.llm = ChatOpenAI(
            model=model,
            api_key=self.api_key,
            temperature=0.7,
            base_url="https://api.deepseek.com/v1",  # DeepSeek API端点
        )
        
        # 缓存词频分析结果
        self._word_freq_cache = {}
    
    def _get_current_time_info(self) -> Dict[str, Any]:
        """获取当前时间信息
        
        Returns:
            Dict[str, Any]: 包含当前时间信息的字典
        """
        now = datetime.datetime.now()
        
        # 判断季节
        month = now.month
        if 3 <= month <= 5:
            season = "春季"
        elif 6 <= month <= 8:
            season = "夏季"
        elif 9 <= month <= 11:
            season = "秋季"
        else:
            season = "冬季"
            
        # 星期几
        weekday_map = {
            0: "星期一",
            1: "星期二",
            2: "星期三", 
            3: "星期四",
            4: "星期五",
            5: "星期六",
            6: "星期日"
        }
        weekday = weekday_map[now.weekday()]
        
        return {
            "date": now.strftime("%Y-%m-%d"),
            "time": now.strftime("%H:%M:%S"),
            "year": now.year,
            "month": now.month,
            "day": now.day,
            "weekday": weekday,
            "season": season,
            "timestamp": time.time()
        }
    
    async def analyze_video(self, video_data: Dict[str, Any]) -> Any:
        """分析视频内容并返回关键信息
        
        Args:
            video_data: 视频数据，包含标题、描述、内容和评论
            
        Returns:
            分析结果列表，包含关键词、推荐标题和内容方向
        """
        try:
            start_time = time.time()
            log_info(LoggerName.API, "开始分析视频内容", title=video_data.get("title", ""))
            
            # 准备输入文本
            input_text = self._prepare_input_text(video_data)
            
            # 使用统一分析方法一次性完成所有分析
            analysis_result = await self._unified_analyze_video(input_text, video_data)
            
            # 记录处理时间
            processing_time = round(time.time() - start_time, 2)
            
            # 正确处理分析结果（列表格式）
            if isinstance(analysis_result, list):
                # 计算所有项中的关键词总数
                total_keywords = sum(len(item.get("keywords", [])) for item in analysis_result if isinstance(item, dict))
                # 记录日志
                log_info(LoggerName.API, "视频内容分析完成", 
                        title=video_data.get("title", ""),
                        keyword_count=total_keywords,
                        item_count=len(analysis_result),
                        processing_time=processing_time)
            else:
                # 如果不是列表（应该不会出现，但以防万一）
                keyword_count = 0
                if isinstance(analysis_result, dict) and "keywords" in analysis_result:
                    keyword_count = len(analysis_result["keywords"])
                
                log_info(LoggerName.API, "视频内容分析完成", 
                        title=video_data.get("title", ""),
                        keyword_count=keyword_count,
                        processing_time=processing_time)
            
            return analysis_result
            
        except Exception as e:
            log_error(LoggerName.API, "视频内容分析失败", 
                     title=video_data.get("title", ""),
                     error_message=str(e))
            
            # 出错时返回基本结构（符合列表格式）
            return [{
                "keywords": [],
                "recommended_title": video_data.get("title", "视频内容分析"),
                "content_direction": f"关于{video_data.get('title', '视频')}的分析"
            }]
    
    def _prepare_input_text(self, video_data: Dict[str, Any]) -> str:
        """准备分析所需的输入文本
        
        Args:
            video_data: 视频数据
            
        Returns:
            用于分析的文本
        """
        parts = []
        
        # 添加标题
        if "title" in video_data and video_data["title"]:
            parts.append(f"标题: {video_data['title']}")
        
        # 添加描述
        if "description" in video_data and video_data["description"]:
            parts.append(f"描述: {video_data['description']}")
        
        # 添加转录内容
        transcript = ""
        if "transcript" in video_data and video_data["transcript"]:
            transcript = video_data["transcript"]
        elif "content" in video_data and isinstance(video_data["content"], dict) and "transcript" in video_data["content"]:
            transcript = video_data["content"]["transcript"]
        
        if transcript:
            # 限制转录文本长度
            if len(transcript) > 5000:
                transcript = transcript[:5000] + "... (转录文本已截断)"
            parts.append(f"转录内容: {transcript}")
        
        # 添加字幕内容
        if "subtitle" in video_data and video_data["subtitle"]:
            subtitle = video_data["subtitle"]
            if len(subtitle) > 5000:
                subtitle = subtitle[:5000] + "... (字幕已截断)"
            parts.append(f"字幕: {subtitle}")
        
        # 添加评论内容
        comments = []
        if "comments" in video_data and isinstance(video_data["comments"], list):
            for comment in video_data["comments"][:10]:  # 只使用前10条评论
                if isinstance(comment, dict) and "content" in comment:
                    comments.append(comment["content"])
            
            if comments:
                parts.append(f"热门评论: {'; '.join(comments)}")
        
        # 组合所有部分
        return "\n\n".join(parts)
    
    
    async def _unified_analyze_video(self, input_text: str, video_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """统一分析视频内容
        
        Args:
            input_text: 视频相关文本(标题、描述、内容、评论等)
            video_data: 原始视频数据
            
        Returns:
            List[Dict]: 包含结构化分析项目的列表，每个项目包含:
                - keywords: 关键词列表
                - recommended_title: 推荐标题
                - content_direction: 内容方向
        """
        # 如果内容太少，使用基本提取
        if len(input_text) < 100:
            log_warning(LoggerName.API, "输入文本太短，使用基本关键词提取")
            return self._extract_basic_keywords(input_text, video_data)
        
        # 获取当前时间信息
        current_time = self._get_current_time_info()
        log_info(LoggerName.API, f"分析时间: {current_time['date']} {current_time['time']}, {current_time['season']} {current_time['weekday']}")
        
        # 设置提示词，加入当前时间信息
        prompt = f"""你是一位专业的内容分析专家，需要对以下视频内容进行全面分析。如果内容包含英文，请先将其翻译为中文再进行分析。
当前时间信息：
- 日期: {current_time['date']}
- 时间: {current_time['time']}
- 季节: {current_time['season']}
- 星期: {current_time['weekday']}

请考虑上述时间因素对内容的影响，如果是季节性或时事内容，请在推荐中适当体现。

视频内容：{input_text}
# Role：视频内容分析与思路推荐专家  

## Background：  
用户已经获取了一篇热点视频的内容，希望对其中内容和评论进行深度分析，并生成10条不同的文章思路。每条思路需包含标题、5个最能概括文章内容的关键词，以及一句话概述文章方向。如果内容包含英文，请先将其翻译为中文再进行分析。

## Attention：  
- 如果提供的内容包含英文内容，请先将其翻译为中文，再进行后续分析。
- 文章分析需提取核心信息，确保推荐的文章思路具有代表性和创意。  
- 每条思路需涵盖 **标题、关键词（5个）、文章摘要描述（简明扼要）**。  
- 推荐的一组关键词需有部分与提供的文章内容相关，每组关键词能够表述文章的方向。  
- 每组关键词应该思维发散，从能够从提供的热点文章为圆心发散，扩散提供更多的写文思路。
- 提供的每组思路或者文章思路要具有明显的差异性，不同得到的文章同质性非常高。
- 文章方向应逻辑清晰，能够为进一步创作提供明确指引。  
- 输出格式需规范，便于用户快速阅读和选择合适的思路。  
- 请考虑当前时间因素（{current_time['season']}、{current_time['weekday']}），在推荐中适当体现时令或当前热点。
- 必须生成完整的10条思路推荐，每条包含独立的标题、关键词和文章方向。

## OutputFormat:  
```json
[
    {{
        "keywords": ["关键词1", "关键词2", "关键词3", "关键词4", "关键词5"],
        "recommended_title": "标题1",
        "content_direction": "一句话概括1"
    }},
    {{
        "keywords": ["关键词1", "关键词2", "关键词3", "关键词4", "关键词5"],
        "recommended_title": "标题2",
        "content_direction": "一句话概括2"
    }},
    // ...其余8条思路...
]
```
"""

        try:
            log_info(LoggerName.API, "开始执行统一视频分析，输入文本长度: " + str(len(input_text)))
            
            # 改用字符串替换而不是format，避免花括号冲突
            formatted_prompt = prompt.replace("{0}", input_text)
            message = [{"role": "user", "content": formatted_prompt}]
            
            # 使用LangChain进行调用
            chain = self.llm
            response = await chain.ainvoke(message)
            
            # 提取回复文本
            reply_text = response.content
            print("这里返回的文本是：", reply_text)
            
            # 提取JSON部分
            json_text = self._extract_json_from_text(reply_text)
            if not json_text:
                log_error(LoggerName.API, "未能从回复中提取JSON，尝试基本分析", 
                            title=video_data.get("title", ""))
                return self._extract_basic_keywords(input_text, video_data)
            
            # 解析JSON
            try:
                log_info(LoggerName.API, f"开始解析JSON，长度: {len(json_text)}")
                analysis_result = json.loads(json_text)
                
                # 确保结果为列表格式
                if not isinstance(analysis_result, list):
                    log_warning(LoggerName.API, "JSON解析结果不是列表格式，将其转换为列表")
                    analysis_result = [analysis_result]
                
                # 记录解析结果数量
                log_info(LoggerName.API, f"成功解析JSON，获取到 {len(analysis_result)} 个推荐思路")
                
                # 记录每条思路的标题，方便调试
                if len(analysis_result) > 0:
                    titles = [item.get("recommended_title", "无标题") for item in analysis_result if isinstance(item, dict)]
                    log_info(LoggerName.API, f"解析到的思路标题列表: {titles[:3]}... 等{len(titles)}个")
                    
            except json.JSONDecodeError as e:
                log_error(LoggerName.API, f"JSON解析失败: {str(e)}", 
                         title=video_data.get("title", ""))
                
                # 尝试使用额外的JSON修复方法
                try:
                    # 基本清理，移除格式问题
                    cleaned_json = json_text.replace('\\', '\\\\').replace('\n', '\\n')
                    
                    # 确保格式正确
                    if not cleaned_json.startswith('['):
                        cleaned_json = '[' + cleaned_json
                    if not cleaned_json.endswith(']'):
                        cleaned_json = cleaned_json + ']'
                    
                    # 尝试再次解析
                    analysis_result = json.loads(cleaned_json)
                    log_info(LoggerName.API, f"JSON修复成功，获取到 {len(analysis_result)} 个推荐思路")
                except:
                    # 如果仍然失败，使用基本提取
                    log_error(LoggerName.API, "JSON修复失败，使用基本关键词提取")
                    return self._extract_basic_keywords(input_text, video_data)
            
            # 标准化每个分析项的结构
            structured_items = []
            skipped_items = 0
            
            for item in analysis_result:
                if not isinstance(item, dict):
                    skipped_items += 1
                    continue
                
                # 提取关键词 - 限制为5个
                keywords = []
                if "keywords" in item and isinstance(item["keywords"], list):
                    keywords = item["keywords"][:5]
                elif "keywords" in item and isinstance(item["keywords"], str):
                    # 处理关键词是字符串的情况
                    keywords_str = item["keywords"]
                    # 尝试按不同分隔符分割
                    for sep in [',', '，', '、', ';', '；']:
                        if sep in keywords_str:
                            keywords = [k.strip() for k in keywords_str.split(sep) if k.strip()][:5]
                            break
                    # 如果没有分隔符但有内容，则将整个字符串作为一个关键词
                    if not keywords and keywords_str.strip():
                        keywords = [keywords_str.strip()]
                
                # 提取标题
                recommended_title = ""
                if "recommended_title" in item:
                    recommended_title = item["recommended_title"]
                elif "recommended_titles" in item:
                    if isinstance(item["recommended_titles"], list) and item["recommended_titles"]:
                        recommended_title = item["recommended_titles"][0]
                    else:
                        recommended_title = str(item["recommended_titles"])
                elif "title" in item:
                    recommended_title = item["title"]
                
                # 提取内容方向
                content_direction = ""
                if "content_direction" in item:
                    content_direction = item["content_direction"]
                elif "content_directions" in item:
                    if isinstance(item["content_directions"], list) and item["content_directions"]:
                        content_direction = item["content_directions"][0]
                    else:
                        content_direction = str(item["content_directions"])
                elif "direction" in item:
                    content_direction = item["direction"]
                
                # 确保至少有标题或关键词
                if recommended_title or keywords:
                    structured_item = {
                        "keywords": keywords if keywords else ["关键词1", "关键词2", "关键词3", "关键词4", "关键词5"],
                        "recommended_title": recommended_title if recommended_title else f"视频内容分析主题{len(structured_items)+1}",
                        "content_direction": content_direction if content_direction else "基于视频内容的深度分析"
                    }
                    structured_items.append(structured_item)
            
            # 记录跳过的项目
            if skipped_items > 0:
                log_warning(LoggerName.API, f"跳过了 {skipped_items} 个非字典格式的项目")
                
            # 如果没有解析出有效的结构化项目，使用基本提取
            if not structured_items:
                log_warning(LoggerName.API, "未能提取有效的结构化分析项目，使用基本提取")
                return self._extract_basic_keywords(input_text, video_data)
            
            # 确保至少有10个推荐项目（如果原始解析不足10个）
            if len(structured_items) < 10:
                log_warning(LoggerName.API, f"只解析到 {len(structured_items)} 个推荐思路，低于目标的10个")
                # 使用基本模板补充至10个
                for i in range(len(structured_items), 10):
                    # 使用视频标题构建默认项
                    video_title = video_data.get("title", "视频内容")
                    default_item = {
                        "keywords": ["视频内容", "分析", "主题", "视角", f"要点{i+1}"],
                        "recommended_title": f"{video_title} - 分析角度{i+1}",
                        "content_direction": f"从不同视角探索{video_title}的内容和影响"
                    }
                    structured_items.append(default_item)
                log_info(LoggerName.API, f"已补充到 {len(structured_items)} 个推荐思路")
            
            # 记录成功解析的结构化项目数量
            log_info(LoggerName.API, f"成功解析并标准化 {len(structured_items)} 个推荐思路")
            
            return structured_items
            
        except Exception as e:
            log_error(LoggerName.API, f"统一分析过程失败: {str(e)}", 
                     title=video_data.get("title", ""),
                     error=str(e))
            # 记录完整的错误堆栈
            import traceback
            log_error(LoggerName.API, f"错误堆栈: {traceback.format_exc()}")
            return self._extract_basic_keywords(input_text, video_data)
    
    def _extract_basic_keywords(self, input_text: str, video_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """从文本中提取基本关键词（当主分析方法失败时使用）
        
        Args:
            input_text: 视频相关文本
            video_data: 原始视频数据
            
        Returns:
            List[Dict]: 包含基本关键词、标题和内容方向的结构化列表
        """
        # 使用jieba提取关键词
        words = jieba.cut(input_text)
        word_freq = {}
        
        # 计算词频
        for word in words:
            if len(word) >= 2 and not word.isdigit() and not re.search(r'^[a-zA-Z0-9]+$', word):
                if word in word_freq:
                    word_freq[word] += 1
                else:
                    word_freq[word] = 1
        
        # 过滤常见词
        stopwords = {'的', '了', '和', '是', '在', '我', '有', '这', '个', '你', '也', '都', '说', '吧', '啊', '很', '就'}
        for word in stopwords:
            if word in word_freq:
                del word_freq[word]
        
        # 获取词频最高的词作为关键词
        keywords = sorted(word_freq.items(), key=lambda x: x[1], reverse=True)[:5]
        keywords = [word for word, _ in keywords]
        
        title = video_data.get("title", "")
        
        # 创建结构化的分析结果
        return [{
            "keywords": keywords,
            "recommended_title": title if title else "视频内容分析",
            "content_direction": "基于视频内容提供信息传递和观众互动的策略"
        }]
        
    def _extract_json_from_text(self, text: str) -> str:
        """从文本中提取JSON部分
        
        Args:
            text: 包含JSON的文本
            
        Returns:
            提取的JSON文本，如果没找到则返回空字符串
        """
        # 记录原始文本长度
        log_info(LoggerName.API, f"开始提取JSON，原始文本长度: {len(text)}")
        
        # 1. 首先尝试提取标准代码块中的JSON
        json_pattern = r'```(?:json)?\s*([\s\S]*?)\s*```'
        match = re.search(json_pattern, text)
        
        if match:
            json_content = match.group(1).strip()
            log_info(LoggerName.API, f"从代码块中提取到JSON，长度: {len(json_content)}")
            return json_content
        
        # 2. 如果文本中有完整的"10条文章思路推荐"部分，优先处理
        recommendations_pattern = r'###\s*10条文章思路推荐[:：]?\s*([\s\S]*?)(?=\n##|\Z)'
        full_recommendations = re.search(recommendations_pattern, text, re.IGNORECASE)
        
        if full_recommendations:
            recs_text = full_recommendations.group(1).strip()
            log_info(LoggerName.API, f"找到完整的10条文章思路推荐部分，长度: {len(recs_text)}")
            # 解析推荐思路
            return self._parse_recommendations(recs_text)
        
        # 3. 尝试查找最外层的方括号及其内容(JSON数组)
        bracket_pattern = r'\[\s*\{[\s\S]*\}\s*\]'
        bracket_match = re.search(bracket_pattern, text)
        
        if bracket_match:
            json_array = bracket_match.group(0).strip()
            log_info(LoggerName.API, f"找到JSON数组，长度: {len(json_array)}")
            return json_array
            
        # 4. 如果无法找到JSON数组，尝试使用json_repair修复整个文本
        try:
            log_info(LoggerName.API, f"尝试使用json_repair修复整个文本")
            repaired_json = json_repair.repair_json(text)
            # 验证修复后的文本是否为有效JSON
            json.loads(repaired_json)
            log_info(LoggerName.API, f"json_repair成功修复JSON，长度: {len(repaired_json)}")
            return repaired_json
        except Exception as e:
            log_warning(LoggerName.API, f"json_repair修复失败: {str(e)}")
            
        # 5. 如果所有方法都失败，尝试查找所有文章思路推荐的部分
        thought_pattern = r'(?:文章思路|推荐|建议)[\s\S]*?(?:\d+\.\s+.*?[\n].*?关键词.*?[\n].*?方向.*?)+' 
        thought_match = re.search(thought_pattern, text, re.DOTALL | re.IGNORECASE)
        
        if thought_match:
            recs_text = thought_match.group(0).strip()
            log_info(LoggerName.API, f"找到可能包含文章思路的文本片段，长度: {len(recs_text)}")
            return self._parse_recommendations(recs_text)
            
        # 如果所有方法都失败，记录错误并返回空字符串
        log_error(LoggerName.API, "无法从文本中提取JSON数据")
        return ""
    
    def _parse_recommendations(self, text: str) -> str:
        """解析推荐思路文本为JSON格式
        
        Args:
            text: 包含推荐思路的文本
            
        Returns:
            JSON格式的推荐思路
        """
        log_info(LoggerName.API, f"开始解析推荐思路，文本长度: {len(text)}")
        items = []
        
        # 首先检查文本是否包含JSON代码块
        json_blocks = re.findall(r'```(?:json)?\s*([\s\S]*?)\s*```', text)
        if json_blocks:
            log_info(LoggerName.API, f"找到JSON代码块，尝试直接解析")
            # 尝试解析每个JSON代码块
            for json_block in json_blocks:
                try:
                    # 使用json_repair尝试修复和解析JSON
                    repaired_json = json_repair.repair_json(json_block)
                    parsed_items = json.loads(repaired_json)
                    if isinstance(parsed_items, list) and len(parsed_items) > 0:
                        log_info(LoggerName.API, f"成功从JSON代码块解析出{len(parsed_items)}条推荐思路")
                        return json.dumps(parsed_items, ensure_ascii=False)
                except Exception as e:
                    log_warning(LoggerName.API, f"JSON代码块解析失败: {str(e)}")
                    # 继续尝试其他解析方法
        
   