"""
B站视频内容提取模块
"""
import re
import json
import time
import asyncio
from typing import Dict, Any, List, Optional, Union
from urllib.parse import urlparse, parse_qs

import aiohttp
from bs4 import BeautifulSoup

from src.services.extractor.video_extractor import VideoExtractor
from src.utils.logger import log_info, log_error, CrawlerLogger
from src.config.logging_config import LoggerName

logger = CrawlerLogger().get_logger(LoggerName.API)

class BilibiliExtractor(VideoExtractor):
    """B站视频内容提取器"""
    
    def __init__(self, api_key: Optional[str] = None, use_dynamic: bool = False):
        """初始化B站视频提取器
        
        Args:
            api_key: API密钥（B站提取器暂不需要）
            use_dynamic: 是否使用动态加载方式提取内容
        """
        super().__init__(api_key=api_key, use_dynamic=use_dynamic)
        
        # B站相关API
        self.video_info_api = "https://api.bilibili.com/x/web-interface/view"
        self.subtitle_api = "https://api.bilibili.com/x/player/v2"
        self.comment_api = "https://api.bilibili.com/x/v2/reply"
        
        # 设置特定的请求头
        self.headers.update({
            "Referer": "https://www.bilibili.com",
            "Origin": "https://www.bilibili.com"
        })
    
    def _get_platform_name(self) -> str:
        """获取平台名称"""
        return "bilibili"
    
    def extract_video_id(self, url: str) -> Optional[str]:
        """从URL中提取视频ID
        
        Args:
            url: B站视频URL
            
        Returns:
            视频ID (BVID或AID)
        """
        # 处理短链接
        if "b23.tv" in url:
            # 需要跟踪重定向获取真实URL
            return None
        
        # 匹配BV号
        bv_match = re.search(r'BV\w+', url)
        if bv_match:
            return bv_match.group()
        
        # 匹配av号
        av_match = re.search(r'av(\d+)', url.lower())
        if av_match:
            return f"av{av_match.group(1)}"
        
        # 从查询参数提取
        parsed_url = urlparse(url)
        query_params = parse_qs(parsed_url.query)
        
        # 检查bvid参数
        if 'bvid' in query_params:
            return query_params['bvid'][0]
        
        # 检查aid参数
        if 'aid' in query_params:
            return f"av{query_params['aid'][0]}"
        
        return None
    
    async def _extract_static(self, url: str) -> Dict[str, Any]:
        """静态方式提取B站视频内容
        
        Args:
            url: B站视频URL
            
        Returns:
            视频信息字典
        """
        try:
            # 提取视频ID
            video_id = self.extract_video_id(url)
            if not video_id:
                # 如果无法直接提取ID，可能是短链接，需要请求获取真实URL
                real_url = await self._resolve_short_url(url)
                if not real_url:
                    return {"error": "无法解析视频ID"}
                
                video_id = self.extract_video_id(real_url)
                if not video_id:
                    return {"error": "无法获取视频ID"}
                
                url = real_url
            
            # 获取视频基本信息
            video_info = await self._fetch_video_info(video_id)
            if "error" in video_info:
                return video_info
            
            # 获取字幕信息
            subtitle_info = await self._fetch_subtitle_info(video_id)
            
            # 获取评论信息
            comments = await self._fetch_comments(video_id)
            
            # 整合结果
            result = {
                "title": video_info.get("title", ""),
                "description": video_info.get("desc", ""),
                "author": video_info.get("owner", {}).get("name", ""),
                "duration": video_info.get("duration", 0),
                "publish_time": video_info.get("pubdate", 0),
                "view_count": video_info.get("stat", {}).get("view", 0),
                "like_count": video_info.get("stat", {}).get("like", 0),
                "comment_count": video_info.get("stat", {}).get("reply", 0),
                "share_count": video_info.get("stat", {}).get("share", 0),
                "favorite_count": video_info.get("stat", {}).get("favorite", 0),
                "source_platform": "bilibili",
                "video_url": url,
                "thumbnail": video_info.get("pic", ""),
                "subtitle": subtitle_info.get("subtitle", ""),
                "comments": comments,
                "tags": video_info.get("tags", []),
                "cid": video_info.get("cid", 0),  # 分P ID，用于获取字幕等
                "bvid": video_info.get("bvid", ""),
                "aid": video_info.get("aid", 0)
            }
            
            return result
            
        except Exception as e:
            log_error(LoggerName.API, "B站视频提取失败", 
                     url=url,
                     error_type=type(e).__name__,
                     error_message=str(e))
            
            return {"error": f"B站视频提取失败: {str(e)}"}
    
    async def _extract_dynamic(self, url: str) -> Dict[str, Any]:
        """动态方式提取B站视频内容（使用浏览器）
        
        Args:
            url: B站视频URL
            
        Returns:
            视频信息字典
        """
        # 目前暂时使用静态提取，未来可以实现浏览器提取
        return await self._extract_static(url)
    
    async def _resolve_short_url(self, url: str) -> Optional[str]:
        """解析B站短链接
        
        Args:
            url: B站短链接
            
        Returns:
            真实链接
        """
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(url, headers=self.headers, allow_redirects=False) as resp:
                    if resp.status in (301, 302):
                        location = resp.headers.get('Location')
                        if location:
                            return location
            return None
        except Exception as e:
            log_error(LoggerName.API, "解析B站短链接失败", url=url, error=str(e))
            return None
    
    async def _fetch_video_info(self, video_id: str) -> Dict[str, Any]:
        """获取视频基本信息
        
        Args:
            video_id: 视频ID (BVID或AID)
            
        Returns:
            视频信息字典
        """
        params = {}
        if video_id.startswith('BV'):
            params['bvid'] = video_id
        elif video_id.startswith('av'):
            params['aid'] = video_id[2:]  # 移除'av'前缀
        else:
            return {"error": "不支持的视频ID格式"}
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(self.video_info_api, params=params, headers=self.headers) as resp:
                    if resp.status != 200:
                        return {"error": f"获取视频信息失败，状态码: {resp.status}"}
                    
                    data = await resp.json()
                    if data['code'] != 0:
                        return {"error": f"获取视频信息失败: {data['message']}"}
                    
                    return data['data']
        except Exception as e:
            return {"error": f"获取视频信息时发生错误: {str(e)}"}
    
    async def _fetch_subtitle_info(self, video_id: str) -> Dict[str, Any]:
        """获取视频字幕信息
        
        Args:
            video_id: 视频ID
            
        Returns:
            字幕信息字典
        """
        params = {}
        if video_id.startswith('BV'):
            params['bvid'] = video_id
        elif video_id.startswith('av'):
            params['aid'] = video_id[2:]
        else:
            return {"subtitle": ""}
        
        try:
            # 首先需要获取cid
            video_info = await self._fetch_video_info(video_id)
            if "error" in video_info:
                return {"subtitle": ""}
            
            cid = video_info.get("cid")
            if not cid:
                return {"subtitle": ""}
            
            params['cid'] = cid
            
            async with aiohttp.ClientSession() as session:
                async with session.get(self.subtitle_api, params=params, headers=self.headers) as resp:
                    if resp.status != 200:
                        return {"subtitle": ""}
                    
                    data = await resp.json()
                    if data['code'] != 0 or 'data' not in data:
                        return {"subtitle": ""}
                    
                    # 提取字幕列表
                    subtitle_list = data['data'].get('subtitle', {}).get('subtitles', [])
                    if not subtitle_list:
                        return {"subtitle": ""}
                    
                    # 获取第一个字幕的URL
                    subtitle_url = subtitle_list[0].get('subtitle_url')
                    if not subtitle_url:
                        return {"subtitle": ""}
                    
                    # 补全URL
                    if subtitle_url.startswith('//'):
                        subtitle_url = f"https:{subtitle_url}"
                    
                    # 获取字幕内容
                    async with session.get(subtitle_url) as subtitle_resp:
                        if subtitle_resp.status != 200:
                            return {"subtitle": ""}
                        
                        subtitle_data = await subtitle_resp.json()
                        subtitle_lines = []
                        
                        for line in subtitle_data.get('body', []):
                            if 'content' in line:
                                subtitle_lines.append(line['content'])
                        
                        return {"subtitle": "\n".join(subtitle_lines)}
        except Exception as e:
            log_error(LoggerName.API, "获取B站字幕失败", video_id=video_id, error=str(e))
            return {"subtitle": ""}
    
    async def _fetch_comments(self, video_id: str, limit: int = 20) -> List[Dict[str, Any]]:
        """获取视频评论
        
        Args:
            video_id: 视频ID
            limit: 评论数量限制
            
        Returns:
            评论列表
        """
        params = {
            'type': 1,  # 视频评论
            'sort': 2,  # 最热评论
            'ps': limit  # 每页评论数
        }
        
        if video_id.startswith('BV'):
            params['oid'] = await self._convert_bvid_to_aid(video_id)
        elif video_id.startswith('av'):
            params['oid'] = video_id[2:]
        else:
            return []
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(self.comment_api, params=params, headers=self.headers) as resp:
                    if resp.status != 200:
                        return []
                    
                    data = await resp.json()
                    if data['code'] != 0 or 'data' not in data:
                        return []
                    
                    comments = []
                    for reply in data['data'].get('replies', []):
                        comments.append({
                            "user": reply.get('member', {}).get('uname', '匿名用户'),
                            "content": reply.get('content', {}).get('message', ''),
                            "time": reply.get('ctime', 0),
                            "like_count": reply.get('like', 0)
                        })
                    
                    return comments
        except Exception as e:
            log_error(LoggerName.API, "获取B站评论失败", video_id=video_id, error=str(e))
            return []
    
    async def _convert_bvid_to_aid(self, bvid: str) -> str:
        """转换BVID到AID
        
        Args:
            bvid: BV号
            
        Returns:
            AV号（数字部分）
        """
        try:
            video_info = await self._fetch_video_info(bvid)
            if "error" in video_info:
                return "0"
            
            return str(video_info.get("aid", "0"))
        except Exception:
            return "0" 