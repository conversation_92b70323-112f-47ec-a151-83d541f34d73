"""
extractor包提供内容提取功能，支持从多种平台获取和分析内容
"""
from src.services.extractor.wechat_extractor import WechatArticleExtractor
from src.services.extractor.video_extractor import (
    VideoExtractor, 
    create_video_extractor,
    extract_video
)
from src.services.extractor.bilibili_extractor import BilibiliExtractor
from src.services.extractor.douyin_extractor import DouyinExtractor
from src.services.extractor.video_analyzer import VideoAnalyzer
from src.services.extractor.content_extractor import (
    ContentExtractor, 
    extract_and_analyze,
    extract_only
)
from src.services.extractor.unified_analyzer import (
    UnifiedAnalyzer, 
    analyze_from_extraction_result,
    analyze_text_content,
    ContentType
)

# 保留ai_analyzer的引入，但建议逐步迁移到统一分析器
from src.services.extractor.ai_analyzer import analyze_from_extraction_result as article_analyze_from_extraction_result

__all__ = [
    # 提取器
    "WechatArticleExtractor", 
    "VideoExtractor",
    "BilibiliExtractor",
    "DouyinExtractor",
    
    # 分析器
    "VideoAnalyzer",
    "UnifiedAnalyzer",
    
    # 工厂函数和便捷函数
    "create_video_extractor",
    "extract_video",
    
    # 通用内容提取器
    "ContentExtractor",
    "ContentType",
    
    # 便捷函数
    "extract_and_analyze",  # 一站式提取和分析
    "extract_only",         # 仅提取，不分析
    "analyze_from_extraction_result",  # 从提取结果分析
    "analyze_text_content",  # 直接分析文本内容
    "article_analyze_from_extraction_result",  # 原文章分析函数，向后兼容
]

# 使用说明
usage_doc = """
统一内容提取和分析接口：

1. 推荐使用方式 - 一站式提取和分析:
   result = await extract_and_analyze("https://example.com/article")

2. 分步提取和分析:
   content = await extract_only("https://example.com/video")
   result = await analyze_from_extraction_result(content)

3. 自定义配置:
   extractor = ContentExtractor(article_api_key="your_key", video_api_key="your_key")
   result = await extractor.extract_and_analyze("https://example.com/content")
   
4. 直接分析文本内容:
   result = await analyze_text_content("文章标题", "文章内容")

详细示例请参考 'usage_examples.py'
"""
