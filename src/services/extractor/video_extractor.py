"""
视频提取器基类 - 定义视频内容提取的通用接口和功能
"""
from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional, Tuple
import os
import re
import time
import json
import base64
import tempfile
import aiohttp
import asyncio
from urllib.parse import urlparse, parse_qs
import logging

from src.utils.logger import log_info, log_error, CrawlerLogger
from src.config.logging_config import LoggerName

logger = CrawlerLogger().get_logger(LoggerName.API)

class VideoExtractor(ABC):
    """视频内容提取基类，提供通用方法和抽象接口"""
    
    def __init__(self, api_key: Optional[str] = None, use_dynamic: bool = False, **kwargs):
        """初始化视频提取器
        
        Args:
            api_key: 可选的API密钥，用于某些平台的API访问
            use_dynamic: 是否使用动态加载方式提取内容
            **kwargs: 额外的初始化参数
        """
        self.api_key = api_key or os.getenv("VIDEO_API_KEY", "")
        self.use_dynamic = use_dynamic
        self._browser = None  # 动态浏览器对象，按需初始化
        
        # 通用HTTP请求头
        self.headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
        }
        
        # 超时设置
        self.timeout = aiohttp.ClientTimeout(total=60)
        
        # 重试设置
        self.max_retries = 3
        self.retry_delay = 2  # 秒
    
    async def extract(self, url: str) -> Dict[str, Any]:
        """提取视频内容（统一入口）
        
        Args:
            url: 视频URL
            
        Returns:
            包含视频完整信息的字典
        """
        try:
            # 记录开始时间
            start_time = time.time()
            log_info(LoggerName.API, "开始提取视频信息", url=url)
            
            # 执行提取方法
            if self.use_dynamic:
                result = await self._extract_dynamic(url)
            else:
                result = await self._extract_static(url)
            
            if "error" in result:
                return result
            
            # 规范化返回字段
            standardized_result = self._standardize_result(result)
            
            # 添加元数据
            standardized_result["processed_at"] = time.time()
            standardized_result["processing_time"] = round(time.time() - start_time, 2)
            standardized_result["source_url"] = url
            
            log_info(LoggerName.API, "视频信息提取完成", 
                    title=standardized_result.get("title", ""),
                    duration=standardized_result.get("duration", 0),
                    processing_time=standardized_result["processing_time"])
            
            return standardized_result
            
        except Exception as e:
            log_error(LoggerName.API, "视频提取失败", 
                     url=url,
                     error_type=type(e).__name__,
                     error_message=str(e))
            
            return {
                "error": f"视频提取失败: {str(e)}",
                "source_url": url,
                "processed_at": time.time()
            }
    
    @abstractmethod
    async def _extract_static(self, url: str) -> Dict[str, Any]:
        """静态提取方法（不使用浏览器）
        
        Args:
            url: 视频URL
            
        Returns:
            包含视频信息的字典
        """
        pass
    
    @abstractmethod
    async def _extract_dynamic(self, url: str) -> Dict[str, Any]:
        """动态提取方法（使用浏览器）
        
        Args:
            url: 视频URL
            
        Returns:
            包含视频信息的字典
        """
        pass
    
    @abstractmethod
    def extract_video_id(self, url: str) -> Optional[str]:
        """从URL中提取视频ID
        
        Args:
            url: 视频URL
            
        Returns:
            视频ID，提取失败时返回None
        """
        pass
    
    def _standardize_result(self, result: Dict[str, Any]) -> Dict[str, Any]:
        """标准化提取结果，确保包含所有必要字段
        
        Args:
            result: 原始提取结果
            
        Returns:
            标准化后的结果
        """
        # 必要字段初始化
        standardized = {
            "title": result.get("title", ""),
            "description": result.get("description", ""),
            "duration": result.get("duration", 0),
            "publish_time": result.get("publish_time", ""),
            "author": result.get("author", ""),
            "source_platform": result.get("source_platform", self._get_platform_name()),
            "video_url": result.get("video_url", ""),
            "thumbnail": result.get("thumbnail", ""),
            "transcript": result.get("transcript", ""),
            "subtitle": result.get("subtitle", ""),
            "comments": result.get("comments", []),
            "like_count": result.get("like_count", 0),
            "view_count": result.get("view_count", 0),
            "comment_count": result.get("comment_count", 0),
            "share_count": result.get("share_count", 0),
        }
        
        # 合并其他字段
        for key, value in result.items():
            if key not in standardized:
                standardized[key] = value
        
        return standardized
    
    def _get_platform_name(self) -> str:
        """获取平台名称，用于标准化结果"""
        # 子类应覆盖此方法返回具体平台名称
        return "unknown"
    
    async def download_media(self, url: str, is_audio: bool = False) -> Optional[str]:
        """下载视频或音频文件到临时位置
        
        Args:
            url: 媒体URL
            is_audio: 是否为音频文件
            
        Returns:
            临时文件路径，失败时返回None
        """
        try:
            ext = "mp3" if is_audio else "mp4"
            suffix = f".{ext}"
            
            # 创建临时文件
            with tempfile.NamedTemporaryFile(suffix=suffix, delete=False) as tmp_file:
                temp_path = tmp_file.name
            
            # 下载文件
            async with aiohttp.ClientSession(timeout=self.timeout) as session:
                async with session.get(url, headers=self.headers) as response:
                    if response.status != 200:
                        raise ValueError(f"下载失败，状态码: {response.status}")
                    
                    with open(temp_path, 'wb') as f:
                        while True:
                            chunk = await response.content.read(8192)
                            if not chunk:
                                break
                            f.write(chunk)
            
            return temp_path
        except Exception as e:
            log_error(LoggerName.API, "媒体下载失败", 
                     url=url,
                     error_type=type(e).__name__,
                     error_message=str(e))
            return None
    
    async def fetch_data(self, url: str, params: Optional[Dict[str, Any]] = None, headers: Optional[Dict[str, str]] = None) -> Dict[str, Any]:
        """通过HTTP请求获取数据
        
        Args:
            url: 请求URL
            params: 请求参数
            headers: 请求头
            
        Returns:
            响应数据，通常是JSON
        """
        _headers = {**self.headers}
        if headers:
            _headers.update(headers)
        
        try:
            async with aiohttp.ClientSession(timeout=self.timeout) as session:
                async with session.get(url, params=params, headers=_headers) as response:
                    if response.status != 200:
                        return {"error": f"HTTP请求失败，状态码: {response.status}"}
                    
                    try:
                        return await response.json()
                    except:
                        text = await response.text()
                        return {"text": text}
        except Exception as e:
            return {"error": f"请求失败: {str(e)}"}
    
    def clean_text(self, text: str) -> str:
        """清理文本内容
        
        Args:
            text: 原始文本
            
        Returns:
            清理后的文本
        """
        if not text:
            return ""
        
        # 移除多余空白行
        text = re.sub(r'\n{3,}', '\n\n', text)
        
        # 移除多余空格
        text = re.sub(r'\s{2,}', ' ', text)
        
        # 移除HTML标签
        text = re.sub(r'<[^>]+>', '', text)
        
        return text.strip()
    
    def close(self):
        """关闭动态浏览器等资源"""
        # 子类应该覆盖此方法以释放资源
        pass
    
    async def extract_all(self, url: str) -> Dict[str, Any]:
        """提取视频的所有信息（兼容旧API）
        
        Args:
            url: 视频URL
            
        Returns:
            包含所有视频信息和内容的字典
        """
        return await self.extract(url)

# 便捷工厂函数
def create_video_extractor(url: str, use_dynamic: bool = False, api_key: Optional[str] = None) -> Optional[VideoExtractor]:
    """根据URL创建合适的视频提取器
    
    Args:
        url: 视频URL
        use_dynamic: 是否使用动态加载
        api_key: API密钥
        
    Returns:
        视频提取器实例，如果不支持返回None
    """
    from src.services.extractor.bilibili_extractor import BilibiliExtractor
    from src.services.extractor.douyin_extractor import DouyinExtractor
    
    # 解析域名
    domain = urlparse(url).netloc.lower()
    
    # 选择合适的提取器
    if "bilibili.com" in domain or "b23.tv" in domain:
        return BilibiliExtractor(api_key=api_key, use_dynamic=use_dynamic)
    elif "douyin.com" in domain or "iesdouyin.com" in domain:
        return DouyinExtractor(api_key=api_key, use_dynamic=use_dynamic)
    
    # 不支持的平台
    return None

# 便捷函数
async def extract_video(url: str, use_dynamic: bool = False, api_key: Optional[str] = None) -> Dict[str, Any]:
    """从URL提取视频信息（便捷函数）
    
    Args:
        url: 视频URL
        use_dynamic: 是否使用动态加载
        api_key: API密钥
        
    Returns:
        提取结果
    """
    extractor = create_video_extractor(url, use_dynamic, api_key)
    if not extractor:
        return {
            "error": "不支持的视频平台",
            "source_url": url,
            "processed_at": time.time()
        }
    
    try:
        result = await extractor.extract(url)
        extractor.close()
        return result
    except Exception as e:
        if extractor:
            extractor.close()
        
        return {
            "error": f"视频提取失败: {str(e)}",
            "source_url": url,
            "processed_at": time.time()
        }

    async def get_thumbnails(self, url: str) -> List[str]:
        """获取视频缩略图
        
        Args:
            url: 视频URL
            
        Returns:
            缩略图URL列表
        """
        # 默认空实现，子类可以覆盖
        return [] 