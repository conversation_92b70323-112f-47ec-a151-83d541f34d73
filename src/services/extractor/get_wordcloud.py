from wordcloud import WordCloud
import matplotlib.pyplot as plt
import jieba 
import re
import codecs
import os

stopwords_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'stopwords.txt')
font_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'alipuhui.ttf')

def get_wordcloud(text):
    # 中文预处理函数
    def process_chinese_text(text):
        # 去除特殊符号和数字
        stopwords = set([line.strip() for line in codecs.open(stopwords_path, 'r', 'utf-8')])
        text = re.sub(r"[^\u4e00-\u9fa5a-zA-Z]", " ", text)
        # 结巴分词
        segs = jieba.cut(text)
        words = []
        for seg in segs:
            word = seg.strip().lower()
            if len(word) > 1 and word not in stopwords:
                words.append(word)
        return " ".join(words)

    # 英文预处理函数（如果是英文文本）
    # def process_english_text(text):
    #     text = re.sub(r"[^a-zA-Z]", " ", text)
    #     return text.lower()

    # 选择语言处理
    processed_text = process_chinese_text(text)  # 中文用这个
    # processed_text = process_english_text(text)  # 英文用这个

    # 生成词云
    wc = WordCloud(
        font_path=font_path,  # 中文需要字体文件（Windows用微软雅黑）
        width=800,
        height=600,
        background_color="white",
        max_words=200,
        collocations=False  # 禁用词组组合
    )

    wordcloud = wc.generate(processed_text)
    # 显示词云
    # plt.figure(figsize=(12, 8))
    # plt.imshow(wordcloud, interpolation="bilinear")
    # plt.axis("off")
    # plt.show()
    wordcloud_data = [
                {"text": word, "value": int(freq*100)}
                for word, freq in wordcloud.words_.items()
            ]

    return wordcloud_data


