"""
抖音视频内容提取模块
"""
import re
import json
import time
import asyncio
from typing import Dict, Any, List, Optional, Union
from urllib.parse import urlparse, parse_qs

import aiohttp
from bs4 import BeautifulSoup

from src.services.extractor.video_extractor import VideoExtractor
from src.utils.logger import log_info, log_error, CrawlerLogger
from src.config.logging_config import LoggerName

logger = CrawlerLogger().get_logger(LoggerName.API)

class DouyinExtractor(VideoExtractor):
    """抖音视频内容提取器"""
    
    def __init__(self, api_key: Optional[str] = None, use_dynamic: bool = False):
        """初始化抖音视频提取器
        
        Args:
            api_key: API密钥（抖音提取器暂不需要）
            use_dynamic: 是否使用动态加载方式提取内容
        """
        super().__init__(api_key=api_key, use_dynamic=use_dynamic)
        
        # 抖音相关API
        self.video_info_api = "https://www.iesdouyin.com/web/api/v2/aweme/iteminfo/"
        self.comment_api = "https://www.douyin.com/aweme/v1/web/comment/list/"
        
        # 设置特定的请求头
        self.headers.update({
            "Referer": "https://www.douyin.com",
            "Origin": "https://www.douyin.com",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
        })
    
    def _get_platform_name(self) -> str:
        """获取平台名称"""
        return "douyin"
    
    def extract_video_id(self, url: str) -> Optional[str]:
        """从URL中提取视频ID
        
        Args:
            url: 抖音视频URL
            
        Returns:
            视频ID
        """
        # 处理短链接
        if "v.douyin.com" in url:
            # 需要请求获取真实URL
            return None
        
        # 尝试从URL路径中提取
        patterns = [
            r'douyin\.com/video/(\d+)',  # 标准视频URL
            r'iesdouyin\.com/share/video/(\d+)',  # 分享URL
        ]
        
        for pattern in patterns:
            match = re.search(pattern, url)
            if match:
                return match.group(1)
        
        # 从查询参数提取
        parsed_url = urlparse(url)
        query_params = parse_qs(parsed_url.query)
        
        if 'item_id' in query_params:
            return query_params['item_id'][0]
        
        # 尝试从路径部分提取
        segments = parsed_url.path.split('/')
        for segment in segments:
            if segment.isdigit() and len(segment) > 5:  # 抖音ID通常较长
                return segment
        
        return None
    
    async def _extract_static(self, url: str) -> Dict[str, Any]:
        """静态方式提取抖音视频内容
        
        Args:
            url: 抖音视频URL
            
        Returns:
            视频信息字典
        """
        try:
            # 提取视频ID
            video_id = self.extract_video_id(url)
            if not video_id:
                # 如果无法直接提取ID，可能是短链接，需要请求获取真实URL
                real_url = await self._resolve_short_url(url)
                if not real_url:
                    return {"error": "无法解析视频ID"}
                
                video_id = self.extract_video_id(real_url)
                if not video_id:
                    return {"error": "无法获取视频ID"}
                
                url = real_url
            
            # 获取视频信息
            video_info = await self._fetch_video_info(video_id)
            if "error" in video_info:
                return video_info
            
            # 提取视频详情
            item_info = video_info.get("item_list", [{}])[0] if "item_list" in video_info and video_info["item_list"] else {}
            
            if not item_info:
                return {"error": "无法获取视频详情"}
            
            # 获取评论
            comments = await self._fetch_comments(video_id)
            
            # 提取视频URL
            video_url = self._extract_play_url(item_info)
            
            # 整合结果
            result = {
                "title": item_info.get("desc", ""),
                "description": item_info.get("desc", ""),
                "author": item_info.get("author", {}).get("nickname", ""),
                "author_id": item_info.get("author", {}).get("uid", ""),
                "duration": item_info.get("video", {}).get("duration", 0) // 1000,  # 毫秒转秒
                "publish_time": item_info.get("create_time", 0),
                "view_count": item_info.get("statistics", {}).get("play_count", 0),
                "like_count": item_info.get("statistics", {}).get("digg_count", 0),
                "comment_count": item_info.get("statistics", {}).get("comment_count", 0),
                "share_count": item_info.get("statistics", {}).get("share_count", 0),
                "source_platform": "douyin",
                "video_url": video_url,
                "thumbnail": self._extract_cover_url(item_info),
                "subtitle": "",  # 抖音通常没有外挂字幕
                "transcript": "",  # 尝试提取视频文字内容，可能是空的
                "comments": comments,
                "tags": self._extract_tags(item_info),
                "music": item_info.get("music", {}).get("title", ""),
                "video_id": video_id
            }
            
            return result
            
        except Exception as e:
            log_error(LoggerName.API, "抖音视频提取失败", 
                    url=url,
                    error_type=type(e).__name__,
                    error_message=str(e))
            
            return {"error": f"抖音视频提取失败: {str(e)}"}
    
    async def _extract_dynamic(self, url: str) -> Dict[str, Any]:
        """动态方式提取抖音视频内容（使用浏览器）
        
        Args:
            url: 抖音视频URL
            
        Returns:
            视频信息字典
        """
        # 目前暂时使用静态提取，未来可以实现浏览器提取
        return await self._extract_static(url)
    
    async def _resolve_short_url(self, url: str) -> Optional[str]:
        """解析抖音短链接
        
        Args:
            url: 抖音短链接
            
        Returns:
            真实链接
        """
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(url, headers=self.headers, allow_redirects=False) as resp:
                    if resp.status in (301, 302):
                        location = resp.headers.get('Location')
                        if location:
                            return location
            return None
        except Exception as e:
            log_error(LoggerName.API, "解析抖音短链接失败", url=url, error=str(e))
            return None
    
    async def _fetch_video_info(self, video_id: str) -> Dict[str, Any]:
        """获取视频信息
        
        Args:
            video_id: 视频ID
            
        Returns:
            视频信息字典
        """
        api_url = f"{self.video_info_api}?item_ids={video_id}"
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(api_url, headers=self.headers) as resp:
                    if resp.status != 200:
                        return {"error": f"获取视频信息失败，状态码: {resp.status}"}
                    
                    data = await resp.json()
                    if "status_code" in data and data["status_code"] != 0:
                        return {"error": f"获取视频信息失败: {data.get('status_msg', '未知错误')}"}
                    
                    return data
        except Exception as e:
            return {"error": f"获取视频信息时发生错误: {str(e)}"}
    
    async def _fetch_comments(self, video_id: str, limit: int = 20) -> List[Dict[str, Any]]:
        """获取视频评论
        
        Args:
            video_id: 视频ID
            limit: 评论数量限制
            
        Returns:
            评论列表
        """
        params = {
            'aweme_id': video_id,
            'count': min(limit, 50),  # 最多50条评论
            'cursor': 0
        }
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(self.comment_api, params=params, headers=self.headers) as resp:
                    if resp.status != 200:
                        return []
                    
                    try:
                        data = await resp.json()
                    except:
                        # 抖音可能返回HTML而不是JSON
                        return []
                    
                    if "status_code" in data and data["status_code"] != 0:
                        return []
                    
                    comments = []
                    for comment in data.get("comments", []):
                        comments.append({
                            "user": comment.get("user", {}).get("nickname", "匿名用户"),
                            "content": comment.get("text", ""),
                            "time": comment.get("create_time", 0),
                            "like_count": comment.get("digg_count", 0)
                        })
                    
                    return comments
        except Exception as e:
            log_error(LoggerName.API, "获取抖音评论失败", video_id=video_id, error=str(e))
            return []
    
    def _extract_play_url(self, item_info: Dict[str, Any]) -> str:
        """提取视频播放URL
        
        Args:
            item_info: 视频信息数据
            
        Returns:
            视频播放URL
        """
        try:
            video_info = item_info.get("video", {})
            
            # 尝试获取无水印链接
            play_urls = video_info.get("play_addr", {}).get("url_list", [])
            if play_urls:
                # 替换域名以获取无水印链接
                url = play_urls[0]
                url = url.replace("playwm", "play")  # 无水印技巧
                return url
            
            # 备用：普通链接
            download_urls = video_info.get("download_addr", {}).get("url_list", [])
            if download_urls:
                return download_urls[0]
            
            return ""
        except Exception:
            return ""
    
    def _extract_cover_url(self, item_info: Dict[str, Any]) -> str:
        """提取视频封面URL
        
        Args:
            item_info: 视频信息数据
            
        Returns:
            视频封面URL
        """
        try:
            cover_urls = item_info.get("video", {}).get("cover", {}).get("url_list", [])
            if cover_urls:
                return cover_urls[0]
            
            origin_cover_urls = item_info.get("video", {}).get("origin_cover", {}).get("url_list", [])
            if origin_cover_urls:
                return origin_cover_urls[0]
            
            dynamic_cover_urls = item_info.get("video", {}).get("dynamic_cover", {}).get("url_list", [])
            if dynamic_cover_urls:
                return dynamic_cover_urls[0]
            
            return ""
        except Exception:
            return ""
    
    def _extract_tags(self, item_info: Dict[str, Any]) -> List[str]:
        """提取视频标签
        
        Args:
            item_info: 视频信息数据
            
        Returns:
            标签列表
        """
        try:
            # 提取话题标签
            tags = []
            
            # 从文字描述中提取话题
            desc = item_info.get("desc", "")
            hashtag_pattern = r'#(\w+)'
            hashtags = re.findall(hashtag_pattern, desc)
            tags.extend(hashtags)
            
            # 从话题信息中提取
            text_extra = item_info.get("text_extra", [])
            for extra in text_extra:
                if "hashtag_name" in extra and extra["hashtag_name"]:
                    tags.append(extra["hashtag_name"])
            
            # 去重
            return list(set(tags))
        except Exception:
            return []
            
    # 以下是兼容方法，用于向后兼容旧代码
    
    async def extract_video_info(self, url: str) -> Dict[str, Any]:
        """兼容方法: 提取抖音视频信息
        
        Args:
            url: 抖音视频URL
            
        Returns:
            包含视频信息的字典
        """
        log_info(LoggerName.API, "使用extract_video_info提取抖音视频", url=url)
        return await self.extract(url)
    
    async def extract_video_content(self, url: str) -> Dict[str, Any]:
        """兼容方法: 提取抖音视频内容（字幕、评论等）
        
        Args:
            url: 抖音视频URL
            
        Returns:
            包含视频内容的字典
        """
        log_info(LoggerName.API, "使用extract_video_content提取抖音视频内容", url=url)
        result = await self.extract(url)
        
        # 格式化为旧接口格式
        content = {
            "description": result.get("description", ""),
            "comments": result.get("comments", []),
            "comments_text": "\n".join([f"评论: {comment['content']}" for comment in result.get("comments", [])[:5]]),
            "tags": result.get("tags", []),
            "content_type": "video",
            "language": "zh"  # 默认中文
        }
        
        return content
    
    async def extract_video_comments(self, url: str, limit: int = 20) -> List[Dict[str, Any]]:
        """兼容方法: 提取抖音视频评论
        
        Args:
            url: 视频URL
            limit: 评论数量限制
            
        Returns:
            评论列表
        """
        log_info(LoggerName.API, "使用extract_video_comments提取抖音评论", url=url)
        result = await self.extract(url)
        return result.get("comments", [])[:limit]
    
    async def get_thumbnails(self, url: str) -> List[str]:
        """兼容方法: 获取抖音视频缩略图
        
        Args:
            url: 抖音视频URL
            
        Returns:
            缩略图URL列表
        """
        log_info(LoggerName.API, "使用get_thumbnails获取抖音缩略图", url=url)
        result = await self.extract(url)
        thumbnail = result.get("thumbnail", "")
        return [thumbnail] if thumbnail else [] 