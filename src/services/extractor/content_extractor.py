"""
统一内容提取器模块 - 提供对文章和视频的统一处理接口
"""
from typing import Dict, Any, List, Optional, Union, Tuple
import os
import re
import json
import time
import logging
import asyncio
from urllib.parse import urlparse

from src.services.extractor.wechat_extractor import WechatArticleExtractor
from src.services.extractor.video_extractor import VideoExtractor
from src.services.extractor.bilibili_extractor import BilibiliExtractor
from src.services.extractor.douyin_extractor import DouyinExtractor
from src.services.extractor.unified_analyzer import UnifiedAnalyzer, ContentType

from src.utils.logger import log_info, log_error, CrawlerLogger
from src.config.logging_config import LoggerName
from src.config.api_config import get_deepseek_api_key

logger = CrawlerLogger().get_logger(LoggerName.API)

class ContentExtractor:
    """统一内容提取器"""
    
    def __init__(self, article_api_key: Optional[str] = None, video_api_key: Optional[str] = None):
        """
        初始化统一内容提取器
        
        Args:
            article_api_key: 文章分析API密钥
            video_api_key: 视频分析API密钥(已废弃，保留参数是为了兼容性)
        """
        self.article_api_key = article_api_key or get_deepseek_api_key()
        # 视频分析也使用DeepSeek API
        self.video_api_key = self.article_api_key
        
        # 初始化各种提取器
        self.wechat_extractor = WechatArticleExtractor(min_content_length=100, max_retries=4)
        self.bilibili_extractor = BilibiliExtractor()
        self.douyin_extractor = DouyinExtractor()
        
        # 初始化分析器
        self.analyzer = UnifiedAnalyzer(
            article_api_key=self.article_api_key,
            video_api_key=self.article_api_key
        )
        
        # 支持的域名映射
        self.domain_handlers = {
            # 文章类
            "mp.weixin.qq.com": (ContentType.ARTICLE, self._extract_wechat_article),
            "weixin.qq.com": (ContentType.ARTICLE, self._extract_wechat_article),
            "zhihu.com": (ContentType.ARTICLE, self._extract_generic_article),
            "toutiao.com": (ContentType.ARTICLE, self._extract_generic_article),
            "jianshu.com": (ContentType.ARTICLE, self._extract_generic_article),
            # 视频类
            "bilibili.com": (ContentType.VIDEO, self._extract_bilibili_video),
            "b23.tv": (ContentType.VIDEO, self._extract_bilibili_video),
            "douyin.com": (ContentType.VIDEO, self._extract_douyin_video),
            "iesdouyin.com": (ContentType.VIDEO, self._extract_douyin_video),
            "youtube.com": (ContentType.VIDEO, self._extract_generic_video),
            "youtu.be": (ContentType.VIDEO, self._extract_generic_video),
        }
    
    def detect_content_type(self, url: str) -> Tuple[str, Any]:
        """
        检测URL对应的内容类型和处理函数
        
        Args:
            url: 内容URL
            
        Returns:
            内容类型和对应的处理函数元组
        """
        try:
            parsed_url = urlparse(url)
            domain = parsed_url.netloc.lower()
            
            # 提取主域名
            domain_parts = domain.split('.')
            if len(domain_parts) > 2:
                main_domain = '.'.join(domain_parts[-2:])
            else:
                main_domain = domain
            
            # 精确匹配
            if domain in self.domain_handlers:
                return self.domain_handlers[domain]
            
            # 部分匹配
            for d, handler in self.domain_handlers.items():
                if d in domain:
                    return handler
            
            # 无法识别
            return ContentType.UNKNOWN, None
        except Exception as e:
            log_error(LoggerName.API, "检测内容类型失败", url=url, error=str(e))
            return ContentType.UNKNOWN, None
    
    async def _process_url(self, url: str, extract_only_mode: bool = False) -> Dict[str, Any]:
        """
        处理URL，提取内容，并可选是否分析
        
        Args:
            url: 内容URL
            extract_only_mode: 是否仅提取，不分析
            
        Returns:
            处理结果
        """
        start_time = time.time()
        log_info(LoggerName.API, "开始处理内容", url=url, mode="仅提取" if extract_only_mode else "提取并分析")
        
        try:
            # 检测内容类型
            content_type, handler = self.detect_content_type(url)
            
            if content_type == ContentType.UNKNOWN or handler is None:
                return {
                    "error": "不支持的URL或内容类型",
                    "url": url,
                    "content_type": ContentType.UNKNOWN,
                    "processed_at": time.time()
                }
            
            # 提取内容
            extraction_result = await handler(url)
            if "error" in extraction_result:
                return extraction_result
            
            # 添加元数据
            extraction_result["processed_at"] = time.time()
            extraction_result["content_type"] = content_type
            extraction_result["source_url"] = url
            
            # 如果仅提取模式，直接返回
            if extract_only_mode:
                extraction_result["processing_time"] = round(time.time() - start_time, 2)
                log_info(LoggerName.API, "内容提取完成", 
                        content_type=content_type,
                        title=extraction_result.get("title", ""),
                        processing_time=extraction_result["processing_time"])
                return extraction_result
            
            # 分析内容
            analysis_result = await self.analyzer.analyze_content(extraction_result)
            analysis_result["processing_time"] = round(time.time() - start_time, 2)
            
            log_info(LoggerName.API, "内容提取和分析完成", 
                    content_type=content_type,
                    title=extraction_result.get("title", ""),
                    processing_time=analysis_result["processing_time"])
            
            return analysis_result
            
        except Exception as e:
            log_error(LoggerName.API, "内容处理失败", 
                     url=url,
                     mode="仅提取" if extract_only_mode else "提取并分析",
                     error_type=type(e).__name__,
                     error_message=str(e))
            
            return {
                "error": f"内容处理失败: {str(e)}",
                "url": url,
                "processed_at": time.time(),
                "processing_time": round(time.time() - start_time, 2)
            }
    
    async def extract_and_analyze(self, url: str) -> Dict[str, Any]:
        """
        提取并分析内容（文章或视频）
        
        Args:
            url: 内容URL
            
        Returns:
            提取和分析结果
        """
        return await self._process_url(url, extract_only_mode=False)
    
    async def extract_only(self, url: str) -> Dict[str, Any]:
        """
        仅提取内容，不进行分析
        
        Args:
            url: 内容URL
            
        Returns:
            提取结果
        """
        return await self._process_url(url, extract_only_mode=True)
    
    async def _extract_wechat_article(self, url: str) -> Dict[str, Any]:
        """
        提取微信文章内容
        
        Args:
            url: 微信文章URL
            
        Returns:
            提取结果
        """
        try:
            # 提取文章
            extraction_result = await self.wechat_extractor.extract_from_url(url)
            return extraction_result
        except Exception as e:
            log_error(LoggerName.API, "微信文章提取失败", url=url, error=str(e))
            return {"error": f"微信文章提取失败: {str(e)}", "url": url}
    
    async def _extract_bilibili_video(self, url: str) -> Dict[str, Any]:
        """
        提取B站视频内容
        
        Args:
            url: B站视频URL
            
        Returns:
            提取结果
        """
        try:
            # 提取视频基本信息和内容
            extraction_result = await self.bilibili_extractor.extract_all(url)
            return extraction_result
        except Exception as e:
            log_error(LoggerName.API, "B站视频提取失败", url=url, error=str(e))
            return {"error": f"B站视频提取失败: {str(e)}", "url": url}
    
    async def _extract_douyin_video(self, url: str) -> Dict[str, Any]:
        """
        提取抖音视频内容
        
        Args:
            url: 抖音视频URL
            
        Returns:
            提取结果
        """
        try:
            # 提取视频基本信息和内容
            extraction_result = await self.douyin_extractor.extract_all(url)
            return extraction_result
        except Exception as e:
            log_error(LoggerName.API, "抖音视频提取失败", url=url, error=str(e))
            return {"error": f"抖音视频提取失败: {str(e)}", "url": url}
    
    async def _extract_generic_article(self, url: str) -> Dict[str, Any]:
        """
        提取通用文章内容
        
        Args:
            url: 文章URL
            
        Returns:
            文章内容
        """
        try:
            log_info(LoggerName.API, "尝试提取通用文章", url=url)
            
            # 通用提取方法实现
            # 这里可以实现一个基于通用网页爬取的方法
            # 目前返回未实现信息
            return {
                "error": "通用文章提取器尚未实现",
                "url": url
            }
        except Exception as e:
            log_error(LoggerName.API, "通用文章提取失败", 
                     url=url, 
                     error_type=type(e).__name__,
                     error_message=str(e))
            
            return {
                "error": f"通用文章提取失败: {str(e)}",
                "url": url
            }
    
    async def _extract_generic_video(self, url: str) -> Dict[str, Any]:
        """
        提取通用视频内容
        
        Args:
            url: 视频URL
            
        Returns:
            视频内容
        """
        try:
            log_info(LoggerName.API, "尝试提取通用视频", url=url)
            
            # 尝试创建适合的视频提取器
            try:
                from src.services.extractor.video_extractor import create_video_extractor, extract_video
                result = await extract_video(url)
                if "error" not in result:
                    return result
            except Exception as e:
                log_error(LoggerName.API, "自动视频提取器失败", url=url, error=str(e))
            
            # 未能提取
            return {
                "error": f"未能找到适合的视频提取器处理URL: {url}",
                "url": url
            }
        except Exception as e:
            log_error(LoggerName.API, "通用视频提取失败", 
                     url=url, 
                     error_type=type(e).__name__,
                     error_message=str(e))
            
            return {
                "error": f"通用视频提取失败: {str(e)}",
                "url": url
            }

# 便捷函数
async def extract_and_analyze(url: str = None, article_api_key: Optional[str] = None, video_api_key: Optional[str] = None) -> Dict[str, Any]:
    """
    提取并分析内容（文章或视频）
    
    Args:
        url: 内容URL
        article_api_key: 文章分析API密钥
        video_api_key: 视频分析API密钥

        
    Returns:
        提取和分析结果
    """
    
    # 若未提供标题，则执行正常的提取和分析流程
    if not url:
        return {"error": "需要提供URL或标题/内容"}
        
    extractor = ContentExtractor(article_api_key=article_api_key, video_api_key=video_api_key)
    return await extractor.extract_and_analyze(url)

async def extract_only(url: str) -> Dict[str, Any]:
    """
    仅提取内容，不进行分析
    
    Args:
        url: 内容URL
        
    Returns:
        提取结果
    """
    extractor = ContentExtractor()
    return await extractor.extract_only(url) 