agentops==0.4.13
aiofiles==24.1.0
aiohappyeyeballs==2.6.1
aiohttp>=3.11.18, <4.0.0
aiosignal==1.3.2
altair==4.2.2
amqp==5.3.1
annotated-types==0.7.0
anthropic==0.52.1
anyio==4.9.0
async-timeout==4.0.3
asyncio==3.4.3
attrs==25.3.0
Automat==24.8.1
autoprompt==1.0.3
babel==2.17.0
backoff==2.2.1
beautifulsoup4==4.13.4
bidict==0.23.1
billiard==4.2.1
boto3==1.38.26
botocore==1.38.26
Brotli==1.1.0
cachetools==5.5.2
celery==5.5.2
certifi==2024.2.2
charset-normalizer==3.4.1
class-registry==2.1.2
click==8.1.8
click-didyoumean==0.3.1
click-plugins==1.1.1
click-repl==0.3.0
colorama==0.4.6
constantly==23.10.4
courlan==1.3.2
cssselect==1.3.0
dataclasses-json==0.6.7
DataRecorder==3.6.2
dateparser==1.2.1
daytona_api_client==0.19.1
daytona_sdk==0.14.0
Deprecated==1.2.18
deprecation==2.1.0
dill==0.4.0
distro==1.9.0
dotenv==0.9.9
DownloadKit==2.0.7
dramatiq==1.18.0
DrissionPage==4.1.0.18
duckduckgo_search==8.0.2
e2b==1.5.0
e2b-code-interpreter==1.5.0
entrypoints==0.4
env==0.1.0
environs==9.5.0
et_xmlfile==2.0.0
exa-py==1.13.1
exceptiongroup==1.3.0
fastapi==0.115.12
ffmpy==0.5.0
filelock==3.18.0
Flask==2.0.1
Flask-Cors==3.0.10
Flask-SocketIO==5.5.1
frozenlist==1.5.0
fsspec==2025.3.2
google-ai-generativelanguage==0.6.15
google-api-core==2.25.0rc1
google-api-python-client==2.170.0
google-auth==2.40.2
google-auth-httplib2==0.2.0
google-generativeai==0.8.5
googleapis-common-protos==1.70.0
gotrue==2.12.0
gradio==5.32.0
gradio_client==1.10.2
greenlet==3.2.2
griffe==1.7.3
groovy==0.1.2
grpcio==1.71.0
grpcio-status==1.71.0
gunicorn==20.1.0
h11==0.14.0
h2==4.2.0
hf-xet==1.1.2
hpack==4.1.0
htmldate==1.9.3
httpcore==1.0.8
httplib2==0.22.0
httpx==0.28.1
httpx-sse==0.4.0
huggingface-hub==0.30.2
hyperframe==6.1.0
hyperlink==21.0.0
idna==3.10
importlib_metadata==8.6.1
incremental==24.7.2
iniconfig==2.1.0
itsdangerous==2.2.0
jieba==0.42.1
Jinja2==3.1.6
jiter==0.9.0
jmespath==1.0.1
joblib==1.5.1
json_repair==0.44.1
jsonpatch==1.33
jsonpointer==3.0.0
jsonschema==4.24.0
jsonschema-specifications==2024.10.1
jusText==3.0.2
kombu==5.5.3
langchain==0.3.25
langchain-community==0.3.24
langchain-core==0.3.60
langchain-deepseek==0.1.3
langchain-mcp-adapters==0.1.7
langchain-openai==0.3.17
langchain-text-splitters==0.3.8
langfuse==2.60.7
langgraph==0.4.5
langgraph-checkpoint==2.0.26
langgraph-prebuilt==0.1.8
langgraph-sdk==0.1.69
langsmith==0.3.42
litellm==1.74.0
loguru==0.7.3
lxml==5.4.0
lxml_html_clean==0.4.2
markdown-it-py==3.0.0
MarkupSafe==3.0.2
marshmallow==3.26.1
mcp==1.9.4
mdurl==0.1.2
multidict==6.4.3
mypy_extensions==1.1.0
nest-asyncio==1.6.0
nodeenv==1.9.1
numpy==2.0.2
openai==1.79.0
openpyxl==3.1.5
opentelemetry-api==1.33.1
opentelemetry-exporter-otlp-proto-common==1.33.1
opentelemetry-exporter-otlp-proto-http==1.33.1
opentelemetry-instrumentation==0.54b1
opentelemetry-proto==1.33.1
opentelemetry-sdk==1.33.1
opentelemetry-semantic-conventions==0.54b1
ordered-set==4.1.0
orjson==3.10.18
ormsgpack==1.9.1
overrides==7.7.0
packaging==24.1
pandas==2.2.3
pika==1.3.2
pillow==11.2.1
pluggy==1.5.0
postgrest==1.0.2
primp==0.15.0
prisma==0.15.0
prometheus_client==0.22.0
prompt-toolkit==3.0.36
propcache==0.3.1
proto-plus==1.26.1
protobuf==5.29.4
psutil==6.0.0
pyasn1==0.6.1
pyasn1_modules==0.4.2
pycryptodomex==3.23.0
pydantic==2.11.3
pydantic-settings==2.9.1
pydantic_core==2.33.1
pydub==0.25.1
Pygments==2.19.1
PyJWT==2.10.1
pyparsing==3.2.3
pytesseract==0.3.13
pytest==8.3.3
pytest-asyncio==0.24.0
pytest-mock==3.14.0
python-dateutil==2.9.0.post0
python-dotenv==1.0.1
python-engineio==4.12.1
python-multipart==0.0.20
python-ripgrep==0.0.6
python-socketio==5.13.0
pytz==2025.2
PyYAML==6.0.2
questionary==2.0.1
realtime==2.4.3
redis==5.2.1
referencing==0.36.2
regex==2024.11.6
requests==2.32.3
requests-file==2.1.0
requests-toolbelt==1.0.0
rich==14.0.0
rpds-py==0.24.0
rsa==4.9.1
ruff==0.11.12
s3transfer==0.13.0
safehttpx==0.1.6
scikit-learn==1.6.1
scipy==1.15.3
semantic-version==2.10.0
shellingham==1.5.4
simple-websocket==1.1.0
six==1.17.0
sniffio==1.3.1
soupsieve==2.7
SQLAlchemy==2.0.41
sse-starlette==2.3.6
starlette==0.46.2
storage3==0.11.3
StrEnum==0.4.15
stripe==12.2.0
supabase==2.15.2
supafunc==0.9.4
tavily-python==0.7.3
tenacity==9.1.2
termcolor==2.4.0
threadpoolctl==3.6.0
tiktoken==0.9.0
tld==0.13.1
tldextract==5.3.0
tokenizers==0.21.1
tomli==2.2.1
tomlkit==0.13.2
toolz==1.0.0
tqdm==4.67.1
trafilatura==2.0.0
Twisted==24.11.0
typer==0.16.0
typing-inspect==0.9.0
typing-inspection==0.4.0
typing_extensions==4.13.2
tzdata==2025.2
tzlocal==5.3.1
upstash-redis==1.3.0
uritemplate==4.1.1
urllib3==2.4.0
uvicorn==0.34.2
vine==5.1.0
vncdotool==1.2.0
wcwidth==0.2.13
websocket-client==1.8.0
websockets==14.2
Werkzeug==2.0.3
wrapt==1.17.2
wsproto==1.2.0
xxhash==3.5.0
yarl==1.19.0
zipp==3.21.0
zope.interface==7.2
zstandard==0.23.0