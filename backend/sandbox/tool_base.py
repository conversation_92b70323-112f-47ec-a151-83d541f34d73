from typing import Optional

from agentpress.thread_manager import ThreadManager
from agentpress.tool import Tool
from daytona_sdk import Sandbox
from sandbox.sandbox import get_or_start_sandbox
from utils.logger import logger
from utils.files_utils import clean_path

class SandboxToolsBase(Tool):
    """Base class for all sandbox tools that provides project-based sandbox access."""
    
    # Class variable to track if sandbox URLs have been printed
    _urls_printed = False
    
    def __init__(self, project_id: str, thread_manager: Optional[ThreadManager] = None):
        super().__init__()
        self.project_id = project_id
        self.thread_manager = thread_manager
        self.workspace_path = "/workspace"
        self._sandbox = None
        self._sandbox_id = None
        self._sandbox_pass = None

    async def _ensure_sandbox(self) -> Sandbox:
        """Ensure we have a valid sandbox instance, retrieving it from the project if needed."""
        if self._sandbox is None:
            try:
                # Get database client
                client = await self.thread_manager.db.client
                
                # Get project data
                project = await client.table('projects').select('*').eq('project_id', self.project_id).execute()
                if not project.data or len(project.data) == 0:
                    raise ValueError(f"Project {self.project_id} not found")
                
                project_data = project.data[0]
                sandbox_info = project_data.get('sandbox', {})
                
                if not sandbox_info.get('id'):
                    # 自动为项目创建沙盒环境，而不是抛出错误
                    logger.info(f"No sandbox found for project {self.project_id}, creating one in tool...")
                    try:
                        from sandbox.sandbox import create_sandbox
                        from uuid import uuid4
                        
                        # 创建沙盒
                        sandbox_pass = str(uuid4())
                        sandbox = create_sandbox(sandbox_pass, self.project_id)
                        sandbox_id = sandbox.id
                        
                        # 获取预览链接
                        vnc_link = sandbox.get_preview_link(6080)
                        website_link = sandbox.get_preview_link(8080)
                        vnc_url = vnc_link.url if hasattr(vnc_link, 'url') else str(vnc_link).split("url='")[1].split("'")[0]
                        website_url = website_link.url if hasattr(website_link, 'url') else str(website_link).split("url='")[1].split("'")[0]
                        
                        # 更新项目的沙盒信息
                        sandbox_data = {
                            "id": sandbox_id,
                            "pass": sandbox_pass,
                            "vnc_preview": vnc_url,
                            "sandbox_url": website_url
                        }
                        
                        await client.table('projects').update({'sandbox': sandbox_data}).eq('project_id', self.project_id).execute()
                        logger.info(f"Created and saved new sandbox {sandbox_id} for project {self.project_id} in tool")
                        
                        # 更新本地sandbox_info变量
                        sandbox_info = sandbox_data
                        
                        logger.info(f"✅ Successfully created sandbox {sandbox_id} for project {self.project_id} in tool")
                        
                    except Exception as create_error:
                        logger.error(f"Failed to create sandbox for project {self.project_id} in tool: {str(create_error)}")
                        raise ValueError(f"Failed to create sandbox for project {self.project_id}: {str(create_error)}")
                
                # Store sandbox info
                self._sandbox_id = sandbox_info['id']
                self._sandbox_pass = sandbox_info.get('pass')
                
                # Get or start the sandbox
                self._sandbox = await get_or_start_sandbox(self._sandbox_id)
                
                # 确保文件系统接口可用 - 添加fs属性如果不存在
                if not hasattr(self._sandbox, 'fs'):
                    from daytona_sdk.filesystem import FileSystem
                    try:
                        # 使用sandbox的instance和toolbox_api创建FileSystem
                        self._sandbox.fs = FileSystem(
                            instance=self._sandbox.instance,
                            toolbox_api=self._sandbox.toolbox_api
                        )
                        logger.info(f"✅ FileSystem interface initialized for sandbox {self._sandbox_id}")
                    except Exception as fs_error:
                        logger.error(f"Failed to initialize FileSystem for sandbox {self._sandbox_id}: {str(fs_error)}")
                        # 不抛出异常，让工具继续运行，可能SDK会自动处理
                
                # # Log URLs if not already printed
                # if not SandboxToolsBase._urls_printed:
                #     vnc_link = self._sandbox.get_preview_link(6080)
                #     website_link = self._sandbox.get_preview_link(8080)
                    
                #     vnc_url = vnc_link.url if hasattr(vnc_link, 'url') else str(vnc_link)
                #     website_url = website_link.url if hasattr(website_link, 'url') else str(website_link)
                    
                #     print("\033[95m***")
                #     print(f"VNC URL: {vnc_url}")
                #     print(f"Website URL: {website_url}")
                #     print("***\033[0m")
                #     SandboxToolsBase._urls_printed = True
                
            except Exception as e:
                logger.error(f"Error retrieving sandbox for project {self.project_id}: {str(e)}", exc_info=True)
                raise e
        
        return self._sandbox

    @property
    def sandbox(self) -> Sandbox:
        """Get the sandbox instance, ensuring it exists."""
        if self._sandbox is None:
            raise RuntimeError("Sandbox not initialized. Call _ensure_sandbox() first.")
        return self._sandbox

    @property
    def sandbox_id(self) -> str:
        """Get the sandbox ID, ensuring it exists."""
        if self._sandbox_id is None:
            raise RuntimeError("Sandbox ID not initialized. Call _ensure_sandbox() first.")
        return self._sandbox_id

    def clean_path(self, path: str) -> str:
        """Clean and normalize a path to be relative to /workspace."""
        cleaned_path = clean_path(path, self.workspace_path)
        logger.debug(f"Cleaned path: {path} -> {cleaned_path}")
        return cleaned_path

    async def _get_user_id(self) -> Optional[str]:
        """获取当前用户ID（从项目信息中获取）"""
        try:
            if self.thread_manager:
                client = await self.thread_manager.db.client
                
                # 从项目获取所有者信息
                project_result = await client.table('projects') \
                    .select('account_id') \
                    .eq('project_id', self.project_id) \
                    .execute()
                
                if project_result.data and len(project_result.data) > 0:
                    return project_result.data[0]['account_id']
            
            logger.warning("Could not determine user_id for tool usage tracking")
            return None
            
        except Exception as e:
            logger.error(f"Error getting user_id: {str(e)}")
            return None
    
    async def _get_project_id(self) -> Optional[str]:
        """获取当前项目ID"""
        return self.project_id