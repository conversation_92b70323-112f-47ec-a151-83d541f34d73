"""
LLMs基础模块 - 提供模型集成的基础类和工具

实现了AI模型调用的抽象接口，支持文本和图像生成功能。
所有模型实现必须遵循定义的抽象基类。
"""

from typing import Dict, List, Any, Optional, Union, AsyncGenerator
from abc import ABC, abstractmethod
import logging
import os
import json
from enum import Enum
from dataclasses import dataclass, field
from pathlib import Path
import asyncio

logger = logging.getLogger(__name__)


class ModelType(Enum):
    """模型类型枚举"""
    TEXT = "text"                # 文本生成模型
    IMAGE = "image"              # 图像生成模型
    EMBEDDING = "embedding"      # 嵌入向量模型
    AUDIO = "audio"              # 音频处理模型


@dataclass
class ModelResponse:
    """模型响应基类"""
    content: Any                 # 响应内容（文本、图像URL等）
    model: str                   # 使用的模型名称
    usage: Dict[str, Any] = field(default_factory=dict)  # 使用量统计
    metadata: Dict[str, Any] = field(default_factory=dict)  # 元数据
    
    @property
    def token_usage(self) -> Dict[str, int]:
        """获取Token使用量"""
        return {
            "prompt_tokens": self.usage.get("prompt_tokens", 0),
            "completion_tokens": self.usage.get("completion_tokens", 0),
            "total_tokens": self.usage.get("total_tokens", 0)
        }


@dataclass
class TextGenerationResponse(ModelResponse):
    """文本生成响应"""
    content: str                  # 生成的文本内容
    finish_reason: Optional[str] = None  # 结束原因
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "TextGenerationResponse":
        """从字典创建响应对象"""
        return cls(
            content=data.get("content", ""),
            model=data.get("model", "unknown"),
            usage=data.get("usage", {}),
            metadata=data.get("metadata", {}),
            finish_reason=data.get("finish_reason")
        )


@dataclass
class ImageGenerationResponse(ModelResponse):
    """图像生成响应"""
    content: List[str]            # 生成的图像URL列表
    prompt: str                   # 使用的提示词
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "ImageGenerationResponse":
        """从字典创建响应对象"""
        return cls(
            content=data.get("images", []),
            model=data.get("model", "unknown"),
            prompt=data.get("prompt", ""),
            usage=data.get("usage", {}),
            metadata=data.get("metadata", {})
        )


class BaseModel(ABC):
    """模型基类"""
    
    def __init__(self, model_name: str, model_type: ModelType):
        self.model_name = model_name
        self.model_type = model_type
        self._initialized = False
        
    async def initialize(self):
        """异步初始化"""
        if not self._initialized:
            await self._initialize()
            self._initialized = True
            
    async def _initialize(self):
        """子类实现具体初始化逻辑"""
        pass
    
    def __str__(self) -> str:
        return f"{self.__class__.__name__}({self.model_name})"


class TextGenerationModel(BaseModel):
    """文本生成模型基类"""
    
    def __init__(self, model_name: str):
        super().__init__(model_name, ModelType.TEXT)
    
    @abstractmethod
    async def generate(self, 
                     prompt: str, 
                     system_prompt: Optional[str] = None,
                     max_tokens: Optional[int] = None,
                     temperature: Optional[float] = None,
                     top_p: Optional[float] = None,
                     stop: Optional[List[str]] = None,
                     **kwargs) -> TextGenerationResponse:
        """生成文本"""
        pass
    
    @abstractmethod
    async def generate_stream(self, 
                            prompt: str, 
                            system_prompt: Optional[str] = None,
                            max_tokens: Optional[int] = None,
                            temperature: Optional[float] = None,
                            top_p: Optional[float] = None,
                            stop: Optional[List[str]] = None,
                            **kwargs) -> AsyncGenerator[str, None]:
        """流式生成文本"""
        yield ""


class ImageGenerationModel(BaseModel):
    """图像生成模型基类"""
    
    def __init__(self, model_name: str):
        super().__init__(model_name, ModelType.IMAGE)
    
    @abstractmethod
    async def generate(self,
                     prompt: str,
                     negative_prompt: Optional[str] = None,
                     width: Optional[int] = None,
                     height: Optional[int] = None,
                     num_images: int = 1,
                     **kwargs) -> ImageGenerationResponse:
        """生成图像"""
        pass


# 模型实现集合
_models: Dict[str, BaseModel] = {}

def register_model(model_id: str, model: BaseModel):
    """注册模型"""
    global _models
    _models[model_id] = model
    logger.info(f"注册模型: {model_id} -> {model}")

def get_model(model_id: str) -> Optional[BaseModel]:
    """获取模型实例"""
    global _models
    return _models.get(model_id)

def get_text_model(model_id: Optional[str] = None) -> Optional[TextGenerationModel]:
    """获取文本模型"""
    global _models
    
    if model_id:
        model = get_model(model_id)
        return model if isinstance(model, TextGenerationModel) else None
        
    # 返回默认文本模型
    for model in _models.values():
        if isinstance(model, TextGenerationModel):
            return model
    return None

def get_image_model(model_id: Optional[str] = None) -> Optional[ImageGenerationModel]:
    """获取图像模型"""
    global _models
    
    if model_id:
        model = get_model(model_id)
        return model if isinstance(model, ImageGenerationModel) else None
        
    # 返回默认图像模型
    for model in _models.values():
        if isinstance(model, ImageGenerationModel):
            return model
    return None

async def load_models():
    """加载所有模型"""
    from .bedrock import init_bedrock_models
    await init_bedrock_models()
    
    logger.info(f"已加载 {len(_models)} 个AI模型")


# 环境变量配置
class EnvConfig:
    """环境变量配置工具"""
    
    @staticmethod
    def get_str(key: str, default: Optional[str] = None) -> Optional[str]:
        """获取字符串环境变量"""
        return os.environ.get(key, default)
    
    @staticmethod
    def get_int(key: str, default: Optional[int] = None) -> Optional[int]:
        """获取整数环境变量"""
        try:
            value = os.environ.get(key)
            return int(value) if value is not None else default
        except ValueError:
            logger.warning(f"环境变量 {key} 不是有效整数")
            return default
    
    @staticmethod
    def get_float(key: str, default: Optional[float] = None) -> Optional[float]:
        """获取浮点数环境变量"""
        try:
            value = os.environ.get(key)
            return float(value) if value is not None else default
        except ValueError:
            logger.warning(f"环境变量 {key} 不是有效浮点数")
            return default
    
    @staticmethod
    def get_bool(key: str, default: Optional[bool] = None) -> Optional[bool]:
        """获取布尔环境变量"""
        value = os.environ.get(key)
        if value is None:
            return default
        return value.lower() in ('true', '1', 'yes', 'y', 't')
    
    @staticmethod
    def load_dotenv(env_path: Optional[str] = None):
        """加载.env文件配置"""
        try:
            from dotenv import load_dotenv
            
            if env_path:
                load_dotenv(env_path)
            else:
                # 尝试从默认位置加载
                dotenv_paths = [
                    ".env",
                    "../.env",
                    Path.home() / ".env.mediaagent"
                ]
                
                for path in dotenv_paths:
                    if os.path.exists(path):
                        load_dotenv(path)
                        logger.info(f"已加载环境变量: {path}")
                        return
                
                # 如果没有找到.env文件，尝试加载当前目录的.env
                load_dotenv()
                
        except ImportError:
            logger.warning("python-dotenv未安装，无法加载.env文件")
        except Exception as e:
            logger.warning(f"加载.env文件失败: {e}")