"""
MediaAgent V3 - 文章创作Agent

基于Generator系统迁移的文章创作能力，集成简化版Prompt管理系统
"""

import json
import logging
from typing import Dict, Any, List, Optional, AsyncIterator
from datetime import datetime

from ..core import SimplePromptManager, SimplePromptTemplate

logger = logging.getLogger(__name__)


class ArticleAgent:
    """文章创作Agent - 集成Generator系统的文章创作能力"""
    
    def __init__(self, prompt_manager: SimplePromptManager, llm_client=None):
        """
        初始化文章Agent
        
        Args:
            prompt_manager: Prompt管理器
            llm_client: LLM客户端（可选，用于实际调用）
        """
        self.prompt_manager = prompt_manager
        self.llm_client = llm_client
        self.name = "article_agent"
        
        # 加载文章创作相关的prompts
        self.prompts = self._load_article_prompts()
        
        logger.info(f"ArticleAgent initialized with {len(self.prompts)} prompts")
    
    def _load_article_prompts(self) -> Dict[str, SimplePromptTemplate]:
        """加载文章创作相关的prompts"""
        prompts = {}
        
        # 需要的prompt列表
        required_prompts = [
            "article_planning",
            "content_writing", 
            "content_enhancement",
            "quality_assessment"
        ]
        
        for prompt_name in required_prompts:
            prompt = self.prompt_manager.get_prompt(prompt_name)
            if prompt:
                prompts[prompt_name] = prompt
                logger.debug(f"Loaded prompt: {prompt_name}")
            else:
                logger.warning(f"Prompt not found: {prompt_name}")
        
        return prompts
    
    async def create_article(self, 
                           topic: str,
                           keywords: List[str] = None,
                           content_direction: str = "",
                           article_type: str = "专业分析",
                           target_audience: str = "一般读者",
                           search_content: str = "",
                           **kwargs) -> Dict[str, Any]:
        """
        完整的文章创作流程
        
        Args:
            topic: 文章主题
            keywords: 关键词列表
            content_direction: 内容方向
            article_type: 文章类型
            target_audience: 目标受众
            search_content: 搜索到的参考资料
            **kwargs: 其他参数
            
        Returns:
            包含完整文章和创作过程信息的字典
        """
        logger.info(f"Starting article creation for topic: {topic}")
        
        try:
            # 第一步：文章规划
            planning_result = await self.plan_article(
                topic=topic,
                keywords=keywords or [],
                content_direction=content_direction,
                article_type=article_type,
                target_audience=target_audience
            )
            
            # 第二步：内容写作
            writing_result = await self.write_content(
                topic=topic,
                keywords=keywords or [],
                outline=planning_result.get("outline", []),
                search_content=search_content,
                writing_plan=planning_result,
                article_type=article_type
            )
            
            # 第三步：质量评估
            assessment_result = await self.assess_quality(
                content=writing_result.get("content", ""),
                topic=topic,
                keywords=keywords or [],
                outline=planning_result.get("outline", [])
            )
            
            # 第四步：内容增强（如果需要）
            final_content = writing_result.get("content", "")
            if assessment_result.get("overall_score", 0) < 8.0:
                enhancement_result = await self.enhance_content(
                    existing_content=writing_result,
                    search_content=search_content,
                    knowledge_gaps=assessment_result.get("knowledge_gaps", []),
                    humanizing_suggestions=assessment_result.get("humanizing_suggestions", []),
                    improvement_suggestion=assessment_result.get("next_action", ""),
                    topic=topic,
                    keywords=keywords or [],
                    article_type=article_type
                )
                final_content = enhancement_result.get("enhanced_content", final_content)
            
            # 组装最终结果
            result = {
                "success": True,
                "article": {
                    "title": planning_result.get("title", topic),
                    "content": final_content,
                    "word_count": len(final_content.split()) if final_content else 0,
                    "metadata": {
                        "topic": topic,
                        "keywords": keywords or [],
                        "article_type": article_type,
                        "target_audience": target_audience,
                        "created_at": datetime.now().isoformat()
                    }
                },
                "creation_process": {
                    "planning": planning_result,
                    "writing": writing_result,
                    "assessment": assessment_result,
                    "enhanced": assessment_result.get("overall_score", 0) < 8.0
                }
            }
            
            logger.info(f"Article creation completed successfully for topic: {topic}")
            return result
            
        except Exception as e:
            logger.error(f"Article creation failed for topic {topic}: {e}")
            return {
                "success": False,
                "error": str(e),
                "article": None,
                "creation_process": None
            }
    
    async def plan_article(self, 
                          topic: str,
                          keywords: List[str],
                          content_direction: str = "",
                          article_type: str = "专业分析",
                          target_audience: str = "一般读者") -> Dict[str, Any]:
        """
        文章规划阶段
        
        Args:
            topic: 文章主题
            keywords: 关键词列表
            content_direction: 内容方向
            article_type: 文章类型
            target_audience: 目标受众
            
        Returns:
            文章规划结果
        """
        logger.info(f"Planning article for topic: {topic}")
        
        # 获取规划prompt
        planning_prompt = self.prompts.get("article_planning")
        if not planning_prompt:
            raise ValueError("Article planning prompt not found")
        
        # 渲染prompt
        rendered_prompt = planning_prompt.render(
            topic=topic,
            keywords=", ".join(keywords) if keywords else "",
            content_direction=content_direction,
            article_type=article_type,
            target_audience=target_audience
        )
        
        # 调用LLM（如果有客户端）
        if self.llm_client:
            llm_response = await self.llm_client.complete(rendered_prompt)
            planning_result = self._parse_json_response(llm_response)
        else:
            # 模拟响应（用于测试）
            planning_result = self._generate_mock_planning(topic, keywords, article_type)
        
        logger.info(f"Article planning completed for topic: {topic}")
        return planning_result
    
    async def write_content(self,
                           topic: str,
                           keywords: List[str],
                           outline: List[Dict[str, Any]],
                           search_content: str = "",
                           writing_plan: Dict[str, Any] = None,
                           article_type: str = "专业分析") -> Dict[str, Any]:
        """
        内容写作阶段
        
        Args:
            topic: 文章主题
            keywords: 关键词列表
            outline: 文章大纲
            search_content: 搜索内容
            writing_plan: 写作计划
            article_type: 文章类型
            
        Returns:
            写作结果
        """
        logger.info(f"Writing content for topic: {topic}")
        
        # 获取写作prompt
        writing_prompt = self.prompts.get("content_writing")
        if not writing_prompt:
            raise ValueError("Content writing prompt not found")
        
        # 渲染prompt
        rendered_prompt = writing_prompt.render(
            topic=topic,
            keywords=", ".join(keywords) if keywords else "",
            outline=json.dumps(outline, ensure_ascii=False, indent=2),
            search_content=search_content,
            writing_plan=json.dumps(writing_plan or {}, ensure_ascii=False, indent=2),
            article_type=article_type,
            target_word_count=writing_plan.get("target_word_count", 1500) if writing_plan else 1500
        )
        
        # 调用LLM（如果有客户端）
        if self.llm_client:
            llm_response = await self.llm_client.complete(rendered_prompt)
            writing_result = self._parse_json_response(llm_response)
        else:
            # 模拟响应（用于测试）
            writing_result = self._generate_mock_content(topic, outline)
        
        logger.info(f"Content writing completed for topic: {topic}")
        return writing_result
    
    async def assess_quality(self,
                            content: str,
                            topic: str,
                            keywords: List[str],
                            outline: List[Dict[str, Any]] = None,
                            evaluation_round: int = 1) -> Dict[str, Any]:
        """
        质量评估阶段
        
        Args:
            content: 文章内容
            topic: 文章主题
            keywords: 关键词列表
            outline: 原始大纲
            evaluation_round: 评估轮次
            
        Returns:
            质量评估结果
        """
        logger.info(f"Assessing quality for topic: {topic}")
        
        # 获取评估prompt
        assessment_prompt = self.prompts.get("quality_assessment")
        if not assessment_prompt:
            raise ValueError("Quality assessment prompt not found")
        
        # 渲染prompt
        rendered_prompt = assessment_prompt.render(
            content=content,
            topic=topic,
            keywords=", ".join(keywords) if keywords else "",
            outline=json.dumps(outline or [], ensure_ascii=False, indent=2),
            evaluation_round=str(evaluation_round)
        )
        
        # 调用LLM（如果有客户端）
        if self.llm_client:
            llm_response = await self.llm_client.complete(rendered_prompt)
            assessment_result = self._parse_json_response(llm_response)
        else:
            # 模拟响应（用于测试）
            assessment_result = self._generate_mock_assessment(content)
        
        logger.info(f"Quality assessment completed for topic: {topic}")
        return assessment_result
    
    async def enhance_content(self,
                             existing_content: Dict[str, Any],
                             search_content: str,
                             knowledge_gaps: List[str] = None,
                             humanizing_suggestions: List[str] = None,
                             improvement_suggestion: str = "",
                             topic: str = "",
                             keywords: List[str] = None,
                             article_type: str = "专业分析") -> Dict[str, Any]:
        """
        内容增强阶段
        
        Args:
            existing_content: 现有内容
            search_content: 搜索内容
            knowledge_gaps: 知识空缺
            humanizing_suggestions: 人性化建议
            improvement_suggestion: 改进建议
            topic: 主题
            keywords: 关键词
            article_type: 文章类型
            
        Returns:
            增强结果
        """
        logger.info(f"Enhancing content for topic: {topic}")
        
        # 获取增强prompt
        enhancement_prompt = self.prompts.get("content_enhancement")
        if not enhancement_prompt:
            raise ValueError("Content enhancement prompt not found")
        
        # 渲染prompt
        rendered_prompt = enhancement_prompt.render(
            existing_content=json.dumps(existing_content, ensure_ascii=False, indent=2),
            search_content=search_content,
            knowledge_gaps=", ".join(knowledge_gaps or []),
            humanizing_suggestions=", ".join(humanizing_suggestions or []),
            improvement_suggestion=improvement_suggestion,
            topic=topic,
            keywords=", ".join(keywords or []),
            article_type=article_type
        )
        
        # 调用LLM（如果有客户端）
        if self.llm_client:
            llm_response = await self.llm_client.complete(rendered_prompt)
            enhancement_result = self._parse_json_response(llm_response)
        else:
            # 模拟响应（用于测试）
            enhancement_result = self._generate_mock_enhancement(existing_content)
        
        logger.info(f"Content enhancement completed for topic: {topic}")
        return enhancement_result

    def _parse_json_response(self, response: str) -> Dict[str, Any]:
        """
        解析LLM的JSON响应

        Args:
            response: LLM响应字符串

        Returns:
            解析后的字典
        """
        try:
            # 尝试直接解析JSON
            return json.loads(response)
        except json.JSONDecodeError:
            try:
                # 尝试提取JSON代码块
                import re
                json_match = re.search(r'```json\s*(.*?)\s*```', response, re.DOTALL)
                if json_match:
                    return json.loads(json_match.group(1))

                # 尝试提取{}包围的内容
                brace_match = re.search(r'\{.*\}', response, re.DOTALL)
                if brace_match:
                    return json.loads(brace_match.group(0))

                # 如果都失败，返回错误信息
                return {"error": "Failed to parse JSON response", "raw_response": response}

            except Exception as e:
                logger.error(f"Failed to parse JSON response: {e}")
                return {"error": str(e), "raw_response": response}

    def _generate_mock_planning(self, topic: str, keywords: List[str], article_type: str) -> Dict[str, Any]:
        """生成模拟的文章规划结果（用于测试）"""
        return {
            "title": f"{topic} - 深度分析",
            "subtitle": f"基于{article_type}的专业解读",
            "target_word_count": 2000,
            "outline": [
                {
                    "section": "引言",
                    "key_points": [f"{topic}的重要性", "当前发展现状", "本文分析框架"],
                    "estimated_words": 300
                },
                {
                    "section": f"{topic}的核心概念",
                    "key_points": ["基本定义", "关键特征", "发展历程"],
                    "estimated_words": 500
                },
                {
                    "section": f"{topic}的应用实践",
                    "key_points": ["实际应用案例", "成功经验", "挑战与问题"],
                    "estimated_words": 600
                },
                {
                    "section": f"{topic}的发展趋势",
                    "key_points": ["技术发展方向", "市场前景", "政策影响"],
                    "estimated_words": 400
                },
                {
                    "section": "总结与展望",
                    "key_points": ["主要观点总结", "未来发展预测", "建议与思考"],
                    "estimated_words": 200
                }
            ],
            "research_points": [
                f"{topic}的最新发展动态",
                f"{topic}的成功案例分析",
                f"{topic}相关的政策法规"
            ],
            "style_guide": {
                "tone": "专业",
                "perspective": "第三人称",
                "special_notes": "注重数据支撑和案例分析"
            },
            "seo_keywords": keywords[:5] if keywords else [topic]
        }

    def _generate_mock_content(self, topic: str, outline: List[Dict[str, Any]]) -> Dict[str, Any]:
        """生成模拟的文章内容（用于测试）"""
        content_parts = []

        # 生成标题
        content_parts.append(f"# {topic} - 深度分析\n")

        # 根据大纲生成内容
        for section in outline:
            section_name = section.get("section", "")
            key_points = section.get("key_points", [])

            content_parts.append(f"\n## {section_name}\n")

            for point in key_points:
                content_parts.append(f"\n### {point}\n")
                content_parts.append(f"关于{point}的详细分析内容。这里会包含相关的理论阐述、实际案例和数据支撑。\n")

        content = "".join(content_parts)

        return {
            "title": f"{topic} - 深度分析",
            "content": content,
            "word_count": len(content.split()),
            "sections_completed": [section.get("section", "") for section in outline],
            "key_points_covered": [point for section in outline for point in section.get("key_points", [])],
            "seo_optimization": {
                "keywords_used": [topic],
                "keyword_density": "2.5%"
            },
            "quality_indicators": {
                "has_introduction": True,
                "has_conclusion": True,
                "logical_flow": True,
                "professional_tone": True
            }
        }

    def _generate_mock_assessment(self, content: str) -> Dict[str, Any]:
        """生成模拟的质量评估结果（用于测试）"""
        word_count = len(content.split()) if content else 0

        # 基于字数简单评估
        if word_count > 1500:
            overall_score = 8.5
        elif word_count > 1000:
            overall_score = 7.5
        else:
            overall_score = 6.5

        return {
            "overall_score": overall_score,
            "dimension_scores": {
                "content_quality": {
                    "depth": 8.0,
                    "accuracy": 8.5,
                    "completeness": 7.5,
                    "originality": 8.0,
                    "professionalism": 8.5
                },
                "structure_quality": {
                    "logic": 8.5,
                    "coherence": 8.0,
                    "hierarchy": 8.5,
                    "completeness": 8.0
                },
                "language_quality": {
                    "fluency": 8.0,
                    "accuracy": 8.5,
                    "readability": 8.0,
                    "engagement": 7.5
                },
                "seo_quality": {
                    "keyword_usage": 7.5,
                    "title_optimization": 8.0,
                    "content_relevance": 8.5
                }
            },
            "strengths": [
                "文章结构清晰，逻辑性强",
                "专业术语使用准确",
                "内容深度适中"
            ],
            "weaknesses": [
                "某些段落可以更加生动",
                "缺少具体案例支撑"
            ],
            "knowledge_gaps": [
                "缺少最新的行业数据",
                "需要补充实际应用案例"
            ],
            "improvement_suggestions": [
                "在关键段落增加具体数据支撑",
                "适当增加小标题提升可读性"
            ],
            "humanizing_suggestions": [
                "增加一些生动的比喻",
                "使用更多主动语态"
            ],
            "next_action": "建议进行内容增强" if overall_score < 8.0 else "内容质量良好，可以发布"
        }

    def _generate_mock_enhancement(self, existing_content: Dict[str, Any]) -> Dict[str, Any]:
        """生成模拟的内容增强结果（用于测试）"""
        original_content = existing_content.get("content", "")
        enhanced_content = original_content + "\n\n## 补充内容\n\n基于最新资料和分析，我们补充以下重要信息...\n"

        return {
            "enhanced_content": enhanced_content,
            "enhancement_summary": {
                "added_sections": ["补充内容"],
                "improved_sections": ["引言", "总结"],
                "knowledge_gaps_filled": ["最新行业数据", "实际应用案例"],
                "humanization_applied": ["增加生动比喻", "优化语言表达"]
            },
            "quality_improvements": {
                "depth_score": "9.0",
                "readability_score": "8.5",
                "professional_score": "9.0",
                "engagement_score": "8.0"
            },
            "word_count_change": {
                "original_count": len(original_content.split()) if original_content else 0,
                "enhanced_count": len(enhanced_content.split()),
                "increase_percentage": "15%"
            },
            "seo_optimization": {
                "keywords_integration": ["新关键词1", "新关键词2"],
                "keyword_density": "3.0%"
            }
        }

    def reload_prompts(self) -> bool:
        """重新加载prompts"""
        try:
            self.prompts = self._load_article_prompts()
            logger.info("Article prompts reloaded successfully")
            return True
        except Exception as e:
            logger.error(f"Failed to reload prompts: {e}")
            return False

    def get_available_prompts(self) -> List[str]:
        """获取可用的prompt列表"""
        return list(self.prompts.keys())

    def __str__(self) -> str:
        return f"ArticleAgent(prompts={len(self.prompts)})"
