"""
Stripe Billing API implementation for Sun<PERSON> on top of Basejump. ONLY HAS SUPPOT FOR USER ACCOUNTS – no team accounts. As we are using the user_id as account_id as is the case with personal accounts. In personal accounts, the account_id equals the user_id. In team accounts, the account_id is unique.

stripe listen --forward-to localhost:8000/api我刚刚/billing/webhook
"""

from fastapi import APIRouter, HTTPException, Depends, Request, Query
from typing import Op<PERSON>, Dict, Tuple, Any
import stripe
from datetime import datetime, timezone, timedelta
from utils.logger import logger
from utils.config import config, EnvMode
from services.supabase import DBConnection
from utils.auth_utils import get_current_user_id_from_jwt
from pydantic import BaseModel
from utils.constants import MODEL_ACCESS_TIERS, MODEL_NAME_ALIASES, HARDCODED_MODEL_PRICES
from litellm.cost_calculator import cost_per_token
import time

# Initialize Stripe
stripe.api_key = config.STRIPE_SECRET_KEY

# Token price multiplier
TOKEN_PRICE_MULTIPLIER = 1.5

# Initialize router
router = APIRouter(prefix="/billing", tags=["billing"])


# Subscription tiers configuration with credit amounts
SUBSCRIPTION_TIERS = {
    config.STRIPE_PRICE_ID_BASIC: {'tier': 'basic','name': 'Basic', 'credits': config.STRIPE_CREDITS_BASIC, 'price': config.STRIPE_PRICE_BASIC},
    config.STRIPE_PRICE_ID_PLUS: {'tier': 'plus','name': 'Plus', 'credits': config.STRIPE_CREDITS_PLUS, 'price': config.STRIPE_PRICE_PLUS},
    config.STRIPE_PRICE_ID_PRO: {'tier': 'pro','name': 'Pro', 'credits': config.STRIPE_CREDITS_PRO, 'price': config.STRIPE_PRICE_PRO},
}

# Legacy credit packages for backward compatibility
LEGACY_CREDIT_PACKAGES = {
    config.STRIPE_CREDITS_100: {'credits': 100, 'name': 'Small Pack'},
    config.STRIPE_CREDITS_500: {'credits': 500, 'name': 'Medium Pack'},
    config.STRIPE_CREDITS_1000: {'credits': 1000, 'name': 'Large Pack'},
    config.STRIPE_CREDITS_5000: {'credits': 5000, 'name': 'Extra Large Pack'},
}

# Pydantic models for request/response validation
class CreateCheckoutSessionRequest(BaseModel):
    price_id: str
    success_url: str
    cancel_url: str
    tolt_referral: Optional[str] = None

class CreatePortalSessionRequest(BaseModel):
    return_url: str

class SubscriptionStatus(BaseModel):
    status: str # e.g., 'active', 'trialing', 'past_due', 'scheduled_downgrade', 'no_subscription'
    plan_name: Optional[str] = None
    price_id: Optional[str] = None # Added price ID
    current_period_end: Optional[datetime] = None
    cancel_at_period_end: bool = False
    trial_end: Optional[datetime] = None
    minutes_limit: Optional[int] = None
    cost_limit: Optional[float] = None
    current_usage: Optional[float] = None
    # Fields for scheduled changes
    has_schedule: bool = False
    scheduled_plan_name: Optional[str] = None
    scheduled_price_id: Optional[str] = None # Added scheduled price ID
    scheduled_change_date: Optional[datetime] = None
    # 新增字段
    tier: Optional[str] = None
    price_usd: Optional[float] = None

class CreditUsageStats(BaseModel):
    today: float
    this_week: float
    this_month: float
    unit: str = "credits"

class TransactionRecord(BaseModel):
    type: str  # 'subscription', 'credit_usage', etc.
    amount: float
    unit: str = "credits"
    created_at: datetime
    description: Optional[str] = None

class TransactionHistory(BaseModel):
    transactions: list[TransactionRecord]

# Helper functions
async def get_stripe_customer_id(client, user_id: str) -> Optional[str]:
    """Get the Stripe customer ID for a user."""
    result = await client.schema('basejump').from_('billing_customers') \
        .select('id') \
        .eq('account_id', user_id) \
        .execute()
    
    if result.data and len(result.data) > 0:
        return result.data[0]['id']
    return None

async def create_stripe_customer(client, user_id: str, email: str) -> str:
    """Create a new Stripe customer for a user."""
    # Create customer in Stripe
    customer = stripe.Customer.create(
        email=email,
        metadata={"user_id": user_id}
    )
    
    # Store customer ID in Supabase
    await client.schema('basejump').from_('billing_customers').insert({
        'id': customer.id,
        'account_id': user_id,
        'email': email,
        'provider': 'stripe'
    }).execute()
    
    return customer.id

async def get_user_subscription(user_id: str) -> Optional[Dict]:
    """Get the current subscription for a user from Stripe."""
    try:
        # Get customer ID
        db = DBConnection()
        client = await db.client
        customer_id = await get_stripe_customer_id(client, user_id)
        
        if not customer_id:
            return None
            
        # Get all active subscriptions for the customer
        subscriptions = stripe.Subscription.list(
            customer=customer_id,
            status='active'
        )
        # print("Found subscriptions:", subscriptions)
        
        # Check if we have any subscriptions
        if not subscriptions or not subscriptions.get('data'):
            return None
            
        # Filter subscriptions to only include our product's subscriptions
        our_subscriptions = []
        for sub in subscriptions['data']:
            # Get the first subscription item
            if sub.get('items') and sub['items'].get('data') and len(sub['items']['data']) > 0:
                item = sub['items']['data'][0]
                if item.get('price') and item['price'].get('id') in [
                    config.STRIPE_PRICE_ID_BASIC,
                    config.STRIPE_PRICE_ID_PLUS,
                    config.STRIPE_PRICE_ID_PRO
                ]:
                    our_subscriptions.append(sub)
        
        if not our_subscriptions:
            return None
            
        # If there are multiple active subscriptions, we need to handle this
        if len(our_subscriptions) > 1:
            logger.warning(f"User {user_id} has multiple active subscriptions: {[sub['id'] for sub in our_subscriptions]}")
            
            # Get the most recent subscription
            most_recent = max(our_subscriptions, key=lambda x: x['created'])
            
            # Cancel all other subscriptions
            for sub in our_subscriptions:
                if sub['id'] != most_recent['id']:
                    try:
                        stripe.Subscription.modify(
                            sub['id'],
                            cancel_at_period_end=True
                        )
                        logger.info(f"Cancelled subscription {sub['id']} for user {user_id}")
                    except Exception as e:
                        logger.error(f"Error cancelling subscription {sub['id']}: {str(e)}")
            
            return most_recent
            
        return our_subscriptions[0]
        
    except Exception as e:
        logger.error(f"Error getting subscription from Stripe: {str(e)}")
        return None

async def calculate_monthly_usage(client, user_id: str) -> float:
    """Calculate total agent run minutes for the current month for a user."""
    start_time = time.time()
    
    # Use get_usage_logs to fetch all LLM usage data (it already handles the date filtering and batching)
    total_cost = 0.0
    page = 0
    items_per_page = 1000
    
    while True:
        # Get usage logs for this page
        usage_result = await get_usage_logs(client, user_id, page, items_per_page)
        
        if not usage_result['logs']:
            break
        
        # Sum up the estimated costs from this page
        for log_entry in usage_result['logs']:
            total_cost += log_entry['estimated_cost']
        
        # If there are no more pages, break
        if not usage_result['has_more']:
            break
            
        page += 1
    
    # Add tool usage costs for the current month
    tool_cost = await calculate_monthly_tool_usage(client, user_id)
    total_cost += tool_cost
    
    end_time = time.time()
    execution_time = end_time - start_time
    logger.info(f"Calculate monthly usage took {execution_time:.3f} seconds, total cost: {total_cost} (LLM: {total_cost - tool_cost}, Tools: {tool_cost})")
    
    return total_cost


async def calculate_monthly_tool_usage(client, user_id: str) -> float:
    """Calculate total tool costs for the current month for a user."""
    try:
        # Get start of current month in UTC
        now = datetime.now(timezone.utc)
        start_of_month = datetime(now.year, now.month, 1, tzinfo=timezone.utc)
        
        # Query tool usage for current month
        result = await client.from_('user_tool_stats') \
            .select('total_cost') \
            .eq('user_id', user_id) \
            .eq('month_year', now.strftime("%Y-%m")) \
            .execute()
        
        if result.data:
            # Sum up all tool costs for this month (in credits)
            total_tool_cost = sum(float(record['total_cost']) for record in result.data)
            # Return credits directly (no conversion needed in credit billing mode)
            return total_tool_cost
        
        return 0.0
        
    except Exception as e:
        logger.error(f"Error calculating monthly tool usage: {str(e)}")
        return 0.0


async def get_usage_logs(client, user_id: str, page: int = 0, items_per_page: int = 1000) -> Dict:
    """Get detailed usage logs for a user with pagination."""
    # Get start of current month in UTC
    now = datetime.now(timezone.utc)
    start_of_month = datetime(now.year, now.month, 1, tzinfo=timezone.utc)
    
    # Use fixed cutoff date: June 26, 2025 midnight UTC
    # Ignore all token counts before this date
    cutoff_date = datetime(2025, 6, 30, 9, 0, 0, tzinfo=timezone.utc)
    
    start_of_month = max(start_of_month, cutoff_date)
    
    # First get all threads for this user in batches
    batch_size = 1000
    offset = 0
    all_threads = []
    
    while True:
        threads_batch = await client.table('threads') \
            .select('thread_id') \
            .eq('account_id', user_id) \
            .gte('created_at', start_of_month.isoformat()) \
            .range(offset, offset + batch_size - 1) \
            .execute()
        
        if not threads_batch.data:
            break
            
        all_threads.extend(threads_batch.data)
        
        # If we got less than batch_size, we've reached the end
        if len(threads_batch.data) < batch_size:
            break
            
        offset += batch_size
    
    if not all_threads:
        return {"logs": [], "has_more": False}
    # Get start of current month in UTC
    now = datetime.now(timezone.utc)
    start_of_month = datetime(now.year, now.month, 1, tzinfo=timezone.utc)
    
    # First get all threads for this user
    threads_result = await client.table('threads') \
        .select('thread_id') \
        .eq('account_id', user_id) \
        .execute()
    
    if not threads_result.data:
        return 0.0

        
    
    thread_ids = [t['thread_id'] for t in all_threads]
    
    # Fetch usage messages with pagination, including thread project info
    start_time = time.time()
    messages_result = await client.table('messages') \
        .select(
            'message_id, thread_id, created_at, content, threads!inner(project_id)'
        ) \
        .in_('thread_id', thread_ids) \
        .eq('type', 'assistant_response_end') \
        .gte('created_at', start_of_month.isoformat()) \
        .order('created_at', desc=True) \
        .range(page * items_per_page, (page + 1) * items_per_page - 1) \
        .execute()
    
    end_time = time.time()
    execution_time = end_time - start_time
    logger.info(f"Database query for usage logs took {execution_time:.3f} seconds")

    if not messages_result.data:
        return {"logs": [], "has_more": False}

    # Process messages into usage log entries
    processed_logs = []
    
    for message in messages_result.data:
        try:
            # Safely extract usage data with defaults
            content = message.get('content', {})
            usage = content.get('usage', {})
            
            # Ensure usage has required fields with safe defaults
            prompt_tokens = usage.get('prompt_tokens', 0)
            completion_tokens = usage.get('completion_tokens', 0)
            model = content.get('model', 'unknown')
            
            # Safely calculate total tokens
            total_tokens = (prompt_tokens or 0) + (completion_tokens or 0)
            
            # Calculate estimated cost using the same logic as calculate_monthly_usage
            estimated_cost = calculate_token_cost(
                prompt_tokens,
                completion_tokens,
                model
            )
            
            # Safely extract project_id from threads relationship
            project_id = 'unknown'
            if message.get('threads') and isinstance(message['threads'], list) and len(message['threads']) > 0:
                project_id = message['threads'][0].get('project_id', 'unknown')
            
            processed_logs.append({
                'message_id': message.get('message_id', 'unknown'),
                'thread_id': message.get('thread_id', 'unknown'),
                'created_at': message.get('created_at', None),
                'content': {
                    'usage': {
                        'prompt_tokens': prompt_tokens,
                        'completion_tokens': completion_tokens
                    },
                    'model': model
                },
                'total_tokens': total_tokens,
                'estimated_cost': estimated_cost,
                'project_id': project_id
            })
        except Exception as e:
            logger.warning(f"Error processing usage log entry for message {message.get('message_id', 'unknown')}: {str(e)}")
            continue
    
    # Check if there are more results
    has_more = len(processed_logs) == items_per_page
    
    return {
        "logs": processed_logs,
        "has_more": has_more
    }


def calculate_token_cost(prompt_tokens: int, completion_tokens: int, model: str) -> float:
    """Calculate the cost for tokens using the same logic as the monthly usage calculation."""
    try:
        # Ensure tokens are valid integers
        prompt_tokens = int(prompt_tokens) if prompt_tokens is not None else 0
        completion_tokens = int(completion_tokens) if completion_tokens is not None else 0
        
        # Try to resolve the model name using MODEL_NAME_ALIASES first
        resolved_model = MODEL_NAME_ALIASES.get(model, model)

        # Check if we have hardcoded pricing for this model (try both original and resolved)
        hardcoded_pricing = get_model_pricing(model) or get_model_pricing(resolved_model)
        if hardcoded_pricing:
            input_cost_per_million, output_cost_per_million = hardcoded_pricing
            input_cost = (prompt_tokens / 1_000_000) * input_cost_per_million
            output_cost = (completion_tokens / 1_000_000) * output_cost_per_million
            message_cost = input_cost + output_cost
        else:
            # Use litellm pricing as fallback - try multiple variations
            try:
                models_to_try = [model]
                
                # Add resolved model if different
                if resolved_model != model:
                    models_to_try.append(resolved_model)
                
                # Try without provider prefix if it has one
                if '/' in model:
                    models_to_try.append(model.split('/', 1)[1])
                if '/' in resolved_model and resolved_model != model:
                    models_to_try.append(resolved_model.split('/', 1)[1])
                    
                # Special handling for Google models accessed via OpenRouter
                if model.startswith('openrouter/google/'):
                    google_model_name = model.replace('openrouter/', '')
                    models_to_try.append(google_model_name)
                if resolved_model.startswith('openrouter/google/'):
                    google_model_name = resolved_model.replace('openrouter/', '')
                    models_to_try.append(google_model_name)
                
                # Try each model name variation until we find one that works
                message_cost = None
                for model_name in models_to_try:
                    try:
                        prompt_token_cost, completion_token_cost = cost_per_token(model_name, prompt_tokens, completion_tokens)
                        if prompt_token_cost is not None and completion_token_cost is not None:
                            message_cost = prompt_token_cost + completion_token_cost
                            break
                    except Exception as e:
                        logger.debug(f"Failed to get pricing for model variation {model_name}: {str(e)}")
                        continue
                
                if message_cost is None:
                    logger.warning(f"Could not get pricing for model {model} (resolved: {resolved_model}), returning 0 cost")
                    return 0.0
                    
            except Exception as e:
                logger.warning(f"Could not get pricing for model {model} (resolved: {resolved_model}): {str(e)}, returning 0 cost")
                return 0.0
        
        # Apply the TOKEN_PRICE_MULTIPLIER
        return message_cost * TOKEN_PRICE_MULTIPLIER
    except Exception as e:
        logger.error(f"Error calculating token cost for model {model}: {str(e)}")
        return 0.0

async def get_allowed_models_for_user(client, user_id: str):
    """
    Get the list of models allowed for a user based on their subscription tier.
    
    Returns:
        List of model names allowed for the user's subscription tier.
    """

    subscription = await get_user_subscription(user_id)
    tier_name = 'Basic'
    
    if subscription:
        price_id = None
        if subscription.get('items') and subscription['items'].get('data') and len(subscription['items']['data']) > 0:
            price_id = subscription['items']['data'][0]['price']['id']
        else:
            price_id = config.STRIPE_PRICE_ID_BASIC
        
        # Get tier info for this price_id
        tier_info = SUBSCRIPTION_TIERS.get(price_id)
        if tier_info:
            tier_name = tier_info['tier']
    
    # Return allowed models for this tier
    return MODEL_ACCESS_TIERS.get(tier_name, MODEL_ACCESS_TIERS['Basic'])  # Default to free tier if unknown


async def can_use_model(client, user_id: str, model_name: str):
    if config.ENV_MODE == EnvMode.LOCAL:
        logger.info("Running in local development mode - billing checks are disabled")
        return True, "Local development mode - billing disabled", {
            "price_id": "local_dev",
            "plan_name": "Local Development",
            "minutes_limit": "no limit"
        }
        
    allowed_models = await get_allowed_models_for_user(client, user_id)
    resolved_model = MODEL_NAME_ALIASES.get(model_name, model_name)
    if resolved_model in allowed_models:
        return True, "Model access allowed", allowed_models
    
    return False, f"Your current subscription plan does not include access to {model_name}. Please upgrade your subscription or choose from your available models: {', '.join(allowed_models)}", allowed_models

async def check_billing_status(client, user_id: str) -> Tuple[bool, str, Optional[Dict]]:
    """
    Check if a user can run agents based on their subscription and credit balance.
    
    Returns:
        Tuple[bool, str, Optional[Dict]]: (can_run, message, subscription_info)
    """
    if config.ENV_MODE == EnvMode.LOCAL:
        logger.info("Running in local development mode - billing checks are disabled")
        return True, "Local development mode - billing disabled", {
            "credits_available": "unlimited",
            "plan_name": "Local Development"
        }
    
    # Get current subscription
    subscription = await get_user_subscription(user_id)
    
    # Check if user has active subscription
    if not subscription or subscription.get('status') not in ['active', 'trialing']:
        return False, "No active subscription found. Please subscribe to a plan to continue.", {
            "subscription_status": "inactive",
            "available_plans": list(SUBSCRIPTION_TIERS.keys())
        }
    
    # Get user's credit balance
    credits = await get_user_credits(client, user_id)
    available_credits = credits['total_credits'] - credits['used_credits']
    
    # Check if user has any credits available
    if available_credits <= 0:
        return False, "No credits available. Credits are added monthly with your subscription.", {
            "credits_available": available_credits,
            "credits_used": credits['used_credits'],
            "total_credits": credits['total_credits'],
            "subscription_status": subscription.get('status')
        }
    
    return True, f"OK - {available_credits} credits available", {
        "credits_available": available_credits,
        "credits_used": credits['used_credits'],
        "total_credits": credits['total_credits'],
        "subscription_status": subscription.get('status'),
        "subscription_id": subscription.get('id')
    }

# 新增：积分管理函数
async def get_user_credits(client, user_id: str) -> Dict[str, float]:
    """
    获取用户的积分余额信息

    Returns:
        Dict with keys: total_credits, used_credits, available_credits
    """
    try:
        # 获取用户当前订阅的积分额度
        subscription = await get_user_subscription(user_id)
        if not subscription or subscription.get('status') not in ['active', 'trialing']:
            return {'total_credits': 0, 'used_credits': 0, 'available_credits': 0}

        # 从订阅中获取积分额度
        price_id = None
        for item in subscription.get('items', {}).get('data', []):
            if item.get('price', {}).get('id'):
                price_id = item['price']['id']
                break

        if not price_id or price_id not in SUBSCRIPTION_TIERS:
            return {'total_credits': 0, 'used_credits': 0, 'available_credits': 0}

        total_credits = SUBSCRIPTION_TIERS[price_id]['credits']

        # 计算本月已使用的积分
        now = datetime.now(timezone.utc)
        start_of_month = datetime(now.year, now.month, 1, tzinfo=timezone.utc)

        # 从工具使用记录计算已使用积分
        tool_usage = await calculate_monthly_tool_usage(client, user_id, start_of_month)

        # 从对话使用记录计算已使用积分（假设1美元=100积分）
        llm_usage = await calculate_monthly_llm_usage(client, user_id, start_of_month)
        llm_credits = llm_usage * 100  # 转换为积分

        used_credits = tool_usage + llm_credits
        available_credits = max(0, total_credits - used_credits)

        return {
            'total_credits': total_credits,
            'used_credits': used_credits,
            'available_credits': available_credits
        }

    except Exception as e:
        logger.error(f"Error getting user credits: {e}")
        return {'total_credits': 0, 'used_credits': 0, 'available_credits': 0}

async def update_user_credits(client, user_id: str, credits_to_add: float = 0, reset_this_month: bool = False) -> Dict:
    """
    更新用户积分（主要用于订阅变更时重置积分）

    Args:
        client: Supabase client
        user_id: 用户ID
        credits_to_add: 要添加的积分数量
        reset_this_month: 是否重置本月积分使用记录

    Returns:
        Dict with operation result
    """
    try:
        if reset_this_month:
            # 重置本月积分使用记录的逻辑
            # 这里可以添加清除本月使用记录的逻辑，或者标记为新的计费周期
            logger.info(f"Reset monthly credits for user {user_id}, new credits: {credits_to_add}")

        return {
            'status': 'success',
            'credits_added': credits_to_add,
            'reset_month': reset_this_month
        }

    except Exception as e:
        logger.error(f"Error updating user credits: {e}")
        return {'status': 'error', 'message': str(e)}

# 积分流水和订阅历史表的写入工具函数
async def log_credit_transaction(client, user_id: str, amount: float, type_: str, description: str = None):
    await client.schema('basejump').from_('credit_transactions').insert({
        'account_id': user_id,
        'amount': amount,
        'type': type_,
        'description': description,
        'created_at': datetime.now(timezone.utc).isoformat()
    }).execute()

async def log_subscription_history(client, user_id: str, price_id: str, action: str, description: str = None):
    await client.schema('basejump').from_('subscription_history').insert({
        'account_id': user_id,
        'price_id': price_id,
        'action': action,
        'description': description,
        'created_at': datetime.now(timezone.utc).isoformat()
    }).execute()

# API endpoints
@router.post("/create-checkout-session")
async def create_checkout_session(
    request: CreateCheckoutSessionRequest,
    current_user_id: str = Depends(get_current_user_id_from_jwt)
):
    """Create a Stripe checkout session for subscription purchase."""
    try:
        db = DBConnection()
        client = await db.client
        
        # Validate price ID is for our subscription tiers
        if request.price_id not in SUBSCRIPTION_TIERS:
            raise HTTPException(status_code=400, detail="Invalid subscription tier")
        
        # Get or create customer
        customer_id = await get_stripe_customer_id(client, current_user_id)
        if not customer_id:
            user_result = await client.auth.admin.get_user_by_id(current_user_id)
            if not user_result:
                raise HTTPException(status_code=404, detail="User not found")
            customer_id = await create_stripe_customer(client, current_user_id, user_result.user.email)
        
        # Get subscription tier info
        tier_info = SUBSCRIPTION_TIERS[request.price_id]
        
        # Create checkout session for subscription
        session = stripe.checkout.Session.create(
            customer=customer_id,
            payment_method_types=['card'],
            line_items=[{
                'price_data': {
                    'currency': 'usd',
                    'recurring': {
                        'interval': 'month'
                    },
                    'product_data': {
                        'name': f"{tier_info['name']} Subscription",
                        'description': f"Monthly subscription with {tier_info['credits']} credits for AI model usage"
                    },
                    'unit_amount': tier_info['price'],  # Price in cents
                },
                'quantity': 1,
            }],
            mode='subscription',  # Recurring subscription
            success_url=request.success_url,
            cancel_url=request.cancel_url,
            metadata={
                'user_id': current_user_id,
                'price_id': request.price_id,
                'credits_per_month': tier_info['credits']
            },
            allow_promotion_codes=True
        )
        
        return {"session_id": session.id, "url": session.url}
    except Exception as e:
        logger.error(f"Error creating checkout session: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/create-portal-session")
async def create_portal_session(
    request: CreatePortalSessionRequest,
    current_user_id: str = Depends(get_current_user_id_from_jwt)
):
    """Create a Stripe Customer Portal session for subscription management."""
    try:
        # Get Supabase client
        db = DBConnection()
        client = await db.client
        
        # Get customer ID
        customer_id = await get_stripe_customer_id(client, current_user_id)
        if not customer_id:
            raise HTTPException(status_code=404, detail="No billing customer found")
        
        # Ensure the portal configuration has subscription_update enabled
        try:
            # First, check if we have a configuration that already enables subscription update
            configurations = stripe.billing_portal.Configuration.list(limit=100)
            active_config = None
            
            # Look for a configuration with subscription_update enabled
            for config in configurations.get('data', []):
                features = config.get('features', {})
                subscription_update = features.get('subscription_update', {})
                if subscription_update.get('enabled', False):
                    active_config = config
                    logger.info(f"Found existing portal configuration with subscription_update enabled: {config['id']}")
                    break
            
            # If no config with subscription_update found, create one or update the active one
            if not active_config:
                # Find the active configuration or create a new one
                if configurations.get('data', []):
                    default_config = configurations['data'][0]
                    logger.info(f"Updating default portal configuration: {default_config['id']} to enable subscription_update")
                    
                    active_config = stripe.billing_portal.Configuration.update(
                        default_config['id'],
                        features={
                            'subscription_update': {
                                'enabled': True,
                                'proration_behavior': 'create_prorations',
                                'default_allowed_updates': ['price']
                            },
                            # Preserve other features that may already be enabled
                            'customer_update': default_config.get('features', {}).get('customer_update', {'enabled': True, 'allowed_updates': ['email', 'address']}),
                            'invoice_history': {'enabled': True},
                            'payment_method_update': {'enabled': True}
                        }
                    )
                else:
                    # Create a new configuration with subscription_update enabled
                    logger.info("Creating new portal configuration with subscription_update enabled")
                    active_config = stripe.billing_portal.Configuration.create(
                        business_profile={
                            'headline': 'Subscription Management',
                            'privacy_policy_url': config.FRONTEND_URL + '/privacy',
                            'terms_of_service_url': config.FRONTEND_URL + '/terms'
                        },
                        features={
                            'subscription_update': {
                                'enabled': True,
                                'proration_behavior': 'create_prorations',
                                'default_allowed_updates': ['price']
                            },
                            'customer_update': {
                                'enabled': True,
                                'allowed_updates': ['email', 'address']
                            },
                            'invoice_history': {'enabled': True},
                            'payment_method_update': {'enabled': True}
                        }
                    )
            
            # Log the active configuration for debugging
            logger.info(f"Using portal configuration: {active_config['id']} with subscription_update: {active_config.get('features', {}).get('subscription_update', {}).get('enabled', False)}")
        
        except Exception as config_error:
            logger.warning(f"Error configuring portal: {config_error}. Continuing with default configuration.")
        
        # Create portal session using the proper configuration if available
        portal_params = {
            "customer": customer_id,
            "return_url": request.return_url
        }
        
        # Add configuration_id if we found or created one with subscription_update enabled
        if active_config:
            portal_params["configuration"] = active_config['id']
        
        # Create the session
        session = stripe.billing_portal.Session.create(**portal_params)
        
        return {"url": session.url}
        
    except Exception as e:
        logger.error(f"Error creating portal session: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/subscription")
async def get_subscription(
    current_user_id: str = Depends(get_current_user_id_from_jwt)
):
    """Get the current subscription status for the current user, including scheduled changes."""
    try:
        # Get subscription from Stripe (this helper already handles filtering/cleanup)
        subscription = await get_user_subscription(current_user_id)
        # print("Subscription data for status:", subscription)
        
        # Calculate current usage
        db = DBConnection()
        client = await db.client
        current_usage = await calculate_monthly_usage(client, current_user_id)

        if not subscription:
            # Default to free tier status if no active subscription for our product
            free_tier_id = config.STRIPE_PRICE_ID_BASIC
            free_tier_info = SUBSCRIPTION_TIERS.get(free_tier_id)
            return SubscriptionStatus(
                status="no_subscription",
                plan_name=free_tier_info.get('name', 'Basic') if free_tier_info else 'Basic',
                price_id=free_tier_id,
                minutes_limit=free_tier_info.get('minutes') if free_tier_info else 0,
                tier=free_tier_info.get('tier') if free_tier_info else 'basic',
                price_usd=free_tier_info.get('price', 0) / 100 if free_tier_info else 0
            )
        
        # Extract current plan details
        current_item = subscription['items']['data'][0]
        current_price_id = current_item['price']['id']
        current_tier_info = SUBSCRIPTION_TIERS.get(current_price_id)
        if not current_tier_info:
            # Fallback if somehow subscribed to an unknown price within our product
             logger.warning(f"User {current_user_id} subscribed to unknown price {current_price_id}. Defaulting info.")
             current_tier_info = {'name': 'unknown', 'minutes': 0, 'tier': 'unknown', 'price': 0}
        
        # Calculate current usage
        db = DBConnection()
        client = await db.client
        current_usage = await calculate_monthly_usage(client, current_user_id)
        
        status_response = SubscriptionStatus(
            status=subscription['status'], # 'active', 'trialing', etc.
            plan_name=subscription['plan'].get('nickname') or current_tier_info['name'],
            price_id=current_price_id,
            current_period_end=datetime.fromtimestamp(current_item['current_period_end'], tz=timezone.utc),
            cancel_at_period_end=subscription['cancel_at_period_end'],
            trial_end=datetime.fromtimestamp(subscription['trial_end'], tz=timezone.utc) if subscription.get('trial_end') else None,
            minutes_limit=current_tier_info['minutes'],
            current_usage=round(current_usage, 2),
            has_schedule=False, # Default
            tier=current_tier_info.get('tier'),
            price_usd=current_tier_info.get('price', 0) / 100 if current_tier_info.get('price') else None
        )

        # Check for an attached schedule (indicates pending downgrade)
        schedule_id = subscription.get('schedule')
        if schedule_id:
            try:
                schedule = stripe.SubscriptionSchedule.retrieve(schedule_id)
                # Find the *next* phase after the current one
                next_phase = None
                current_phase_end = current_item['current_period_end']
                
                for phase in schedule.get('phases', []):
                    # Check if this phase starts exactly when the current one ends
                    if phase.get('start_date') == current_phase_end:
                        next_phase = phase
                        break # Found the immediate next phase

                if next_phase:
                    scheduled_item = next_phase['items'][0] # Assuming single item
                    scheduled_price_id = scheduled_item['price'] # Price ID might be string here
                    scheduled_tier_info = SUBSCRIPTION_TIERS.get(scheduled_price_id)
                    
                    status_response.has_schedule = True
                    status_response.status = 'scheduled_downgrade' # Override status
                    status_response.scheduled_plan_name = scheduled_tier_info.get('name', 'unknown') if scheduled_tier_info else 'unknown'
                    status_response.scheduled_price_id = scheduled_price_id
                    status_response.scheduled_change_date = datetime.fromtimestamp(next_phase['start_date'], tz=timezone.utc)
                    # 新增：计划变更后的tier和price_usd
                    status_response.tier = scheduled_tier_info.get('tier') if scheduled_tier_info else status_response.tier
                    status_response.price_usd = scheduled_tier_info.get('price', 0) / 100 if scheduled_tier_info and scheduled_tier_info.get('price') else status_response.price_usd
                    
            except Exception as schedule_error:
                logger.error(f"Error retrieving or parsing schedule {schedule_id} for sub {subscription['id']}: {schedule_error}")
                # Proceed without schedule info if retrieval fails

        return status_response
        
    except Exception as e:
        logger.exception(f"Error getting subscription status for user {current_user_id}: {str(e)}") # Use logger.exception
        raise HTTPException(status_code=500, detail="Error retrieving subscription status.")

@router.get("/check-status")
async def check_status(
    current_user_id: str = Depends(get_current_user_id_from_jwt)
):
    """Check if the user can run agents based on their subscription and usage."""
    try:
        # Get Supabase client
        db = DBConnection()
        client = await db.client
        
        can_run, message, subscription = await check_billing_status(client, current_user_id)
        
        return {
            "can_run": can_run,
            "message": message,
            "subscription": subscription
        }
        
    except Exception as e:
        logger.error(f"Error checking billing status: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/webhook")
async def stripe_webhook(request: Request):
    """Handle Stripe webhook events for subscription management."""
    try:
        webhook_secret = config.STRIPE_WEBHOOK_SECRET
        payload = await request.body()
        sig_header = request.headers.get('stripe-signature')
        try:
            event = stripe.Webhook.construct_event(
                payload, sig_header, webhook_secret
            )
        except ValueError as e:
            logger.error(f"Webhook: Invalid payload: {e}")
            raise HTTPException(status_code=400, detail="Invalid payload")
        except stripe.error.SignatureVerificationError as e:
            logger.error(f"Webhook: Invalid signature: {e}")
            raise HTTPException(status_code=400, detail="Invalid signature")

        logger.info(f"Webhook: Received event type: {event.type}")
        # 处理订阅相关事件
        if event.type in [
            'customer.subscription.created',
            'customer.subscription.updated',
            'invoice.payment_succeeded',
            'checkout.session.completed'
        ]:
            db = DBConnection()
            client = await db.client
            user_id = None
            price_id = None
            tier_info = None
            # 统一获取订阅对象
            if event.type.startswith('customer.subscription.'):
                subscription = event.data.object
                customer_id = subscription.get('customer')
                logger.info(f"Webhook: Stripe customer_id: {customer_id}")
                # 获取用户ID
                customer_result = await client.schema('basejump').from_('billing_customers') \
                    .select('account_id') \
                    .eq('id', customer_id) \
                    .execute()
                logger.info(f"Webhook: customer_result: {customer_result.data}")
                if customer_result.data:
                    user_id = customer_result.data[0]['account_id']
                if subscription.get('items') and subscription['items'].get('data'):
                    price_id = subscription['items']['data'][0]['price']['id']
                    tier_info = SUBSCRIPTION_TIERS.get(price_id)
                logger.info(f"Webhook: user_id={user_id}, price_id={price_id}, tier_info={tier_info}")
            elif event.type == 'invoice.payment_succeeded':
                invoice = event.data.object
                subscription_id = invoice.get('subscription')
                logger.info(f"Webhook: invoice.subscription_id: {subscription_id}")
                if subscription_id:
                    subscription = stripe.Subscription.retrieve(subscription_id)
                    customer_id = subscription.get('customer')
                    customer_result = await client.schema('basejump').from_('billing_customers') \
                        .select('account_id') \
                        .eq('id', customer_id) \
                        .execute()
                    logger.info(f"Webhook: customer_result: {customer_result.data}")
                    if customer_result.data:
                        user_id = customer_result.data[0]['account_id']
                    if subscription.get('items') and subscription['items'].get('data'):
                        price_id = subscription['items']['data'][0]['price']['id']
                        tier_info = SUBSCRIPTION_TIERS.get(price_id)
                    logger.info(f"Webhook: user_id={user_id}, price_id={price_id}, tier_info={tier_info}")
            elif event.type == 'checkout.session.completed':
                session = event.data.object
                customer_id = session.get('customer')
                price_id = session.get('metadata', {}).get('price_id')
                tier_info = SUBSCRIPTION_TIERS.get(price_id)
                customer_result = await client.schema('basejump').from_('billing_customers') \
                    .select('account_id') \
                    .eq('id', customer_id) \
                    .execute()
                logger.info(f"Webhook: customer_result: {customer_result.data}")
                if customer_result.data:
                    user_id = customer_result.data[0]['account_id']
                logger.info(f"Webhook: user_id={user_id}, price_id={price_id}, tier_info={tier_info}")
            # 统一处理订阅变更
            if user_id and price_id and tier_info:
                # 检查当前本地订阅计划（可扩展为本地表，或直接用 Stripe 查询）
                current = await client.schema('basejump').from_('billing_customers') \
                    .select('id, price_id') \
                    .eq('account_id', user_id) \
                    .single() \
                    .execute()
                logger.info(f"Webhook: current local price_id: {current.data['price_id'] if current and current.data and 'price_id' in current.data else None}")
                current_price_id = current.data['price_id'] if current and current.data and 'price_id' in current.data else None
                # 如果本地订阅计划与新计划不一致，更新
                if current_price_id != price_id:
                    update_result = await client.schema('basejump').from_('billing_customers').update({
                        'price_id': price_id,
                        'updated_at': datetime.now(timezone.utc).isoformat()
                    }).eq('account_id', user_id).execute()
                    logger.info(f"Webhook: Updated user {user_id} subscription plan to {price_id}, update_result: {update_result}")
                    sub_log_result = await log_subscription_history(client, user_id, price_id, action="plan_changed", description=f"订阅变更为 {price_id}")
                    logger.info(f"Webhook: log_subscription_history result: {sub_log_result}")
                # 更新用户当月剩余积分为新套餐额度
                credit_log_result = await update_user_credits(client, user_id, credits_to_add=tier_info['credits'], reset_this_month=True)
                logger.info(f"Webhook: Reset user {user_id} credits to {tier_info['credits']} for new subscription plan {price_id}, credit_log_result: {credit_log_result}")
                credit_tx_result = await log_credit_transaction(client, user_id, amount=tier_info['credits'], type_="reset", description=f"订阅变更重置积分 {tier_info['credits']}")
                logger.info(f"Webhook: log_credit_transaction result: {credit_tx_result}")
        return {"status": "success"}
    except Exception as e:
        logger.error(f"Error processing webhook: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/available-models")
async def get_available_models(
    current_user_id: str = Depends(get_current_user_id_from_jwt)
):
    """Get the list of models available to the user based on their subscription tier."""
    try:
        # Get Supabase client
        db = DBConnection()
        client = await db.client
        
        # Check if we're in local development mode
        if config.ENV_MODE == EnvMode.LOCAL:
            logger.info("Running in local development mode - billing checks are disabled")
            
            # In local mode, return all models from MODEL_NAME_ALIASES
            model_info = []
            for short_name, full_name in MODEL_NAME_ALIASES.items():
                # Skip entries where the key is a full name to avoid duplicates
                # if short_name == full_name or '/' in short_name:
                #     continue
                
                model_info.append({
                    "id": full_name,
                    "display_name": short_name,
                    "short_name": short_name,
                    "requires_subscription": False  # Always false in local dev mode
                })
            
            return {
                "models": model_info,
                "subscription_tier": "Local Development",
                "total_models": len(model_info)
            }
        
        # For non-local mode, get list of allowed models for this user
        allowed_models = await get_allowed_models_for_user(client, current_user_id)
        free_tier_models = MODEL_ACCESS_TIERS.get('Basic', [])
        
        # Get subscription info for context
        subscription = await get_user_subscription(current_user_id)
        
        # Determine tier name from subscription
        tier_name = 'Basic'
        if subscription:
            price_id = None
            if subscription.get('items') and subscription['items'].get('data') and len(subscription['items']['data']) > 0:
                price_id = subscription['items']['data'][0]['price']['id']
            else:
                price_id = config.STRIPE_PRICE_ID_BASIC
            
            # Get tier info for this price_id
            tier_info = SUBSCRIPTION_TIERS.get(price_id)
            if tier_info:
                tier_name = tier_info['name']
        
        # Get all unique full model names from MODEL_NAME_ALIASES
        all_models = set()
        model_aliases = {}
        
        for short_name, full_name in MODEL_NAME_ALIASES.items():
            # Add all unique full model names
            all_models.add(full_name)
            
            # Only include short names that don't match their full names for aliases
            if short_name != full_name and not short_name.startswith("openai/") and not short_name.startswith("anthropic/") and not short_name.startswith("openrouter/") and not short_name.startswith("xai/"):
                if full_name not in model_aliases:
                    model_aliases[full_name] = short_name
        
        # Create model info with display names for ALL models
        model_info = []
        for model in all_models:
            display_name = model_aliases.get(model, model.split('/')[-1] if '/' in model else model)
            
            # Check if model requires subscription (not in free tier)
            requires_sub = model not in free_tier_models
            
            # Check if model is available with current subscription
            is_available = model in allowed_models
            
            # Get pricing information - check hardcoded prices first, then litellm
            pricing_info = {}
            
            # Check if we have hardcoded pricing for this model
            hardcoded_pricing = get_model_pricing(model)
            if hardcoded_pricing:
                input_cost_per_million, output_cost_per_million = hardcoded_pricing
                pricing_info = {
                    "input_cost_per_million_tokens": input_cost_per_million * TOKEN_PRICE_MULTIPLIER,
                    "output_cost_per_million_tokens": output_cost_per_million * TOKEN_PRICE_MULTIPLIER,
                    "max_tokens": None
                }
            else:
                try:
                    # Try to get pricing using cost_per_token function
                    models_to_try = []
                    
                    # Add the original model name
                    models_to_try.append(model)
                    
                    # Try to resolve the model name using MODEL_NAME_ALIASES
                    if model in MODEL_NAME_ALIASES:
                        resolved_model = MODEL_NAME_ALIASES[model]
                        models_to_try.append(resolved_model)
                        # Also try without provider prefix if it has one
                        if '/' in resolved_model:
                            models_to_try.append(resolved_model.split('/', 1)[1])
                    
                    # If model is a value in aliases, try to find a matching key
                    for alias_key, alias_value in MODEL_NAME_ALIASES.items():
                        if alias_value == model:
                            models_to_try.append(alias_key)
                            break
                    
                    # Also try without provider prefix for the original model
                    if '/' in model:
                        models_to_try.append(model.split('/', 1)[1])
                    
                    # Special handling for Google models accessed via OpenRouter
                    if model.startswith('openrouter/google/'):
                        google_model_name = model.replace('openrouter/', '')
                        models_to_try.append(google_model_name)
                    
                    # Try each model name variation until we find one that works
                    input_cost_per_token = None
                    output_cost_per_token = None
                    
                    for model_name in models_to_try:
                        try:
                            # Use cost_per_token with sample token counts to get the per-token costs
                            input_cost, output_cost = cost_per_token(model_name, 1000000, 1000000)
                            if input_cost is not None and output_cost is not None:
                                input_cost_per_token = input_cost
                                output_cost_per_token = output_cost
                                break
                        except Exception:
                            continue
                    
                    if input_cost_per_token is not None and output_cost_per_token is not None:
                        pricing_info = {
                            "input_cost_per_million_tokens": input_cost_per_token * TOKEN_PRICE_MULTIPLIER,
                            "output_cost_per_million_tokens": output_cost_per_token * TOKEN_PRICE_MULTIPLIER,
                            "max_tokens": None  # cost_per_token doesn't provide max_tokens info
                        }
                    else:
                        pricing_info = {
                            "input_cost_per_million_tokens": None,
                            "output_cost_per_million_tokens": None,
                            "max_tokens": None
                        }
                except Exception as e:
                    logger.warning(f"Could not get pricing for model {model}: {str(e)}")
                    pricing_info = {
                        "input_cost_per_million_tokens": None,
                        "output_cost_per_million_tokens": None,
                        "max_tokens": None
                    }

            model_info.append({
                "id": model,
                "display_name": display_name,
                "short_name": model_aliases.get(model),
                "requires_subscription": requires_sub,
                "is_available": is_available,
                **pricing_info
            })
        
        return {
            "models": model_info,
            "subscription_tier": tier_name,
            "total_models": len(model_info)
        }
        
    except Exception as e:
        logger.error(f"Error getting available models: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error getting available models: {str(e)}")

@router.get("/subscription-plans")
async def get_subscription_plans(
    current_user_id: str = Depends(get_current_user_id_from_jwt)
):
    """Get available subscription plans with credit amounts."""
    try:
        plans = []
        for price_id, tier_info in SUBSCRIPTION_TIERS.items():
            plans.append({
                "tier": tier_info['tier'],
                "price_id": price_id,
                "name": tier_info['name'],
                "credits_per_month": tier_info['credits'],
                "price_usd": tier_info['price'] / 100,  # Convert from cents
                "description": f"{tier_info['credits']} credits per month"
            })
        
        return {
            "plans": plans,
            "total_plans": len(plans)
        }
    except Exception as e:
        logger.error(f"Error getting subscription plans: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/credit-usage-stats", response_model=CreditUsageStats)
async def get_credit_usage_stats(current_user_id: str = Depends(get_current_user_id_from_jwt)):
    """统计今日、本周、本月积分消耗（基于 agent_runs 表的积分消耗字段或运行时长推算）"""
    try:
        db = DBConnection()
        client = await db.client
        # 获取当前用户所有 thread_id
        threads_result = await client.table('threads') \
            .select('thread_id') \
            .eq('account_id', current_user_id) \
            .execute()
        if not threads_result.data:
            return CreditUsageStats(today=0, this_week=0, this_month=0)
        thread_ids = [t['thread_id'] for t in threads_result.data]
        now = datetime.now(timezone.utc)
        start_of_today = datetime(now.year, now.month, now.day, tzinfo=timezone.utc)
        start_of_week = start_of_today - timedelta(days=start_of_today.weekday())
        start_of_month = datetime(now.year, now.month, 1, tzinfo=timezone.utc)
        # 查询 agent_runs 表，假设有 credits_used 字段，否则用运行时长推算
        runs_result = await client.table('agent_runs') \
            .select('started_at, completed_at') \
            .in_('thread_id', thread_ids) \
            .gte('started_at', start_of_month.isoformat()) \
            .execute()
        today_credits = 0
        week_credits = 0
        month_credits = 0
        now_ts = now.timestamp()
        for run in runs_result.data or []:
            try:
                started_at = datetime.fromisoformat(run['started_at'].replace('Z', '+00:00'))
                credits = run.get('credits_used', 0)
                # 如果没有 credits_used 字段，则用运行时长推算（假设1分钟1积分）
                if credits is None or credits == 0:
                    if run.get('completed_at'):
                        end_time = datetime.fromisoformat(run['completed_at'].replace('Z', '+00:00')).timestamp()
                    else:
                        end_time = now_ts
                    credits = (end_time - started_at.timestamp()) / 60  # 1分钟1积分
                # 确保 credits 是数字类型
                credits = float(credits) if credits is not None else 0
                if started_at >= start_of_today:
                    today_credits += credits
                if started_at >= start_of_week:
                    week_credits += credits
                if started_at >= start_of_month:
                    month_credits += credits
            except (ValueError, TypeError, KeyError) as e:
                logger.warning(f"Error processing run record: {e}, skipping...")
                continue
        return CreditUsageStats(today=round(today_credits, 2), this_week=round(week_credits, 2), this_month=round(month_credits, 2))
    except Exception as e:
        logger.error(f"Error getting credit usage stats: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error getting credit usage stats: {str(e)}")

@router.get("/transactions", response_model=TransactionHistory)
async def get_transactions(current_user_id: str = Depends(get_current_user_id_from_jwt), limit: int = Query(20, ge=1, le=100)):
    """返回最近的积分交易和订阅历史记录"""
    try:
        db = DBConnection()
        client = await db.client
        # 查询积分流水
        credit_result = await client.schema('basejump').from_('credit_transactions') \
            .select('amount, type, description, created_at') \
            .eq('account_id', current_user_id) \
            .order('created_at', desc=True) \
            .limit(limit) \
            .execute()
        credit_records = [
            TransactionRecord(
                type=row['type'],
                amount=row['amount'],
                created_at=datetime.fromisoformat(row['created_at'].replace('Z', '+00:00')),
                description=row.get('description', "")
            ) for row in (credit_result.data or [])
        ]
        # 查询订阅历史
        sub_result = await client.schema('basejump').from_('subscription_history') \
            .select('price_id, action, description, created_at') \
            .eq('account_id', current_user_id) \
            .order('created_at', desc=True) \
            .limit(limit) \
            .execute()
        sub_records = [
            TransactionRecord(
                type=row['action'],
                amount=0,
                created_at=datetime.fromisoformat(row['created_at'].replace('Z', '+00:00')),
                description=row.get('description', f"订阅计划变更为 {row['price_id']}")
            ) for row in (sub_result.data or [])
        ]
        all_records = credit_records + sub_records
        all_records.sort(key=lambda x: x.created_at, reverse=True)
        return TransactionHistory(transactions=all_records[:limit])
    except Exception as e:
        logger.error(f"Error getting transactions: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error getting transactions: {str(e)}")