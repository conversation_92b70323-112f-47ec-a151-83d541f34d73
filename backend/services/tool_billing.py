"""
MediaAgent工具计费系统核心模块
提供统一的工具成本计算、跟踪和管理功能，包含管理员接口
"""

from fastapi import APIRouter, HTTPException, Depends, Query
from enum import Enum
from typing import Dict, Any, Optional, Union, List, Tuple
from decimal import Decimal
from datetime import datetime, timezone, timedelta
from abc import ABC, abstractmethod
import time
import json
from functools import wraps
from dataclasses import dataclass, asdict
from pydantic import BaseModel

from utils.logger import logger
from utils.config import config, EnvMode
from services.supabase import DBConnection
from utils.auth_utils import get_current_user_id_from_jwt, verify_admin_user


class CostModel(Enum):
    """工具成本模型类型"""
    FIXED = "fixed"              # 固定费用
    PER_CALL = "per_call"        # 按调用次数
    PER_TOKEN = "per_token"      # 按token计费（LLM模型）
    PER_SECOND = "per_second"    # 按时间计费
    PER_ITEM = "per_item"        # 按处理项目数量
    PER_MB = "per_mb"            # 按数据量计费
    CUSTOM = "custom"            # 自定义计费逻辑


class CostUnit(Enum):
    """成本单位"""
    USD_CENTS = "usd_cents"      # 美分
    USD_DOLLARS = "usd_dollars"  # 美元
    CREDITS = "credits"          # 积分


@dataclass
class CostCalculationResult:
    """成本计算结果"""
    amount: Decimal
    unit: CostUnit = CostUnit.CREDITS  # 改为积分模式
    breakdown: Dict[str, Any] = None
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.breakdown is None:
            self.breakdown = {}
        if self.metadata is None:
            self.metadata = {}
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "amount": float(self.amount),
            "unit": self.unit.value,
            "breakdown": self.breakdown,
            "metadata": self.metadata
        }
    
    def to_usd_dollars(self) -> float:
        """转换为美元"""
        if self.unit == CostUnit.USD_CENTS:
            return float(self.amount) / 100
        elif self.unit == CostUnit.USD_DOLLARS:
            return float(self.amount)
        else:
            # 积分转换需要配置
            return float(self.amount) * 0.01  # 假设1积分=1美分


@dataclass 
class ToolCostConfig:
    """工具成本配置"""
    model: CostModel
    base_cost: Decimal = Decimal('0')
    unit_cost: Decimal = Decimal('0')
    minimum_cost: Decimal = Decimal('0')
    maximum_cost: Optional[Decimal] = None
    free_tier_limit: Optional[int] = None
    custom_params: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.custom_params is None:
            self.custom_params = {}


class ToolBillingCalculator:
    """工具计费计算器"""
    
    def __init__(self):
        self.token_price_multiplier = 1.5  # 与现有系统保持一致
        
    def calculate_cost(self, 
                      config: ToolCostConfig,
                      execution_result: Dict[str, Any] = None, 
                      execution_time: float = 0,
                      execution_count: int = 1,
                      **kwargs) -> CostCalculationResult:
        """
        计算工具使用成本
        
        Args:
            config: 工具成本配置
            execution_result: 工具执行结果
            execution_time: 执行时间（秒）
            execution_count: 执行次数
            **kwargs: 其他参数
            
        Returns:
            成本计算结果
        """
        if execution_result is None:
            execution_result = {}
            
        try:
            if config.model == CostModel.FIXED:
                return self._calculate_fixed_cost(config)
            elif config.model == CostModel.PER_CALL:
                return self._calculate_per_call_cost(config, execution_count)
            elif config.model == CostModel.PER_TOKEN:
                return self._calculate_token_cost(config, execution_result, **kwargs)
            elif config.model == CostModel.PER_SECOND:
                return self._calculate_time_cost(config, execution_time)
            elif config.model == CostModel.PER_ITEM:
                return self._calculate_item_cost(config, execution_result)
            elif config.model == CostModel.PER_MB:
                return self._calculate_data_cost(config, execution_result)
            elif config.model == CostModel.CUSTOM:
                return self._calculate_custom_cost(config, execution_result, execution_time, **kwargs)
            else:
                logger.warning(f"Unknown cost model: {config.model}")
                return CostCalculationResult(Decimal('0'))
        except Exception as e:
            logger.error(f"Error calculating tool cost: {str(e)}")
            return CostCalculationResult(Decimal('0'))
    
    def _calculate_fixed_cost(self, config: ToolCostConfig) -> CostCalculationResult:
        """计算固定成本"""
        return CostCalculationResult(
            amount=config.base_cost,
            breakdown={"type": "fixed", "base_cost": float(config.base_cost)}
        )
    
    def _calculate_per_call_cost(self, config: ToolCostConfig, execution_count: int) -> CostCalculationResult:
        """计算按调用次数的成本"""
        # 检查免费额度
        if config.free_tier_limit and execution_count <= config.free_tier_limit:
            return CostCalculationResult(
                amount=Decimal('0'),
                breakdown={
                    "type": "per_call", 
                    "free_tier": True, 
                    "remaining_free": config.free_tier_limit - execution_count
                }
            )
        
        # 计算超出免费额度的调用成本
        paid_calls = max(0, execution_count - (config.free_tier_limit or 0))
        cost = config.base_cost + (config.unit_cost * paid_calls)
        
        return CostCalculationResult(
            amount=max(cost, config.minimum_cost),
            breakdown={
                "type": "per_call",
                "base_cost": float(config.base_cost), 
                "unit_cost": float(config.unit_cost),
                "paid_calls": paid_calls,
                "free_calls": min(execution_count, config.free_tier_limit or 0)
            }
        )
    
    def _calculate_token_cost(self, config: ToolCostConfig, execution_result: Dict[str, Any], **kwargs) -> CostCalculationResult:
        """计算token成本（集成现有LiteLLM逻辑）"""
        from services.billing import calculate_token_cost
        
        prompt_tokens = kwargs.get('prompt_tokens', 0)
        completion_tokens = kwargs.get('completion_tokens', 0)
        model = kwargs.get('model', 'gpt-3.5-turbo')
        
        # 使用现有的token成本计算逻辑
        token_cost = calculate_token_cost(prompt_tokens, completion_tokens, model)
        
        return CostCalculationResult(
            amount=Decimal(str(token_cost)),
            breakdown={
                "type": "per_token",
                "model": model,
                "prompt_tokens": prompt_tokens,
                "completion_tokens": completion_tokens,
                "token_cost": token_cost
            }
        )
    
    def _calculate_time_cost(self, config: ToolCostConfig, execution_time: float) -> CostCalculationResult:
        """计算按时间的成本"""
        time_units = Decimal(str(execution_time))  # 秒
        cost = config.base_cost + (time_units * config.unit_cost)
        
        if config.maximum_cost:
            cost = min(cost, config.maximum_cost)
        
        return CostCalculationResult(
            amount=max(cost, config.minimum_cost),
            breakdown={
                "type": "per_second",
                "execution_time": execution_time,
                "base_cost": float(config.base_cost),
                "unit_cost": float(config.unit_cost)
            }
        )
    
    def _calculate_item_cost(self, config: ToolCostConfig, execution_result: Dict[str, Any]) -> CostCalculationResult:
        """计算按项目数量的成本"""
        item_count = execution_result.get('processed_items', 1)
        cost = config.base_cost + (Decimal(str(item_count)) * config.unit_cost)
        
        return CostCalculationResult(
            amount=max(cost, config.minimum_cost),
            breakdown={
                "type": "per_item",
                "item_count": item_count,
                "base_cost": float(config.base_cost),
                "unit_cost": float(config.unit_cost)
            }
        )
    
    def _calculate_data_cost(self, config: ToolCostConfig, execution_result: Dict[str, Any]) -> CostCalculationResult:
        """计算按数据量的成本"""
        data_size_mb = execution_result.get('data_size_mb', 0)
        cost = config.base_cost + (Decimal(str(data_size_mb)) * config.unit_cost)
        
        return CostCalculationResult(
            amount=max(cost, config.minimum_cost),
            breakdown={
                "type": "per_mb",
                "data_size_mb": data_size_mb,
                "base_cost": float(config.base_cost),
                "unit_cost": float(config.unit_cost)
            }
        )
    
    def _calculate_custom_cost(self, config: ToolCostConfig, execution_result: Dict[str, Any], execution_time: float, **kwargs) -> CostCalculationResult:
        """
        自定义成本计算逻辑的默认实现
        工具类可以重写此方法实现特定的成本计算
        """
        return CostCalculationResult(config.base_cost)


class ToolUsageTracker:
    """工具使用跟踪器"""
    
    def __init__(self):
        self.calculator = ToolBillingCalculator()
    
    async def record_tool_usage(self, 
                               user_id: str, 
                               tool_name: str, 
                               method_name: str,
                               project_id: str,
                               cost_result: CostCalculationResult,
                               execution_time: float = 0,
                               execution_result: Dict[str, Any] = None) -> None:
        """记录工具使用到数据库"""
        if config.ENV_MODE == EnvMode.LOCAL:
            logger.info("Local development mode - tool usage recording disabled")
            return
        
        try:
            db = DBConnection()
            client = await db.client
            
            # 记录到tool_usage表
            usage_record = {
                "user_id": user_id,
                "tool_name": tool_name,
                "method_name": method_name,
                "project_id": project_id,
                "cost_amount": float(cost_result.amount),
                "cost_unit": cost_result.unit.value,
                "cost_breakdown": cost_result.breakdown,
                "execution_time": execution_time,
                "execution_result": execution_result or {},
                "created_at": datetime.now(timezone.utc).isoformat()
            }
            
            await client.from_('tool_usage').insert(usage_record).execute()
            
            # 更新用户当月使用量统计
            await self._update_monthly_usage(client, user_id, tool_name, method_name, cost_result)
            
            logger.info(f"Recorded tool usage: {tool_name}.{method_name} for user {user_id}, cost: {cost_result.amount}")
            
        except Exception as e:
            logger.error(f"Error recording tool usage: {str(e)}")
    
    async def _update_monthly_usage(self, client, user_id: str, tool_name: str, method_name: str, cost_result: CostCalculationResult):
        """更新用户当月工具使用统计"""
        try:
            now = datetime.now(timezone.utc)
            month_year = now.strftime("%Y-%m")
            
            # 尝试更新现有记录
            result = await client.from_('user_tool_stats').upsert({
                "user_id": user_id,
                "tool_name": tool_name,
                "method_name": method_name,
                "month_year": month_year,
                "usage_count": 1,
                "total_cost": float(cost_result.amount),
                "updated_at": now.isoformat()
            }, on_conflict="user_id,tool_name,method_name,month_year").execute()
            
            if not result.data:
                # 如果upsert失败，尝试手动更新
                existing = await client.from_('user_tool_stats') \
                    .select('*') \
                    .eq('user_id', user_id) \
                    .eq('tool_name', tool_name) \
                    .eq('method_name', method_name) \
                    .eq('month_year', month_year) \
                    .execute()
                
                if existing.data:
                    # 更新现有记录
                    current_stats = existing.data[0]
                    await client.from_('user_tool_stats').update({
                        "usage_count": current_stats['usage_count'] + 1,
                        "total_cost": current_stats['total_cost'] + float(cost_result.amount),
                        "updated_at": now.isoformat()
                    }).eq('id', current_stats['id']).execute()
                else:
                    # 创建新记录
                    await client.from_('user_tool_stats').insert({
                        "user_id": user_id,
                        "tool_name": tool_name,
                        "method_name": method_name,
                        "month_year": month_year,
                        "usage_count": 1,
                        "total_cost": float(cost_result.amount),
                        "created_at": now.isoformat(),
                        "updated_at": now.isoformat()
                    }).execute()
            
        except Exception as e:
            logger.error(f"Error updating monthly usage stats: {str(e)}")


# =============================================================================
# 统一的方法级别工具成本配置
# 配置格式: "ToolClassName.method_name": ToolCostConfig(...)
# =============================================================================

TOOL_COST_CONFIGS = {
    # ===== Web搜索工具 ===== (积分模式)
    "SandboxWebSearchTool.web_search": ToolCostConfig(
        model=CostModel.PER_CALL,
        base_cost=Decimal('1'),      # 1积分
        unit_cost=Decimal('0.5'),    # 0.5积分
        free_tier_limit=100
    ),

    "SandboxWebSearchTool.scrape_webpage": ToolCostConfig(
        model=CostModel.PER_CALL,
        base_cost=Decimal('2'),      # 2积分
        unit_cost=Decimal('1'),      # 1积分
        free_tier_limit=50
    ),

    # ===== 创作工具 ===== (积分模式)
    "SandboxArticleAdapterTool.generate_article": ToolCostConfig(
        model=CostModel.FIXED,
        base_cost=Decimal('50'),     # 50积分
        minimum_cost=Decimal('50')
    ),

    "SandboxStoryAdapterTool.create_story_workflow": ToolCostConfig(
        model=CostModel.FIXED,
        base_cost=Decimal('75'),     # 75积分
        minimum_cost=Decimal('75')
    ),

    "SandboxReportAdapterTool.create_report_workflow": ToolCostConfig(
        model=CostModel.FIXED,
        base_cost=Decimal('100'),    # 100积分
        minimum_cost=Decimal('100')
    ),

    # ===== 图像工具 ===== (积分模式)
    "SandboxImageGenerateTool.image_generate": ToolCostConfig(
        model=CostModel.PER_CALL,
        base_cost=Decimal('10'),     # 10积分
        unit_cost=Decimal('5'),      # 5积分
        minimum_cost=Decimal('10'),
        free_tier_limit=5
    ),

    # ===== Shell工具 ===== (积分模式)
    "SandboxShellTool.run_shell_command": ToolCostConfig(
        model=CostModel.PER_CALL,
        base_cost=Decimal('0.1'),    # 0.1积分
        unit_cost=Decimal('0.05'),   # 0.05积分
        free_tier_limit=500
    ),
    
    # ===== 文件工具 ===== (积分模式)
    "SandboxFilesTool.create_file": ToolCostConfig(
        model=CostModel.PER_CALL,
        base_cost=Decimal('0.1'),    # 0.1积分
        unit_cost=Decimal('0.05'),   # 0.05积分
        free_tier_limit=1000
    ),

    "SandboxFilesTool.str_replace": ToolCostConfig(
        model=CostModel.PER_CALL,
        base_cost=Decimal('0.12'),   # 0.12积分
        unit_cost=Decimal('0.06'),   # 0.06积分
        free_tier_limit=800
    ),

    "SandboxFilesTool.full_file_rewrite": ToolCostConfig(
        model=CostModel.PER_CALL,
        base_cost=Decimal('0.15'),   # 0.15积分
        unit_cost=Decimal('0.08'),   # 0.08积分
        free_tier_limit=500
    ),

    "SandboxFilesTool.delete_file": ToolCostConfig(
        model=CostModel.PER_CALL,
        base_cost=Decimal('0.05'),   # 0.05积分
        unit_cost=Decimal('0.02'),   # 0.02积分
        free_tier_limit=1500
    ),

    # ===== 浏览器工具 ===== (积分模式)
    "SandboxBrowserTool.browser_navigate_to": ToolCostConfig(
        model=CostModel.PER_CALL,
        base_cost=Decimal('2'),      # 2积分
        unit_cost=Decimal('1'),      # 1积分
        free_tier_limit=50
    ),

    "SandboxBrowserTool.browser_click_element": ToolCostConfig(
        model=CostModel.PER_CALL,
        base_cost=Decimal('1'),      # 1积分
        unit_cost=Decimal('0.5'),    # 0.5积分
        free_tier_limit=100
    ),

    "SandboxBrowserTool.browser_input_text": ToolCostConfig(
        model=CostModel.PER_CALL,
        base_cost=Decimal('0.5'),    # 0.5积分
        unit_cost=Decimal('0.2'),    # 0.2积分
        free_tier_limit=200
    ),

    "SandboxBrowserTool.browser_scroll_down": ToolCostConfig(
        model=CostModel.PER_CALL,
        base_cost=Decimal('0.2'),    # 0.2积分
        unit_cost=Decimal('0.1'),    # 0.1积分
        free_tier_limit=500
    ),

    "SandboxBrowserTool.browser_scroll_up": ToolCostConfig(
        model=CostModel.PER_CALL,
        base_cost=Decimal('0.2'),    # 0.2积分
        unit_cost=Decimal('0.1'),    # 0.1积分
        free_tier_limit=500
    ),

    # ===== 视觉工具 ===== (积分模式)
    "SandboxVisionTool.see_image": ToolCostConfig(
        model=CostModel.PER_CALL,
        base_cost=Decimal('5'),      # 5积分
        unit_cost=Decimal('2'),      # 2积分
        free_tier_limit=20
    ),

    # ===== 部署工具 ===== (积分模式)
    "SandboxDeployTool.deploy": ToolCostConfig(
        model=CostModel.PER_SECOND,
        base_cost=Decimal('10'),     # 10积分
        unit_cost=Decimal('0.2'),    # 0.2积分/秒
        minimum_cost=Decimal('20')   # 最少20积分
    ),

    # ===== 暴露工具 ===== (积分模式)
    "SandboxExposeTool.expose_port": ToolCostConfig(
        model=CostModel.PER_SECOND,
        base_cost=Decimal('5'),      # 5积分
        unit_cost=Decimal('0.1'),    # 0.1积分/秒
        minimum_cost=Decimal('10')   # 最少10积分
    ),

    # ===== 内容分析工具 ===== (积分模式)
    "SandboxContentAnalyzerTool.analyze_url_content": ToolCostConfig(
        model=CostModel.PER_ITEM,
        base_cost=Decimal('5'),      # 5积分
        unit_cost=Decimal('2'),      # 2积分/项目
        minimum_cost=Decimal('5'),   # 最少5积分
        free_tier_limit=50
    ),

    # ===== 数据提供商工具 ===== (积分模式)
    "DataProvidersTool.get_data_provider_endpoints": ToolCostConfig(
        model=CostModel.PER_CALL,
        base_cost=Decimal('1'),      # 1积分
        unit_cost=Decimal('0.5'),    # 0.5积分
        free_tier_limit=100
    ),

    "DataProvidersTool.execute_data_provider_call": ToolCostConfig(
        model=CostModel.PER_CALL,
        base_cost=Decimal('3'),      # 3积分
        unit_cost=Decimal('1'),      # 1积分
        free_tier_limit=30
    ),

    # ===== 计算机使用工具 ===== (积分模式)
    "ComputerUseTool.click": ToolCostConfig(
        model=CostModel.PER_CALL,
        base_cost=Decimal('3'),      # 3积分
        unit_cost=Decimal('1'),      # 1积分
        minimum_cost=Decimal('3'),   # 最少3积分
        free_tier_limit=50
    ),
    
    "ComputerUseTool.typing": ToolCostConfig(
        model=CostModel.PER_CALL,
        base_cost=Decimal('2'),      # 2积分
        unit_cost=Decimal('0.5'),    # 0.5积分
        minimum_cost=Decimal('2'),   # 最少2积分
        free_tier_limit=100
    ),

    "ComputerUseTool.move_to": ToolCostConfig(
        model=CostModel.PER_CALL,
        base_cost=Decimal('1'),      # 1积分
        unit_cost=Decimal('0.2'),    # 0.2积分
        minimum_cost=Decimal('1'),   # 最少1积分
        free_tier_limit=200
    ),

    "ComputerUseTool.scroll": ToolCostConfig(
        model=CostModel.PER_CALL,
        base_cost=Decimal('1'),      # 1积分
        unit_cost=Decimal('0.2'),    # 0.2积分
        minimum_cost=Decimal('1'),   # 最少1积分
        free_tier_limit=200
    ),

    # ===== 消息工具（免费） =====
    "SandboxMessageTool.send_message": ToolCostConfig(
        model=CostModel.FIXED,
        base_cost=Decimal('0')       # 0积分（免费）
    )
}


def _smart_match_tool_config(identifier: str) -> Optional[ToolCostConfig]:
    """
    智能匹配工具配置，支持多种匹配策略
    
    Args:
        identifier: 工具标识符（可能是tool_name, method_name, 或其他形式）
        
    Returns:
        匹配到的配置或None
    """
    # 直接匹配
    if identifier in TOOL_COST_CONFIGS:
        return TOOL_COST_CONFIGS[identifier]
    
    # 按方法名匹配
    for config_key, config in TOOL_COST_CONFIGS.items():
        if '.' in config_key:
            _, method = config_key.split('.', 1)
            if method == identifier:
                return config
    
    # 模糊匹配（包含关系）
    for config_key, config in TOOL_COST_CONFIGS.items():
        if identifier.lower() in config_key.lower() or config_key.lower() in identifier.lower():
            return config
    
    # 常见别名映射
    alias_mapping = {
        'web_search': 'SandboxWebSearchTool.web_search',
        'scrape_webpage': 'SandboxWebSearchTool.scrape_webpage',
        'create_file': 'SandboxFilesTool.create_file',
        'str_replace': 'SandboxFilesTool.str_replace',
        'delete_file': 'SandboxFilesTool.delete_file',
        'image_generate': 'SandboxImageGenerateTool.image_generate',
        'see_image': 'SandboxVisionTool.see_image',
        'run_shell_command': 'SandboxShellTool.run_shell_command',
        'browser_navigate_to': 'SandboxBrowserTool.browser_navigate_to',
        'browser_click_element': 'SandboxBrowserTool.browser_click_element',
        'send_message': 'SandboxMessageTool.send_message'
    }
    
    if identifier in alias_mapping:
        mapped_key = alias_mapping[identifier]
        if mapped_key in TOOL_COST_CONFIGS:
            return TOOL_COST_CONFIGS[mapped_key]
    
    return None

def get_tool_cost_config(tool_name: str, method_name: Optional[str] = None) -> ToolCostConfig:
    """
    获取工具的成本配置（支持多种匹配方式）
    
    Args:
        tool_name: 工具类名或方法名
        method_name: 方法名（可选）
        
    Returns:
        对应的成本配置，如果没有找到则返回免费配置
    """
    # 1. 精确匹配：ToolClass.method_name
    if method_name:
        exact_key = f"{tool_name}.{method_name}"
        config = _smart_match_tool_config(exact_key)
        if config:
            return config
    
    # 2. 尝试匹配tool_name（可能是方法名）
    config = _smart_match_tool_config(tool_name)
    if config:
        return config
    
    # 3. 如果有method_name，尝试匹配method_name
    if method_name:
        config = _smart_match_tool_config(method_name)
        if config:
            return config
    
    # 4. 如果都没有找到配置，返回免费配置
    logger.warning(f"No cost config found for tool_name='{tool_name}', method_name='{method_name}', using free config")
    return ToolCostConfig(
        model=CostModel.FIXED,
        base_cost=Decimal('0')
    )


# =============================================================================
# 管理员API接口 (原tool_admin.py内容)
# =============================================================================

# 初始化路由
router = APIRouter(prefix="/admin/tools", tags=["tool-admin"])

# Pydantic模型
class ToolConfigUpdate(BaseModel):
    cost_model: Optional[str] = None
    base_cost: Optional[float] = None
    unit_cost: Optional[float] = None
    minimum_cost: Optional[float] = None
    maximum_cost: Optional[float] = None
    free_tier_limit: Optional[int] = None
    custom_params: Optional[Dict[str, Any]] = None
    is_active: Optional[bool] = None

class ToolPermissionUpdate(BaseModel):
    subscription_tier: str
    tool_name: str
    is_allowed: bool
    usage_limit: Optional[int] = None
    cost_multiplier: Optional[float] = None

class ToolAnalyticsFilter(BaseModel):
    start_date: Optional[str] = None
    end_date: Optional[str] = None
    tool_names: Optional[List[str]] = None
    user_ids: Optional[List[str]] = None


@router.get("/configurations")
async def get_tool_configurations(
    tool_name: Optional[str] = None,
    is_active: Optional[bool] = None,
    admin_user: str = Depends(verify_admin_user)
):
    """获取工具配置列表"""
    try:
        db = DBConnection()
        client = await db.client
        
        query = client.from_('tool_configurations').select('*')
        
        if tool_name:
            query = query.eq('tool_name', tool_name)
        if is_active is not None:
            query = query.eq('is_active', is_active)
        
        result = await query.execute()
        
        return {
            "configurations": result.data or [],
            "total": len(result.data) if result.data else 0
        }
        
    except Exception as e:
        logger.error(f"Error getting tool configurations: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.put("/configurations/{tool_name}")
async def update_tool_configuration(
    tool_name: str,
    config_update: ToolConfigUpdate,
    admin_user: str = Depends(verify_admin_user)
):
    """更新工具配置"""
    try:
        db = DBConnection()
        client = await db.client
        
        # 准备更新数据
        update_data = {}
        if config_update.cost_model is not None:
            update_data['cost_model'] = config_update.cost_model
        if config_update.base_cost is not None:
            update_data['base_cost'] = config_update.base_cost
        if config_update.unit_cost is not None:
            update_data['unit_cost'] = config_update.unit_cost
        if config_update.minimum_cost is not None:
            update_data['minimum_cost'] = config_update.minimum_cost
        if config_update.maximum_cost is not None:
            update_data['maximum_cost'] = config_update.maximum_cost
        if config_update.free_tier_limit is not None:
            update_data['free_tier_limit'] = config_update.free_tier_limit
        if config_update.custom_params is not None:
            update_data['custom_params'] = config_update.custom_params
        if config_update.is_active is not None:
            update_data['is_active'] = config_update.is_active
        
        if not update_data:
            raise HTTPException(status_code=400, detail="No valid update fields provided")
        
        # 执行更新
        result = await client.from_('tool_configurations') \
            .update(update_data) \
            .eq('tool_name', tool_name) \
            .execute()
        
        if not result.data:
            raise HTTPException(status_code=404, detail="Tool configuration not found")
        
        logger.info(f"Updated tool configuration for {tool_name} by admin {admin_user}")
        
        return {
            "message": f"Tool configuration for {tool_name} updated successfully",
            "updated_config": result.data[0]
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating tool configuration: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/permissions")
async def get_tool_permissions(
    subscription_tier: Optional[str] = None,
    tool_name: Optional[str] = None,
    admin_user: str = Depends(verify_admin_user)
):
    """获取工具权限配置"""
    try:
        db = DBConnection()
        client = await db.client
        
        query = client.from_('tool_subscription_permissions').select('*')
        
        if subscription_tier:
            query = query.eq('subscription_tier', subscription_tier)
        if tool_name:
            query = query.eq('tool_name', tool_name)
        
        result = await query.execute()
        
        return {
            "permissions": result.data or [],
            "total": len(result.data) if result.data else 0
        }
        
    except Exception as e:
        logger.error(f"Error getting tool permissions: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.put("/permissions")
async def update_tool_permission(
    permission_update: ToolPermissionUpdate,
    admin_user: str = Depends(verify_admin_user)
):
    """更新工具权限"""
    try:
        db = DBConnection()
        client = await db.client
        
        # 准备数据
        permission_data = {
            "subscription_tier": permission_update.subscription_tier,
            "tool_name": permission_update.tool_name,
            "is_allowed": permission_update.is_allowed,
            "usage_limit": permission_update.usage_limit,
            "cost_multiplier": permission_update.cost_multiplier or 1.0
        }
        
        # 使用upsert更新或创建
        result = await client.from_('tool_subscription_permissions') \
            .upsert(permission_data, on_conflict="subscription_tier,tool_name") \
            .execute()
        
        logger.info(f"Updated tool permission for {permission_update.tool_name} in tier {permission_update.subscription_tier} by admin {admin_user}")
        
        return {
            "message": f"Tool permission updated successfully",
            "updated_permission": result.data[0] if result.data else permission_data
        }
        
    except Exception as e:
        logger.error(f"Error updating tool permission: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/analytics/usage")
async def get_tool_usage_analytics(
    start_date: Optional[str] = Query(None, description="Start date (YYYY-MM-DD)"),
    end_date: Optional[str] = Query(None, description="End date (YYYY-MM-DD)"),
    tool_name: Optional[str] = None,
    group_by: str = Query("day", enum=["day", "week", "month"]),
    admin_user: str = Depends(verify_admin_user)
):
    """获取工具使用分析数据"""
    try:
        db = DBConnection()
        client = await db.client
        
        # 设置默认日期范围（最近30天）
        if not start_date:
            start_date = (datetime.now(timezone.utc) - timedelta(days=30)).strftime("%Y-%m-%d")
        if not end_date:
            end_date = datetime.now(timezone.utc).strftime("%Y-%m-%d")
        
        # 构建查询
        query = f"""
        SELECT 
            DATE_TRUNC('{group_by}', created_at) as period,
            tool_name,
            COUNT(*) as usage_count,
            SUM(cost_amount) as total_cost,
            AVG(cost_amount) as avg_cost,
            COUNT(DISTINCT user_id) as unique_users,
            AVG(execution_time) as avg_execution_time
        FROM tool_usage 
        WHERE created_at >= '{start_date}' 
        AND created_at <= '{end_date}'
        """
        
        if tool_name:
            query += f" AND tool_name = '{tool_name}'"
        
        query += f" GROUP BY DATE_TRUNC('{group_by}', created_at), tool_name ORDER BY period DESC, total_cost DESC"
        
        result = await client.rpc('execute_sql', {'query': query}).execute()
        
        # 计算汇总统计
        total_usage = sum(row['usage_count'] for row in result.data) if result.data else 0
        total_cost = sum(row['total_cost'] for row in result.data) if result.data else 0
        unique_tools = len(set(row['tool_name'] for row in result.data)) if result.data else 0
        
        return {
            "analytics": result.data or [],
            "summary": {
                "total_usage": total_usage,
                "total_cost": total_cost,
                "unique_tools": unique_tools,
                "date_range": {"start": start_date, "end": end_date},
                "group_by": group_by
            }
        }
        
    except Exception as e:
        logger.error(f"Error getting tool usage analytics: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/health")
async def get_tool_system_health(admin_user: str = Depends(verify_admin_user)):
    """获取工具系统健康状态"""
    try:
        db = DBConnection()
        client = await db.client
        
        # 检查表是否存在
        tables_check = await client.rpc('execute_sql', {
            'query': """
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = 'public' 
            AND table_name IN ('tool_usage', 'tool_configurations', 'user_tool_stats', 'tool_subscription_permissions')
            """
        }).execute()
        
        existing_tables = [row['table_name'] for row in tables_check.data] if tables_check.data else []
        
        # 检查最近24小时的使用情况
        recent_usage = await client.from_('tool_usage') \
            .select('tool_name', count='exact') \
            .gte('created_at', (datetime.now(timezone.utc) - timedelta(hours=24)).isoformat()) \
            .execute()
        
        # 检查配置数量
        config_count = await client.from_('tool_configurations') \
            .select('tool_name', count='exact') \
            .execute()
        
        return {
            "status": "healthy" if len(existing_tables) == 4 else "degraded",
            "database": {
                "tables_exist": existing_tables,
                "missing_tables": [t for t in ['tool_usage', 'tool_configurations', 'user_tool_stats', 'tool_subscription_permissions'] if t not in existing_tables]
            },
            "usage": {
                "recent_24h": recent_usage.count if hasattr(recent_usage, 'count') else 0
            },
            "configuration": {
                "tool_configs": config_count.count if hasattr(config_count, 'count') else 0
            },
            "billing_system": {
                "available": True,
                "env_mode": config.ENV_MODE.value if config.ENV_MODE else "unknown"
            },
            "checked_at": datetime.now(timezone.utc).isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error checking tool system health: {str(e)}")
        return {
            "status": "error",
            "error": str(e),
            "checked_at": datetime.now(timezone.utc).isoformat()
        }


def cost_tracking_decorator(cost_config: Optional[ToolCostConfig] = None):
    """
    成本跟踪装饰器
    
    Args:
        cost_config: 可选的成本配置，如果不提供会自动根据方法名推断
    """
    def decorator(func):
        @wraps(func)
        async def wrapper(self, *args, **kwargs):
            start_time = time.time()
            
            # 如果在本地开发模式，跳过成本跟踪
            if config.ENV_MODE == EnvMode.LOCAL:
                return await func(self, *args, **kwargs)
            
            try:
                # 执行原函数
                result = await func(self, *args, **kwargs)
                execution_time = time.time() - start_time
                
                # 获取成本配置
                actual_config = cost_config
                if not actual_config:
                    tool_name = self.__class__.__name__
                    actual_config = get_tool_cost_config(tool_name)
                
                # 计算成本
                calculator = ToolBillingCalculator()
                cost_result = calculator.calculate_cost(
                    config=actual_config,
                    execution_result=result if isinstance(result, dict) else {},
                    execution_time=execution_time
                )
                
                # 记录使用情况（需要从上下文获取用户ID和项目ID）
                if hasattr(self, 'project_id'):
                    # 这里需要获取用户ID，可能需要从线程管理器或其他地方获取
                    # 暂时跳过实际记录，只记录日志
                    logger.info(f"Tool {self.__class__.__name__} cost: {cost_result.amount} cents")
                
                return result
                
            except Exception as e:
                execution_time = time.time() - start_time
                logger.error(f"Tool execution failed: {str(e)}")
                raise e
        
        return wrapper
    return decorator 