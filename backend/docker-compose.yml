services:
  api:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "8000:8000"
    env_file:
      - .env
    volumes:
      - .:/app
      - ./logs:/app/logs
    restart: unless-stopped
    depends_on:
      redis:
        condition: service_healthy
      rabbitmq:
        condition: service_healthy
    networks:
      - app-network
    environment:
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=
      - LOG_LEVEL=INFO
      - RABBITMQ_HOST=rabbitmq
      - RABBITMQ_PORT=5672
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    deploy:
      resources:
        limits:
          cpus: "14"
          memory: 48G
        reservations:
          cpus: "8"
          memory: 32G
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  worker-1:
    build:
      context: .
      dockerfile: Dockerfile
    command: python -m dramatiq --processes 16 --threads 16 run_agent_background
    env_file:
      - .env
    volumes:
      - .:/app
      - ./worker-1-logs:/app/logs
    restart: unless-stopped
    depends_on:
      redis:
        condition: service_healthy
      rabbitmq:
        condition: service_healthy
    networks:
      - app-network
    environment:
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=
      - LOG_LEVEL=INFO
      - RABBITMQ_HOST=rabbitmq
      - RABBITMQ_PORT=5672
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    deploy:
      resources:
        limits:
          cpus: "14"
          memory: 48G
        reservations:
          cpus: "8"
          memory: 32G

  worker-2:
    build:
      context: .
      dockerfile: Dockerfile
    command: python -m dramatiq run_agent_background
    env_file:
      - .env
    volumes:
      - .:/app
      - ./worker-2-logs:/app/logs
    restart: unless-stopped
    depends_on:
      redis:
        condition: service_healthy
      rabbitmq:
        condition: service_healthy
    networks:
      - app-network
    environment:
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=
      - LOG_LEVEL=INFO
      - RABBITMQ_HOST=rabbitmq
      - RABBITMQ_PORT=5672
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    deploy:
      resources:
        limits:
          cpus: "14"
          memory: 48G
        reservations:
          cpus: "8"
          memory: 32G

  redis:
    image: redis:7-alpine
    ports:
      - "127.0.0.1:6379:6379"
    volumes:
      - redis_data:/data
      - ./services/docker/redis.conf:/usr/local/etc/redis/redis.conf:ro
    restart: unless-stopped
    networks:
      - app-network
    command: redis-server /usr/local/etc/redis/redis.conf --appendonly yes --bind 0.0.0.0 --protected-mode no --maxmemory 8gb --maxmemory-policy allkeys-lru
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 10s
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    deploy:
      resources:
        limits:
          cpus: "2"
          memory: 12G
        reservations:
          cpus: "1"
          memory: 8G

  rabbitmq:
    image: rabbitmq
    ports:
      - "127.0.0.1:5672:5672"
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq
    restart: unless-stopped
    networks:
      - app-network
    healthcheck:
      test: ["CMD", "rabbitmq-diagnostics", "-q", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 10s
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    deploy:
      resources:
        limits:
          cpus: "2"
          memory: 12G
        reservations:
          cpus: "1"
          memory: 8G

networks:
  app-network:
    driver: bridge

volumes:
  redis_data:
  rabbitmq_data:
