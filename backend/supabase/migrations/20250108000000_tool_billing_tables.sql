-- 工具计费表迁移SQL (PostgreSQL版本)
-- 请在Supabase Dashboard的SQL编辑器中执行此文件
-- Dashboard地址: https://supabase.com/dashboard/project/cgpgenuezcbygcrrarni/sql

-- 工具使用记录表（积分计费模式）
CREATE TABLE IF NOT EXISTS tool_usage (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL,
    tool_name VARCHAR(100) NOT NULL,
    method_name VARCHAR(100) NOT NULL,
    project_id UUID NOT NULL,
    cost_amount DECIMAL(10,6) NOT NULL DEFAULT 0,  -- 积分数量
    cost_unit VARCHAR(20) NOT NULL DEFAULT 'credits',  -- 积分单位
    cost_breakdown JSONB DEFAULT '{}',
    execution_time DECIMAL(10,3) DEFAULT 0,
    execution_result JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 工具配置表（积分计费模式）
CREATE TABLE IF NOT EXISTS tool_configurations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tool_name VARCHAR(100) UNIQUE NOT NULL,
    cost_model VARCHAR(20) NOT NULL DEFAULT 'fixed',
    base_cost DECIMAL(10,6) DEFAULT 0,      -- 基础积分成本
    unit_cost DECIMAL(10,6) DEFAULT 0,      -- 单位积分成本
    minimum_cost DECIMAL(10,6) DEFAULT 0,   -- 最小积分成本
    maximum_cost DECIMAL(10,6),             -- 最大积分成本
    free_tier_limit INTEGER,                -- 免费层限制
    custom_params JSONB DEFAULT '{}',
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 用户工具使用统计表
CREATE TABLE IF NOT EXISTS user_tool_stats (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL,
    tool_name VARCHAR(100) NOT NULL,
    method_name VARCHAR(100) NOT NULL,
    month_year VARCHAR(7) NOT NULL,
    usage_count INTEGER DEFAULT 0,
    total_cost DECIMAL(10,6) DEFAULT 0,
    total_execution_time DECIMAL(10,3) DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- 唯一约束
    UNIQUE(user_id, tool_name, method_name, month_year)
);

-- 工具订阅权限表
CREATE TABLE IF NOT EXISTS tool_subscription_permissions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    subscription_tier VARCHAR(50) NOT NULL,
    tool_name VARCHAR(100) NOT NULL,
    is_allowed BOOLEAN DEFAULT false,
    usage_limit INTEGER,
    cost_multiplier DECIMAL(3,2) DEFAULT 1.0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- 唯一约束
    UNIQUE(subscription_tier, tool_name)
);

-- 创建索引 (PostgreSQL语法)
CREATE INDEX IF NOT EXISTS idx_tool_usage_user_id ON tool_usage (user_id);
CREATE INDEX IF NOT EXISTS idx_tool_usage_tool_name ON tool_usage (tool_name);
CREATE INDEX IF NOT EXISTS idx_tool_usage_method_name ON tool_usage (method_name);
CREATE INDEX IF NOT EXISTS idx_tool_usage_tool_method ON tool_usage (tool_name, method_name);
CREATE INDEX IF NOT EXISTS idx_tool_usage_project_id ON tool_usage (project_id);
CREATE INDEX IF NOT EXISTS idx_tool_usage_created_at ON tool_usage (created_at);
-- 移除有问题的函数索引，改为简单的时间索引
-- CREATE INDEX IF NOT EXISTS idx_tool_usage_user_tool_method_month ON tool_usage (user_id, tool_name, method_name, date_trunc('month', created_at));
CREATE INDEX IF NOT EXISTS idx_tool_usage_user_tool_method_time ON tool_usage (user_id, tool_name, method_name, created_at);

CREATE INDEX IF NOT EXISTS idx_tool_configs_tool_name ON tool_configurations (tool_name);
CREATE INDEX IF NOT EXISTS idx_tool_configs_active ON tool_configurations (is_active);

CREATE INDEX IF NOT EXISTS idx_user_tool_stats_user_month ON user_tool_stats (user_id, month_year);
CREATE INDEX IF NOT EXISTS idx_user_tool_stats_tool_method_month ON user_tool_stats (tool_name, method_name, month_year);
CREATE INDEX IF NOT EXISTS idx_user_tool_stats_updated_at ON user_tool_stats (updated_at);

CREATE INDEX IF NOT EXISTS idx_tool_permissions_tier ON tool_subscription_permissions (subscription_tier);
CREATE INDEX IF NOT EXISTS idx_tool_permissions_tool ON tool_subscription_permissions (tool_name);

-- 创建或更新时间戳触发器函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 为需要的表添加更新时间戳触发器
CREATE TRIGGER update_tool_configurations_updated_at
    BEFORE UPDATE ON tool_configurations
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_tool_stats_updated_at
    BEFORE UPDATE ON user_tool_stats
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 插入默认工具配置数据
INSERT INTO tool_configurations (tool_name, cost_model, base_cost, unit_cost, minimum_cost, maximum_cost, free_tier_limit, custom_params) 
VALUES 
    -- 基础工具
    ('web_search', 'per_call', 1, 0.5, 0, NULL, 100, '{}'),
    ('shell_tool', 'per_call', 0.1, 0.05, 0, NULL, 500, '{}'),
    ('files_tool', 'per_call', 0.1, 0.05, 0, NULL, 1000, '{}'),
    ('browser_tool', 'per_call', 2, 1, 0, NULL, 50, '{}'),
    
    -- 视觉和图像工具
    ('vision_tool', 'per_call', 5, 2, 0, NULL, 20, '{}'),
    ('image_generate', 'per_call', 10, 5, 10, NULL, 5, '{}'),
    
    -- 创作工具
    ('article_tool', 'fixed', 50, 0, 50, NULL, NULL, '{}'),
    ('story_tool', 'fixed', 75, 0, 75, NULL, NULL, '{}'),
    ('report_tool', 'fixed', 100, 0, 100, NULL, NULL, '{}'),
    ('content_analyzer', 'per_item', 5, 2, 5, NULL, NULL, '{}'),
    
    -- 部署工具
    ('deploy_tool', 'per_second', 5, 0.1, 10, NULL, NULL, '{}'),
    ('expose_tool', 'per_second', 2, 0.05, 5, NULL, NULL, '{}'),
    
    -- 其他工具
    ('data_providers', 'per_call', 3, 1, 0, NULL, 30, '{}'),
    ('message_tool', 'fixed', 0, 0, 0, NULL, NULL, '{}'),

    -- 具体方法级别的工具配置（匹配代码中的TOOL_COST_CONFIGS）
    ('SandboxWebSearchTool.web_search', 'per_call', 1, 0.5, 0, NULL, 100, '{}'),
    ('SandboxWebSearchTool.scrape_webpage', 'per_call', 2, 1, 0, NULL, 50, '{}'),
    ('SandboxShellTool.run_shell_command', 'per_call', 0.1, 0.05, 0, NULL, 500, '{}'),
    ('SandboxFilesTool.create_file', 'per_call', 0.1, 0.05, 0, NULL, 1000, '{}'),
    ('SandboxFilesTool.str_replace', 'per_call', 0.12, 0.06, 0, NULL, 800, '{}'),
    ('SandboxFilesTool.full_file_rewrite', 'per_call', 0.15, 0.08, 0, NULL, 500, '{}'),
    ('SandboxFilesTool.delete_file', 'per_call', 0.05, 0.02, 0, NULL, 1500, '{}'),
    ('SandboxBrowserTool.browser_navigate_to', 'per_call', 2, 1, 0, NULL, 50, '{}'),
    ('SandboxBrowserTool.browser_click_element', 'per_call', 1, 0.5, 0, NULL, 100, '{}'),
    ('SandboxBrowserTool.browser_input_text', 'per_call', 0.5, 0.2, 0, NULL, 200, '{}'),
    ('SandboxBrowserTool.browser_scroll_down', 'per_call', 0.2, 0.1, 0, NULL, 500, '{}'),
    ('SandboxBrowserTool.browser_scroll_up', 'per_call', 0.2, 0.1, 0, NULL, 500, '{}'),
    ('SandboxVisionTool.see_image', 'per_call', 5, 2, 0, NULL, 20, '{}'),
    ('SandboxImageGenerateTool.image_generate', 'per_call', 10, 5, 10, NULL, 5, '{}'),
    ('SandboxDeployTool.deploy', 'per_second', 10, 0.2, 20, NULL, NULL, '{}'),
    ('SandboxExposeTool.expose_port', 'per_second', 5, 0.1, 10, NULL, NULL, '{}'),
    ('DataProvidersTool.get_data_provider_endpoints', 'per_call', 1, 0.5, 0, NULL, 100, '{}'),
    ('DataProvidersTool.execute_data_provider_call', 'per_call', 3, 1, 0, NULL, 30, '{}'),
    ('ComputerUseTool.click', 'per_call', 3, 1, 3, NULL, 50, '{}'),
    ('ComputerUseTool.typing', 'per_call', 2, 0.5, 2, NULL, 100, '{}'),
    ('ComputerUseTool.move_to', 'per_call', 1, 0.2, 1, NULL, 200, '{}'),
    ('ComputerUseTool.scroll', 'per_call', 1, 0.2, 1, NULL, 200, '{}'),

    -- 创作工具（具体方法）
    ('SandboxArticleAdapterTool.generate_article', 'fixed', 50, 0, 50, NULL, NULL, '{}'),
    ('SandboxStoryAdapterTool.create_story_workflow', 'fixed', 75, 0, 75, NULL, NULL, '{}'),
    ('SandboxReportAdapterTool.create_report_workflow', 'fixed', 100, 0, 100, NULL, NULL, '{}'),

    -- 内容分析工具
    ('SandboxContentAnalyzerTool.analyze_url_content', 'per_item', 5, 2, 5, NULL, 50, '{}'),

    -- 消息工具（免费）
    ('SandboxMessageTool.send_message', 'fixed', 0, 0, 0, NULL, NULL, '{}')
ON CONFLICT (tool_name) DO NOTHING;

-- 插入默认订阅层级工具权限（新的三种等级：basic, plus, pro）
INSERT INTO tool_subscription_permissions (subscription_tier, tool_name, is_allowed, usage_limit, cost_multiplier)
VALUES
    -- Basic层级（基础版）
    ('basic', 'web_search', true, 200, 1.0),
    ('basic', 'shell_tool', true, 800, 1.0),
    ('basic', 'files_tool', true, 1500, 1.0),
    ('basic', 'browser_tool', true, 100, 1.0),
    ('basic', 'vision_tool', true, 40, 1.0),
    ('basic', 'image_generate', true, 10, 1.0),
    ('basic', 'data_providers', true, 50, 1.0),
    ('basic', 'message_tool', true, NULL, 1.0),
    -- Basic层级允许少量创作工具使用
    ('basic', 'article_tool', true, 5, 1.0),
    ('basic', 'story_tool', true, 3, 1.0),
    ('basic', 'report_tool', true, 2, 1.0),
    ('basic', 'content_analyzer', true, 20, 1.0),
    ('basic', 'deploy_tool', true, NULL, 1.0),
    ('basic', 'expose_tool', true, NULL, 1.0),

    -- Plus层级（增强版）
    ('plus', 'web_search', true, 500, 0.9),
    ('plus', 'shell_tool', true, 2000, 0.9),
    ('plus', 'files_tool', true, 4000, 0.9),
    ('plus', 'browser_tool', true, 250, 0.9),
    ('plus', 'vision_tool', true, 100, 0.9),
    ('plus', 'image_generate', true, 25, 0.9),
    ('plus', 'article_tool', true, 15, 0.9),
    ('plus', 'story_tool', true, 12, 0.9),
    ('plus', 'report_tool', true, 8, 0.9),
    ('plus', 'content_analyzer', true, 100, 0.9),
    ('plus', 'deploy_tool', true, NULL, 0.9),
    ('plus', 'expose_tool', true, NULL, 0.9),
    ('plus', 'data_providers', true, 150, 0.9),
    ('plus', 'message_tool', true, NULL, 0.9),

    -- Pro层级（专业版）
    ('pro', 'web_search', true, NULL, 0.8),  -- 无限制
    ('pro', 'shell_tool', true, NULL, 0.8),
    ('pro', 'files_tool', true, NULL, 0.8),
    ('pro', 'browser_tool', true, NULL, 0.8),
    ('pro', 'vision_tool', true, NULL, 0.8),
    ('pro', 'image_generate', true, NULL, 0.8),
    ('pro', 'article_tool', true, NULL, 0.8),
    ('pro', 'story_tool', true, NULL, 0.8),
    ('pro', 'report_tool', true, NULL, 0.8),
    ('pro', 'content_analyzer', true, NULL, 0.8),
    ('pro', 'deploy_tool', true, NULL, 0.8),
    ('pro', 'expose_tool', true, NULL, 0.8),
    ('pro', 'data_providers', true, NULL, 0.8),
    ('pro', 'message_tool', true, NULL, 0.8)
ON CONFLICT (subscription_tier, tool_name) DO NOTHING;

-- 添加RLS (Row Level Security) 策略
ALTER TABLE tool_usage ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_tool_stats ENABLE ROW LEVEL SECURITY;

-- 用户只能查看自己的工具使用记录
CREATE POLICY "Users can view own tool usage" ON tool_usage
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can view own tool stats" ON user_tool_stats
    FOR SELECT USING (auth.uid() = user_id);

-- 系统可以插入工具使用记录
CREATE POLICY "System can insert tool usage" ON tool_usage
    FOR INSERT WITH CHECK (true);

CREATE POLICY "System can insert/update tool stats" ON user_tool_stats
    FOR ALL WITH CHECK (true);

-- 工具配置表对所有用户只读
CREATE POLICY "Tool configurations are readable by all" ON tool_configurations
    FOR SELECT USING (true);

-- 工具权限表对所有用户只读
CREATE POLICY "Tool permissions are readable by all" ON tool_subscription_permissions
    FOR SELECT USING (true);
