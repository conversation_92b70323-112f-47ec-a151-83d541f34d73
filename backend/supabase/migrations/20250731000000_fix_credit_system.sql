-- Fix credit system tables with proper UUID generation
-- This migration fixes the uuid_generate_v4() issue

-- Enable pgcrypto extension for gen_random_uuid() if not already enabled
CREATE EXTENSION IF NOT EXISTS pgcrypto;

-- Create user_credits table (if not exists)
CREATE TABLE IF NOT EXISTS public.user_credits (
    user_id UUID PRIMARY KEY REFERENCES auth.users(id),
    total_credits INTEGER NOT NULL DEFAULT 10,
    used_credits INTEGER NOT NULL DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL
);

-- Create credit_purchases table (if not exists)
CREATE TABLE IF NOT EXISTS public.credit_purchases (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES auth.users(id),
    credits_purchased INTEGER NOT NULL,
    amount_paid DECIMAL(10,2) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
    payment_id TEXT,
    status TEXT DEFAULT 'completed'
);

-- Create credit_usage table (if not exists)
CREATE TABLE IF NOT EXISTS public.credit_usage (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES auth.users(id),
    model_name TEXT NOT NULL,
    credits_used INTEGER NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL
);

-- Add indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_credit_purchases_user_id ON public.credit_purchases(user_id);
CREATE INDEX IF NOT EXISTS idx_credit_usage_user_id ON public.credit_usage(user_id);
CREATE INDEX IF NOT EXISTS idx_credit_usage_created_at ON public.credit_usage(created_at);

-- Add RLS policies
ALTER TABLE public.user_credits ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.credit_purchases ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.credit_usage ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist (to avoid conflicts)
DROP POLICY IF EXISTS user_credits_select ON public.user_credits;
DROP POLICY IF EXISTS user_credits_insert ON public.user_credits;
DROP POLICY IF EXISTS user_credits_update ON public.user_credits;
DROP POLICY IF EXISTS credit_purchases_select ON public.credit_purchases;
DROP POLICY IF EXISTS credit_purchases_insert ON public.credit_purchases;
DROP POLICY IF EXISTS credit_usage_select ON public.credit_usage;
DROP POLICY IF EXISTS credit_usage_insert ON public.credit_usage;

-- Users can only read their own credit information
CREATE POLICY user_credits_select ON public.user_credits
    FOR SELECT USING (auth.uid() = user_id);

-- Only the application can insert/update credit information
CREATE POLICY user_credits_insert ON public.user_credits
    FOR INSERT WITH CHECK (auth.role() = 'service_role');
CREATE POLICY user_credits_update ON public.user_credits
    FOR UPDATE USING (auth.role() = 'service_role');

-- Users can only view their own purchase history
CREATE POLICY credit_purchases_select ON public.credit_purchases
    FOR SELECT USING (auth.uid() = user_id);

-- Only the application can insert purchase records
CREATE POLICY credit_purchases_insert ON public.credit_purchases
    FOR INSERT WITH CHECK (auth.role() = 'service_role');

-- Users can only view their own usage history
CREATE POLICY credit_usage_select ON public.credit_usage
    FOR SELECT USING (auth.uid() = user_id);

-- Only the application can insert usage records
CREATE POLICY credit_usage_insert ON public.credit_usage
    FOR INSERT WITH CHECK (auth.role() = 'service_role');
