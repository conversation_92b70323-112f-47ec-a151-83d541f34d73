"""
Compatibility Layer - MediaAgent V2 兼容性层

提供新旧系统之间的兼容性桥梁，确保平滑迁移和向后兼容。
"""

import asyncio
import logging
from typing import Dict, Any, List, Optional, Union, Callable
import time
import json
from dataclasses import asdict

from ..core.models import UnifiedState, AgentResponse, TaskType, AgentStatus
from ..core.engine import MediaAgentV2Engine
from ..agents.enhanced_article_adapter import EnhancedArticleAdapter

# 导入现有系统组件
try:
    from ...agent.api import APIHandler as V1APIHandler
    from ...agent.run import AgentRunner as V1AgentRunner
    V1_AVAILABLE = True
except ImportError:
    V1_AVAILABLE = False
    logging.warning("V1 system not available")

logger = logging.getLogger(__name__)


class CompatibilityLayer:
    """
    兼容性层
    
    职责：
    - 提供统一的API接口
    - 处理格式转换
    - 管理系统路由
    - 保证向后兼容性
    """
    
    def __init__(self, config: Dict[str, Any] = None):
        self.config = config or {}
        
        # 兼容性配置
        self.compatibility_config = {
            "enable_v1_compatibility": True,     # 启用V1兼容性
            "enable_v2_features": True,          # 启用V2新功能
            "default_system": "v2",              # 默认使用的系统
            "auto_migration": True,              # 自动迁移
            "preserve_v1_behavior": False,       # 保持V1行为
            "enable_format_conversion": True     # 启用格式转换
        }
        
        # 初始化V2引擎
        self.v2_engine = MediaAgentV2Engine(config.get("v2_config", {}))
        self.enhanced_adapter = EnhancedArticleAdapter(config.get("adapter_config", {}))
        
        # V1系统组件
        self.v1_api_handler = None
        self.v1_agent_runner = None
        
        if V1_AVAILABLE and self.compatibility_config["enable_v1_compatibility"]:
            try:
                self.v1_api_handler = V1APIHandler()
                self.v1_agent_runner = V1AgentRunner()
                logger.info("V1 system components initialized")
            except Exception as e:
                logger.warning(f"Failed to initialize V1 components: {e}")
                V1_AVAILABLE = False
        
        # 兼容性统计
        self.compatibility_stats = {
            "v1_requests": 0,
            "v2_requests": 0,
            "conversion_requests": 0,
            "migration_requests": 0,
            "compatibility_issues": 0
        }
        
        logger.info("Compatibility Layer initialized")
    
    async def initialize(self):
        """初始化兼容性层"""
        
        logger.info("Initializing Compatibility Layer...")
        
        # 初始化V2引擎
        await self.v2_engine.initialize()
        
        logger.info("Compatibility Layer initialized successfully")
    
    async def handle_request(self, request: Dict[str, Any], system_hint: str = None) -> Dict[str, Any]:
        """统一的请求处理入口"""
        
        try:
            # 检测请求格式和目标系统
            detection_result = await self._detect_request_system(request, system_hint)
            
            # 更新统计
            if detection_result["target_system"] == "v1":
                self.compatibility_stats["v1_requests"] += 1
            else:
                self.compatibility_stats["v2_requests"] += 1
            
            # 路由到相应系统
            if detection_result["target_system"] == "v1" and detection_result["use_v1"]:
                return await self._handle_v1_request(request, detection_result)
            else:
                return await self._handle_v2_request(request, detection_result)
                
        except Exception as e:
            logger.error(f"Request handling failed: {e}")
            self.compatibility_stats["compatibility_issues"] += 1
            
            return {
                "success": False,
                "error": str(e),
                "compatibility_info": {
                    "error_type": "request_handling_failed",
                    "system_attempted": detection_result.get("target_system", "unknown")
                }
            }
    
    async def _detect_request_system(self, request: Dict[str, Any], system_hint: str = None) -> Dict[str, Any]:
        """检测请求的目标系统"""
        
        detection = {
            "target_system": "v2",  # 默认使用V2
            "use_v1": False,
            "requires_conversion": False,
            "confidence": 0.8
        }
        
        # 系统提示优先
        if system_hint:
            if system_hint.lower() in ["v1", "legacy", "old"]:
                detection["target_system"] = "v1"
                detection["use_v1"] = V1_AVAILABLE
            elif system_hint.lower() in ["v2", "new", "enhanced"]:
                detection["target_system"] = "v2"
        
        # 基于请求格式检测
        else:
            # V1格式特征检测
            v1_indicators = [
                "workflow_id" in request,
                "thread_id" in request,
                "tool_choice" in request,
                "stream" in request and isinstance(request["stream"], bool)
            ]
            
            # V2格式特征检测
            v2_indicators = [
                "session_id" in request,
                "task_type" in request,
                "unified_state" in request,
                "agent_config" in request
            ]
            
            v1_score = sum(v1_indicators)
            v2_score = sum(v2_indicators)
            
            if v1_score > v2_score and V1_AVAILABLE:
                detection["target_system"] = "v1"
                detection["use_v1"] = True
                detection["confidence"] = 0.7
            else:
                detection["target_system"] = "v2"
                detection["requires_conversion"] = v1_score > 0  # 需要格式转换
        
        # 兼容性策略检查
        if not self.compatibility_config["enable_v1_compatibility"]:
            detection["target_system"] = "v2"
            detection["use_v1"] = False
        
        if not self.compatibility_config["enable_v2_features"]:
            detection["target_system"] = "v1"
            detection["use_v1"] = V1_AVAILABLE
        
        return detection
    
    async def _handle_v1_request(self, request: Dict[str, Any], detection: Dict[str, Any]) -> Dict[str, Any]:
        """处理V1格式请求"""
        
        logger.info("Processing V1 format request")
        
        if not V1_AVAILABLE or not self.v1_api_handler:
            # 转换到V2处理
            logger.info("V1 system not available, converting to V2")
            return await self._convert_and_handle_as_v2(request)
        
        try:
            # 使用V1系统处理
            if self.compatibility_config["preserve_v1_behavior"]:
                # 保持原始V1行为
                result = await self._execute_v1_original(request)
            else:
                # 使用增强适配器处理V1请求
                result = await self._execute_v1_enhanced(request)
            
            # 转换响应格式
            if self.compatibility_config["enable_format_conversion"]:
                result = await self._convert_v1_response_format(result)
            
            return result
            
        except Exception as e:
            logger.error(f"V1 request handling failed: {e}")
            
            # Fallback到V2
            logger.info("Falling back to V2 system")
            return await self._convert_and_handle_as_v2(request)
    
    async def _handle_v2_request(self, request: Dict[str, Any], detection: Dict[str, Any]) -> Dict[str, Any]:
        """处理V2格式请求"""
        
        logger.info("Processing V2 format request")
        
        try:
            # 转换请求格式（如果需要）
            if detection.get("requires_conversion"):
                request = await self._convert_v1_to_v2_format(request)
                self.compatibility_stats["conversion_requests"] += 1
            
            # 使用V2引擎处理
            response = await self.v2_engine.process_request(request)
            
            # 转换为标准响应格式
            result = await self._convert_v2_response_format(response)
            
            return result
            
        except Exception as e:
            logger.error(f"V2 request handling failed: {e}")
            raise
    
    async def _convert_and_handle_as_v2(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """转换V1请求并用V2处理"""
        
        self.compatibility_stats["migration_requests"] += 1
        
        # 转换请求格式
        v2_request = await self._convert_v1_to_v2_format(request)
        
        # 用V2处理
        response = await self.v2_engine.process_request(v2_request)
        
        # 转换响应格式
        result = await self._convert_v2_response_format(response)
        
        # 添加迁移信息
        result["compatibility_info"] = {
            "migrated_from_v1": True,
            "original_format": "v1",
            "processed_by": "v2_engine"
        }
        
        return result
    
    async def _execute_v1_original(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """执行原始V1处理"""
        
        # 这里需要调用原始的V1 API处理逻辑
        # 由于原始代码可能很复杂，这里提供一个简化实现
        
        try:
            # 模拟V1处理
            result = {
                "success": True,
                "content": "V1 system processed content",
                "workflow_id": request.get("workflow_id"),
                "processing_time": time.time()
            }
            
            return result
            
        except Exception as e:
            logger.error(f"V1 original execution failed: {e}")
            raise
    
    async def _execute_v1_enhanced(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """使用增强适配器执行V1请求"""
        
        # 转换V1请求到统一状态
        state = await self._convert_v1_request_to_state(request)
        
        # 使用增强适配器处理
        final_response = None
        async for response in self.enhanced_adapter.process(state):
            final_response = response
        
        if final_response and final_response.success:
            return {
                "success": True,
                "content": final_response.content,
                "data": final_response.data,
                "workflow_id": request.get("workflow_id"),
                "processing_info": {
                    "processed_by": "enhanced_adapter",
                    "execution_time": final_response.execution_time
                }
            }
        else:
            return {
                "success": False,
                "error": "Enhanced adapter processing failed",
                "errors": final_response.errors if final_response else ["No response generated"]
            }
    
    async def _convert_v1_to_v2_format(self, v1_request: Dict[str, Any]) -> Dict[str, Any]:
        """转换V1格式到V2格式"""
        
        # 提取V1请求中的信息
        topic = v1_request.get("topic", v1_request.get("prompt", ""))
        requirements = v1_request.get("requirements", v1_request.get("instructions", ""))
        keywords = v1_request.get("keywords", [])
        
        # 从其他字段推断信息
        if "messages" in v1_request and isinstance(v1_request["messages"], list):
            # 从消息中提取主题
            for msg in v1_request["messages"]:
                if isinstance(msg, dict) and "content" in msg:
                    if not topic:
                        topic = msg["content"][:100]  # 取前100个字符作为主题
                    requirements += " " + msg["content"]
        
        # 构建V2请求
        v2_request = {
            "topic": topic,
            "requirements": requirements.strip(),
            "keywords": keywords,
            "target_length": v1_request.get("target_length", 3500),
            "language": v1_request.get("language", "zh-CN"),
            "tone": v1_request.get("tone", "professional"),
            "user_id": v1_request.get("user_id"),
            "thread_id": v1_request.get("thread_id"),
            "original_format": "v1"
        }
        
        return v2_request
    
    async def _convert_v1_request_to_state(self, v1_request: Dict[str, Any]) -> UnifiedState:
        """转换V1请求到统一状态"""
        
        v2_request = await self._convert_v1_to_v2_format(v1_request)
        
        state = UnifiedState(
            session_id=v1_request.get("workflow_id", v1_request.get("thread_id")),
            task_type=TaskType.ARTICLE_GENERATION,
            user_id=v2_request.get("user_id"),
            topic=v2_request["topic"],
            requirements=v2_request["requirements"],
            keywords=v2_request["keywords"],
            target_length=v2_request["target_length"],
            language=v2_request["language"],
            tone=v2_request["tone"]
        )
        
        # 保存原始请求信息
        state.shared_context["original_v1_request"] = v1_request
        
        return state
    
    async def _convert_v1_response_format(self, v1_result: Dict[str, Any]) -> Dict[str, Any]:
        """转换V1响应格式"""
        
        # 标准化V1响应格式
        standardized = {
            "success": v1_result.get("success", True),
            "content": v1_result.get("content", ""),
            "data": v1_result.get("data", {}),
            "metadata": {
                "system_used": "v1",
                "workflow_id": v1_result.get("workflow_id"),
                "processing_time": v1_result.get("processing_time", 0)
            }
        }
        
        # 添加错误信息
        if not standardized["success"]:
            standardized["errors"] = v1_result.get("errors", [v1_result.get("error", "Unknown error")])
        
        return standardized
    
    async def _convert_v2_response_format(self, v2_response: AgentResponse) -> Dict[str, Any]:
        """转换V2响应格式"""
        
        # 转换为标准响应格式
        result = {
            "success": v2_response.success,
            "content": v2_response.content,
            "data": v2_response.data,
            "metadata": {
                "system_used": "v2",
                "execution_time": v2_response.execution_time,
                "current_stage": v2_response.current_stage,
                "progress": v2_response.progress
            }
        }
        
        # 添加错误信息
        if not v2_response.success:
            result["errors"] = v2_response.errors
        
        # 添加警告信息
        if v2_response.warnings:
            result["warnings"] = v2_response.warnings
        
        # 添加V2特有的元数据
        if v2_response.metadata:
            result["metadata"].update(v2_response.metadata)
        
        return result
    
    async def migrate_v1_data(self, v1_data: Dict[str, Any]) -> Dict[str, Any]:
        """迁移V1数据到V2格式"""
        
        logger.info("Migrating V1 data to V2 format")
        
        try:
            # 数据结构转换
            migrated_data = {
                "sessions": [],
                "user_contexts": [],
                "migration_info": {
                    "migrated_at": time.time(),
                    "source_system": "v1",
                    "target_system": "v2",
                    "data_version": "1.0"
                }
            }
            
            # 转换会话数据
            if "workflows" in v1_data:
                for workflow in v1_data["workflows"]:
                    session = await self._convert_workflow_to_session(workflow)
                    migrated_data["sessions"].append(session)
            
            # 转换用户数据
            if "users" in v1_data:
                for user in v1_data["users"]:
                    user_context = await self._convert_user_to_context(user)
                    migrated_data["user_contexts"].append(user_context)
            
            logger.info(f"Migration completed: {len(migrated_data['sessions'])} sessions, {len(migrated_data['user_contexts'])} users")
            
            return migrated_data
            
        except Exception as e:
            logger.error(f"Data migration failed: {e}")
            raise
    
    async def _convert_workflow_to_session(self, workflow: Dict[str, Any]) -> Dict[str, Any]:
        """转换工作流到会话"""
        
        session = {
            "session_id": workflow.get("workflow_id", workflow.get("id")),
            "user_id": workflow.get("user_id"),
            "created_at": workflow.get("created_at", time.time()),
            "updated_at": workflow.get("updated_at", time.time()),
            "status": self._convert_workflow_status(workflow.get("status", "unknown")),
            "task_type": "article_generation",
            "input_data": {
                "topic": workflow.get("topic", ""),
                "requirements": workflow.get("requirements", ""),
                "keywords": workflow.get("keywords", [])
            },
            "output_data": {
                "content": workflow.get("result", {}).get("content", "")
            },
            "metadata": {
                "migrated_from_v1": True,
                "original_workflow_id": workflow.get("workflow_id")
            }
        }
        
        return session
    
    async def _convert_user_to_context(self, user: Dict[str, Any]) -> Dict[str, Any]:
        """转换用户到用户上下文"""
        
        user_context = {
            "user_id": user.get("user_id", user.get("id")),
            "preferences": {
                "language": user.get("language", "zh-CN"),
                "tone": user.get("preferred_tone", "professional"),
                "target_length": user.get("preferred_length", 3500)
            },
            "history": user.get("history", []),
            "statistics": {
                "total_requests": user.get("total_requests", 0),
                "successful_requests": user.get("successful_requests", 0)
            },
            "metadata": {
                "migrated_from_v1": True,
                "migration_date": time.time()
            }
        }
        
        return user_context
    
    def _convert_workflow_status(self, v1_status: str) -> str:
        """转换工作流状态"""
        
        status_mapping = {
            "pending": "idle",
            "running": "processing",
            "completed": "completed",
            "failed": "failed",
            "cancelled": "failed"
        }
        
        return status_mapping.get(v1_status.lower(), "idle")
    
    def get_compatibility_stats(self) -> Dict[str, Any]:
        """获取兼容性统计"""
        
        stats = self.compatibility_stats.copy()
        
        total_requests = stats["v1_requests"] + stats["v2_requests"]
        if total_requests > 0:
            stats["v1_usage_rate"] = stats["v1_requests"] / total_requests
            stats["v2_usage_rate"] = stats["v2_requests"] / total_requests
            stats["migration_rate"] = stats["migration_requests"] / total_requests
            stats["conversion_rate"] = stats["conversion_requests"] / total_requests
            stats["compatibility_issue_rate"] = stats["compatibility_issues"] / total_requests
        
        return stats
    
    def enable_v1_compatibility(self, enable: bool = True):
        """启用/禁用V1兼容性"""
        
        self.compatibility_config["enable_v1_compatibility"] = enable
        logger.info(f"V1 compatibility {'enabled' if enable else 'disabled'}")
    
    def enable_v2_features(self, enable: bool = True):
        """启用/禁用V2功能"""
        
        self.compatibility_config["enable_v2_features"] = enable
        logger.info(f"V2 features {'enabled' if enable else 'disabled'}")
    
    def set_default_system(self, system: str):
        """设置默认系统"""
        
        if system in ["v1", "v2"]:
            self.compatibility_config["default_system"] = system
            logger.info(f"Default system set to {system}")
        else:
            raise ValueError("System must be 'v1' or 'v2'")
    
    async def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        
        health_info = {
            "status": "healthy",
            "compatibility_config": self.compatibility_config,
            "compatibility_stats": self.get_compatibility_stats(),
            "systems_available": {
                "v1_system": V1_AVAILABLE and self.v1_api_handler is not None,
                "v2_system": True  # V2始终可用
            }
        }
        
        # 检查V2引擎健康状态
        try:
            v2_health = await self.v2_engine.get_system_health()
            health_info["v2_engine_health"] = v2_health
        except Exception as e:
            health_info["v2_engine_health"] = {"status": "error", "error": str(e)}
            health_info["status"] = "degraded"
        
        # 检查增强适配器健康状态
        try:
            adapter_health = await self.enhanced_adapter.health_check()
            health_info["enhanced_adapter_health"] = adapter_health
        except Exception as e:
            health_info["enhanced_adapter_health"] = {"status": "error", "error": str(e)}
            health_info["status"] = "degraded"
        
        return health_info
    
    async def shutdown(self):
        """关闭兼容性层"""
        
        logger.info("Shutting down Compatibility Layer...")
        
        # 关闭V2引擎
        if self.v2_engine:
            await self.v2_engine.shutdown()
        
        # 清理V1组件
        if self.v1_api_handler:
            # V1组件可能没有标准的shutdown方法
            pass
        
        logger.info("Compatibility Layer shutdown complete")