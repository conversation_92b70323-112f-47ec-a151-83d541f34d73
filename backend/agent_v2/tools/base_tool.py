"""
Base Tool for Agent V2 - Enhanced tool architecture
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional
import time
import asyncio

from ..core.models import ToolResult, UnifiedState


class BaseToolV2(ABC):
    """
    Enhanced Base Tool for Agent V2
    
    Key improvements:
    - Unified state integration
    - Better error handling
    - Execution tracking
    - Context awareness
    """
    
    def __init__(self, name: str, description: str = "", config: Dict[str, Any] = None):
        self.name = name
        self.description = description
        self.config = config or {}
        
        # 执行历史
        self.execution_history: List[Dict[str, Any]] = []
        
        # 工具元数据
        self.metadata = self._get_metadata()
    
    @abstractmethod
    async def execute(
        self, 
        parameters: Dict[str, Any], 
        state: UnifiedState
    ) -> ToolResult:
        """
        执行工具
        
        Args:
            parameters: 工具参数
            state: 统一状态对象
            
        Returns:
            ToolResult: 执行结果
        """
        pass
    
    @abstractmethod
    def _get_metadata(self) -> Dict[str, Any]:
        """获取工具元数据"""
        pass
    
    async def execute_with_tracking(
        self, 
        parameters: Dict[str, Any], 
        state: UnifiedState
    ) -> ToolResult:
        """带跟踪的执行"""
        
        start_time = time.time()
        
        # 验证参数
        if not self._validate_parameters(parameters):
            return ToolResult(
                success=False,
                error="Invalid parameters",
                tool_name=self.name,
                parameters=parameters
            )
        
        try:
            # 执行工具
            result = await self.execute(parameters, state)
            
            # 设置执行信息
            result.tool_name = self.name
            result.parameters = parameters
            result.execution_time = time.time() - start_time
            
            # 记录执行历史
            self._record_execution(parameters, result, state)
            
            # 更新状态
            if result.success and self.name not in state.tools_used:
                state.tools_used.append(self.name)
            
            return result
            
        except Exception as e:
            execution_time = time.time() - start_time
            result = ToolResult(
                success=False,
                error=f"Tool execution failed: {str(e)}",
                execution_time=execution_time,
                tool_name=self.name,
                parameters=parameters
            )
            
            self._record_execution(parameters, result, state)
            return result
    
    def _validate_parameters(self, parameters: Dict[str, Any]) -> bool:
        """验证参数 - 子类可重写"""
        return True
    
    def _record_execution(
        self, 
        parameters: Dict[str, Any], 
        result: ToolResult, 
        state: UnifiedState
    ):
        """记录执行历史"""
        
        execution_record = {
            "timestamp": time.time(),
            "session_id": state.session_id,
            "parameters": parameters,
            "success": result.success,
            "execution_time": result.execution_time,
            "error": result.error
        }
        
        self.execution_history.append(execution_record)
        
        # 保持最近100条记录
        if len(self.execution_history) > 100:
            self.execution_history = self.execution_history[-100:]
    
    def get_usage_stats(self) -> Dict[str, Any]:
        """获取使用统计"""
        
        if not self.execution_history:
            return {}
        
        recent_executions = self.execution_history[-20:]  # 最近20次
        
        success_count = sum(1 for exec in recent_executions if exec["success"])
        total_count = len(recent_executions)
        avg_execution_time = sum(exec["execution_time"] for exec in recent_executions) / total_count
        
        return {
            "total_executions": len(self.execution_history),
            "recent_success_rate": success_count / total_count if total_count > 0 else 0,
            "average_execution_time": avg_execution_time,
            "last_execution": self.execution_history[-1] if self.execution_history else None
        }
    
    async def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        return {
            "tool_name": self.name,
            "status": "healthy",
            "executions_count": len(self.execution_history),
            "metadata": self.metadata
        }


class ToolRegistry:
    """工具注册表"""
    
    def __init__(self):
        self.tools: Dict[str, BaseToolV2] = {}
        self.tool_chains: Dict[str, List[str]] = {}
    
    def register_tool(self, tool: BaseToolV2):
        """注册工具"""
        self.tools[tool.name] = tool
    
    def get_tool(self, name: str) -> Optional[BaseToolV2]:
        """获取工具"""
        return self.tools.get(name)
    
    def list_tools(self) -> List[str]:
        """列出所有工具"""
        return list(self.tools.keys())
    
    def create_tool_chain(self, name: str, tool_names: List[str]):
        """创建工具链"""
        # 验证所有工具都存在
        for tool_name in tool_names:
            if tool_name not in self.tools:
                raise ValueError(f"Tool '{tool_name}' not found")
        
        self.tool_chains[name] = tool_names
    
    async def execute_tool_chain(
        self, 
        chain_name: str, 
        parameters_list: List[Dict[str, Any]], 
        state: UnifiedState
    ) -> List[ToolResult]:
        """执行工具链"""
        
        if chain_name not in self.tool_chains:
            return [ToolResult(
                success=False,
                error=f"Tool chain '{chain_name}' not found"
            )]
        
        tool_names = self.tool_chains[chain_name]
        results = []
        
        for i, tool_name in enumerate(tool_names):
            tool = self.get_tool(tool_name)
            if not tool:
                results.append(ToolResult(
                    success=False,
                    error=f"Tool '{tool_name}' not found"
                ))
                break
            
            # 获取参数
            params = parameters_list[i] if i < len(parameters_list) else {}
            
            # 执行工具
            result = await tool.execute_with_tracking(params, state)
            results.append(result)
            
            # 如果失败，中断链式执行
            if not result.success:
                break
        
        return results
    
    async def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        
        tool_health = {}
        for name, tool in self.tools.items():
            tool_health[name] = await tool.health_check()
        
        return {
            "status": "healthy",
            "tools_count": len(self.tools),
            "tool_chains_count": len(self.tool_chains),
            "tools": tool_health
        }