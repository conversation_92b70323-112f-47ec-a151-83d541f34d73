"""
Quality Monitor - Evaluates and monitors content quality
"""

from typing import Dict, Any, List, Optional
import re
import time
import asyncio

from ..core.models import UnifiedState, AgentResponse, QualityMetrics


class QualityMonitor:
    """
    质量监控器
    
    负责：
    - 内容质量评估
    - 质量趋势分析
    - 质量问题检测
    - 改进建议生成
    """
    
    def __init__(self):
        self.quality_thresholds = {
            "min_content_length": 1000,
            "max_content_length": 10000,
            "min_paragraph_count": 3,
            "min_section_count": 2,
            "keyword_coverage_threshold": 0.6,
            "readability_threshold": 0.7
        }
        
        # 质量历史
        self.quality_history: List[Dict[str, Any]] = []
    
    async def evaluate_result(self, state: UnifiedState, response: AgentResponse) -> QualityMetrics:
        """评估结果质量"""
        
        start_time = time.time()
        
        # 提取内容
        content = self._extract_content(response)
        if not content:
            return QualityMetrics(
                overall_score=0.0,
                issues=["No content found in response"]
            )
        
        # 各项质量指标评估
        metrics = QualityMetrics()
        
        # 1. 内容质量评估
        metrics.content_quality = await self._evaluate_content_quality(content, state)
        
        # 2. 结构质量评估
        metrics.structure_quality = self._evaluate_structure_quality(content)
        
        # 3. 关键词覆盖率
        if state.keywords:
            metrics.keyword_coverage = self._evaluate_keyword_coverage(content, state.keywords)
        else:
            metrics.keyword_coverage = 1.0  # 没有关键词要求时给满分
        
        # 4. 可读性评估
        metrics.readability_score = self._evaluate_readability(content)
        
        # 5. 长度合规性
        metrics.length_compliance = self._evaluate_length_compliance(content, state.target_length)
        
        # 6. 计算总体分数
        metrics.overall_score = self._calculate_overall_score(metrics)
        
        # 7. 提取详细指标
        self._extract_detailed_metrics(content, metrics)
        
        # 8. 生成问题和建议
        await self._generate_issues_and_suggestions(metrics, state)
        
        # 记录质量历史
        quality_record = {
            "timestamp": time.time(),
            "session_id": state.session_id,
            "task_type": state.task_type.value,
            "overall_score": metrics.overall_score,
            "content_quality": metrics.content_quality,
            "structure_quality": metrics.structure_quality,
            "keyword_coverage": metrics.keyword_coverage,
            "evaluation_time": time.time() - start_time
        }
        
        self.quality_history.append(quality_record)
        if len(self.quality_history) > 1000:
            self.quality_history = self.quality_history[-1000:]
        
        return metrics
    
    def _extract_content(self, response: AgentResponse) -> str:
        """从响应中提取内容"""
        
        # 优先从data中提取
        if "article" in response.data:
            article = response.data["article"]
            if isinstance(article, dict):
                return article.get("content", "")
            return str(article)
        
        # 从content字段提取
        if response.content:
            return response.content
        
        # 从其他可能的字段提取
        if "final_content" in response.data:
            final_content = response.data["final_content"]
            if isinstance(final_content, dict):
                return final_content.get("content", "")
            return str(final_content)
        
        return ""
    
    async def _evaluate_content_quality(self, content: str, state: UnifiedState) -> float:
        """评估内容质量"""
        
        score = 1.0
        
        # 长度检查
        content_length = len(content)
        if content_length < self.quality_thresholds["min_content_length"]:
            score -= 0.3
        elif content_length > self.quality_thresholds["max_content_length"]:
            score -= 0.1
        
        # 重复内容检查
        repetition_score = self._check_repetition(content)
        score *= repetition_score
        
        # 内容完整性检查
        completeness_score = self._check_completeness(content, state)
        score *= completeness_score
        
        return max(0.0, min(1.0, score))
    
    def _evaluate_structure_quality(self, content: str) -> float:
        """评估结构质量"""
        
        score = 1.0
        
        # 段落检查
        paragraphs = content.split('\n\n')
        paragraph_count = len([p for p in paragraphs if p.strip()])
        
        if paragraph_count < self.quality_thresholds["min_paragraph_count"]:
            score -= 0.2
        
        # 标题/章节检查
        section_count = len(re.findall(r'^#{1,3}\s+.+$', content, re.MULTILINE))
        if section_count < self.quality_thresholds["min_section_count"]:
            score -= 0.2
        
        # 列表和格式化检查
        has_lists = bool(re.search(r'^[-*]\s+', content, re.MULTILINE))
        has_formatting = bool(re.search(r'\*\*.+?\*\*|__.+?__|``.+?``', content))
        
        if has_lists:
            score += 0.1
        if has_formatting:
            score += 0.1
        
        return max(0.0, min(1.0, score))
    
    def _evaluate_keyword_coverage(self, content: str, keywords: List[str]) -> float:
        """评估关键词覆盖率"""
        
        if not keywords:
            return 1.0
        
        content_lower = content.lower()
        covered_keywords = 0
        
        for keyword in keywords:
            if keyword.lower() in content_lower:
                covered_keywords += 1
        
        coverage_rate = covered_keywords / len(keywords)
        
        # 根据覆盖率计算分数
        if coverage_rate >= self.quality_thresholds["keyword_coverage_threshold"]:
            return 1.0
        else:
            return coverage_rate / self.quality_thresholds["keyword_coverage_threshold"]
    
    def _evaluate_readability(self, content: str) -> float:
        """评估可读性"""
        
        # 简化的可读性评估
        sentences = re.split(r'[.!?]+', content)
        sentences = [s.strip() for s in sentences if s.strip()]
        
        if not sentences:
            return 0.0
        
        # 平均句子长度
        avg_sentence_length = sum(len(s.split()) for s in sentences) / len(sentences)
        
        # 理想句子长度 15-25 词
        if 15 <= avg_sentence_length <= 25:
            length_score = 1.0
        elif avg_sentence_length < 15:
            length_score = 0.8
        else:
            length_score = max(0.3, 1.0 - (avg_sentence_length - 25) * 0.02)
        
        # 句子变化性
        sentence_lengths = [len(s.split()) for s in sentences]
        length_variance = len(set(sentence_lengths)) / len(sentence_lengths) if sentence_lengths else 0
        variance_score = min(1.0, length_variance * 2)
        
        return (length_score * 0.7 + variance_score * 0.3)
    
    def _evaluate_length_compliance(self, content: str, target_length: int) -> float:
        """评估长度合规性"""
        
        actual_length = len(content)
        
        if target_length <= 0:
            return 1.0
        
        # 允许 ±20% 的偏差
        lower_bound = target_length * 0.8
        upper_bound = target_length * 1.2
        
        if lower_bound <= actual_length <= upper_bound:
            return 1.0
        elif actual_length < lower_bound:
            # 不足的惩罚更重
            return max(0.0, actual_length / lower_bound)
        else:
            # 超出的惩罚较轻
            excess_ratio = (actual_length - upper_bound) / target_length
            return max(0.3, 1.0 - excess_ratio * 0.5)
    
    def _calculate_overall_score(self, metrics: QualityMetrics) -> float:
        """计算总体质量分数"""
        
        # 加权平均
        weights = {
            "content_quality": 0.3,
            "structure_quality": 0.25,
            "keyword_coverage": 0.2,
            "readability_score": 0.15,
            "length_compliance": 0.1
        }
        
        overall_score = (
            metrics.content_quality * weights["content_quality"] +
            metrics.structure_quality * weights["structure_quality"] +
            metrics.keyword_coverage * weights["keyword_coverage"] +
            metrics.readability_score * weights["readability_score"] +
            metrics.length_compliance * weights["length_compliance"]
        )
        
        return max(0.0, min(1.0, overall_score))
    
    def _extract_detailed_metrics(self, content: str, metrics: QualityMetrics):
        """提取详细指标"""
        
        # 字数统计
        metrics.word_count = len(content.split())
        
        # 段落数
        paragraphs = content.split('\n\n')
        metrics.paragraph_count = len([p for p in paragraphs if p.strip()])
        
        # 章节数
        metrics.section_count = len(re.findall(r'^#{1,3}\s+.+$', content, re.MULTILINE))
    
    async def _generate_issues_and_suggestions(self, metrics: QualityMetrics, state: UnifiedState):
        """生成问题和建议"""
        
        # 检查问题
        if metrics.content_quality < 0.7:
            metrics.issues.append("内容质量需要改进")
            metrics.suggestions.append("增加更多具体示例和详细说明")
        
        if metrics.structure_quality < 0.7:
            metrics.issues.append("文章结构不够清晰")
            metrics.suggestions.append("添加更多小标题和段落分隔")
        
        if metrics.keyword_coverage < 0.6:
            metrics.issues.append("关键词覆盖不足")
            metrics.suggestions.append(f"确保包含所有关键词：{', '.join(state.keywords)}")
        
        if metrics.length_compliance < 0.8:
            if metrics.word_count < state.target_length * 0.8:
                metrics.issues.append("内容长度不足")
                metrics.suggestions.append("增加更多内容以达到目标长度")
            else:
                metrics.issues.append("内容过长")
                metrics.suggestions.append("精简内容，保持核心要点")
        
        if metrics.readability_score < 0.7:
            metrics.issues.append("可读性需要改进")
            metrics.suggestions.append("调整句子长度，增加句式变化")
    
    def _check_repetition(self, content: str) -> float:
        """检查重复内容"""
        
        sentences = re.split(r'[.!?]+', content)
        sentences = [s.strip().lower() for s in sentences if s.strip()]
        
        if len(sentences) <= 1:
            return 1.0
        
        unique_sentences = set(sentences)
        repetition_rate = 1.0 - (len(unique_sentences) / len(sentences))
        
        # 重复率超过20%开始扣分
        if repetition_rate > 0.2:
            return max(0.3, 1.0 - (repetition_rate - 0.2) * 2)
        
        return 1.0
    
    def _check_completeness(self, content: str, state: UnifiedState) -> float:
        """检查内容完整性"""
        
        # 检查是否有明显的结论/总结
        has_conclusion = bool(re.search(
            r'(总结|总之|综上|结论|最后|总的来说)', 
            content, 
            re.IGNORECASE
        ))
        
        # 检查是否有引言/开头
        has_introduction = len(content) > 100  # 简单检查，至少有一定的开头内容
        
        completeness_score = 0.5  # 基础分
        if has_introduction:
            completeness_score += 0.25
        if has_conclusion:
            completeness_score += 0.25
        
        return completeness_score
    
    def get_quality_trends(self, days: int = 7) -> Dict[str, Any]:
        """获取质量趋势"""
        
        if not self.quality_history:
            return {}
        
        # 过滤最近N天的数据
        cutoff_time = time.time() - (days * 24 * 3600)
        recent_records = [
            record for record in self.quality_history
            if record["timestamp"] >= cutoff_time
        ]
        
        if not recent_records:
            return {}
        
        # 计算趋势
        avg_overall_score = sum(r["overall_score"] for r in recent_records) / len(recent_records)
        avg_content_quality = sum(r["content_quality"] for r in recent_records) / len(recent_records)
        avg_structure_quality = sum(r["structure_quality"] for r in recent_records) / len(recent_records)
        avg_keyword_coverage = sum(r["keyword_coverage"] for r in recent_records) / len(recent_records)
        
        return {
            "period_days": days,
            "total_evaluations": len(recent_records),
            "average_scores": {
                "overall": avg_overall_score,
                "content_quality": avg_content_quality,
                "structure_quality": avg_structure_quality,
                "keyword_coverage": avg_keyword_coverage
            },
            "trend_direction": self._calculate_trend_direction(recent_records)
        }
    
    def _calculate_trend_direction(self, records: List[Dict[str, Any]]) -> str:
        """计算趋势方向"""
        
        if len(records) < 3:
            return "insufficient_data"
        
        # 简单的趋势计算：比较前半段和后半段的平均分数
        mid_point = len(records) // 2
        early_records = records[:mid_point]
        recent_records = records[mid_point:]
        
        early_avg = sum(r["overall_score"] for r in early_records) / len(early_records)
        recent_avg = sum(r["overall_score"] for r in recent_records) / len(recent_records)
        
        diff = recent_avg - early_avg
        
        if diff > 0.05:
            return "improving"
        elif diff < -0.05:
            return "declining"
        else:
            return "stable"
    
    async def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        return {
            "status": "healthy",
            "quality_history_count": len(self.quality_history),
            "thresholds": self.quality_thresholds
        }