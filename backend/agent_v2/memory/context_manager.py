"""
Shared Context Manager - Handles user preferences, session state, and learning
"""

from typing import Dict, Any, List, Optional
import json
import os
import time
import asyncio
from pathlib import Path

from ..core.models import UserContext


class SharedContextManager:
    """
    共享上下文管理器
    
    负责管理：
    - 用户偏好和历史
    - 会话上下文
    - 学习模式
    """
    
    def __init__(self, storage_dir: str = "tmp/agent_v2_context"):
        self.storage_dir = Path(storage_dir)
        self.storage_dir.mkdir(parents=True, exist_ok=True)
        
        # 内存缓存
        self._user_contexts: Dict[str, UserContext] = {}
        self._session_contexts: Dict[str, Dict[str, Any]] = {}
        
        # 文件路径
        self.users_file = self.storage_dir / "users.json"
        self.sessions_file = self.storage_dir / "sessions.json"
        self.patterns_file = self.storage_dir / "learned_patterns.json"
        
        # 加载现有数据
        asyncio.create_task(self._load_data())
    
    async def _load_data(self):
        """加载存储的数据"""
        try:
            # 加载用户数据
            if self.users_file.exists():
                with open(self.users_file, 'r', encoding='utf-8') as f:
                    users_data = json.load(f)
                    for user_id, data in users_data.items():
                        self._user_contexts[user_id] = UserContext(
                            user_id=user_id,
                            **data
                        )
            
            # 加载会话数据
            if self.sessions_file.exists():
                with open(self.sessions_file, 'r', encoding='utf-8') as f:
                    self._session_contexts = json.load(f)
                    
        except Exception as e:
            print(f"Error loading context data: {e}")
    
    async def _save_data(self):
        """保存数据到文件"""
        try:
            # 保存用户数据
            users_data = {}
            for user_id, context in self._user_contexts.items():
                users_data[user_id] = {
                    "preferences": context.preferences,
                    "history": context.history,
                    "learned_patterns": context.learned_patterns,
                    "recent_topics": context.recent_topics,
                    "recent_quality_scores": context.recent_quality_scores
                }
            
            with open(self.users_file, 'w', encoding='utf-8') as f:
                json.dump(users_data, f, ensure_ascii=False, indent=2)
            
            # 保存会话数据
            with open(self.sessions_file, 'w', encoding='utf-8') as f:
                json.dump(self._session_contexts, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            print(f"Error saving context data: {e}")
    
    async def get_user_context(self, user_id: str) -> Optional[UserContext]:
        """获取用户上下文"""
        if user_id not in self._user_contexts:
            # 创建新的用户上下文
            self._user_contexts[user_id] = UserContext(user_id=user_id)
        
        return self._user_contexts[user_id]
    
    async def update_user_context(self, user_id: str, updates: Dict[str, Any]):
        """更新用户上下文"""
        context = await self.get_user_context(user_id)
        
        # 更新偏好
        if "preferences" in updates:
            context.preferences.update(updates["preferences"])
        
        # 更新单个偏好字段
        preference_fields = ["preferred_length", "preferred_tone", "preferred_language"]
        for field in preference_fields:
            if field in updates:
                context.preferences[field] = updates[field]
        
        # 添加历史记录
        if any(key in updates for key in ["last_task_type", "last_topic", "last_quality_score"]):
            history_entry = {
                "task_type": updates.get("last_task_type"),
                "topic": updates.get("last_topic"),
                "quality_score": updates.get("last_quality_score"),
                "timestamp": updates.get("timestamp", time.time())
            }
            context.add_history_entry(history_entry)
            
            # 更新最近的主题和质量分数
            if updates.get("last_topic"):
                context.recent_topics.append(updates["last_topic"])
                if len(context.recent_topics) > 10:
                    context.recent_topics = context.recent_topics[-10:]
            
            if updates.get("last_quality_score"):
                context.recent_quality_scores.append(updates["last_quality_score"])
                if len(context.recent_quality_scores) > 20:
                    context.recent_quality_scores = context.recent_quality_scores[-20:]
        
        # 学习成功模式
        await self._learn_user_patterns(context, updates)
        
        # 保存到文件
        await self._save_data()
    
    async def _learn_user_patterns(self, context: UserContext, updates: Dict[str, Any]):
        """学习用户使用模式"""
        
        # 如果质量分数高，学习成功模式
        quality_score = updates.get("last_quality_score", 0.0)
        if quality_score > 0.8:
            success_pattern = {
                "topic": updates.get("last_topic"),
                "length": updates.get("last_target_length"),
                "tone": updates.get("last_tone"),
                "language": updates.get("last_language"),
                "quality_score": quality_score,
                "timestamp": time.time()
            }
            
            if "successful_patterns" not in context.learned_patterns:
                context.learned_patterns["successful_patterns"] = []
            
            context.learned_patterns["successful_patterns"].append(success_pattern)
            
            # 保持最近20个成功模式
            if len(context.learned_patterns["successful_patterns"]) > 20:
                context.learned_patterns["successful_patterns"] = \
                    context.learned_patterns["successful_patterns"][-20:]
        
        # 分析偏好趋势
        if len(context.recent_quality_scores) >= 5:
            avg_quality = sum(context.recent_quality_scores) / len(context.recent_quality_scores)
            context.learned_patterns["average_quality"] = avg_quality
            
            # 如果平均质量高，提取偏好模式
            if avg_quality > 0.7:
                self._extract_preference_patterns(context)
    
    def _extract_preference_patterns(self, context: UserContext):
        """提取偏好模式"""
        
        successful_patterns = context.learned_patterns.get("successful_patterns", [])
        if len(successful_patterns) < 3:
            return
        
        # 分析长度偏好
        lengths = [p.get("length") for p in successful_patterns if p.get("length")]
        if lengths:
            avg_length = sum(lengths) / len(lengths)
            context.preferences["learned_preferred_length"] = int(avg_length)
        
        # 分析语调偏好
        tones = [p.get("tone") for p in successful_patterns if p.get("tone")]
        if tones:
            tone_counts = {}
            for tone in tones:
                tone_counts[tone] = tone_counts.get(tone, 0) + 1
            most_common_tone = max(tone_counts, key=tone_counts.get)
            context.preferences["learned_preferred_tone"] = most_common_tone
    
    async def get_session_context(self, session_id: str) -> Dict[str, Any]:
        """获取会话上下文"""
        if session_id not in self._session_contexts:
            self._session_contexts[session_id] = {
                "created_at": time.time(),
                "task_history": [],
                "shared_data": {}
            }
        
        return self._session_contexts[session_id]
    
    async def update_session_context(self, session_id: str, updates: Dict[str, Any]):
        """更新会话上下文"""
        context = await self.get_session_context(session_id)
        
        # 添加任务历史
        if "last_task" in updates:
            context["task_history"].append(updates["last_task"])
            # 保持最近10个任务
            if len(context["task_history"]) > 10:
                context["task_history"] = context["task_history"][-10:]
        
        # 更新共享数据
        if "shared_data" in updates:
            context["shared_data"].update(updates["shared_data"])
        
        # 更新其他字段
        for key, value in updates.items():
            if key not in ["last_task", "shared_data"]:
                context[key] = value
        
        context["updated_at"] = time.time()
        
        # 保存到文件
        await self._save_data()
    
    async def get_user_recommendations(self, user_id: str, task_type: str) -> Dict[str, Any]:
        """获取用户推荐设置"""
        context = await self.get_user_context(user_id)
        if not context:
            return {}
        
        recommendations = {}
        
        # 基于历史成功模式推荐
        successful_patterns = context.learned_patterns.get("successful_patterns", [])
        if successful_patterns:
            recent_patterns = successful_patterns[-5:]  # 最近5个成功模式
            
            # 推荐长度
            lengths = [p.get("length") for p in recent_patterns if p.get("length")]
            if lengths:
                recommendations["recommended_length"] = int(sum(lengths) / len(lengths))
            
            # 推荐语调
            tones = [p.get("tone") for p in recent_patterns if p.get("tone")]
            if tones:
                tone_counts = {}
                for tone in tones:
                    tone_counts[tone] = tone_counts.get(tone, 0) + 1
                recommendations["recommended_tone"] = max(tone_counts, key=tone_counts.get)
        
        # 基于偏好推荐
        if context.preferences:
            recommendations.update({
                "user_preferred_length": context.get_preferred_length(),
                "user_preferred_tone": context.get_preferred_tone()
            })
        
        return recommendations
    
    async def get_similar_successful_cases(self, user_id: str, topic: str, limit: int = 3) -> List[Dict[str, Any]]:
        """获取相似的成功案例"""
        context = await self.get_user_context(user_id)
        if not context:
            return []
        
        successful_patterns = context.learned_patterns.get("successful_patterns", [])
        if not successful_patterns:
            return []
        
        # 简单的相似度匹配（实际应该使用更复杂的算法）
        similar_cases = []
        topic_lower = topic.lower()
        
        for pattern in successful_patterns:
            pattern_topic = pattern.get("topic", "").lower()
            if pattern_topic and (topic_lower in pattern_topic or pattern_topic in topic_lower):
                similar_cases.append(pattern)
        
        # 按质量分数排序
        similar_cases.sort(key=lambda x: x.get("quality_score", 0), reverse=True)
        
        return similar_cases[:limit]
    
    async def cleanup_old_data(self, max_age_days: int = 30):
        """清理旧数据"""
        current_time = time.time()
        max_age_seconds = max_age_days * 24 * 3600
        
        # 清理旧会话
        sessions_to_remove = []
        for session_id, context in self._session_contexts.items():
            if current_time - context.get("created_at", 0) > max_age_seconds:
                sessions_to_remove.append(session_id)
        
        for session_id in sessions_to_remove:
            del self._session_contexts[session_id]
        
        # 清理用户历史中的旧记录
        for context in self._user_contexts.values():
            context.history = [
                entry for entry in context.history
                if current_time - entry.get("timestamp", 0) <= max_age_seconds
            ]
        
        if sessions_to_remove:
            await self._save_data()
    
    async def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        return {
            "status": "healthy",
            "users_count": len(self._user_contexts),
            "sessions_count": len(self._session_contexts),
            "storage_dir": str(self.storage_dir),
            "files_exist": {
                "users": self.users_file.exists(),
                "sessions": self.sessions_file.exists(),
                "patterns": self.patterns_file.exists()
            }
        }