"""
Analysis Agent - MediaAgent V2 分析Agent
"""

import asyncio
import logging
from typing import Dict, Any, List, Optional, AsyncIterator
import time
import re

from ..core.base_agent import BaseAgentV2
from ..core.models import UnifiedState, AgentResponse, TaskType, AgentStatus

logger = logging.getLogger(__name__)


class AnalysisAgent(BaseAgentV2):
    """
    分析Agent
    
    职责：
    - 内容分析和评估
    - 质量检查和优化
    - 数据处理和统计
    - 性能评估和改进建议
    """
    
    def __init__(self):
        super().__init__(
            name="analysis_agent",
            description="专业的分析Agent，负责内容分析、质量评估和数据处理",
            supported_tasks=[TaskType.CONTENT_ANALYSIS, TaskType.ARTICLE_GENERATION, TaskType.EDITING],
            config={
                "analysis_depth": "deep",
                "quality_threshold": 0.8,
                "enable_suggestions": True
            }
        )
        
        # 分析配置
        self.analysis_config = {
            "content_analysis_enabled": True,
            "quality_analysis_enabled": True,
            "seo_analysis_enabled": True,
            "readability_analysis_enabled": True,
            "sentiment_analysis_enabled": False
        }
        
        logger.info("Analysis Agent initialized")
    
    def _initialize_tools(self):
        """初始化工具"""
        
        self.tools = {
            "content_analyzer": None,    # 内容分析工具
            "quality_checker": None,     # 质量检查工具
            "seo_analyzer": None,        # SEO分析工具
            "readability_checker": None, # 可读性检查工具
            "data_processor": None       # 数据处理工具
        }
    
    async def process(self, state: UnifiedState) -> AsyncIterator[AgentResponse]:
        """处理分析任务"""
        
        logger.info(f"Starting analysis for session {state.session_id}")
        
        try:
            # 获取要分析的内容
            content = self._extract_content_for_analysis(state)
            
            if not content:
                yield AgentResponse(
                    success=False,
                    content="没有找到可分析的内容",
                    errors=["No content found for analysis"]
                )
                return
            
            # 阶段1: 内容预处理
            yield AgentResponse(
                success=True,
                content="开始预处理分析内容...",
                is_partial=True,
                progress=0.1,
                current_stage="内容预处理"
            )
            
            preprocessed_content = await self._preprocess_content(content)
            
            # 阶段2: 基础内容分析
            yield AgentResponse(
                success=True,
                content="进行基础内容分析...",
                is_partial=True,
                progress=0.3,
                current_stage="基础分析"
            )
            
            basic_analysis = await self._basic_content_analysis(preprocessed_content, state)
            
            # 阶段3: 质量分析
            yield AgentResponse(
                success=True,
                content="进行质量分析...",
                is_partial=True,
                progress=0.5,
                current_stage="质量分析"
            )
            
            quality_analysis = await self._quality_analysis(preprocessed_content, state)
            
            # 阶段4: SEO分析
            yield AgentResponse(
                success=True,
                content="进行SEO分析...",
                is_partial=True,
                progress=0.7,
                current_stage="SEO分析"
            )
            
            seo_analysis = await self._seo_analysis(preprocessed_content, state)
            
            # 阶段5: 综合分析和建议
            yield AgentResponse(
                success=True,
                content="生成综合分析报告...",
                is_partial=True,
                progress=0.9,
                current_stage="综合分析"
            )
            
            comprehensive_report = await self._generate_comprehensive_report(
                basic_analysis, quality_analysis, seo_analysis, state
            )
            
            # 返回最终结果
            yield AgentResponse(
                success=True,
                content="分析完成！",
                is_partial=False,
                progress=1.0,
                current_stage="完成",
                data={
                    "analysis_report": comprehensive_report,
                    "basic_metrics": basic_analysis,
                    "quality_metrics": quality_analysis,
                    "seo_metrics": seo_analysis,
                    "overall_score": comprehensive_report.get("overall_score", 0.0)
                }
            )
            
        except Exception as e:
            logger.error(f"Analysis failed: {e}")
            yield AgentResponse(
                success=False,
                content="分析任务失败",
                errors=[str(e)],
                current_stage="错误"
            )
    
    async def execute(self, task: Dict[str, Any], state: UnifiedState) -> AgentResponse:
        """兼容性方法 - 支持Planning Agent调用"""
        
        # 从task中提取参数并更新state
        if "stage_name" in task:
            state.current_stage = task["stage_name"]
        
        # 收集所有流式响应
        responses = []
        async for response in self.process(state):
            responses.append(response)
        
        # 返回最后一个响应（通常是最终结果）
        return responses[-1] if responses else AgentResponse(
            success=False,
            errors=["No response generated"]
        )
    
    async def optimize_content(self, content: str, requirements: Dict[str, Any] = None) -> Dict[str, Any]:
        """内容优化方法 - 供其他Agent调用"""
        
        # 创建临时状态
        temp_state = UnifiedState(
            task_type=TaskType.CONTENT_ANALYSIS,
            output_data={"content": content}
        )
        
        if requirements:
            temp_state.requirements = requirements.get("requirements", "")
            temp_state.keywords = requirements.get("keywords", [])
            temp_state.target_length = requirements.get("target_length", 0)
        
        # 执行分析
        final_response = None
        async for response in self.process(temp_state):
            final_response = response
        
        if final_response and final_response.success:
            analysis_report = final_response.data.get("analysis_report", {})
            
            # 基于分析结果优化内容
            optimized_content = await self._apply_optimizations(content, analysis_report)
            
            return {
                "optimized_content": optimized_content,
                "improvements": analysis_report.get("suggestions", []),
                "quality_score": analysis_report.get("overall_score", 0.0)
            }
        
        return {"optimized_content": content, "improvements": [], "quality_score": 0.0}
    
    def _extract_content_for_analysis(self, state: UnifiedState) -> str:
        """提取要分析的内容"""
        
        # 优先从output_data中提取
        if "article_content" in state.output_data:
            return state.output_data["article_content"]
        
        if "content" in state.output_data:
            return state.output_data["content"]
        
        # 从shared_context中提取
        if "article" in state.shared_context:
            article = state.shared_context["article"]
            if isinstance(article, dict):
                return article.get("content", "")
            return str(article)
        
        # 从input_data中提取
        if "content" in state.input_data:
            return state.input_data["content"]
        
        return ""
    
    async def _preprocess_content(self, content: str) -> Dict[str, Any]:
        """预处理内容"""
        
        # 清理和标准化内容
        cleaned_content = self._clean_content(content)
        
        # 提取结构信息
        structure_info = self._extract_structure(cleaned_content)
        
        # 分词和基础统计
        words = cleaned_content.split()
        sentences = self._split_sentences(cleaned_content)
        paragraphs = self._split_paragraphs(cleaned_content)
        
        preprocessed = {
            "original_content": content,
            "cleaned_content": cleaned_content,
            "structure": structure_info,
            "words": words,
            "sentences": sentences,
            "paragraphs": paragraphs,
            "word_count": len(words),
            "sentence_count": len(sentences),
            "paragraph_count": len(paragraphs)
        }
        
        return preprocessed
    
    def _clean_content(self, content: str) -> str:
        """清理内容"""
        
        # 移除多余的空白字符
        cleaned = re.sub(r'\s+', ' ', content)
        
        # 移除HTML标签（如果有）
        cleaned = re.sub(r'<[^>]+>', '', cleaned)
        
        # 标准化标点符号
        cleaned = cleaned.replace('。。', '。').replace('，，', '，')
        
        return cleaned.strip()
    
    def _extract_structure(self, content: str) -> Dict[str, Any]:
        """提取内容结构"""
        
        # 提取标题
        titles = re.findall(r'^#{1,6}\s+(.+)$', content, re.MULTILINE)
        
        # 提取列表
        lists = re.findall(r'^[-*]\s+(.+)$', content, re.MULTILINE)
        
        # 提取链接
        links = re.findall(r'\[([^\]]+)\]\(([^)]+)\)', content)
        
        # 提取粗体文本
        bold_text = re.findall(r'\*\*([^*]+)\*\*', content)
        
        structure = {
            "has_title": bool(titles),
            "title_count": len(titles),
            "titles": titles,
            "has_lists": bool(lists),
            "list_items": len(lists),
            "has_links": bool(links),
            "link_count": len(links),
            "has_formatting": bool(bold_text),
            "bold_count": len(bold_text)
        }
        
        return structure
    
    def _split_sentences(self, content: str) -> List[str]:
        """分割句子"""
        
        # 基于标点符号分割句子
        sentences = re.split(r'[。！？!?]+', content)
        
        # 清理空句子
        sentences = [s.strip() for s in sentences if s.strip()]
        
        return sentences
    
    def _split_paragraphs(self, content: str) -> List[str]:
        """分割段落"""
        
        # 基于双换行符分割段落
        paragraphs = content.split('\n\n')
        
        # 清理空段落
        paragraphs = [p.strip() for p in paragraphs if p.strip()]
        
        return paragraphs
    
    async def _basic_content_analysis(self, preprocessed: Dict[str, Any], state: UnifiedState) -> Dict[str, Any]:
        """基础内容分析"""
        
        content = preprocessed["cleaned_content"]
        words = preprocessed["words"]
        sentences = preprocessed["sentences"]
        paragraphs = preprocessed["paragraphs"]
        
        # 基础统计
        basic_stats = {
            "word_count": len(words),
            "sentence_count": len(sentences),
            "paragraph_count": len(paragraphs),
            "avg_words_per_sentence": len(words) / len(sentences) if sentences else 0,
            "avg_sentences_per_paragraph": len(sentences) / len(paragraphs) if paragraphs else 0,
            "character_count": len(content),
            "reading_time_minutes": len(words) / 200  # 假设每分钟200词
        }
        
        # 词汇分析
        vocabulary_analysis = self._analyze_vocabulary(words)
        
        # 句子长度分析
        sentence_analysis = self._analyze_sentences(sentences)
        
        # 段落分析
        paragraph_analysis = self._analyze_paragraphs(paragraphs)
        
        # 关键词分析
        keyword_analysis = self._analyze_keywords(content, state.keywords)
        
        basic_analysis = {
            "basic_stats": basic_stats,
            "vocabulary": vocabulary_analysis,
            "sentences": sentence_analysis,
            "paragraphs": paragraph_analysis,
            "keywords": keyword_analysis,
            "structure": preprocessed["structure"]
        }
        
        return basic_analysis
    
    def _analyze_vocabulary(self, words: List[str]) -> Dict[str, Any]:
        """分析词汇"""
        
        # 词频统计
        word_freq = {}
        for word in words:
            word_lower = word.lower()
            word_freq[word_lower] = word_freq.get(word_lower, 0) + 1
        
        # 去除常见停用词的词汇
        stopwords = {"的", "是", "在", "有", "和", "与", "或", "但", "如果", "因为", "所以", "这", "那", "我", "你", "他", "她", "它"}
        content_words = {k: v for k, v in word_freq.items() if k not in stopwords and len(k) > 1}
        
        # 词汇多样性
        unique_words = len(set(words))
        total_words = len(words)
        vocabulary_diversity = unique_words / total_words if total_words > 0 else 0
        
        # 最常用词
        top_words = sorted(content_words.items(), key=lambda x: x[1], reverse=True)[:10]
        
        vocabulary = {
            "total_words": total_words,
            "unique_words": unique_words,
            "vocabulary_diversity": vocabulary_diversity,
            "top_words": top_words,
            "average_word_length": sum(len(word) for word in words) / total_words if total_words > 0 else 0
        }
        
        return vocabulary
    
    def _analyze_sentences(self, sentences: List[str]) -> Dict[str, Any]:
        """分析句子"""
        
        if not sentences:
            return {"sentence_count": 0}
        
        # 句子长度统计
        sentence_lengths = [len(sentence.split()) for sentence in sentences]
        
        sentence_analysis = {
            "sentence_count": len(sentences),
            "avg_sentence_length": sum(sentence_lengths) / len(sentence_lengths),
            "min_sentence_length": min(sentence_lengths),
            "max_sentence_length": max(sentence_lengths),
            "sentence_length_variance": self._calculate_variance(sentence_lengths),
            "short_sentences": len([l for l in sentence_lengths if l < 10]),
            "medium_sentences": len([l for l in sentence_lengths if 10 <= l <= 20]),
            "long_sentences": len([l for l in sentence_lengths if l > 20])
        }
        
        return sentence_analysis
    
    def _analyze_paragraphs(self, paragraphs: List[str]) -> Dict[str, Any]:
        """分析段落"""
        
        if not paragraphs:
            return {"paragraph_count": 0}
        
        # 段落长度统计
        paragraph_lengths = [len(paragraph.split()) for paragraph in paragraphs]
        
        paragraph_analysis = {
            "paragraph_count": len(paragraphs),
            "avg_paragraph_length": sum(paragraph_lengths) / len(paragraph_lengths),
            "min_paragraph_length": min(paragraph_lengths),
            "max_paragraph_length": max(paragraph_lengths),
            "short_paragraphs": len([l for l in paragraph_lengths if l < 50]),
            "medium_paragraphs": len([l for l in paragraph_lengths if 50 <= l <= 150]),
            "long_paragraphs": len([l for l in paragraph_lengths if l > 150])
        }
        
        return paragraph_analysis
    
    def _analyze_keywords(self, content: str, keywords: List[str]) -> Dict[str, Any]:
        """分析关键词"""
        
        if not keywords:
            return {"keyword_count": 0, "keyword_density": 0.0}
        
        content_lower = content.lower()
        total_words = len(content.split())
        
        keyword_stats = {}
        total_keyword_occurrences = 0
        
        for keyword in keywords:
            keyword_lower = keyword.lower()
            occurrences = content_lower.count(keyword_lower)
            density = occurrences / total_words if total_words > 0 else 0
            
            keyword_stats[keyword] = {
                "occurrences": occurrences,
                "density": density
            }
            
            total_keyword_occurrences += occurrences
        
        overall_density = total_keyword_occurrences / total_words if total_words > 0 else 0
        
        keyword_analysis = {
            "keyword_count": len(keywords),
            "total_occurrences": total_keyword_occurrences,
            "overall_density": overall_density,
            "individual_keywords": keyword_stats,
            "keywords_covered": len([k for k, v in keyword_stats.items() if v["occurrences"] > 0])
        }
        
        return keyword_analysis
    
    def _calculate_variance(self, values: List[float]) -> float:
        """计算方差"""
        
        if len(values) < 2:
            return 0.0
        
        mean = sum(values) / len(values)
        variance = sum((x - mean) ** 2 for x in values) / (len(values) - 1)
        
        return variance
    
    async def _quality_analysis(self, preprocessed: Dict[str, Any], state: UnifiedState) -> Dict[str, Any]:
        """质量分析"""
        
        content = preprocessed["cleaned_content"]
        basic_stats = preprocessed
        
        # 内容完整性检查
        completeness_score = self._assess_completeness(content, basic_stats)
        
        # 结构质量检查
        structure_score = self._assess_structure_quality(preprocessed["structure"], basic_stats)
        
        # 可读性检查
        readability_score = self._assess_readability(preprocessed)
        
        # 长度合规性检查
        length_compliance = self._assess_length_compliance(basic_stats["word_count"], state.target_length)
        
        # 关键词覆盖检查
        keyword_coverage = self._assess_keyword_coverage(content, state.keywords)
        
        # 计算总体质量分数
        overall_quality = (
            completeness_score * 0.25 +
            structure_score * 0.25 +
            readability_score * 0.25 +
            length_compliance * 0.15 +
            keyword_coverage * 0.10
        )
        
        quality_analysis = {
            "overall_quality": overall_quality,
            "completeness_score": completeness_score,
            "structure_score": structure_score,
            "readability_score": readability_score,
            "length_compliance": length_compliance,
            "keyword_coverage": keyword_coverage,
            "quality_issues": self._identify_quality_issues(
                completeness_score, structure_score, readability_score, 
                length_compliance, keyword_coverage
            ),
            "improvement_suggestions": self._generate_quality_suggestions(
                completeness_score, structure_score, readability_score,
                length_compliance, keyword_coverage, state
            )
        }
        
        return quality_analysis
    
    def _assess_completeness(self, content: str, basic_stats: Dict[str, Any]) -> float:
        """评估内容完整性"""
        
        score = 0.5  # 基础分数
        
        # 检查是否有引言
        if any(word in content[:200].lower() for word in ["介绍", "引言", "概述", "背景"]):
            score += 0.2
        
        # 检查是否有结论
        if any(word in content[-200:].lower() for word in ["总结", "结论", "总之", "综上"]):
            score += 0.2
        
        # 检查内容长度
        word_count = basic_stats.get("word_count", 0)
        if word_count > 500:
            score += 0.1
        
        return min(1.0, score)
    
    def _assess_structure_quality(self, structure: Dict[str, Any], basic_stats: Dict[str, Any]) -> float:
        """评估结构质量"""
        
        score = 0.0
        
        # 标题结构
        if structure.get("has_title"):
            score += 0.3
        
        # 段落结构
        paragraph_count = basic_stats.get("paragraph_count", 0)
        if paragraph_count >= 3:
            score += 0.3
        elif paragraph_count >= 2:
            score += 0.2
        
        # 列表和格式化
        if structure.get("has_lists"):
            score += 0.2
        
        if structure.get("has_formatting"):
            score += 0.2
        
        return min(1.0, score)
    
    def _assess_readability(self, preprocessed: Dict[str, Any]) -> float:
        """评估可读性"""
        
        sentences = preprocessed.get("sentences", [])
        if not sentences:
            return 0.0
        
        # 平均句子长度
        avg_sentence_length = sum(len(s.split()) for s in sentences) / len(sentences)
        
        # 理想句子长度为15-25词
        if 15 <= avg_sentence_length <= 25:
            length_score = 1.0
        elif avg_sentence_length < 15:
            length_score = 0.8
        else:
            length_score = max(0.3, 1.0 - (avg_sentence_length - 25) * 0.02)
        
        # 句子长度变化性
        sentence_lengths = [len(s.split()) for s in sentences]
        length_variance = len(set(sentence_lengths)) / len(sentence_lengths) if sentence_lengths else 0
        variance_score = min(1.0, length_variance * 2)
        
        # 综合可读性分数
        readability = length_score * 0.7 + variance_score * 0.3
        
        return readability
    
    def _assess_length_compliance(self, actual_length: int, target_length: int) -> float:
        """评估长度合规性"""
        
        if target_length <= 0:
            return 1.0
        
        # 允许±20%的偏差
        lower_bound = target_length * 0.8
        upper_bound = target_length * 1.2
        
        if lower_bound <= actual_length <= upper_bound:
            return 1.0
        elif actual_length < lower_bound:
            return max(0.0, actual_length / lower_bound)
        else:
            excess_ratio = (actual_length - upper_bound) / target_length
            return max(0.3, 1.0 - excess_ratio * 0.5)
    
    def _assess_keyword_coverage(self, content: str, keywords: List[str]) -> float:
        """评估关键词覆盖度"""
        
        if not keywords:
            return 1.0
        
        content_lower = content.lower()
        covered_keywords = 0
        
        for keyword in keywords:
            if keyword.lower() in content_lower:
                covered_keywords += 1
        
        return covered_keywords / len(keywords)
    
    def _identify_quality_issues(self, *scores) -> List[str]:
        """识别质量问题"""
        
        issues = []
        
        completeness, structure, readability, length, keywords = scores
        
        if completeness < 0.7:
            issues.append("内容完整性需要改进")
        
        if structure < 0.7:
            issues.append("文章结构需要优化")
        
        if readability < 0.7:
            issues.append("可读性需要提升")
        
        if length < 0.8:
            issues.append("文章长度不符合要求")
        
        if keywords < 0.6:
            issues.append("关键词覆盖不足")
        
        return issues
    
    def _generate_quality_suggestions(self, *scores, state) -> List[str]:
        """生成质量改进建议"""
        
        suggestions = []
        
        completeness, structure, readability, length, keywords = scores
        
        if completeness < 0.7:
            suggestions.append("建议添加引言和结论部分")
        
        if structure < 0.7:
            suggestions.append("建议优化文章结构，添加小标题和段落分隔")
        
        if readability < 0.7:
            suggestions.append("建议调整句子长度，增加句式变化")
        
        if length < 0.8:
            if state and hasattr(state, 'target_length'):
                suggestions.append(f"建议调整内容长度至{state.target_length}词左右")
        
        if keywords < 0.6:
            if state and hasattr(state, 'keywords'):
                suggestions.append(f"建议增加关键词使用：{', '.join(state.keywords[:3])}")
        
        return suggestions
    
    async def _seo_analysis(self, preprocessed: Dict[str, Any], state: UnifiedState) -> Dict[str, Any]:
        """SEO分析"""
        
        content = preprocessed["cleaned_content"]
        structure = preprocessed["structure"]
        
        # 标题SEO检查
        title_seo = self._analyze_title_seo(structure.get("titles", []), state.keywords)
        
        # 关键词密度分析
        keyword_density = self._analyze_keyword_density_seo(content, state.keywords)
        
        # 内容长度SEO评估
        length_seo = self._analyze_length_seo(preprocessed["word_count"])
        
        # 内部结构SEO
        structure_seo = self._analyze_structure_seo(structure)
        
        # 计算SEO总分
        seo_score = (
            title_seo["score"] * 0.3 +
            keyword_density["score"] * 0.3 +
            length_seo["score"] * 0.2 +
            structure_seo["score"] * 0.2
        )
        
        seo_analysis = {
            "seo_score": seo_score,
            "title_seo": title_seo,
            "keyword_density": keyword_density,
            "length_seo": length_seo,
            "structure_seo": structure_seo,
            "seo_suggestions": self._generate_seo_suggestions(
                title_seo, keyword_density, length_seo, structure_seo, state
            )
        }
        
        return seo_analysis
    
    def _analyze_title_seo(self, titles: List[str], keywords: List[str]) -> Dict[str, Any]:
        """分析标题SEO"""
        
        if not titles:
            return {"score": 0.0, "issues": ["缺少标题"]}
        
        main_title = titles[0] if titles else ""
        score = 0.0
        issues = []
        
        # 检查标题长度
        title_length = len(main_title)
        if 30 <= title_length <= 60:
            score += 0.4
        else:
            issues.append("标题长度建议在30-60字符之间")
        
        # 检查关键词包含
        if keywords:
            main_keyword = keywords[0]
            if main_keyword.lower() in main_title.lower():
                score += 0.6
            else:
                issues.append(f"标题建议包含主要关键词：{main_keyword}")
        
        return {
            "score": score,
            "main_title": main_title,
            "title_length": title_length,
            "issues": issues
        }
    
    def _analyze_keyword_density_seo(self, content: str, keywords: List[str]) -> Dict[str, Any]:
        """分析关键词密度SEO"""
        
        if not keywords:
            return {"score": 1.0, "density": 0.0}
        
        content_lower = content.lower()
        total_words = len(content.split())
        
        keyword_densities = {}
        total_density = 0.0
        
        for keyword in keywords:
            keyword_lower = keyword.lower()
            occurrences = content_lower.count(keyword_lower)
            density = occurrences / total_words if total_words > 0 else 0
            keyword_densities[keyword] = density
            total_density += density
        
        # SEO最佳关键词密度为1-3%
        score = 0.0
        if 0.01 <= total_density <= 0.03:
            score = 1.0
        elif total_density < 0.01:
            score = total_density / 0.01
        else:
            score = max(0.3, 1.0 - (total_density - 0.03) * 10)
        
        return {
            "score": score,
            "total_density": total_density,
            "keyword_densities": keyword_densities,
            "optimal_range": "1-3%"
        }
    
    def _analyze_length_seo(self, word_count: int) -> Dict[str, Any]:
        """分析长度SEO"""
        
        # SEO最佳文章长度通常为1000-2500词
        score = 0.0
        
        if 1000 <= word_count <= 2500:
            score = 1.0
        elif word_count < 1000:
            score = word_count / 1000
        else:
            score = max(0.5, 1.0 - (word_count - 2500) / 2500)
        
        return {
            "score": score,
            "word_count": word_count,
            "optimal_range": "1000-2500词"
        }
    
    def _analyze_structure_seo(self, structure: Dict[str, Any]) -> Dict[str, Any]:
        """分析结构SEO"""
        
        score = 0.0
        
        # 标题结构
        if structure.get("has_title"):
            score += 0.3
        
        # 子标题
        if structure.get("title_count", 0) > 1:
            score += 0.3
        
        # 列表结构
        if structure.get("has_lists"):
            score += 0.2
        
        # 格式化
        if structure.get("has_formatting"):
            score += 0.2
        
        return {
            "score": score,
            "title_structure": structure.get("title_count", 0),
            "has_lists": structure.get("has_lists", False),
            "has_formatting": structure.get("has_formatting", False)
        }
    
    def _generate_seo_suggestions(self, title_seo, keyword_density, length_seo, structure_seo, state) -> List[str]:
        """生成SEO建议"""
        
        suggestions = []
        
        if title_seo["score"] < 0.8:
            suggestions.extend(title_seo.get("issues", []))
        
        if keyword_density["score"] < 0.8:
            if keyword_density["total_density"] < 0.01:
                suggestions.append("建议增加关键词使用频率")
            else:
                suggestions.append("建议降低关键词密度，避免过度优化")
        
        if length_seo["score"] < 0.8:
            suggestions.append("建议调整文章长度至SEO最佳范围")
        
        if structure_seo["score"] < 0.8:
            suggestions.append("建议优化文章结构，添加子标题和列表")
        
        return suggestions
    
    async def _generate_comprehensive_report(self, basic_analysis, quality_analysis, seo_analysis, state) -> Dict[str, Any]:
        """生成综合分析报告"""
        
        # 计算总体评分
        overall_score = (
            quality_analysis["overall_quality"] * 0.6 +
            seo_analysis["seo_score"] * 0.4
        )
        
        # 收集所有问题和建议
        all_issues = []
        all_suggestions = []
        
        all_issues.extend(quality_analysis.get("quality_issues", []))
        all_suggestions.extend(quality_analysis.get("improvement_suggestions", []))
        all_suggestions.extend(seo_analysis.get("seo_suggestions", []))
        
        # 优先级排序
        prioritized_suggestions = self._prioritize_suggestions(all_suggestions, overall_score)
        
        comprehensive_report = {
            "overall_score": overall_score,
            "grade": self._calculate_grade(overall_score),
            "summary": self._create_analysis_summary(basic_analysis, quality_analysis, seo_analysis),
            "strengths": self._identify_strengths(basic_analysis, quality_analysis, seo_analysis),
            "weaknesses": all_issues,
            "suggestions": prioritized_suggestions,
            "detailed_scores": {
                "content_quality": quality_analysis["overall_quality"],
                "seo_optimization": seo_analysis["seo_score"],
                "readability": quality_analysis["readability_score"],
                "structure": quality_analysis["structure_score"],
                "keyword_coverage": quality_analysis["keyword_coverage"]
            },
            "metrics_summary": {
                "word_count": basic_analysis["basic_stats"]["word_count"],
                "reading_time": basic_analysis["basic_stats"]["reading_time_minutes"],
                "paragraph_count": basic_analysis["basic_stats"]["paragraph_count"],
                "sentence_count": basic_analysis["basic_stats"]["sentence_count"],
                "keywords_covered": basic_analysis["keywords"]["keywords_covered"]
            },
            "generated_at": time.time()
        }
        
        return comprehensive_report
    
    def _calculate_grade(self, score: float) -> str:
        """计算等级"""
        
        if score >= 0.9:
            return "A+"
        elif score >= 0.8:
            return "A"
        elif score >= 0.7:
            return "B"
        elif score >= 0.6:
            return "C"
        else:
            return "D"
    
    def _create_analysis_summary(self, basic_analysis, quality_analysis, seo_analysis) -> str:
        """创建分析摘要"""
        
        word_count = basic_analysis["basic_stats"]["word_count"]
        quality_score = quality_analysis["overall_quality"]
        seo_score = seo_analysis["seo_score"]
        
        summary = f"本文共{word_count}词，质量评分{quality_score:.1%}，SEO优化程度{seo_score:.1%}。"
        
        if quality_score >= 0.8:
            summary += "内容质量优秀。"
        elif quality_score >= 0.6:
            summary += "内容质量良好，有改进空间。"
        else:
            summary += "内容质量需要显著改进。"
        
        return summary
    
    def _identify_strengths(self, basic_analysis, quality_analysis, seo_analysis) -> List[str]:
        """识别优势"""
        
        strengths = []
        
        if quality_analysis["completeness_score"] >= 0.8:
            strengths.append("内容完整性良好")
        
        if quality_analysis["structure_score"] >= 0.8:
            strengths.append("文章结构清晰")
        
        if quality_analysis["readability_score"] >= 0.8:
            strengths.append("可读性优秀")
        
        if seo_analysis["seo_score"] >= 0.8:
            strengths.append("SEO优化到位")
        
        if basic_analysis["vocabulary"]["vocabulary_diversity"] >= 0.5:
            strengths.append("词汇丰富度高")
        
        return strengths
    
    def _prioritize_suggestions(self, suggestions: List[str], overall_score: float) -> List[str]:
        """优先级排序建议"""
        
        # 根据整体评分确定建议的紧急程度
        if overall_score < 0.6:
            # 低分情况，优先结构和完整性
            priority_keywords = ["结构", "完整", "引言", "结论"]
        elif overall_score < 0.8:
            # 中等分数，优先细节优化
            priority_keywords = ["关键词", "可读性", "长度"]
        else:
            # 高分情况，优先SEO优化
            priority_keywords = ["SEO", "标题", "密度"]
        
        # 简单的优先级排序
        prioritized = []
        remaining = []
        
        for suggestion in suggestions:
            if any(keyword in suggestion for keyword in priority_keywords):
                prioritized.append(suggestion)
            else:
                remaining.append(suggestion)
        
        return prioritized + remaining
    
    async def _apply_optimizations(self, content: str, analysis_report: Dict[str, Any]) -> str:
        """应用优化建议"""
        
        optimized_content = content
        suggestions = analysis_report.get("suggestions", [])
        
        # 简单的优化应用
        # 实际实现中会根据具体建议进行更精确的优化
        
        for suggestion in suggestions[:3]:  # 应用前3个建议
            if "关键词" in suggestion and "增加" in suggestion:
                # 添加关键词使用
                optimized_content = self._enhance_keyword_usage(optimized_content)
            elif "结构" in suggestion:
                # 优化结构
                optimized_content = self._improve_structure(optimized_content)
            elif "可读性" in suggestion:
                # 改善可读性
                optimized_content = self._improve_readability(optimized_content)
        
        return optimized_content
    
    def _enhance_keyword_usage(self, content: str) -> str:
        """增强关键词使用"""
        # 简单实现：在适当位置添加关键词相关内容
        return content
    
    def _improve_structure(self, content: str) -> str:
        """改善结构"""
        # 简单实现：添加段落分隔
        return content
    
    def _improve_readability(self, content: str) -> str:
        """改善可读性"""
        # 简单实现：优化句子长度
        return content
    
    async def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        
        base_health = await super().health_check()
        
        analysis_specific_health = {
            "analysis_config": self.analysis_config,
            "quality_threshold": self.config.get("quality_threshold"),
            "analysis_tools_available": len([tool for tool in self.tools.values() if tool is not None])
        }
        
        return {**base_health, **analysis_specific_health}