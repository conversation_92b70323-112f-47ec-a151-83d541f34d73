"""
Enhanced Article Adapter - MediaAgent V2 增强文章适配器

这个适配器将现有的Generator系统平滑集成到新的Agent V2架构中，
提供向后兼容性的同时引入新的增强功能。
"""

import asyncio
import logging
from typing import Dict, Any, List, Optional, AsyncIterator, Union
import time
import json

from ..core.models import UnifiedState, AgentResponse, TaskType, AgentStatus
from ..core.base_agent import BaseAgentV2
from ..agents.article_agent import ArticleAgent
from ..agents.research_agent import ResearchAgent
from ..agents.analysis_agent import AnalysisAgent

# 导入现有Generator系统组件
try:
    from ...services.generator.article_processor import ArticleProcessor
    from ...services.generator.state import ArticleState
    GENERATOR_AVAILABLE = True
except ImportError:
    GENERATOR_AVAILABLE = False
    logging.warning("Generator system not available, using V2 agents only")

logger = logging.getLogger(__name__)


class EnhancedArticleAdapter(BaseAgentV2):
    """
    增强文章适配器
    
    职责：
    - 桥接新旧两套系统
    - 提供统一的文章生成接口
    - 平滑迁移和向后兼容
    - 智能路由和fallback机制
    """
    
    def __init__(self, config: Dict[str, Any] = None):
        super().__init__(
            name="enhanced_article_adapter",
            description="增强文章适配器，桥接V1和V2系统",
            supported_tasks=[TaskType.ARTICLE_GENERATION, TaskType.EDITING],
            config=config or {}
        )
        
        # 系统配置
        self.adapter_config = {
            "use_v2_by_default": True,                    # 默认使用V2系统
            "enable_fallback": True,                      # 启用fallback机制
            "enable_quality_comparison": True,            # 启用质量对比
            "enable_hybrid_mode": False,                  # 混合模式（同时使用两套系统）
            "migration_mode": "gradual",                  # 迁移模式：gradual/immediate
            "quality_threshold": 0.8                      # 质量阈值
        }
        
        # 性能和质量统计
        self.performance_stats = {
            "v1_usage": 0,
            "v2_usage": 0,
            "fallback_usage": 0,
            "quality_improvements": 0,
            "total_requests": 0
        }
        
        # 初始化V2 Agent
        self.article_agent = ArticleAgent()
        self.research_agent = ResearchAgent()
        self.analysis_agent = AnalysisAgent()
        
        # 初始化Generator系统（如果可用）
        self.generator_processor = None
        if GENERATOR_AVAILABLE:
            try:
                self.generator_processor = ArticleProcessor()
                logger.info("Generator system initialized successfully")
            except Exception as e:
                logger.warning(f"Failed to initialize Generator system: {e}")
                GENERATOR_AVAILABLE = False
        
        logger.info("Enhanced Article Adapter initialized")
    
    def _initialize_tools(self):
        """初始化工具"""
        
        self.tools = {
            "quality_comparator": None,   # 质量对比工具
            "system_router": None,        # 系统路由工具
            "performance_monitor": None   # 性能监控工具
        }
    
    async def process(self, state: UnifiedState) -> AsyncIterator[AgentResponse]:
        """处理文章生成请求"""
        
        logger.info(f"Enhanced adapter processing request for session {state.session_id}")
        
        try:
            # 更新统计
            self.performance_stats["total_requests"] += 1
            
            # 阶段1: 智能路由决策
            yield AgentResponse(
                success=True,
                content="分析请求，选择最佳处理方案...",
                is_partial=True,
                progress=0.05,
                current_stage="智能路由"
            )
            
            routing_decision = await self._make_routing_decision(state)
            state.shared_context["routing_decision"] = routing_decision
            
            yield AgentResponse(
                success=True,
                content=f"选择使用{routing_decision['system']}系统处理",
                is_partial=True,
                progress=0.1,
                current_stage="路由完成",
                data={"routing": routing_decision}
            )
            
            # 阶段2: 执行主要处理
            if routing_decision["system"] == "v2":
                async for response in self._process_with_v2_system(state):
                    yield response
            elif routing_decision["system"] == "v1" and GENERATOR_AVAILABLE:
                async for response in self._process_with_v1_system(state):
                    yield response
            elif routing_decision["system"] == "hybrid":
                async for response in self._process_with_hybrid_mode(state):
                    yield response
            else:
                # Fallback到V2系统
                logger.warning("Falling back to V2 system")
                self.performance_stats["fallback_usage"] += 1
                async for response in self._process_with_v2_system(state):
                    yield response
            
        except Exception as e:
            logger.error(f"Enhanced adapter failed: {e}")
            
            # 尝试fallback
            if self.adapter_config["enable_fallback"]:
                logger.info("Attempting fallback processing")
                try:
                    async for response in self._fallback_processing(state):
                        yield response
                except Exception as fallback_error:
                    logger.error(f"Fallback also failed: {fallback_error}")
                    yield AgentResponse(
                        success=False,
                        content="文章生成失败，所有处理方案均不可用",
                        errors=[str(e), str(fallback_error)],
                        current_stage="错误"
                    )
            else:
                yield AgentResponse(
                    success=False,
                    content="文章生成失败",
                    errors=[str(e)],
                    current_stage="错误"
                )
    
    async def execute(self, task: Dict[str, Any], state: UnifiedState) -> AgentResponse:
        """兼容性方法 - 支持Planning Agent调用"""
        
        # 收集所有流式响应
        responses = []
        async for response in self.process(state):
            responses.append(response)
        
        # 返回最后一个响应（通常是最终结果）
        return responses[-1] if responses else AgentResponse(
            success=False,
            errors=["No response generated"]
        )
    
    async def _make_routing_decision(self, state: UnifiedState) -> Dict[str, Any]:
        """智能路由决策"""
        
        decision = {
            "system": "v2",  # 默认选择
            "confidence": 0.8,
            "reasons": [],
            "fallback_plan": "v2"
        }
        
        # 检查V1系统可用性
        v1_available = GENERATOR_AVAILABLE and self.generator_processor is not None
        
        # 决策逻辑
        if self.adapter_config["use_v2_by_default"]:
            decision["system"] = "v2"
            decision["reasons"].append("默认使用V2系统")
            self.performance_stats["v2_usage"] += 1
        elif v1_available and self.adapter_config["migration_mode"] == "gradual":
            # 渐进迁移模式：根据任务复杂度决策
            complexity = self._assess_task_complexity(state)
            
            if complexity == "simple" and v1_available:
                decision["system"] = "v1"
                decision["reasons"].append("简单任务使用V1系统")
                self.performance_stats["v1_usage"] += 1
            else:
                decision["system"] = "v2"
                decision["reasons"].append("复杂任务使用V2系统")
                self.performance_stats["v2_usage"] += 1
        
        # 混合模式检查
        if (self.adapter_config["enable_hybrid_mode"] and 
            v1_available and 
            self._should_use_hybrid_mode(state)):
            decision["system"] = "hybrid"
            decision["reasons"].append("使用混合模式以获得最佳质量")
        
        # 设置fallback计划
        if decision["system"] == "v1" and not v1_available:
            decision["system"] = "v2"
            decision["fallback_plan"] = "v2"
            decision["reasons"].append("V1系统不可用，使用V2系统")
        
        return decision
    
    def _assess_task_complexity(self, state: UnifiedState) -> str:
        """评估任务复杂度"""
        
        complexity_score = 0
        
        # 基于文本长度
        if len(state.topic) + len(state.requirements) > 500:
            complexity_score += 2
        
        # 基于关键词数量
        if len(state.keywords) > 10:
            complexity_score += 2
        
        # 基于目标长度
        if state.target_length > 5000:
            complexity_score += 1
        
        # 基于特殊要求
        if any(word in state.requirements.lower() for word in ["深入", "专业", "技术", "详细"]):
            complexity_score += 1
        
        if complexity_score >= 4:
            return "complex"
        elif complexity_score >= 2:
            return "medium"
        else:
            return "simple"
    
    def _should_use_hybrid_mode(self, state: UnifiedState) -> bool:
        """判断是否应该使用混合模式"""
        
        # 质量要求高的任务使用混合模式
        if any(word in state.requirements.lower() for word in ["高质量", "专业", "权威"]):
            return True
        
        # 长文章使用混合模式
        if state.target_length > 3000:
            return True
        
        # 关键词较多的任务使用混合模式
        if len(state.keywords) > 5:
            return True
        
        return False
    
    async def _process_with_v2_system(self, state: UnifiedState) -> AsyncIterator[AgentResponse]:
        """使用V2系统处理"""
        
        logger.info("Processing with V2 system")
        
        # 使用V2系统的Article Agent
        async for response in self.article_agent.process(state):
            # 调整进度以留出后处理空间
            if response.progress < 1.0:
                response.progress = 0.1 + response.progress * 0.8
            
            yield response
        
        # 最后的质量检查和优化
        if state.output_data.get("article_content"):
            yield AgentResponse(
                success=True,
                content="进行最终质量检查和优化...",
                is_partial=True,
                progress=0.95,
                current_stage="质量优化"
            )
            
            final_optimization = await self._final_quality_optimization(state)
            
            yield AgentResponse(
                success=True,
                content="V2系统处理完成！",
                is_partial=False,
                progress=1.0,
                current_stage="完成",
                data=final_optimization
            )
    
    async def _process_with_v1_system(self, state: UnifiedState) -> AsyncIterator[AgentResponse]:
        """使用V1系统处理"""
        
        logger.info("Processing with V1 (Generator) system")
        
        try:
            # 转换状态格式
            v1_state = await self._convert_state_to_v1(state)
            
            yield AgentResponse(
                success=True,
                content="使用Generator系统开始处理...",
                is_partial=True,
                progress=0.2,
                current_stage="V1系统处理"
            )
            
            # 使用Generator系统处理
            result = await self._execute_v1_generation(v1_state)
            
            yield AgentResponse(
                success=True,
                content="Generator系统处理完成，转换结果格式...",
                is_partial=True,
                progress=0.8,
                current_stage="结果转换"
            )
            
            # 转换结果格式
            converted_result = await self._convert_v1_result_to_v2(result, state)
            
            yield AgentResponse(
                success=True,
                content="V1系统处理完成！",
                is_partial=False,
                progress=1.0,
                current_stage="完成",
                data=converted_result
            )
            
        except Exception as e:
            logger.error(f"V1 system processing failed: {e}")
            yield AgentResponse(
                success=False,
                content="V1系统处理失败",
                errors=[str(e)],
                current_stage="错误"
            )
    
    async def _process_with_hybrid_mode(self, state: UnifiedState) -> AsyncIterator[AgentResponse]:
        """使用混合模式处理"""
        
        logger.info("Processing with hybrid mode")
        
        yield AgentResponse(
            success=True,
            content="启动混合模式，同时使用V1和V2系统...",
            is_partial=True,
            progress=0.1,
            current_stage="混合模式启动"
        )
        
        # 并行执行两套系统
        v1_task = asyncio.create_task(self._execute_v1_generation_hybrid(state))
        v2_task = asyncio.create_task(self._execute_v2_generation_hybrid(state))
        
        yield AgentResponse(
            success=True,
            content="正在并行生成多个版本...",
            is_partial=True,
            progress=0.5,
            current_stage="并行生成"
        )
        
        # 等待两个任务完成
        v1_result, v2_result = await asyncio.gather(v1_task, v2_task, return_exceptions=True)
        
        yield AgentResponse(
            success=True,
            content="对比和融合不同版本的结果...",
            is_partial=True,
            progress=0.8,
            current_stage="结果融合"
        )
        
        # 对比和融合结果
        final_result = await self._compare_and_merge_results(v1_result, v2_result, state)
        
        yield AgentResponse(
            success=True,
            content="混合模式处理完成！",
            is_partial=False,
            progress=1.0,
            current_stage="完成",
            data=final_result
        )
    
    async def _fallback_processing(self, state: UnifiedState) -> AsyncIterator[AgentResponse]:
        """Fallback处理"""
        
        logger.info("Executing fallback processing")
        
        yield AgentResponse(
            success=True,
            content="启动Fallback模式...",
            is_partial=True,
            progress=0.1,
            current_stage="Fallback启动"
        )
        
        # 简化的文章生成逻辑
        basic_article = await self._generate_basic_article(state)
        
        yield AgentResponse(
            success=True,
            content="Fallback处理完成",
            is_partial=False,
            progress=1.0,
            current_stage="Fallback完成",
            data={"article": basic_article, "mode": "fallback"}
        )
    
    async def _convert_state_to_v1(self, state: UnifiedState) -> 'ArticleState':
        """转换状态到V1格式"""
        
        if not GENERATOR_AVAILABLE:
            raise Exception("Generator system not available")
        
        # 创建V1状态对象
        v1_state = ArticleState(
            topic=state.topic,
            requirements=state.requirements,
            keywords=state.keywords,
            target_length=state.target_length,
            language=state.language,
            tone=state.tone
        )
        
        return v1_state
    
    async def _execute_v1_generation(self, v1_state: 'ArticleState') -> Dict[str, Any]:
        """执行V1生成"""
        
        if not self.generator_processor:
            raise Exception("Generator processor not available")
        
        # 调用Generator系统
        result = await self.generator_processor.process_article(v1_state)
        
        return result
    
    async def _convert_v1_result_to_v2(self, v1_result: Dict[str, Any], state: UnifiedState) -> Dict[str, Any]:
        """转换V1结果到V2格式"""
        
        # 转换结果格式以匹配V2期望
        v2_result = {
            "article": {
                "title": v1_result.get("title", "无标题"),
                "content": v1_result.get("content", ""),
                "metadata": {
                    "generated_by": "v1_generator",
                    "word_count": len(v1_result.get("content", "").split()),
                    "language": state.language,
                    "topic": state.topic
                }
            },
            "generation_info": {
                "system_used": "v1",
                "processing_time": v1_result.get("processing_time", 0),
                "quality_score": v1_result.get("quality_score", 0.8)
            }
        }
        
        return v2_result
    
    async def _execute_v1_generation_hybrid(self, state: UnifiedState) -> Dict[str, Any]:
        """混合模式下执行V1生成"""
        
        try:
            v1_state = await self._convert_state_to_v1(state)
            result = await self._execute_v1_generation(v1_state)
            return await self._convert_v1_result_to_v2(result, state)
        except Exception as e:
            logger.error(f"V1 generation in hybrid mode failed: {e}")
            return {"error": str(e), "system": "v1"}
    
    async def _execute_v2_generation_hybrid(self, state: UnifiedState) -> Dict[str, Any]:
        """混合模式下执行V2生成"""
        
        try:
            # 创建V2状态副本避免冲突
            v2_state = UnifiedState(
                session_id=f"{state.session_id}_v2",
                topic=state.topic,
                requirements=state.requirements,
                keywords=state.keywords,
                target_length=state.target_length,
                language=state.language,
                tone=state.tone
            )
            
            # 收集V2结果
            final_response = None
            async for response in self.article_agent.process(v2_state):
                final_response = response
            
            return final_response.data if final_response and final_response.success else {"error": "V2 generation failed", "system": "v2"}
            
        except Exception as e:
            logger.error(f"V2 generation in hybrid mode failed: {e}")
            return {"error": str(e), "system": "v2"}
    
    async def _compare_and_merge_results(self, v1_result: Dict[str, Any], v2_result: Dict[str, Any], state: UnifiedState) -> Dict[str, Any]:
        """对比和融合结果"""
        
        # 检查结果有效性
        v1_valid = isinstance(v1_result, dict) and "error" not in v1_result
        v2_valid = isinstance(v2_result, dict) and "error" not in v2_result
        
        if v1_valid and v2_valid:
            # 两个结果都有效，进行质量对比
            quality_comparison = await self._compare_quality(v1_result, v2_result, state)
            
            if quality_comparison["winner"] == "v1":
                final_result = v1_result
                final_result["comparison_info"] = quality_comparison
                self.performance_stats["quality_improvements"] += 1
            else:
                final_result = v2_result
                final_result["comparison_info"] = quality_comparison
            
            final_result["generation_mode"] = "hybrid"
            
        elif v1_valid:
            final_result = v1_result
            final_result["generation_mode"] = "hybrid_v1_only"
            
        elif v2_valid:
            final_result = v2_result
            final_result["generation_mode"] = "hybrid_v2_only"
            
        else:
            # 两个都失败，返回错误
            final_result = {
                "error": "Both V1 and V2 systems failed",
                "v1_error": v1_result.get("error", "Unknown error"),
                "v2_error": v2_result.get("error", "Unknown error"),
                "generation_mode": "hybrid_failed"
            }
        
        return final_result
    
    async def _compare_quality(self, v1_result: Dict[str, Any], v2_result: Dict[str, Any], state: UnifiedState) -> Dict[str, Any]:
        """质量对比"""
        
        # 提取内容
        v1_content = v1_result.get("article", {}).get("content", "")
        v2_content = v2_result.get("article", {}).get("content", "")
        
        # 基础质量指标对比
        v1_score = await self._calculate_quality_score(v1_content, state)
        v2_score = await self._calculate_quality_score(v2_content, state)
        
        comparison = {
            "v1_score": v1_score,
            "v2_score": v2_score,
            "winner": "v1" if v1_score > v2_score else "v2",
            "score_difference": abs(v1_score - v2_score),
            "comparison_criteria": [
                "内容长度符合度",
                "关键词覆盖率",
                "结构完整性",
                "可读性"
            ]
        }
        
        return comparison
    
    async def _calculate_quality_score(self, content: str, state: UnifiedState) -> float:
        """计算质量分数"""
        
        score = 0.0
        
        # 长度符合度
        word_count = len(content.split())
        if state.target_length > 0:
            length_ratio = word_count / state.target_length
            if 0.8 <= length_ratio <= 1.2:
                score += 0.3
            else:
                score += max(0, 0.3 - abs(length_ratio - 1.0) * 0.5)
        
        # 关键词覆盖
        if state.keywords:
            content_lower = content.lower()
            covered_keywords = sum(1 for kw in state.keywords if kw.lower() in content_lower)
            keyword_coverage = covered_keywords / len(state.keywords)
            score += keyword_coverage * 0.3
        
        # 结构完整性
        has_title = content.startswith('#') or 'title' in content.lower()
        has_sections = content.count('##') >= 2
        has_conclusion = any(word in content.lower() for word in ['总结', '结论', '总之'])
        
        structure_score = (has_title + has_sections + has_conclusion) / 3
        score += structure_score * 0.2
        
        # 内容丰富度
        unique_words = len(set(content.lower().split()))
        richness = min(1.0, unique_words / max(1, word_count * 0.5))
        score += richness * 0.2
        
        return min(1.0, score)
    
    async def _final_quality_optimization(self, state: UnifiedState) -> Dict[str, Any]:
        """最终质量优化"""
        
        content = state.output_data.get("article_content", "")
        
        if not content:
            return {"optimization": "no_content", "improvements": []}
        
        # 使用Analysis Agent进行质量检查
        optimization_result = await self.analysis_agent.optimize_content(content, {
            "requirements": state.requirements,
            "keywords": state.keywords,
            "target_length": state.target_length
        })
        
        # 更新状态中的内容
        if optimization_result.get("optimized_content"):
            state.output_data["article_content"] = optimization_result["optimized_content"]
        
        return {
            "optimization": "completed",
            "improvements": optimization_result.get("improvements", []),
            "quality_score": optimization_result.get("quality_score", 0.8),
            "system_used": "v2_analysis_agent"
        }
    
    async def _generate_basic_article(self, state: UnifiedState) -> Dict[str, Any]:
        """生成基础文章（Fallback模式）"""
        
        # 非常简单的文章生成逻辑
        title = f"{state.topic}详细指南"
        
        content_parts = [
            f"# {title}\n",
            f"\n## 引言\n",
            f"{state.topic}是一个重要的话题。本文将为您详细介绍相关内容。\n",
            f"\n## 主要内容\n",
            f"关于{state.topic}，我们需要了解以下几个方面：\n"
        ]
        
        # 添加关键词相关内容
        for i, keyword in enumerate(state.keywords[:5], 1):
            content_parts.append(f"\n### {i}. {keyword}\n")
            content_parts.append(f"{keyword}是{state.topic}的重要组成部分。\n")
        
        content_parts.append(f"\n## 总结\n")
        content_parts.append(f"通过本文的介绍，相信您对{state.topic}有了更深入的了解。\n")
        
        content = "".join(content_parts)
        
        return {
            "title": title,
            "content": content,
            "metadata": {
                "generated_by": "fallback_mode",
                "word_count": len(content.split()),
                "quality_note": "基础版本，建议使用完整系统重新生成"
            }
        }
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计"""
        
        stats = self.performance_stats.copy()
        
        if stats["total_requests"] > 0:
            stats["v1_usage_rate"] = stats["v1_usage"] / stats["total_requests"]
            stats["v2_usage_rate"] = stats["v2_usage"] / stats["total_requests"]
            stats["fallback_rate"] = stats["fallback_usage"] / stats["total_requests"]
            stats["quality_improvement_rate"] = stats["quality_improvements"] / stats["total_requests"]
        
        return stats
    
    def update_config(self, new_config: Dict[str, Any]):
        """更新配置"""
        
        self.adapter_config.update(new_config)
        logger.info(f"Adapter configuration updated: {new_config}")
    
    async def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        
        base_health = await super().health_check()
        
        adapter_health = {
            "adapter_config": self.adapter_config,
            "performance_stats": self.get_performance_stats(),
            "v1_system_available": GENERATOR_AVAILABLE and self.generator_processor is not None,
            "v2_system_available": True,
            "subsystems_health": {
                "article_agent": await self.article_agent.health_check(),
                "research_agent": await self.research_agent.health_check(),
                "analysis_agent": await self.analysis_agent.health_check()
            }
        }
        
        return {**base_health, **adapter_health}