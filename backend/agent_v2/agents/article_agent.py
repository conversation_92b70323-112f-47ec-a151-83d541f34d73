"""
Article Agent - MediaAgent V2 文章生成Agent
"""

import asyncio
import logging
from typing import Dict, Any, List, Optional, AsyncIterator
import time

from ..core.base_agent import BaseAgentV2
from ..core.models import UnifiedState, AgentResponse, TaskType, AgentStatus

logger = logging.getLogger(__name__)


class ArticleAgent(BaseAgentV2):
    """
    文章生成Agent
    
    职责：
    - 长文章创作
    - SEO优化
    - 多语言支持
    - 质量控制
    """
    
    def __init__(self):
        super().__init__(
            name="article_agent",
            description="专业的文章生成Agent，支持长文章创作、SEO优化和多语言支持",
            supported_tasks=[TaskType.ARTICLE_GENERATION, TaskType.EDITING],
            config={
                "max_article_length": 10000,
                "min_article_length": 500,
                "default_language": "zh-CN"
            }
        )
        
        # 文章生成配置
        self.generation_config = {
            "temperature": 0.7,
            "max_tokens": 4000,
            "enable_seo": True,
            "enable_quality_check": True
        }
        
        logger.info("Article Agent initialized")
    
    def _initialize_tools(self):
        """初始化工具"""
        
        # 这里会注册需要的工具
        # 实际实现中，这些工具会从工具系统中获取
        
        self.tools = {
            "content_generator": None,  # 内容生成工具
            "seo_optimizer": None,      # SEO优化工具
            "quality_checker": None,    # 质量检查工具
            "content_analyzer": None    # 内容分析工具
        }
    
    async def process(self, state: UnifiedState) -> AsyncIterator[AgentResponse]:
        """处理文章生成任务"""
        
        logger.info(f"Starting article generation for session {state.session_id}")
        
        try:
            # 阶段1: 分析需求和规划
            yield AgentResponse(
                success=True,
                content="开始分析需求和制定文章规划...",
                is_partial=True,
                progress=0.1,
                current_stage="需求分析"
            )
            
            planning_result = await self._analyze_requirements_and_plan(state)
            state.shared_context["article_plan"] = planning_result
            
            yield AgentResponse(
                success=True,
                content=f"文章规划完成：{planning_result['title']}",
                is_partial=True,
                progress=0.2,
                current_stage="规划完成",
                data={"plan": planning_result}
            )
            
            # 阶段2: 研究和信息收集
            yield AgentResponse(
                success=True,
                content="开始收集相关资料和背景信息...",
                is_partial=True,
                progress=0.3,
                current_stage="信息收集"
            )
            
            research_data = await self._conduct_research(state, planning_result)
            state.shared_context["research_data"] = research_data
            
            yield AgentResponse(
                success=True,
                content=f"信息收集完成，获得{len(research_data.get('sources', []))}个参考资料",
                is_partial=True,
                progress=0.4,
                current_stage="研究完成",
                data={"research": research_data}
            )
            
            # 阶段3: 创建详细大纲
            yield AgentResponse(
                success=True,
                content="基于研究结果创建详细大纲...",
                is_partial=True,
                progress=0.5,
                current_stage="大纲创建"
            )
            
            outline = await self._create_detailed_outline(state, planning_result, research_data)
            state.shared_context["article_outline"] = outline
            
            yield AgentResponse(
                success=True,
                content="详细大纲创建完成",
                is_partial=True,
                progress=0.6,
                current_stage="大纲完成",
                data={"outline": outline}
            )
            
            # 阶段4: 撰写文章
            yield AgentResponse(
                success=True,
                content="开始根据大纲撰写文章...",
                is_partial=True,
                progress=0.7,
                current_stage="文章撰写"
            )
            
            article_content = await self._write_article(state, outline, research_data)
            state.output_data["article_content"] = article_content
            
            yield AgentResponse(
                success=True,
                content="文章撰写完成，正在进行质量检查...",
                is_partial=True,
                progress=0.8,
                current_stage="撰写完成"
            )
            
            # 阶段5: 质量检查和优化
            quality_result = await self._quality_check_and_optimize(state, article_content)
            final_article = quality_result["optimized_content"]
            
            yield AgentResponse(
                success=True,
                content="质量检查和优化完成",
                is_partial=True,
                progress=0.9,
                current_stage="质量优化"
            )
            
            # 阶段6: 最终处理
            final_result = await self._finalize_article(state, final_article, quality_result)
            
            # 返回最终结果
            yield AgentResponse(
                success=True,
                content="文章生成完成！",
                is_partial=False,
                progress=1.0,
                current_stage="完成",
                data={
                    "article": final_result,
                    "quality_metrics": quality_result.get("quality_metrics", {}),
                    "word_count": len(final_article.split()),
                    "reading_time": self._estimate_reading_time(final_article)
                }
            )
            
        except Exception as e:
            logger.error(f"Article generation failed: {e}")
            yield AgentResponse(
                success=False,
                content="文章生成失败",
                errors=[str(e)],
                current_stage="错误"
            )
    
    async def execute(self, task: Dict[str, Any], state: UnifiedState) -> AgentResponse:
        """兼容性方法 - 支持Planning Agent调用"""
        
        # 从task中提取参数并更新state
        if "stage_name" in task:
            state.current_stage = task["stage_name"]
        
        # 收集所有流式响应
        responses = []
        async for response in self.process(state):
            responses.append(response)
        
        # 返回最后一个响应（通常是最终结果）
        return responses[-1] if responses else AgentResponse(
            success=False,
            errors=["No response generated"]
        )
    
    async def _analyze_requirements_and_plan(self, state: UnifiedState) -> Dict[str, Any]:
        """分析需求和制定计划"""
        
        # 分析用户需求
        topic = state.topic
        requirements = state.requirements
        keywords = state.keywords
        target_length = state.target_length
        
        # 生成文章标题
        title = await self._generate_title(topic, keywords)
        
        # 制定文章计划
        plan = {
            "title": title,
            "target_length": target_length,
            "estimated_sections": max(3, target_length // 800),  # 每800字一个章节
            "primary_keywords": keywords[:5] if len(keywords) > 5 else keywords,
            "tone": state.tone,
            "language": state.language,
            "seo_focus": keywords[0] if keywords else topic.split()[0],
            "content_structure": "introduction-body-conclusion"
        }
        
        return plan
    
    async def _generate_title(self, topic: str, keywords: List[str]) -> str:
        """生成文章标题"""
        
        # 简单的标题生成逻辑
        # 实际实现中会使用LLM生成更好的标题
        
        if keywords:
            primary_keyword = keywords[0]
            title = f"{primary_keyword}：{topic}全面解析"
        else:
            title = f"{topic}详细指南"
        
        return title
    
    async def _conduct_research(self, state: UnifiedState, plan: Dict[str, Any]) -> Dict[str, Any]:
        """进行研究和信息收集"""
        
        # 模拟研究过程
        # 实际实现中会使用搜索工具收集信息
        
        research_data = {
            "sources": [
                {"url": "https://example.com/1", "title": f"关于{state.topic}的专业分析"},
                {"url": "https://example.com/2", "title": f"{state.topic}最新趋势"}
            ],
            "key_facts": [
                f"{state.topic}是一个重要的话题",
                f"近年来{state.topic}发展迅速"
            ],
            "statistics": {
                "market_size": "未知",
                "growth_rate": "未知"
            },
            "related_topics": [
                f"{state.topic}应用",
                f"{state.topic}发展趋势"
            ]
        }
        
        return research_data
    
    async def _create_detailed_outline(
        self, 
        state: UnifiedState, 
        plan: Dict[str, Any], 
        research_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """创建详细大纲"""
        
        sections = []
        
        # 引言
        sections.append({
            "title": "引言",
            "content_points": [
                f"什么是{state.topic}",
                "为什么这个话题重要",
                "本文将要探讨的内容"
            ],
            "estimated_length": 200
        })
        
        # 主体章节
        estimated_sections = plan.get("estimated_sections", 3)
        section_length = (state.target_length - 400) // estimated_sections  # 减去引言和结论
        
        for i in range(estimated_sections):
            sections.append({
                "title": f"{state.topic}的{['基础概念', '核心原理', '实际应用', '发展趋势', '最佳实践'][i % 5]}",
                "content_points": [
                    f"要点 {i+1}.1",
                    f"要点 {i+1}.2",
                    f"要点 {i+1}.3"
                ],
                "estimated_length": section_length
            })
        
        # 结论
        sections.append({
            "title": "总结",
            "content_points": [
                "主要观点总结",
                "未来展望",
                "行动建议"
            ],
            "estimated_length": 200
        })
        
        outline = {
            "title": plan["title"],
            "sections": sections,
            "total_estimated_length": sum(section["estimated_length"] for section in sections),
            "keywords_distribution": self._distribute_keywords(state.keywords, sections)
        }
        
        return outline
    
    async def _write_article(
        self, 
        state: UnifiedState, 
        outline: Dict[str, Any], 
        research_data: Dict[str, Any]
    ) -> str:
        """撰写文章"""
        
        # 模拟文章撰写
        # 实际实现中会使用LLM生成内容
        
        article_parts = []
        
        # 标题
        article_parts.append(f"# {outline['title']}\n")
        
        # 各个章节
        for section in outline["sections"]:
            article_parts.append(f"\n## {section['title']}\n")
            
            # 生成章节内容
            section_content = self._generate_section_content(section, state, research_data)
            article_parts.append(section_content)
        
        full_article = "\n".join(article_parts)
        
        # 确保达到目标长度
        if len(full_article.split()) < state.target_length * 0.8:
            full_article = await self._expand_content(full_article, state.target_length)
        
        return full_article
    
    def _generate_section_content(
        self, 
        section: Dict[str, Any], 
        state: UnifiedState, 
        research_data: Dict[str, Any]
    ) -> str:
        """生成章节内容"""
        
        # 简单的内容生成
        # 实际实现中会使用更sophisticated的方法
        
        content_parts = []
        
        for point in section["content_points"]:
            # 为每个要点生成段落
            paragraph = f"{point}是{state.topic}的重要方面。" \
                       f"根据研究，这一点对于理解{state.topic}具有重要意义。" \
                       f"我们需要从多个角度来分析这个问题，包括理论基础和实际应用。"
            
            content_parts.append(paragraph)
        
        return "\n\n".join(content_parts) + "\n"
    
    async def _expand_content(self, content: str, target_length: int) -> str:
        """扩展内容以达到目标长度"""
        
        current_length = len(content.split())
        if current_length >= target_length * 0.9:
            return content
        
        # 简单的扩展策略：添加更多细节和例子
        expanded_content = content + "\n\n### 补充说明\n\n"
        expanded_content += "为了更好地理解这个话题，我们需要考虑更多的细节和实际案例。" \
                           "这些额外的信息将帮助读者获得更全面的理解。"
        
        return expanded_content
    
    async def _quality_check_and_optimize(self, state: UnifiedState, content: str) -> Dict[str, Any]:
        """质量检查和优化"""
        
        # 进行基本的质量检查
        word_count = len(content.split())
        paragraph_count = len([p for p in content.split('\n\n') if p.strip()])
        
        # 检查关键词密度
        keyword_density = self._calculate_keyword_density(content, state.keywords)
        
        # 生成质量报告
        quality_metrics = {
            "word_count": word_count,
            "paragraph_count": paragraph_count,
            "keyword_density": keyword_density,
            "readability_score": 0.8,  # 模拟分数
            "seo_score": 0.75,         # 模拟分数
            "overall_score": 0.8
        }
        
        # 基本优化
        optimized_content = content
        
        # 如果关键词密度太低，添加一些关键词
        if keyword_density < 0.01 and state.keywords:
            optimized_content = self._enhance_keyword_usage(content, state.keywords)
        
        return {
            "optimized_content": optimized_content,
            "quality_metrics": quality_metrics,
            "optimization_notes": ["已优化关键词使用", "已检查可读性"]
        }
    
    def _calculate_keyword_density(self, content: str, keywords: List[str]) -> float:
        """计算关键词密度"""
        
        if not keywords:
            return 0.0
        
        content_lower = content.lower()
        total_words = len(content.split())
        keyword_count = 0
        
        for keyword in keywords:
            keyword_count += content_lower.count(keyword.lower())
        
        return keyword_count / total_words if total_words > 0 else 0.0
    
    def _enhance_keyword_usage(self, content: str, keywords: List[str]) -> str:
        """增强关键词使用"""
        
        # 简单的关键词优化
        # 在内容末尾添加一个包含关键词的段落
        
        if not keywords:
            return content
        
        keyword_paragraph = f"\n\n总的来说，{keywords[0]}是一个值得深入研究的话题。"
        if len(keywords) > 1:
            keyword_paragraph += f"结合{keywords[1]}等相关概念，"
        keyword_paragraph += "我们可以获得更全面的理解。"
        
        return content + keyword_paragraph
    
    async def _finalize_article(self, state: UnifiedState, article: str, quality_result: Dict[str, Any]) -> Dict[str, Any]:
        """最终处理文章"""
        
        final_article = {
            "title": self._extract_title(article),
            "content": article,
            "metadata": {
                "author": "MediaAgent AI",
                "created_at": time.time(),
                "language": state.language,
                "topic": state.topic,
                "keywords": state.keywords,
                "target_length": state.target_length,
                "actual_length": len(article.split())
            },
            "quality_info": quality_result["quality_metrics"],
            "seo_info": {
                "primary_keyword": state.keywords[0] if state.keywords else state.topic,
                "keyword_density": quality_result["quality_metrics"]["keyword_density"],
                "meta_description": self._generate_meta_description(article, state.keywords)
            }
        }
        
        return final_article
    
    def _extract_title(self, article: str) -> str:
        """从文章中提取标题"""
        
        lines = article.split('\n')
        for line in lines:
            if line.startswith('# '):
                return line[2:].strip()
        
        return "未命名文章"
    
    def _generate_meta_description(self, article: str, keywords: List[str]) -> str:
        """生成meta描述"""
        
        # 提取文章前几句作为描述
        sentences = article.replace('\n', ' ').split('。')
        
        # 找到第一个实质性句子
        for sentence in sentences:
            if len(sentence.strip()) > 20 and not sentence.strip().startswith('#'):
                description = sentence.strip()[:150]
                if keywords:
                    # 确保包含主要关键词
                    if keywords[0].lower() not in description.lower():
                        description = f"{keywords[0]}相关内容：{description}"
                return description + "..."
        
        return "专业的文章内容，提供深入的分析和见解。"
    
    def _distribute_keywords(self, keywords: List[str], sections: List[Dict[str, Any]]) -> Dict[str, List[str]]:
        """分配关键词到各个章节"""
        
        if not keywords:
            return {}
        
        distribution = {}
        keywords_per_section = max(1, len(keywords) // len(sections))
        
        for i, section in enumerate(sections):
            start_idx = i * keywords_per_section
            end_idx = min(start_idx + keywords_per_section, len(keywords))
            section_keywords = keywords[start_idx:end_idx]
            
            if section_keywords:
                distribution[section["title"]] = section_keywords
        
        return distribution
    
    def _estimate_reading_time(self, content: str) -> str:
        """估算阅读时间"""
        
        word_count = len(content.split())
        # 假设每分钟阅读200个中文字词
        reading_time = max(1, word_count // 200)
        
        return f"{reading_time}分钟阅读"
    
    async def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        
        base_health = await super().health_check()
        
        article_specific_health = {
            "generation_config": self.generation_config,
            "max_article_length": self.config.get("max_article_length"),
            "tools_available": len([tool for tool in self.tools.values() if tool is not None])
        }
        
        return {**base_health, **article_specific_health}