"""
Research Agent - MediaAgent V2 研究Agent
"""

import asyncio
import logging
from typing import Dict, Any, List, Optional, AsyncIterator
import time

from ..core.base_agent import BaseAgentV2
from ..core.models import UnifiedState, AgentResponse, TaskType, AgentStatus

logger = logging.getLogger(__name__)


class ResearchAgent(BaseAgentV2):
    """
    研究Agent
    
    职责：
    - 信息搜集和收集
    - 数据分析和整理
    - 资料筛选和验证
    - 研究报告生成
    """
    
    def __init__(self):
        super().__init__(
            name="research_agent",
            description="专业的研究Agent，负责信息收集、数据分析和研究报告生成",
            supported_tasks=[TaskType.RESEARCH, TaskType.ARTICLE_GENERATION],
            config={
                "max_sources": 20,
                "search_depth": 3,
                "quality_threshold": 0.7
            }
        )
        
        # 研究配置
        self.research_config = {
            "enable_web_search": True,
            "enable_academic_search": False,
            "enable_real_time_data": True,
            "max_search_results": 50,
            "source_verification": True
        }
        
        logger.info("Research Agent initialized")
    
    def _initialize_tools(self):
        """初始化工具"""
        
        self.tools = {
            "web_search": None,        # 网络搜索工具
            "content_extractor": None, # 内容提取工具
            "data_analyzer": None,     # 数据分析工具
            "source_validator": None,  # 来源验证工具
            "file_reader": None        # 文件读取工具
        }
    
    async def process(self, state: UnifiedState) -> AsyncIterator[AgentResponse]:
        """处理研究任务"""
        
        logger.info(f"Starting research for session {state.session_id}")
        
        try:
            # 阶段1: 研究规划
            yield AgentResponse(
                success=True,
                content="开始制定研究计划...",
                is_partial=True,
                progress=0.1,
                current_stage="研究规划"
            )
            
            research_plan = await self._create_research_plan(state)
            state.shared_context["research_plan"] = research_plan
            
            yield AgentResponse(
                success=True,
                content=f"研究计划制定完成，将从{len(research_plan['search_queries'])}个方向进行调研",
                is_partial=True,
                progress=0.2,
                current_stage="规划完成",
                data={"plan": research_plan}
            )
            
            # 阶段2: 信息搜索
            yield AgentResponse(
                success=True,
                content="开始信息搜索和收集...",
                is_partial=True,
                progress=0.3,
                current_stage="信息搜索"
            )
            
            search_results = await self._conduct_search(state, research_plan)
            state.shared_context["search_results"] = search_results
            
            yield AgentResponse(
                success=True,
                content=f"信息搜索完成，获得{len(search_results.get('sources', []))}个信息源",
                is_partial=True,
                progress=0.5,
                current_stage="搜索完成",
                data={"search_results": search_results}
            )
            
            # 阶段3: 内容提取和分析
            yield AgentResponse(
                success=True,
                content="开始提取和分析内容...",
                is_partial=True,
                progress=0.6,
                current_stage="内容分析"
            )
            
            analyzed_data = await self._analyze_content(state, search_results)
            state.shared_context["analyzed_data"] = analyzed_data
            
            yield AgentResponse(
                success=True,
                content="内容分析完成，正在整理研究结果...",
                is_partial=True,
                progress=0.8,
                current_stage="数据整理"
            )
            
            # 阶段4: 生成研究报告
            research_report = await self._generate_research_report(state, analyzed_data)
            
            # 返回最终结果
            yield AgentResponse(
                success=True,
                content="研究任务完成！",
                is_partial=False,
                progress=1.0,
                current_stage="完成",
                data={
                    "research_report": research_report,
                    "sources_count": len(search_results.get('sources', [])),
                    "key_findings": analyzed_data.get('key_findings', []),
                    "confidence_score": analyzed_data.get('confidence_score', 0.8)
                }
            )
            
        except Exception as e:
            logger.error(f"Research failed: {e}")
            yield AgentResponse(
                success=False,
                content="研究任务失败",
                errors=[str(e)],
                current_stage="错误"
            )
    
    async def execute(self, task: Dict[str, Any], state: UnifiedState) -> AgentResponse:
        """兼容性方法 - 支持Planning Agent调用"""
        
        # 从task中提取参数并更新state
        if "stage_name" in task:
            state.current_stage = task["stage_name"]
        
        # 收集所有流式响应
        responses = []
        async for response in self.process(state):
            responses.append(response)
        
        # 返回最后一个响应（通常是最终结果）
        return responses[-1] if responses else AgentResponse(
            success=False,
            errors=["No response generated"]
        )
    
    async def conduct_research(self, topic: str, keywords: List[str] = None) -> Dict[str, Any]:
        """独立的研究方法 - 供其他Agent调用"""
        
        # 创建临时状态
        temp_state = UnifiedState(
            topic=topic,
            keywords=keywords or [],
            task_type=TaskType.RESEARCH
        )
        
        # 执行研究
        final_response = None
        async for response in self.process(temp_state):
            final_response = response
        
        return final_response.data if final_response and final_response.success else {}
    
    async def _create_research_plan(self, state: UnifiedState) -> Dict[str, Any]:
        """创建研究计划"""
        
        topic = state.topic
        keywords = state.keywords
        
        # 生成搜索查询
        search_queries = []
        
        # 基础查询
        search_queries.append(topic)
        
        # 关键词相关查询
        for keyword in keywords[:5]:  # 最多5个关键词
            search_queries.append(f"{topic} {keyword}")
            search_queries.append(keyword)
        
        # 相关概念查询
        related_queries = [
            f"{topic} 定义",
            f"{topic} 发展历史",
            f"{topic} 应用案例",
            f"{topic} 最新发展",
            f"{topic} 未来趋势"
        ]
        
        search_queries.extend(related_queries)
        
        # 研究计划
        research_plan = {
            "topic": topic,
            "keywords": keywords,
            "search_queries": search_queries[:10],  # 限制查询数量
            "research_objectives": [
                "收集基础概念和定义",
                "了解发展历史和现状",
                "分析应用案例和实践",
                "探索未来趋势和发展方向"
            ],
            "expected_sources": self.config.get("max_sources", 20),
            "quality_criteria": {
                "authority": 0.8,
                "relevance": 0.9,
                "recency": 0.7
            }
        }
        
        return research_plan
    
    async def _conduct_search(self, state: UnifiedState, research_plan: Dict[str, Any]) -> Dict[str, Any]:
        """进行信息搜索"""
        
        all_sources = []
        search_summary = {
            "total_queries": len(research_plan["search_queries"]),
            "successful_queries": 0,
            "total_results": 0
        }
        
        for query in research_plan["search_queries"]:
            try:
                # 模拟搜索结果
                # 实际实现中会使用真实的搜索工具
                query_results = await self._simulate_search(query)
                
                if query_results:
                    all_sources.extend(query_results)
                    search_summary["successful_queries"] += 1
                    search_summary["total_results"] += len(query_results)
                
                # 避免过于频繁的请求
                await asyncio.sleep(0.1)
                
            except Exception as e:
                logger.warning(f"Search failed for query '{query}': {e}")
        
        # 去重和排序
        unique_sources = self._deduplicate_sources(all_sources)
        ranked_sources = self._rank_sources(unique_sources, state)
        
        # 限制结果数量
        max_sources = self.config.get("max_sources", 20)
        final_sources = ranked_sources[:max_sources]
        
        return {
            "sources": final_sources,
            "search_summary": search_summary,
            "ranking_criteria": "权威性、相关性、时效性综合评分",
            "total_unique_sources": len(unique_sources)
        }
    
    async def _simulate_search(self, query: str) -> List[Dict[str, Any]]:
        """模拟搜索 - 实际实现中会被真实搜索工具替代"""
        
        # 模拟搜索结果
        mock_results = [
            {
                "title": f"关于{query}的专业分析",
                "url": f"https://example.com/search/{hash(query) % 1000}",
                "snippet": f"这是关于{query}的详细介绍和分析。本文提供了comprehensive的视角...",
                "source": "学术网站",
                "date": "2024-01-15",
                "authority_score": 0.8,
                "relevance_score": 0.9
            },
            {
                "title": f"{query}最新发展动态",
                "url": f"https://news.example.com/{hash(query) % 1000}",
                "snippet": f"最新消息显示，{query}领域出现了重要突破...",
                "source": "新闻媒体",
                "date": "2024-01-10",
                "authority_score": 0.7,
                "relevance_score": 0.8
            }
        ]
        
        return mock_results
    
    def _deduplicate_sources(self, sources: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """去重数据源"""
        
        seen_urls = set()
        unique_sources = []
        
        for source in sources:
            url = source.get("url", "")
            if url not in seen_urls:
                seen_urls.add(url)
                unique_sources.append(source)
        
        return unique_sources
    
    def _rank_sources(self, sources: List[Dict[str, Any]], state: UnifiedState) -> List[Dict[str, Any]]:
        """对数据源进行排序"""
        
        def calculate_score(source):
            authority = source.get("authority_score", 0.5)
            relevance = source.get("relevance_score", 0.5)
            
            # 时效性评分
            import datetime
            try:
                source_date = datetime.datetime.strptime(source.get("date", "2020-01-01"), "%Y-%m-%d")
                days_old = (datetime.datetime.now() - source_date).days
                recency = max(0, 1 - days_old / 365)  # 一年内的文章权重较高
            except:
                recency = 0.3
            
            # 综合评分
            return authority * 0.4 + relevance * 0.4 + recency * 0.2
        
        # 计算评分并排序
        for source in sources:
            source["composite_score"] = calculate_score(source)
        
        return sorted(sources, key=lambda x: x["composite_score"], reverse=True)
    
    async def _analyze_content(self, state: UnifiedState, search_results: Dict[str, Any]) -> Dict[str, Any]:
        """分析搜索到的内容"""
        
        sources = search_results.get("sources", [])
        
        # 提取关键信息
        key_findings = []
        important_facts = []
        statistics = {}
        expert_opinions = []
        
        for source in sources[:10]:  # 分析前10个最重要的来源
            # 模拟内容分析
            # 实际实现中会使用NLP工具进行深度分析
            
            analysis = await self._analyze_single_source(source, state)
            
            if analysis.get("key_points"):
                key_findings.extend(analysis["key_points"])
            
            if analysis.get("facts"):
                important_facts.extend(analysis["facts"])
            
            if analysis.get("statistics"):
                statistics.update(analysis["statistics"])
            
            if analysis.get("opinions"):
                expert_opinions.extend(analysis["opinions"])
        
        # 数据聚合和去重
        unique_findings = self._deduplicate_findings(key_findings)
        verified_facts = self._verify_facts(important_facts)
        
        # 生成洞察
        insights = self._generate_insights(unique_findings, verified_facts, statistics)
        
        analyzed_data = {
            "key_findings": unique_findings[:10],  # 最重要的10个发现
            "important_facts": verified_facts[:15],  # 最重要的15个事实
            "statistics": statistics,
            "expert_opinions": expert_opinions[:8],  # 最重要的8个专家观点
            "insights": insights,
            "confidence_score": self._calculate_confidence_score(sources),
            "analysis_summary": {
                "sources_analyzed": len(sources),
                "findings_extracted": len(unique_findings),
                "facts_verified": len(verified_facts),
                "statistics_collected": len(statistics)
            }
        }
        
        return analyzed_data
    
    async def _analyze_single_source(self, source: Dict[str, Any], state: UnifiedState) -> Dict[str, Any]:
        """分析单个数据源"""
        
        # 模拟内容分析
        # 实际实现中会提取和分析真实内容
        
        topic = state.topic
        
        analysis = {
            "key_points": [
                f"{topic}的核心概念是...",
                f"{topic}在实际应用中...",
                f"关于{topic}的最新发展..."
            ],
            "facts": [
                f"{topic}起源于...",
                f"{topic}的市场规模达到..."
            ],
            "statistics": {
                f"{topic}_growth_rate": "15%",
                f"{topic}_market_size": "10亿美元"
            },
            "opinions": [
                f"专家认为{topic}将会..."
            ]
        }
        
        return analysis
    
    def _deduplicate_findings(self, findings: List[str]) -> List[str]:
        """去重发现"""
        
        # 简单的文本相似度去重
        unique_findings = []
        
        for finding in findings:
            is_duplicate = False
            for existing in unique_findings:
                # 简单的相似度检查
                if len(set(finding.split()) & set(existing.split())) > len(finding.split()) * 0.7:
                    is_duplicate = True
                    break
            
            if not is_duplicate:
                unique_findings.append(finding)
        
        return unique_findings
    
    def _verify_facts(self, facts: List[str]) -> List[str]:
        """验证事实"""
        
        # 简单的事实验证
        # 实际实现中会进行更复杂的事实检查
        
        verified_facts = []
        
        for fact in facts:
            # 模拟验证过程
            confidence = 0.8  # 模拟置信度
            
            if confidence > 0.7:
                verified_facts.append(fact)
        
        return verified_facts
    
    def _generate_insights(self, findings: List[str], facts: List[str], statistics: Dict[str, Any]) -> List[str]:
        """生成洞察"""
        
        insights = []
        
        # 基于发现生成洞察
        if len(findings) > 5:
            insights.append("研究显示该领域有丰富的发展机会")
        
        # 基于统计数据生成洞察
        if statistics:
            insights.append("数据表明该领域正在快速发展")
        
        # 基于事实生成洞察
        if len(facts) > 3:
            insights.append("该领域有深厚的理论基础和实践经验")
        
        return insights
    
    def _calculate_confidence_score(self, sources: List[Dict[str, Any]]) -> float:
        """计算置信度评分"""
        
        if not sources:
            return 0.0
        
        # 基于数据源质量计算置信度
        total_score = 0
        weight_sum = 0
        
        for source in sources:
            authority = source.get("authority_score", 0.5)
            relevance = source.get("relevance_score", 0.5)
            weight = authority * relevance
            
            total_score += weight * 0.8  # 假设每个来源基础可信度0.8
            weight_sum += weight
        
        return total_score / weight_sum if weight_sum > 0 else 0.5
    
    async def _generate_research_report(self, state: UnifiedState, analyzed_data: Dict[str, Any]) -> Dict[str, Any]:
        """生成研究报告"""
        
        report = {
            "title": f"{state.topic}研究报告",
            "executive_summary": self._create_executive_summary(state, analyzed_data),
            "methodology": {
                "search_queries": len(state.shared_context.get("research_plan", {}).get("search_queries", [])),
                "sources_analyzed": analyzed_data["analysis_summary"]["sources_analyzed"],
                "analysis_approach": "综合分析法，结合定量和定性数据"
            },
            "key_findings": analyzed_data["key_findings"],
            "important_facts": analyzed_data["important_facts"],
            "statistics": analyzed_data["statistics"],
            "expert_opinions": analyzed_data["expert_opinions"],
            "insights_and_conclusions": analyzed_data["insights"],
            "recommendations": self._generate_recommendations(state, analyzed_data),
            "limitations": [
                "搜索结果可能存在时效性限制",
                "部分信息需要进一步验证",
                "研究范围受限于公开可获得的信息"
            ],
            "confidence_level": analyzed_data["confidence_score"],
            "generated_at": time.time(),
            "metadata": {
                "research_agent_version": "2.0",
                "total_sources": len(state.shared_context.get("search_results", {}).get("sources", [])),
                "analysis_depth": "standard"
            }
        }
        
        return report
    
    def _create_executive_summary(self, state: UnifiedState, analyzed_data: Dict[str, Any]) -> str:
        """创建执行摘要"""
        
        topic = state.topic
        findings_count = len(analyzed_data.get("key_findings", []))
        sources_count = analyzed_data["analysis_summary"]["sources_analyzed"]
        confidence = analyzed_data["confidence_score"]
        
        summary = f"本研究对{topic}进行了全面分析，通过对{sources_count}个数据源的深入研究，" \
                 f"提取了{findings_count}个关键发现。研究显示{topic}是一个具有重要意义的领域。" \
                 f"基于现有数据，本研究的可信度为{confidence:.1%}。"
        
        # 添加主要洞察
        if analyzed_data.get("insights"):
            main_insight = analyzed_data["insights"][0]
            summary += f"主要发现：{main_insight}。"
        
        return summary
    
    def _generate_recommendations(self, state: UnifiedState, analyzed_data: Dict[str, Any]) -> List[str]:
        """生成建议"""
        
        recommendations = []
        
        # 基于发现生成建议
        if analyzed_data.get("key_findings"):
            recommendations.append("建议深入研究发现的关键领域")
        
        # 基于统计数据生成建议
        if analyzed_data.get("statistics"):
            recommendations.append("建议关注相关数据趋势的变化")
        
        # 基于专家观点生成建议
        if analyzed_data.get("expert_opinions"):
            recommendations.append("建议考虑专家观点中的重要建议")
        
        # 通用建议
        recommendations.extend([
            "建议持续跟踪相关领域的最新发展",
            "建议验证关键数据和事实的准确性",
            "建议结合实际情况制定具体行动计划"
        ])
        
        return recommendations[:5]  # 返回前5个最重要的建议
    
    async def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        
        base_health = await super().health_check()
        
        research_specific_health = {
            "research_config": self.research_config,
            "max_sources": self.config.get("max_sources"),
            "search_tools_available": len([tool for tool in self.tools.values() if tool is not None])
        }
        
        return {**base_health, **research_specific_health}