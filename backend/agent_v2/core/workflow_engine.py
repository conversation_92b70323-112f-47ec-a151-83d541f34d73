"""
Workflow Engine - MediaAgent V2 交互式工作流引擎
"""

import asyncio
import logging
from typing import Dict, Any, List, Optional, TYPE_CHECKING
import time
from enum import Enum

from .models import UnifiedState, AgentResponse, AgentStatus

if TYPE_CHECKING:
    from .agent_registry import AgentRegistry
    from .tool_system import ToolSystem
    from .state_manager import StateManager

logger = logging.getLogger(__name__)


class WorkflowStatus(str, Enum):
    """工作流状态"""
    PENDING = "pending"
    RUNNING = "running"
    PAUSED = "paused"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class InteractiveWorkflowEngine:
    """
    交互式工作流引擎
    
    职责：
    - 支持用户交互的工作流执行
    - 支持中断和恢复
    - 多Agent协作编排
    - 状态管理和检查点
    """
    
    def __init__(
        self,
        agent_registry: 'AgentRegistry',
        tool_system: 'ToolSystem',
        state_manager: 'StateManager',
        config: Dict[str, Any] = None
    ):
        self.agent_registry = agent_registry
        self.tool_system = tool_system
        self.state_manager = state_manager
        self.config = config or {}
        
        # 运行时状态
        self.active_workflows: Dict[str, Dict[str, Any]] = {}
        self.workflow_locks: Dict[str, asyncio.Lock] = {}
        self.user_interaction_queues: Dict[str, asyncio.Queue] = {}
        
        # 配置参数
        self.max_concurrent_workflows = self.config.get("max_concurrent_workflows", 10)
        self.default_timeout = self.config.get("default_timeout", 3600)  # 1小时
        self.checkpoint_interval = self.config.get("checkpoint_interval", 300)  # 5分钟
        
        logger.info("Interactive Workflow Engine initialized")
    
    async def initialize(self):
        """初始化工作流引擎"""
        logger.info("Initializing Workflow Engine...")
        
        # 可以在这里加载持久化的工作流状态
        await self._restore_workflows()
    
    async def execute_plan(self, execution_plan: Dict[str, Any], state: UnifiedState) -> AgentResponse:
        """执行工作流计划"""
        
        plan_id = execution_plan["plan_id"]
        session_id = state.session_id
        
        logger.info(f"Starting workflow execution for plan {plan_id}")
        
        # 创建工作流锁
        if session_id not in self.workflow_locks:
            self.workflow_locks[session_id] = asyncio.Lock()
        
        async with self.workflow_locks[session_id]:
            try:
                # 初始化工作流状态
                workflow_state = {
                    "plan_id": plan_id,
                    "session_id": session_id,
                    "status": WorkflowStatus.RUNNING,
                    "current_stage_index": 0,
                    "completed_stages": [],
                    "stage_results": {},
                    "checkpoints": [],
                    "start_time": time.time(),
                    "pause_time": None,
                    "total_pause_duration": 0.0
                }
                
                self.active_workflows[session_id] = workflow_state
                
                # 创建用户交互队列
                if session_id not in self.user_interaction_queues:
                    self.user_interaction_queues[session_id] = asyncio.Queue()
                
                # 执行工作流
                result = await self._execute_workflow_stages(execution_plan, state, workflow_state)
                
                # 更新最终状态
                workflow_state["status"] = WorkflowStatus.COMPLETED if result.success else WorkflowStatus.FAILED
                workflow_state["end_time"] = time.time()
                
                return result
                
            except Exception as e:
                logger.error(f"Workflow execution failed: {e}")
                
                if session_id in self.active_workflows:
                    self.active_workflows[session_id]["status"] = WorkflowStatus.FAILED
                    self.active_workflows[session_id]["error"] = str(e)
                
                return AgentResponse(
                    success=False,
                    content="工作流执行失败",
                    errors=[str(e)]
                )
    
    async def _execute_workflow_stages(
        self, 
        execution_plan: Dict[str, Any], 
        state: UnifiedState, 
        workflow_state: Dict[str, Any]
    ) -> AgentResponse:
        """执行工作流阶段"""
        
        stages = execution_plan["stages"]
        session_id = state.session_id
        
        try:
            for stage_index, stage in enumerate(stages):
                # 更新当前阶段
                workflow_state["current_stage_index"] = stage_index
                state.current_stage = stage["stage_id"]
                
                logger.info(f"Executing stage {stage['stage_id']}: {stage['name']}")
                
                # 检查是否需要暂停
                if workflow_state["status"] == WorkflowStatus.PAUSED:
                    await self._handle_workflow_pause(session_id, state)
                
                # 检查依赖
                if not await self._check_stage_dependencies(stage, workflow_state["completed_stages"]):
                    raise Exception(f"Stage {stage['stage_id']} dependencies not met")
                
                # 执行阶段
                stage_result = await self._execute_single_stage(stage, state, workflow_state)
                
                # 保存阶段结果
                workflow_state["stage_results"][stage["stage_id"]] = stage_result
                workflow_state["completed_stages"].append(stage["stage_id"])
                state.mark_stage_completed(stage["stage_id"], stage_result)
                
                # 处理用户交互
                if stage.get("user_interaction", False):
                    await self._handle_user_interaction(stage, stage_result, state, workflow_state)
                
                # 创建检查点
                if self._should_create_checkpoint(stage_index, workflow_state):
                    await self._create_checkpoint(state, workflow_state)
                
                logger.info(f"Stage {stage['stage_id']} completed successfully")
            
            # 所有阶段完成
            final_result = await self._finalize_workflow_result(workflow_state, state)
            
            return AgentResponse(
                success=True,
                content=final_result.get("content", "工作流执行完成"),
                data=final_result,
                execution_time=time.time() - workflow_state["start_time"] - workflow_state["total_pause_duration"]
            )
            
        except Exception as e:
            logger.error(f"Stage execution failed: {e}")
            raise
    
    async def _execute_single_stage(
        self, 
        stage: Dict[str, Any], 
        state: UnifiedState, 
        workflow_state: Dict[str, Any]
    ) -> Dict[str, Any]:
        """执行单个阶段"""
        
        stage_id = stage["stage_id"]
        assigned_agent = stage.get("assigned_agent")
        
        if not assigned_agent:
            raise Exception(f"No agent assigned to stage {stage_id}")
        
        # 准备任务参数
        task_params = {
            "stage_id": stage_id,
            "stage_name": stage["name"],
            "description": stage["description"],
            "tools_required": stage.get("tools_required", []),
            "success_criteria": stage.get("success_criteria", []),
            "previous_results": workflow_state.get("stage_results", {})
        }
        
        # 执行Agent
        start_time = time.time()
        agent_result = await self.agent_registry.execute_agent(assigned_agent, task_params, state)
        execution_time = time.time() - start_time
        
        # 构建阶段结果
        stage_result = {
            "stage_id": stage_id,
            "agent_name": assigned_agent,
            "success": agent_result.success,
            "content": agent_result.content,
            "data": agent_result.data,
            "execution_time": execution_time,
            "timestamp": time.time(),
            "errors": agent_result.errors,
            "warnings": agent_result.warnings
        }
        
        if not agent_result.success:
            raise Exception(f"Stage {stage_id} failed: {agent_result.errors}")
        
        return stage_result
    
    async def _handle_user_interaction(
        self, 
        stage: Dict[str, Any], 
        stage_result: Dict[str, Any], 
        state: UnifiedState, 
        workflow_state: Dict[str, Any]
    ):
        """处理用户交互"""
        
        session_id = state.session_id
        
        # 创建用户确认请求
        interaction_request = {
            "type": "stage_confirmation",
            "stage_id": stage["stage_id"],
            "stage_name": stage["name"],
            "result": {
                "content": stage_result["content"],
                "summary": self._create_result_summary(stage_result)
            },
            "options": ["继续", "修改", "重新执行", "取消"],
            "timestamp": time.time()
        }
        
        # 保存交互请求到状态
        state.shared_context["pending_interaction"] = interaction_request
        state.update_status(AgentStatus.WAITING_INPUT, f"等待用户确认阶段：{stage['name']}")
        
        # 保存状态
        await self.state_manager.save_state(state)
        
        # 等待用户响应（这里需要与前端交互系统集成）
        user_response = await self._wait_for_user_response(session_id, timeout=300)
        
        if user_response["action"] == "取消":
            workflow_state["status"] = WorkflowStatus.CANCELLED
            raise Exception("用户取消了工作流执行")
        elif user_response["action"] == "修改":
            # 处理用户修改请求
            await self._handle_user_modification(user_response, stage, state, workflow_state)
        elif user_response["action"] == "重新执行":
            # 重新执行当前阶段
            return await self._execute_single_stage(stage, state, workflow_state)
        
        # 继续执行
        state.update_status(AgentStatus.PROCESSING, "继续执行工作流")
    
    async def _wait_for_user_response(self, session_id: str, timeout: int = 300) -> Dict[str, Any]:
        """等待用户响应"""
        
        try:
            # 从用户交互队列中等待响应
            queue = self.user_interaction_queues.get(session_id)
            if not queue:
                queue = asyncio.Queue()
                self.user_interaction_queues[session_id] = queue
            
            # 等待响应（带超时）
            user_response = await asyncio.wait_for(queue.get(), timeout=timeout)
            
            return user_response
            
        except asyncio.TimeoutError:
            logger.warning(f"User response timeout for session {session_id}")
            # 默认响应：继续执行
            return {"action": "继续", "message": "超时自动继续"}
    
    async def send_user_response(self, session_id: str, response: Dict[str, Any]) -> bool:
        """发送用户响应"""
        
        if session_id not in self.user_interaction_queues:
            logger.error(f"No interaction queue for session {session_id}")
            return False
        
        try:
            queue = self.user_interaction_queues[session_id]
            await queue.put(response)
            logger.info(f"User response sent for session {session_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to send user response: {e}")
            return False
    
    async def pause_session(self, session_id: str) -> bool:
        """暂停会话"""
        
        if session_id not in self.active_workflows:
            return False
        
        workflow_state = self.active_workflows[session_id]
        workflow_state["status"] = WorkflowStatus.PAUSED
        workflow_state["pause_time"] = time.time()
        
        logger.info(f"Workflow paused for session {session_id}")
        return True
    
    async def resume_session(self, session_id: str) -> bool:
        """恢复会话"""
        
        if session_id not in self.active_workflows:
            return False
        
        workflow_state = self.active_workflows[session_id]
        
        if workflow_state["status"] != WorkflowStatus.PAUSED:
            return False
        
        # 计算暂停时长
        if workflow_state.get("pause_time"):
            pause_duration = time.time() - workflow_state["pause_time"]
            workflow_state["total_pause_duration"] += pause_duration
            workflow_state["pause_time"] = None
        
        workflow_state["status"] = WorkflowStatus.RUNNING
        
        logger.info(f"Workflow resumed for session {session_id}")
        return True
    
    async def cancel_session(self, session_id: str) -> bool:
        """取消会话"""
        
        if session_id not in self.active_workflows:
            return False
        
        workflow_state = self.active_workflows[session_id]
        workflow_state["status"] = WorkflowStatus.CANCELLED
        workflow_state["end_time"] = time.time()
        
        # 清理资源
        self._cleanup_session_resources(session_id)
        
        logger.info(f"Workflow cancelled for session {session_id}")
        return True
    
    async def get_workflow_status(self, session_id: str) -> Optional[Dict[str, Any]]:
        """获取工作流状态"""
        
        if session_id not in self.active_workflows:
            return None
        
        workflow_state = self.active_workflows[session_id]
        
        return {
            "session_id": session_id,
            "plan_id": workflow_state["plan_id"],
            "status": workflow_state["status"].value,
            "current_stage_index": workflow_state["current_stage_index"],
            "completed_stages": workflow_state["completed_stages"],
            "total_stages": len(workflow_state.get("stages", [])),
            "progress": len(workflow_state["completed_stages"]) / len(workflow_state.get("stages", [])) if workflow_state.get("stages") else 0,
            "start_time": workflow_state["start_time"],
            "execution_time": time.time() - workflow_state["start_time"] - workflow_state["total_pause_duration"],
            "checkpoints": len(workflow_state["checkpoints"])
        }
    
    async def _check_stage_dependencies(self, stage: Dict[str, Any], completed_stages: List[str]) -> bool:
        """检查阶段依赖"""
        
        dependencies = stage.get("dependencies", [])
        
        for dep in dependencies:
            if dep not in completed_stages:
                logger.error(f"Dependency {dep} not met for stage {stage['stage_id']}")
                return False
        
        return True
    
    async def _handle_workflow_pause(self, session_id: str, state: UnifiedState):
        """处理工作流暂停"""
        
        logger.info(f"Workflow paused for session {session_id}")
        
        # 等待恢复信号
        while self.active_workflows[session_id]["status"] == WorkflowStatus.PAUSED:
            await asyncio.sleep(1)
        
        logger.info(f"Workflow resumed for session {session_id}")
    
    def _should_create_checkpoint(self, stage_index: int, workflow_state: Dict[str, Any]) -> bool:
        """判断是否应该创建检查点"""
        
        # 在每个阶段后创建检查点，或者根据时间间隔
        return True  # 简化实现，每个阶段都创建检查点
    
    async def _create_checkpoint(self, state: UnifiedState, workflow_state: Dict[str, Any]):
        """创建检查点"""
        
        checkpoint = {
            "checkpoint_id": f"checkpoint_{state.session_id}_{len(workflow_state['checkpoints']) + 1}",
            "timestamp": time.time(),
            "completed_stages": workflow_state["completed_stages"].copy(),
            "stage_results": workflow_state["stage_results"].copy(),
            "state_snapshot": {
                "current_stage": state.current_stage,
                "shared_context": state.shared_context.copy(),
                "stage_outputs": state.stage_outputs.copy()
            }
        }
        
        workflow_state["checkpoints"].append(checkpoint)
        
        # 持久化检查点
        await self.state_manager.save_checkpoint(state, checkpoint)
        
        logger.info(f"Checkpoint created: {checkpoint['checkpoint_id']}")
    
    async def _handle_user_modification(
        self, 
        user_response: Dict[str, Any], 
        stage: Dict[str, Any], 
        state: UnifiedState, 
        workflow_state: Dict[str, Any]
    ):
        """处理用户修改请求"""
        
        modifications = user_response.get("modifications", {})
        
        # 应用用户修改到状态中
        if "requirements" in modifications:
            state.requirements = modifications["requirements"]
        
        if "keywords" in modifications:
            state.keywords = modifications["keywords"]
        
        if "tone" in modifications:
            state.tone = modifications["tone"]
        
        # 记录修改历史
        if "modification_history" not in state.shared_context:
            state.shared_context["modification_history"] = []
        
        state.shared_context["modification_history"].append({
            "timestamp": time.time(),
            "stage_id": stage["stage_id"],
            "modifications": modifications,
            "user_feedback": user_response.get("feedback", "")
        })
        
        logger.info(f"User modifications applied for stage {stage['stage_id']}")
    
    def _create_result_summary(self, stage_result: Dict[str, Any]) -> str:
        """创建结果摘要"""
        
        content = stage_result.get("content", "")
        
        # 简单的摘要生成
        if len(content) > 200:
            return content[:200] + "..."
        
        return content
    
    async def _finalize_workflow_result(self, workflow_state: Dict[str, Any], state: UnifiedState) -> Dict[str, Any]:
        """最终化工作流结果"""
        
        # 收集所有阶段的结果
        all_results = workflow_state["stage_results"]
        
        # 构建最终结果
        final_result = {
            "workflow_id": workflow_state["plan_id"],
            "session_id": workflow_state["session_id"],
            "total_stages": len(all_results),
            "execution_time": time.time() - workflow_state["start_time"] - workflow_state["total_pause_duration"],
            "stage_results": all_results,
            "final_output": None
        }
        
        # 如果最后一个阶段有最终输出，使用它
        if all_results:
            last_stage = list(all_results.values())[-1]
            final_result["final_output"] = last_stage.get("data", {})
            final_result["content"] = last_stage.get("content", "")
        
        return final_result
    
    def _cleanup_session_resources(self, session_id: str):
        """清理会话资源"""
        
        # 清理工作流状态
        if session_id in self.active_workflows:
            del self.active_workflows[session_id]
        
        # 清理锁
        if session_id in self.workflow_locks:
            del self.workflow_locks[session_id]
        
        # 清理用户交互队列
        if session_id in self.user_interaction_queues:
            del self.user_interaction_queues[session_id]
    
    async def _restore_workflows(self):
        """恢复持久化的工作流"""
        
        # 从状态管理器中恢复暂停的工作流
        # 这里可以实现工作流的持久化恢复逻辑
        
        logger.info("Workflow restoration completed")
    
    async def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        
        return {
            "status": "healthy",
            "active_workflows": len(self.active_workflows),
            "max_concurrent_workflows": self.max_concurrent_workflows,
            "workflow_statuses": {
                status.value: sum(1 for w in self.active_workflows.values() if w["status"] == status)
                for status in WorkflowStatus
            }
        }


# 创建别名以保持向后兼容
WorkflowEngine = InteractiveWorkflowEngine