"""
Base Agent V2 - Enhanced agent architecture with unified state management
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional, AsyncIterator, Union
import asyncio
import time
import logging

from .models import UnifiedState, AgentResponse, AgentStatus, TaskType, QualityMetrics
from ..memory.context_manager import SharedContextManager
from ..memory.quality_monitor import QualityMonitor


class BaseAgentV2(ABC):
    """
    Enhanced Base Agent class - V2 Architecture
    
    Key improvements:
    - Unified state management
    - Built-in memory/context system
    - Quality monitoring
    - Streaming response support
    - Better error handling
    """
    
    def __init__(
        self,
        name: str,
        description: str = "",
        supported_tasks: List[TaskType] = None,
        config: Dict[str, Any] = None
    ):
        self.name = name
        self.description = description
        self.supported_tasks = supported_tasks or []
        self.config = config or {}
        
        # 初始化组件
        self.context_manager = SharedContextManager()
        self.quality_monitor = QualityMonitor()
        self.logger = logging.getLogger(f"agent_v2.{name}")
        
        # 执行历史
        self.execution_history: List[Dict[str, Any]] = []
        
        # 工具注册表
        self.tools: Dict[str, Any] = {}
        self._initialize_tools()
    
    @abstractmethod
    async def process(self, state: UnifiedState) -> AsyncIterator[AgentResponse]:
        """
        核心处理方法 - 必须由子类实现
        
        Args:
            state: 统一状态对象
            
        Yields:
            AgentResponse: 响应结果，支持流式输出
        """
        pass
    
    @abstractmethod
    def _initialize_tools(self):
        """初始化工具 - 由子类实现"""
        pass
    
    async def run(
        self,
        task_type: TaskType,
        user_id: Optional[str] = None,
        session_id: Optional[str] = None,
        **kwargs
    ) -> AsyncIterator[AgentResponse]:
        """
        运行Agent - 统一入口点
        
        Args:
            task_type: 任务类型
            user_id: 用户ID
            session_id: 会话ID
            **kwargs: 任务参数
        """
        
        # 1. 检查任务类型支持
        if task_type not in self.supported_tasks:
            yield AgentResponse(
                success=False,
                errors=[f"Task type {task_type} not supported by {self.name}"]
            )
            return
        
        # 2. 初始化状态
        state = UnifiedState(
            session_id=session_id,
            task_type=task_type,
            user_id=user_id,
            **kwargs
        )
        
        start_time = time.time()
        
        try:
            # 3. 预处理
            state = await self._pre_process(state)
            
            # 4. 执行主要处理逻辑
            final_response = None
            async for response in self.process(state):
                # 更新执行时间
                response.execution_time = time.time() - start_time
                final_response = response
                yield response
            
            # 5. 后处理
            if final_response:
                await self._post_process(state, final_response)
            
        except Exception as e:
            self.logger.error(f"Agent {self.name} execution failed: {str(e)}")
            state.add_error("agent_execution", str(e))
            
            yield AgentResponse(
                success=False,
                errors=[f"Agent execution failed: {str(e)}"],
                execution_time=time.time() - start_time
            )
        
        finally:
            # 记录执行历史
            self._record_execution(state, time.time() - start_time)
    
    async def _pre_process(self, state: UnifiedState) -> UnifiedState:
        """预处理 - 加载上下文，准备环境"""
        
        state.update_status(AgentStatus.INITIALIZING, "Loading context and preparing environment")
        
        # 加载用户上下文
        if state.user_id:
            user_context = await self.context_manager.get_user_context(state.user_id)
            state.shared_context["user_context"] = user_context
            
            # 应用用户偏好
            if not state.target_length and user_context:
                state.target_length = user_context.get_preferred_length()
            if not state.tone and user_context:
                state.tone = user_context.get_preferred_tone()
        
        # 加载会话上下文
        if state.session_id:
            session_context = await self.context_manager.get_session_context(state.session_id)
            state.shared_context["session_context"] = session_context
        
        state.update_status(AgentStatus.PROCESSING, "Context loaded, starting processing")
        return state
    
    async def _post_process(self, state: UnifiedState, response: AgentResponse):
        """后处理 - 保存上下文，更新记忆"""
        
        # 质量评估
        if response.success and state.task_type == TaskType.ARTICLE_GENERATION:
            quality_metrics = await self.quality_monitor.evaluate_result(state, response)
            response.metadata["quality_metrics"] = quality_metrics
        
        # 更新用户上下文
        if state.user_id and response.success:
            await self._update_user_context(state, response)
        
        # 保存会话上下文
        if state.session_id:
            await self._update_session_context(state, response)
        
        # 更新最终状态
        if response.success:
            state.update_status(AgentStatus.COMPLETED, "Task completed successfully")
        else:
            state.update_status(AgentStatus.FAILED, "Task failed")
    
    async def _update_user_context(self, state: UnifiedState, response: AgentResponse):
        """更新用户上下文"""
        
        user_context_data = {
            "last_task_type": state.task_type.value,
            "last_topic": state.topic,
            "last_target_length": state.target_length,
            "last_tone": state.tone,
            "last_language": state.language,
            "timestamp": time.time()
        }
        
        # 如果有质量指标，也保存
        if "quality_metrics" in response.metadata:
            quality = response.metadata["quality_metrics"]
            user_context_data["last_quality_score"] = quality.get("overall_score", 0.0)
        
        await self.context_manager.update_user_context(state.user_id, user_context_data)
    
    async def _update_session_context(self, state: UnifiedState, response: AgentResponse):
        """更新会话上下文"""
        
        session_data = {
            "last_task": {
                "type": state.task_type.value,
                "topic": state.topic,
                "success": response.success,
                "timestamp": time.time()
            },
            "tools_used": state.tools_used,
            "stages_completed": state.completed_stages
        }
        
        await self.context_manager.update_session_context(state.session_id, session_data)
    
    def _record_execution(self, state: UnifiedState, execution_time: float):
        """记录执行历史"""
        
        execution_record = {
            "timestamp": time.time(),
            "session_id": state.session_id,
            "task_type": state.task_type.value,
            "status": state.status.value,
            "execution_time": execution_time,
            "stages_completed": len(state.completed_stages),
            "tools_used": len(state.tools_used),
            "errors": len(state.errors),
            "warnings": len(state.warnings)
        }
        
        self.execution_history.append(execution_record)
        
        # 保持最近100条记录
        if len(self.execution_history) > 100:
            self.execution_history = self.execution_history[-100:]
    
    def get_tool(self, tool_name: str) -> Optional[Any]:
        """获取工具"""
        return self.tools.get(tool_name)
    
    def register_tool(self, name: str, tool: Any):
        """注册工具"""
        self.tools[name] = tool
        self.logger.info(f"Tool '{name}' registered for agent '{self.name}'")
    
    def get_execution_stats(self) -> Dict[str, Any]:
        """获取执行统计"""
        if not self.execution_history:
            return {}
        
        recent_executions = self.execution_history[-20:]  # 最近20次
        
        success_count = sum(1 for exec in recent_executions if exec["status"] == "completed")
        total_count = len(recent_executions)
        avg_execution_time = sum(exec["execution_time"] for exec in recent_executions) / total_count
        
        return {
            "total_executions": len(self.execution_history),
            "recent_success_rate": success_count / total_count if total_count > 0 else 0,
            "average_execution_time": avg_execution_time,
            "most_used_tools": self._get_most_used_tools(),
            "common_errors": self._get_common_errors()
        }
    
    def _get_most_used_tools(self) -> List[str]:
        """获取最常用的工具"""
        tool_usage = {}
        for record in self.execution_history[-50:]:  # 最近50次
            for tool in record.get("tools_used", []):
                tool_usage[tool] = tool_usage.get(tool, 0) + 1
        
        return sorted(tool_usage.items(), key=lambda x: x[1], reverse=True)[:5]
    
    def _get_common_errors(self) -> List[str]:
        """获取常见错误"""
        # 这里简化处理，实际应该分析错误模式
        return []
    
    async def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        
        health_status = {
            "agent_name": self.name,
            "status": "healthy",
            "supported_tasks": [task.value for task in self.supported_tasks],
            "tools_count": len(self.tools),
            "execution_history_count": len(self.execution_history),
            "last_execution": None
        }
        
        if self.execution_history:
            health_status["last_execution"] = self.execution_history[-1]
        
        # 检查关键组件
        try:
            await self.context_manager.health_check()
            await self.quality_monitor.health_check()
        except Exception as e:
            health_status["status"] = "unhealthy"
            health_status["error"] = str(e)
        
        return health_status