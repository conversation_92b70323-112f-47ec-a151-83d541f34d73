"""
Agent Registry - MediaAgent V2 Agent注册中心
"""

import asyncio
import logging
from typing import Dict, Any, List, Optional, Type
import importlib
import inspect

from .models import UnifiedState, AgentResponse
from .base_agent import BaseAgentV2

logger = logging.getLogger(__name__)


class AgentRegistry:
    """
    Agent注册中心
    
    职责：
    - Agent发现和注册
    - 动态路由和调度
    - 能力匹配
    - Agent生命周期管理
    """
    
    def __init__(self, config: Dict[str, Any] = None):
        self.config = config or {}
        
        # Agent存储
        self.agents: Dict[str, BaseAgentV2] = {}
        self.agent_metadata: Dict[str, Dict[str, Any]] = {}
        self.agent_capabilities: Dict[str, List[str]] = {}
        
        # 路由配置
        self.routing_rules: Dict[str, str] = {}
        self.load_balancing_config: Dict[str, Any] = {}
        
        # 统计信息
        self.agent_stats: Dict[str, Dict[str, Any]] = {}
        
        logger.info("Agent Registry initialized")
    
    async def initialize(self):
        """初始化注册中心"""
        
        logger.info("Initializing Agent Registry...")
        
        # 自动发现和注册Agent
        await self._auto_discover_agents()
        
        # 加载路由规则
        self._load_routing_rules()
        
        logger.info(f"Agent Registry initialized with {len(self.agents)} agents")
    
    async def register_agent(self, agent: BaseAgentV2, metadata: Dict[str, Any] = None) -> bool:
        """注册Agent"""
        
        try:
            agent_name = agent.name
            
            if agent_name in self.agents:
                logger.warning(f"Agent {agent_name} already registered, updating...")
            
            # 注册Agent
            self.agents[agent_name] = agent
            
            # 存储元数据
            agent_metadata = metadata or {}
            agent_metadata.update({
                "registered_at": asyncio.get_event_loop().time(),
                "class_name": agent.__class__.__name__,
                "module": agent.__class__.__module__,
                "capabilities": await self._extract_capabilities(agent)
            })
            
            self.agent_metadata[agent_name] = agent_metadata
            self.agent_capabilities[agent_name] = agent_metadata["capabilities"]
            
            # 初始化统计信息
            self.agent_stats[agent_name] = {
                "total_executions": 0,
                "successful_executions": 0,
                "failed_executions": 0,
                "average_execution_time": 0.0,
                "last_execution": None
            }
            
            # 初始化Agent
            if hasattr(agent, 'initialize'):
                await agent.initialize()
            
            logger.info(f"Agent {agent_name} registered successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to register agent {agent.name}: {e}")
            return False
    
    async def unregister_agent(self, agent_name: str) -> bool:
        """注销Agent"""
        
        if agent_name not in self.agents:
            logger.warning(f"Agent {agent_name} not found")
            return False
        
        try:
            agent = self.agents[agent_name]
            
            # 清理Agent
            if hasattr(agent, 'shutdown'):
                await agent.shutdown()
            
            # 移除注册信息
            del self.agents[agent_name]
            del self.agent_metadata[agent_name]
            del self.agent_capabilities[agent_name]
            del self.agent_stats[agent_name]
            
            logger.info(f"Agent {agent_name} unregistered successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to unregister agent {agent_name}: {e}")
            return False
    
    async def get_agent(self, agent_name: str) -> Optional[BaseAgentV2]:
        """获取Agent实例"""
        
        return self.agents.get(agent_name)
    
    async def execute_agent(
        self, 
        agent_name: str, 
        task: Dict[str, Any], 
        state: UnifiedState
    ) -> AgentResponse:
        """执行Agent任务"""
        
        if agent_name not in self.agents:
            logger.error(f"Agent {agent_name} not found")
            return AgentResponse(
                success=False,
                errors=[f"Agent {agent_name} not found"]
            )
        
        agent = self.agents[agent_name]
        
        try:
            # 更新统计信息
            self.agent_stats[agent_name]["total_executions"] += 1
            start_time = asyncio.get_event_loop().time()
            
            # 执行Agent
            result = await agent.execute(task, state)
            
            # 更新执行统计
            execution_time = asyncio.get_event_loop().time() - start_time
            stats = self.agent_stats[agent_name]
            
            if result.success:
                stats["successful_executions"] += 1
            else:
                stats["failed_executions"] += 1
            
            # 更新平均执行时间
            total_executions = stats["total_executions"]
            current_avg = stats["average_execution_time"]
            stats["average_execution_time"] = (
                (current_avg * (total_executions - 1) + execution_time) / total_executions
            )
            stats["last_execution"] = asyncio.get_event_loop().time()
            
            # 设置执行信息
            result.execution_time = execution_time
            
            logger.info(f"Agent {agent_name} executed in {execution_time:.2f}s")
            return result
            
        except Exception as e:
            logger.error(f"Agent {agent_name} execution failed: {e}")
            
            # 更新失败统计
            self.agent_stats[agent_name]["failed_executions"] += 1
            
            return AgentResponse(
                success=False,
                errors=[f"Agent execution failed: {str(e)}"]
            )
    
    async def find_capable_agents(self, required_capabilities: List[str]) -> List[str]:
        """查找具备指定能力的Agent"""
        
        capable_agents = []
        
        for agent_name, capabilities in self.agent_capabilities.items():
            if all(cap in capabilities for cap in required_capabilities):
                capable_agents.append(agent_name)
        
        # 根据统计信息排序（成功率和平均执行时间）
        capable_agents.sort(key=lambda name: (
            -self._get_success_rate(name),  # 成功率降序
            self.agent_stats[name]["average_execution_time"]  # 执行时间升序
        ))
        
        return capable_agents
    
    async def get_best_agent_for_task(self, task_type: str, requirements: Dict[str, Any] = None) -> Optional[str]:
        """为任务选择最佳Agent"""
        
        # 基于路由规则
        if task_type in self.routing_rules:
            return self.routing_rules[task_type]
        
        # 基于能力匹配
        required_capabilities = requirements.get("capabilities", []) if requirements else []
        if not required_capabilities:
            # 根据任务类型推断需要的能力
            required_capabilities = self._infer_capabilities_from_task(task_type)
        
        capable_agents = await self.find_capable_agents(required_capabilities)
        
        if not capable_agents:
            logger.warning(f"No capable agents found for task type: {task_type}")
            return None
        
        # 返回最佳Agent（排序后的第一个）
        best_agent = capable_agents[0]
        logger.info(f"Selected agent {best_agent} for task type {task_type}")
        
        return best_agent
    
    def list_agents(self) -> List[Dict[str, Any]]:
        """列出所有注册的Agent"""
        
        agent_list = []
        
        for agent_name, agent in self.agents.items():
            metadata = self.agent_metadata.get(agent_name, {})
            stats = self.agent_stats.get(agent_name, {})
            
            agent_info = {
                "name": agent_name,
                "class_name": metadata.get("class_name", "Unknown"),
                "capabilities": metadata.get("capabilities", []),
                "registered_at": metadata.get("registered_at"),
                "stats": stats,
                "status": "active" if hasattr(agent, 'is_healthy') and agent.is_healthy else "unknown"
            }
            
            agent_list.append(agent_info)
        
        return agent_list
    
    async def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        
        health_info = {
            "status": "healthy",
            "total_agents": len(self.agents),
            "active_agents": 0,
            "agent_health": {}
        }
        
        for agent_name, agent in self.agents.items():
            try:
                if hasattr(agent, 'health_check'):
                    agent_health = await agent.health_check()
                    health_info["agent_health"][agent_name] = agent_health
                    
                    if agent_health.get("status") == "healthy":
                        health_info["active_agents"] += 1
                else:
                    health_info["agent_health"][agent_name] = {
                        "status": "unknown",
                        "message": "No health check method"
                    }
                    
            except Exception as e:
                health_info["agent_health"][agent_name] = {
                    "status": "error",
                    "error": str(e)
                }
        
        return health_info
    
    async def _auto_discover_agents(self):
        """自动发现Agent类"""
        
        # 扫描agents目录
        agents_module_path = "backend.agent_v2.agents"
        
        try:
            # 导入agents包
            agents_package = importlib.import_module(agents_module_path)
            
            # 获取所有模块
            import pkgutil
            for importer, modname, ispkg in pkgutil.iter_modules(agents_package.__path__):
                if not ispkg:
                    try:
                        # 导入模块
                        module = importlib.import_module(f"{agents_module_path}.{modname}")
                        
                        # 查找Agent类
                        for name, obj in inspect.getmembers(module):
                            if (inspect.isclass(obj) and 
                                issubclass(obj, BaseAgentV2) and 
                                obj != BaseAgentV2):
                                
                                # 实例化并注册Agent
                                agent_instance = obj()
                                await self.register_agent(agent_instance)
                                
                    except Exception as e:
                        logger.error(f"Failed to load agent module {modname}: {e}")
                        
        except ImportError as e:
            logger.warning(f"Agents package not found: {e}")
            # 手动注册一些基础Agent
            await self._register_builtin_agents()
    
    async def _register_builtin_agents(self):
        """注册内置Agent"""
        
        # 这里可以手动注册一些基础Agent
        # 实际实现中，这些Agent会在对应的模块中定义
        
        logger.info("Registering built-in agents...")
        
        # 注册一个示例Agent（实际中会从具体文件导入）
        class DummyAgent(BaseAgentV2):
            def __init__(self):
                super().__init__(name="dummy_agent")
            
            async def execute(self, task: Dict[str, Any], state: UnifiedState) -> AgentResponse:
                return AgentResponse(success=True, content="Dummy response")
        
        await self.register_agent(DummyAgent())
    
    async def _extract_capabilities(self, agent: BaseAgentV2) -> List[str]:
        """提取Agent能力"""
        
        capabilities = []
        
        # 基于Agent名称推断能力
        agent_name = agent.name.lower()
        
        if "article" in agent_name:
            capabilities.extend(["content_generation", "writing", "seo_optimization"])
        elif "research" in agent_name:
            capabilities.extend(["information_gathering", "web_search", "data_collection"])
        elif "analysis" in agent_name:
            capabilities.extend(["content_analysis", "quality_assessment", "data_processing"])
        elif "story" in agent_name:
            capabilities.extend(["creative_writing", "narrative_creation", "character_development"])
        elif "script" in agent_name:
            capabilities.extend(["script_writing", "video_content", "presentation"])
        
        # 通过方法检查推断更多能力
        agent_methods = [method for method in dir(agent) if not method.startswith('_')]
        
        if "search" in str(agent_methods):
            capabilities.append("search_capability")
        if "analyze" in str(agent_methods):
            capabilities.append("analysis_capability")
        if "generate" in str(agent_methods):
            capabilities.append("generation_capability")
        
        # 去重
        capabilities = list(set(capabilities))
        
        return capabilities
    
    def _load_routing_rules(self):
        """加载路由规则"""
        
        # 默认路由规则
        default_rules = {
            "article_generation": "article_agent",
            "content_analysis": "analysis_agent", 
            "research": "research_agent",
            "story_creation": "story_agent",
            "script_writing": "script_agent"
        }
        
        # 从配置中加载自定义规则
        custom_rules = self.config.get("routing_rules", {})
        
        self.routing_rules = {**default_rules, **custom_rules}
        
        logger.info(f"Loaded {len(self.routing_rules)} routing rules")
    
    def _get_success_rate(self, agent_name: str) -> float:
        """获取Agent成功率"""
        
        stats = self.agent_stats.get(agent_name, {})
        total = stats.get("total_executions", 0)
        successful = stats.get("successful_executions", 0)
        
        return successful / total if total > 0 else 0.0
    
    def _infer_capabilities_from_task(self, task_type: str) -> List[str]:
        """从任务类型推断所需能力"""
        
        capability_mapping = {
            "article_generation": ["content_generation", "writing"],
            "content_analysis": ["content_analysis", "quality_assessment"],
            "research": ["information_gathering", "web_search"],
            "story_creation": ["creative_writing", "narrative_creation"],
            "script_writing": ["script_writing", "video_content"],
            "editing": ["content_editing", "quality_improvement"],
            "seo_optimization": ["seo_optimization", "content_analysis"]
        }
        
        return capability_mapping.get(task_type, [])