"""
State Manager - MediaAgent V2 统一状态管理
"""

import asyncio
import json
import logging
from typing import Dict, Any, List, Optional
import time
import pickle
import redis
from dataclasses import asdict

from .models import UnifiedState, AgentStatus

logger = logging.getLogger(__name__)


class StateManager:
    """
    统一状态管理器
    
    职责：
    - 会话状态管理
    - 用户上下文保持
    - 状态持久化和恢复
    - 状态变更追踪
    """
    
    def __init__(self, config: Dict[str, Any] = None):
        self.config = config or {}
        
        # 存储配置
        self.storage_backend = self.config.get("storage_backend", "redis")
        self.redis_config = self.config.get("redis", {
            "host": "localhost",
            "port": 6379,
            "db": 0,
            "decode_responses": False  # 用于存储二进制数据
        })
        
        # 状态缓存
        self.state_cache: Dict[str, UnifiedState] = {}
        self.cache_ttl = self.config.get("cache_ttl", 3600)  # 1小时
        self.max_cache_size = self.config.get("max_cache_size", 1000)
        
        # Redis连接
        self.redis_client: Optional[redis.Redis] = None
        
        # 状态变更监听器
        self.state_change_listeners: List[callable] = []
        
        logger.info("State Manager initialized")
    
    async def initialize(self):
        """初始化状态管理器"""
        
        logger.info("Initializing State Manager...")
        
        if self.storage_backend == "redis":
            await self._initialize_redis()
        
        # 清理过期的缓存状态
        asyncio.create_task(self._cleanup_cache_periodically())
        
        logger.info("State Manager initialized successfully")
    
    async def _initialize_redis(self):
        """初始化Redis连接"""
        
        try:
            self.redis_client = redis.Redis(**self.redis_config)
            
            # 测试连接
            await asyncio.get_event_loop().run_in_executor(
                None, self.redis_client.ping
            )
            
            logger.info("Redis connection established")
            
        except Exception as e:
            logger.error(f"Failed to connect to Redis: {e}")
            raise
    
    async def save_state(self, state: UnifiedState) -> bool:
        """保存状态"""
        
        try:
            session_id = state.session_id
            
            # 更新时间戳
            state.updated_at = time.time()
            
            # 保存到缓存
            self.state_cache[session_id] = state
            
            # 持久化存储
            await self._persist_state(state)
            
            # 触发状态变更事件
            await self._notify_state_change(state, "save")
            
            logger.debug(f"State saved for session {session_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to save state: {e}")
            return False
    
    async def load_state(self, session_id: str) -> Optional[UnifiedState]:
        """加载状态"""
        
        try:
            # 先从缓存查找
            if session_id in self.state_cache:
                logger.debug(f"State loaded from cache for session {session_id}")
                return self.state_cache[session_id]
            
            # 从持久化存储加载
            state = await self._load_state_from_storage(session_id)
            
            if state:
                # 加入缓存
                self.state_cache[session_id] = state
                logger.debug(f"State loaded from storage for session {session_id}")
                return state
            
            logger.warning(f"State not found for session {session_id}")
            return None
            
        except Exception as e:
            logger.error(f"Failed to load state: {e}")
            return None
    
    async def delete_state(self, session_id: str) -> bool:
        """删除状态"""
        
        try:
            # 从缓存删除
            if session_id in self.state_cache:
                del self.state_cache[session_id]
            
            # 从持久化存储删除
            await self._delete_state_from_storage(session_id)
            
            logger.info(f"State deleted for session {session_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to delete state: {e}")
            return False
    
    async def list_sessions(self, user_id: Optional[str] = None, status: Optional[AgentStatus] = None) -> List[Dict[str, Any]]:
        """列出会话"""
        
        try:
            sessions = []
            
            # 从缓存获取
            for session_id, state in self.state_cache.items():
                if user_id and state.user_id != user_id:
                    continue
                if status and state.status != status:
                    continue
                
                sessions.append({
                    "session_id": session_id,
                    "user_id": state.user_id,
                    "status": state.status.value,
                    "created_at": state.created_at,
                    "updated_at": state.updated_at,
                    "topic": state.topic
                })
            
            # 从存储获取更多会话（如果需要）
            if self.storage_backend == "redis":
                storage_sessions = await self._list_sessions_from_redis(user_id, status)
                
                # 合并结果，去重
                session_ids = {s["session_id"] for s in sessions}
                for storage_session in storage_sessions:
                    if storage_session["session_id"] not in session_ids:
                        sessions.append(storage_session)
            
            # 按更新时间排序
            sessions.sort(key=lambda x: x["updated_at"], reverse=True)
            
            return sessions
            
        except Exception as e:
            logger.error(f"Failed to list sessions: {e}")
            return []
    
    async def save_checkpoint(self, state: UnifiedState, checkpoint_data: Dict[str, Any]) -> bool:
        """保存检查点"""
        
        try:
            checkpoint_id = checkpoint_data["checkpoint_id"]
            
            if self.storage_backend == "redis":
                key = f"checkpoint:{state.session_id}:{checkpoint_id}"
                
                checkpoint_json = json.dumps({
                    **checkpoint_data,
                    "state_data": asdict(state)
                })
                
                await asyncio.get_event_loop().run_in_executor(
                    None, self.redis_client.setex, key, 86400, checkpoint_json  # 24小时过期
                )
            
            logger.info(f"Checkpoint saved: {checkpoint_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to save checkpoint: {e}")
            return False
    
    async def load_checkpoint(self, session_id: str, checkpoint_id: str) -> Optional[Dict[str, Any]]:
        """加载检查点"""
        
        try:
            if self.storage_backend == "redis":
                key = f"checkpoint:{session_id}:{checkpoint_id}"
                
                checkpoint_data = await asyncio.get_event_loop().run_in_executor(
                    None, self.redis_client.get, key
                )
                
                if checkpoint_data:
                    return json.loads(checkpoint_data)
            
            return None
            
        except Exception as e:
            logger.error(f"Failed to load checkpoint: {e}")
            return None
    
    async def list_checkpoints(self, session_id: str) -> List[Dict[str, Any]]:
        """列出检查点"""
        
        try:
            checkpoints = []
            
            if self.storage_backend == "redis":
                pattern = f"checkpoint:{session_id}:*"
                
                keys = await asyncio.get_event_loop().run_in_executor(
                    None, self.redis_client.keys, pattern
                )
                
                for key in keys:
                    checkpoint_data = await asyncio.get_event_loop().run_in_executor(
                        None, self.redis_client.get, key
                    )
                    
                    if checkpoint_data:
                        checkpoint = json.loads(checkpoint_data)
                        checkpoints.append({
                            "checkpoint_id": checkpoint["checkpoint_id"],
                            "timestamp": checkpoint["timestamp"],
                            "completed_stages": len(checkpoint.get("completed_stages", [])),
                            "description": f"检查点 - {checkpoint['checkpoint_id']}"
                        })
            
            # 按时间排序
            checkpoints.sort(key=lambda x: x["timestamp"], reverse=True)
            
            return checkpoints
            
        except Exception as e:
            logger.error(f"Failed to list checkpoints: {e}")
            return []
    
    async def restore_from_checkpoint(self, session_id: str, checkpoint_id: str) -> Optional[UnifiedState]:
        """从检查点恢复状态"""
        
        try:
            checkpoint_data = await self.load_checkpoint(session_id, checkpoint_id)
            
            if not checkpoint_data:
                return None
            
            # 恢复状态
            state_data = checkpoint_data.get("state_data", {})
            state = UnifiedState(**state_data)
            
            # 恢复工作流状态
            state.shared_context["restored_from_checkpoint"] = {
                "checkpoint_id": checkpoint_id,
                "restored_at": time.time()
            }
            
            # 保存恢复的状态
            await self.save_state(state)
            
            logger.info(f"State restored from checkpoint {checkpoint_id}")
            return state
            
        except Exception as e:
            logger.error(f"Failed to restore from checkpoint: {e}")
            return None
    
    async def update_state_field(self, session_id: str, field_path: str, value: Any) -> bool:
        """更新状态字段"""
        
        try:
            state = await self.load_state(session_id)
            if not state:
                return False
            
            # 支持嵌套字段更新，如 "shared_context.user_preferences"
            field_parts = field_path.split(".")
            target = state
            
            for part in field_parts[:-1]:
                if hasattr(target, part):
                    target = getattr(target, part)
                else:
                    return False
            
            # 设置最终值
            final_field = field_parts[-1]
            if hasattr(target, final_field):
                setattr(target, final_field, value)
            elif isinstance(target, dict):
                target[final_field] = value
            else:
                return False
            
            # 保存更新后的状态
            await self.save_state(state)
            
            logger.debug(f"State field updated: {field_path} = {value}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to update state field: {e}")
            return False
    
    def add_state_change_listener(self, listener: callable):
        """添加状态变更监听器"""
        
        self.state_change_listeners.append(listener)
        logger.debug("State change listener added")
    
    def remove_state_change_listener(self, listener: callable):
        """移除状态变更监听器"""
        
        if listener in self.state_change_listeners:
            self.state_change_listeners.remove(listener)
            logger.debug("State change listener removed")
    
    async def _persist_state(self, state: UnifiedState):
        """持久化状态"""
        
        if self.storage_backend == "redis":
            await self._save_state_to_redis(state)
        # 可以扩展支持其他存储后端
    
    async def _load_state_from_storage(self, session_id: str) -> Optional[UnifiedState]:
        """从存储加载状态"""
        
        if self.storage_backend == "redis":
            return await self._load_state_from_redis(session_id)
        
        return None
    
    async def _delete_state_from_storage(self, session_id: str):
        """从存储删除状态"""
        
        if self.storage_backend == "redis":
            await self._delete_state_from_redis(session_id)
    
    async def _save_state_to_redis(self, state: UnifiedState):
        """保存状态到Redis"""
        
        key = f"state:{state.session_id}"
        
        # 序列化状态
        state_data = pickle.dumps(state)
        
        # 保存到Redis，设置过期时间
        await asyncio.get_event_loop().run_in_executor(
            None, self.redis_client.setex, key, self.cache_ttl, state_data
        )
        
        # 保存会话索引
        await self._update_session_index(state)
    
    async def _load_state_from_redis(self, session_id: str) -> Optional[UnifiedState]:
        """从Redis加载状态"""
        
        key = f"state:{session_id}"
        
        state_data = await asyncio.get_event_loop().run_in_executor(
            None, self.redis_client.get, key
        )
        
        if state_data:
            return pickle.loads(state_data)
        
        return None
    
    async def _delete_state_from_redis(self, session_id: str):
        """从Redis删除状态"""
        
        key = f"state:{session_id}"
        
        await asyncio.get_event_loop().run_in_executor(
            None, self.redis_client.delete, key
        )
        
        # 从会话索引中删除
        await self._remove_from_session_index(session_id)
    
    async def _update_session_index(self, state: UnifiedState):
        """更新会话索引"""
        
        # 用户会话索引
        if state.user_id:
            user_sessions_key = f"user_sessions:{state.user_id}"
            session_info = json.dumps({
                "session_id": state.session_id,
                "status": state.status.value,
                "created_at": state.created_at,
                "updated_at": state.updated_at,
                "topic": state.topic
            })
            
            await asyncio.get_event_loop().run_in_executor(
                None, self.redis_client.hset, user_sessions_key, state.session_id, session_info
            )
        
        # 全局会话索引
        global_sessions_key = "all_sessions"
        session_info = json.dumps({
            "session_id": state.session_id,
            "user_id": state.user_id,
            "status": state.status.value,
            "created_at": state.created_at,
            "updated_at": state.updated_at,
            "topic": state.topic
        })
        
        await asyncio.get_event_loop().run_in_executor(
            None, self.redis_client.hset, global_sessions_key, state.session_id, session_info
        )
    
    async def _remove_from_session_index(self, session_id: str):
        """从会话索引中删除"""
        
        # 从全局索引删除
        global_sessions_key = "all_sessions"
        
        await asyncio.get_event_loop().run_in_executor(
            None, self.redis_client.hdel, global_sessions_key, session_id
        )
    
    async def _list_sessions_from_redis(self, user_id: Optional[str] = None, status: Optional[AgentStatus] = None) -> List[Dict[str, Any]]:
        """从Redis列出会话"""
        
        sessions = []
        
        if user_id:
            # 从用户会话索引获取
            user_sessions_key = f"user_sessions:{user_id}"
            session_data = await asyncio.get_event_loop().run_in_executor(
                None, self.redis_client.hgetall, user_sessions_key
            )
            
            for session_id, session_info in session_data.items():
                session = json.loads(session_info)
                if status and session["status"] != status.value:
                    continue
                sessions.append(session)
        else:
            # 从全局索引获取
            global_sessions_key = "all_sessions"
            session_data = await asyncio.get_event_loop().run_in_executor(
                None, self.redis_client.hgetall, global_sessions_key
            )
            
            for session_id, session_info in session_data.items():
                session = json.loads(session_info)
                if status and session["status"] != status.value:
                    continue
                sessions.append(session)
        
        return sessions
    
    async def _notify_state_change(self, state: UnifiedState, operation: str):
        """通知状态变更"""
        
        for listener in self.state_change_listeners:
            try:
                if asyncio.iscoroutinefunction(listener):
                    await listener(state, operation)
                else:
                    listener(state, operation)
            except Exception as e:
                logger.error(f"State change listener error: {e}")
    
    async def _cleanup_cache_periodically(self):
        """定期清理缓存"""
        
        while True:
            try:
                await asyncio.sleep(300)  # 每5分钟清理一次
                
                current_time = time.time()
                expired_sessions = []
                
                for session_id, state in self.state_cache.items():
                    if current_time - state.updated_at > self.cache_ttl:
                        expired_sessions.append(session_id)
                
                for session_id in expired_sessions:
                    del self.state_cache[session_id]
                
                # 如果缓存过大，清理最老的条目
                if len(self.state_cache) > self.max_cache_size:
                    sorted_sessions = sorted(
                        self.state_cache.items(),
                        key=lambda x: x[1].updated_at
                    )
                    
                    # 删除最老的20%
                    cleanup_count = int(len(sorted_sessions) * 0.2)
                    for i in range(cleanup_count):
                        session_id, _ = sorted_sessions[i]
                        del self.state_cache[session_id]
                
                if expired_sessions or cleanup_count > 0:
                    logger.debug(f"Cache cleanup: removed {len(expired_sessions) + cleanup_count} sessions")
                
            except Exception as e:
                logger.error(f"Cache cleanup error: {e}")
    
    async def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        
        health_info = {
            "status": "healthy",
            "storage_backend": self.storage_backend,
            "cache_size": len(self.state_cache),
            "max_cache_size": self.max_cache_size
        }
        
        # 检查Redis连接
        if self.storage_backend == "redis" and self.redis_client:
            try:
                await asyncio.get_event_loop().run_in_executor(
                    None, self.redis_client.ping
                )
                health_info["redis_status"] = "connected"
            except Exception as e:
                health_info["redis_status"] = "error"
                health_info["redis_error"] = str(e)
                health_info["status"] = "degraded"
        
        return health_info
    
    async def shutdown(self):
        """关闭状态管理器"""
        
        logger.info("Shutting down State Manager...")
        
        # 保存所有缓存的状态
        for session_id, state in self.state_cache.items():
            await self._persist_state(state)
        
        # 关闭Redis连接
        if self.redis_client:
            self.redis_client.close()
        
        logger.info("State Manager shutdown complete")