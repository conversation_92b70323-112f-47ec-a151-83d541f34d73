"""
Tool System - MediaAgent V2 工具系统
"""

import asyncio
import logging
from typing import Dict, Any, List, Optional, Type
import importlib
import inspect

from ..tools.base_tool import BaseToolV2, ToolRegistry
from .models import UnifiedState, ToolResult

logger = logging.getLogger(__name__)


class ToolSystem:
    """
    工具系统
    
    职责：
    - 工具注册和发现
    - 统一调用接口
    - 工具链编排
    - 工具性能监控
    """
    
    def __init__(self, config: Dict[str, Any] = None):
        self.config = config or {}
        
        # 工具注册表
        self.tool_registry = ToolRegistry()
        
        # 工具配置
        self.tool_configs: Dict[str, Dict[str, Any]] = {}
        
        # 工具性能统计
        self.tool_stats: Dict[str, Dict[str, Any]] = {}
        
        # 工具依赖关系
        self.tool_dependencies: Dict[str, List[str]] = {}
        
        # 并发控制
        self.tool_semaphores: Dict[str, asyncio.Semaphore] = {}
        self.max_concurrent_tools = self.config.get("max_concurrent_tools", 5)
        
        logger.info("Tool System initialized")
    
    async def initialize(self):
        """初始化工具系统"""
        
        logger.info("Initializing Tool System...")
        
        # 自动发现并注册工具
        await self._auto_discover_tools()
        
        # 初始化工具依赖关系
        self._initialize_tool_dependencies()
        
        # 初始化并发控制
        self._initialize_concurrency_control()
        
        logger.info(f"Tool System initialized with {len(self.tool_registry.tools)} tools")
    
    async def execute_tool(
        self, 
        tool_name: str, 
        parameters: Dict[str, Any], 
        state: UnifiedState
    ) -> ToolResult:
        """执行工具"""
        
        tool = self.tool_registry.get_tool(tool_name)
        if not tool:
            logger.error(f"Tool {tool_name} not found")
            return ToolResult(
                success=False,
                error=f"Tool {tool_name} not found"
            )
        
        # 获取工具的并发信号量
        semaphore = self.tool_semaphores.get(tool_name)
        if semaphore:
            async with semaphore:
                return await self._execute_tool_with_monitoring(tool, parameters, state)
        else:
            return await self._execute_tool_with_monitoring(tool, parameters, state)
    
    async def execute_tool_chain(
        self, 
        chain_config: Dict[str, Any], 
        state: UnifiedState
    ) -> List[ToolResult]:
        """执行工具链"""
        
        chain_name = chain_config.get("name", "unnamed_chain")
        tools = chain_config.get("tools", [])
        
        logger.info(f"Executing tool chain: {chain_name}")
        
        results = []
        
        try:
            for tool_config in tools:
                tool_name = tool_config["name"]
                parameters = tool_config.get("parameters", {})
                
                # 支持从前一个工具的结果中提取参数
                if "parameter_mapping" in tool_config:
                    parameters = self._map_parameters_from_results(
                        parameters, 
                        tool_config["parameter_mapping"], 
                        results
                    )
                
                # 执行工具
                result = await self.execute_tool(tool_name, parameters, state)
                results.append(result)
                
                # 如果工具失败且配置为停止，则中断链
                if not result.success and tool_config.get("stop_on_failure", True):
                    logger.error(f"Tool chain stopped at {tool_name} due to failure")
                    break
                
                # 检查是否需要等待
                if "delay" in tool_config:
                    await asyncio.sleep(tool_config["delay"])
            
            logger.info(f"Tool chain {chain_name} completed with {len(results)} tools")
            return results
            
        except Exception as e:
            logger.error(f"Tool chain execution failed: {e}")
            
            # 返回包含错误的结果
            if not results:
                results.append(ToolResult(
                    success=False,
                    error=f"Tool chain execution failed: {str(e)}"
                ))
            
            return results
    
    async def get_available_tools(self, capabilities: List[str] = None) -> List[Dict[str, Any]]:
        """获取可用工具列表"""
        
        available_tools = []
        
        for tool_name, tool in self.tool_registry.tools.items():
            tool_info = {
                "name": tool_name,
                "description": tool.description,
                "metadata": tool.metadata,
                "stats": self.tool_stats.get(tool_name, {}),
                "config": self.tool_configs.get(tool_name, {})
            }
            
            # 如果指定了能力过滤
            if capabilities:
                tool_capabilities = tool.metadata.get("capabilities", [])
                if not any(cap in tool_capabilities for cap in capabilities):
                    continue
            
            available_tools.append(tool_info)
        
        return available_tools
    
    async def suggest_tools_for_task(self, task_description: str, requirements: Dict[str, Any] = None) -> List[str]:
        """为任务推荐工具"""
        
        # 简单的关键词匹配推荐
        suggested_tools = []
        task_lower = task_description.lower()
        
        keyword_mappings = {
            "搜索": ["web_search_tool", "content_search_tool"],
            "search": ["web_search_tool", "content_search_tool"],
            "文件": ["file_tool", "content_analyzer"],
            "file": ["file_tool", "content_analyzer"],
            "分析": ["content_analyzer", "quality_checker"],
            "analyze": ["content_analyzer", "quality_checker"],
            "质量": ["quality_checker", "content_analyzer"],
            "quality": ["quality_checker", "content_analyzer"],
            "seo": ["seo_tool", "content_analyzer"],
            "浏览器": ["browser_tool"],
            "browser": ["browser_tool"],
            "代码": ["sandbox_tool", "code_executor"],
            "code": ["sandbox_tool", "code_executor"]
        }
        
        for keyword, tools in keyword_mappings.items():
            if keyword in task_lower:
                for tool in tools:
                    if tool in self.tool_registry.tools and tool not in suggested_tools:
                        suggested_tools.append(tool)
        
        # 根据工具性能排序
        suggested_tools.sort(key=lambda tool_name: self._get_tool_score(tool_name), reverse=True)
        
        return suggested_tools[:5]  # 返回前5个推荐
    
    async def validate_tool_chain(self, chain_config: Dict[str, Any]) -> Dict[str, Any]:
        """验证工具链配置"""
        
        validation_result = {
            "valid": True,
            "errors": [],
            "warnings": [],
            "estimated_time": 0.0
        }
        
        tools = chain_config.get("tools", [])
        
        for i, tool_config in enumerate(tools):
            tool_name = tool_config.get("name")
            
            if not tool_name:
                validation_result["errors"].append(f"Tool {i} missing name")
                validation_result["valid"] = False
                continue
            
            # 检查工具是否存在
            if tool_name not in self.tool_registry.tools:
                validation_result["errors"].append(f"Tool {tool_name} not found")
                validation_result["valid"] = False
                continue
            
            # 检查参数
            parameters = tool_config.get("parameters", {})
            tool = self.tool_registry.tools[tool_name]
            
            # 这里可以添加更详细的参数验证
            
            # 估算执行时间
            tool_stats = self.tool_stats.get(tool_name, {})
            avg_time = tool_stats.get("average_execution_time", 5.0)  # 默认5秒
            validation_result["estimated_time"] += avg_time
        
        return validation_result
    
    def register_tool(self, tool: BaseToolV2, config: Dict[str, Any] = None) -> bool:
        """注册工具"""
        
        try:
            # 注册到工具注册表
            self.tool_registry.register_tool(tool)
            
            # 保存工具配置
            if config:
                self.tool_configs[tool.name] = config
            
            # 初始化统计信息
            self.tool_stats[tool.name] = {
                "total_executions": 0,
                "successful_executions": 0,  
                "failed_executions": 0,
                "average_execution_time": 0.0,
                "last_execution": None
            }
            
            # 设置并发控制
            max_concurrent = config.get("max_concurrent", 3) if config else 3
            self.tool_semaphores[tool.name] = asyncio.Semaphore(max_concurrent)
            
            logger.info(f"Tool {tool.name} registered successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to register tool {tool.name}: {e}")
            return False
    
    def unregister_tool(self, tool_name: str) -> bool:
        """注销工具"""
        
        try:
            # 从注册表移除
            if tool_name in self.tool_registry.tools:
                del self.tool_registry.tools[tool_name]
            
            # 清理配置和统计
            if tool_name in self.tool_configs:
                del self.tool_configs[tool_name]
            
            if tool_name in self.tool_stats:
                del self.tool_stats[tool_name]
            
            if tool_name in self.tool_semaphores:
                del self.tool_semaphores[tool_name]
            
            logger.info(f"Tool {tool_name} unregistered successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to unregister tool {tool_name}: {e}")
            return False
    
    async def _execute_tool_with_monitoring(
        self, 
        tool: BaseToolV2, 
        parameters: Dict[str, Any], 
        state: UnifiedState
    ) -> ToolResult:
        """带监控的工具执行"""
        
        tool_name = tool.name
        start_time = asyncio.get_event_loop().time()
        
        try:
            # 更新统计信息
            self.tool_stats[tool_name]["total_executions"] += 1
            
            # 执行工具
            result = await tool.execute_with_tracking(parameters, state)
            
            # 更新执行统计
            execution_time = asyncio.get_event_loop().time() - start_time
            stats = self.tool_stats[tool_name]
            
            if result.success:
                stats["successful_executions"] += 1
            else:
                stats["failed_executions"] += 1
            
            # 更新平均执行时间
            total_executions = stats["total_executions"]
            current_avg = stats["average_execution_time"]
            stats["average_execution_time"] = (
                (current_avg * (total_executions - 1) + execution_time) / total_executions
            )
            stats["last_execution"] = asyncio.get_event_loop().time()
            
            logger.debug(f"Tool {tool_name} executed in {execution_time:.2f}s")
            return result
            
        except Exception as e:
            logger.error(f"Tool {tool_name} execution failed: {e}")
            
            # 更新失败统计
            self.tool_stats[tool_name]["failed_executions"] += 1
            
            return ToolResult(
                success=False,
                error=f"Tool execution failed: {str(e)}",
                execution_time=asyncio.get_event_loop().time() - start_time,
                tool_name=tool_name,
                parameters=parameters
            )
    
    async def _auto_discover_tools(self):
        """自动发现工具"""
        
        # 扫描tools包
        tools_package_path = "backend.agent_v2.tools"
        
        try:
            # 导入tools包
            tools_package = importlib.import_module(tools_package_path)
            
            # 获取所有模块
            import pkgutil
            for importer, modname, ispkg in pkgutil.iter_modules(tools_package.__path__):
                if not ispkg and modname != "base_tool":
                    try:
                        # 导入模块
                        module = importlib.import_module(f"{tools_package_path}.{modname}")
                        
                        # 查找工具类
                        for name, obj in inspect.getmembers(module):
                            if (inspect.isclass(obj) and 
                                issubclass(obj, BaseToolV2) and 
                                obj != BaseToolV2):
                                
                                # 实例化并注册工具
                                tool_instance = obj()
                                self.register_tool(tool_instance)
                                
                    except Exception as e:
                        logger.error(f"Failed to load tool module {modname}: {e}")
                        
        except ImportError as e:
            logger.warning(f"Tools package not found: {e}")
            # 注册一些内置工具
            await self._register_builtin_tools()
    
    async def _register_builtin_tools(self):
        """注册内置工具"""
        
        logger.info("Registering built-in tools...")
        
        # 这里可以注册一些基础工具
        # 实际实现中，这些工具会在对应的模块中定义
        
        class DummyTool(BaseToolV2):
            def __init__(self):
                super().__init__(name="dummy_tool", description="Dummy tool for testing")
            
            async def execute(self, parameters: Dict[str, Any], state: UnifiedState) -> ToolResult:
                return ToolResult(success=True, data="Dummy result")
            
            def _get_metadata(self) -> Dict[str, Any]:
                return {
                    "capabilities": ["testing"],
                    "version": "1.0.0"
                }
        
        self.register_tool(DummyTool())
    
    def _initialize_tool_dependencies(self):
        """初始化工具依赖关系"""
        
        # 定义工具依赖关系
        self.tool_dependencies = {
            "content_analyzer": ["file_tool"],
            "seo_tool": ["content_analyzer"],
            "quality_checker": ["content_analyzer"]
        }
    
    def _initialize_concurrency_control(self):
        """初始化并发控制"""
        
        # 为没有设置信号量的工具设置默认信号量
        for tool_name in self.tool_registry.tools:
            if tool_name not in self.tool_semaphores:
                self.tool_semaphores[tool_name] = asyncio.Semaphore(3)  # 默认最多3个并发
    
    def _map_parameters_from_results(
        self, 
        base_parameters: Dict[str, Any], 
        parameter_mapping: Dict[str, str], 
        results: List[ToolResult]
    ) -> Dict[str, Any]:
        """从之前的结果中映射参数"""
        
        mapped_parameters = base_parameters.copy()
        
        for param_name, source_path in parameter_mapping.items():
            # source_path格式: "result[0].data.content"
            try:
                parts = source_path.split(".")
                if parts[0].startswith("result[") and parts[0].endswith("]"):
                    # 提取结果索引
                    index = int(parts[0][7:-1])
                    if 0 <= index < len(results):
                        value = results[index]
                        
                        # 遍历路径获取值
                        for part in parts[1:]:
                            if hasattr(value, part):
                                value = getattr(value, part)
                            elif isinstance(value, dict) and part in value:
                                value = value[part]
                            else:
                                value = None
                                break
                        
                        if value is not None:
                            mapped_parameters[param_name] = value
                            
            except Exception as e:
                logger.warning(f"Failed to map parameter {param_name}: {e}")
        
        return mapped_parameters
    
    def _get_tool_score(self, tool_name: str) -> float:
        """获取工具评分"""
        
        stats = self.tool_stats.get(tool_name, {})
        
        # 基于成功率和执行时间计算评分
        total_executions = stats.get("total_executions", 0)
        if total_executions == 0:
            return 0.5  # 默认评分
        
        success_rate = stats.get("successful_executions", 0) / total_executions
        avg_time = stats.get("average_execution_time", 10.0)
        
        # 评分公式：成功率权重70%，速度权重30%
        # 速度评分：1 / (1 + avg_time/10) 
        speed_score = 1 / (1 + avg_time / 10)
        
        return success_rate * 0.7 + speed_score * 0.3
    
    async def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        
        health_info = {
            "status": "healthy",
            "total_tools": len(self.tool_registry.tools),
            "active_tools": 0,
            "tool_health": {}
        }
        
        # 检查每个工具的健康状态
        for tool_name, tool in self.tool_registry.tools.items():
            try:
                if hasattr(tool, 'health_check'):
                    tool_health = await tool.health_check()
                    health_info["tool_health"][tool_name] = tool_health
                    
                    if tool_health.get("status") == "healthy":
                        health_info["active_tools"] += 1
                else:
                    health_info["tool_health"][tool_name] = {
                        "status": "unknown",
                        "message": "No health check method"
                    }
                    
            except Exception as e:
                health_info["tool_health"][tool_name] = {
                    "status": "error",
                    "error": str(e)
                }
        
        return health_info
    
    async def shutdown(self):
        """关闭工具系统"""
        
        logger.info("Shutting down Tool System...")
        
        # 关闭所有工具
        for tool_name, tool in self.tool_registry.tools.items():
            if hasattr(tool, 'shutdown'):
                try:
                    await tool.shutdown()
                except Exception as e:
                    logger.error(f"Error shutting down tool {tool_name}: {e}")
        
        logger.info("Tool System shutdown complete")