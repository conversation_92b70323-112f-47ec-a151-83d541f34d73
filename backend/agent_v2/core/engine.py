"""
MediaAgent V2 Core Engine - 核心引擎
"""

import asyncio
import logging
from typing import Dict, Any, List, Optional
import time

from .models import UnifiedState, AgentResponse, AgentStatus
from .planning_agent import PlanningAgent
from .agent_registry import AgentRegistry
from .workflow_engine import WorkflowEngine
from .state_manager import StateManager
from .tool_system import ToolSystem

logger = logging.getLogger(__name__)


class MediaAgentV2Engine:
    """
    MediaAgent V2 核心引擎
    
    职责：
    - 系统初始化和启动
    - 组件协调管理
    - 全局状态管理
    - 错误处理和恢复
    """
    
    def __init__(self, config: Dict[str, Any] = None):
        self.config = config or {}
        self.is_initialized = False
        self.start_time = time.time()
        
        # 核心组件
        self.planning_agent: Optional[PlanningAgent] = None
        self.agent_registry: Optional[AgentRegistry] = None
        self.workflow_engine: Optional[WorkflowEngine] = None
        self.state_manager: Optional[StateManager] = None
        self.tool_system: Optional[ToolSystem] = None
        
        # 运行时状态
        self.active_sessions: Dict[str, UnifiedState] = {}
        self.system_metrics: Dict[str, Any] = {}
        
        logger.info("MediaAgent V2 Engine initialized")
    
    async def initialize(self):
        """初始化系统"""
        
        if self.is_initialized:
            logger.warning("Engine already initialized")
            return
        
        logger.info("Initializing MediaAgent V2 Engine...")
        
        try:
            # 1. 初始化状态管理器
            self.state_manager = StateManager(self.config.get("state_config", {}))
            await self.state_manager.initialize()
            
            # 2. 初始化工具系统
            self.tool_system = ToolSystem(self.config.get("tool_config", {}))
            await self.tool_system.initialize()
            
            # 3. 初始化Agent注册中心
            self.agent_registry = AgentRegistry(self.config.get("agent_config", {}))
            await self.agent_registry.initialize()
            
            # 4. 初始化工作流引擎
            self.workflow_engine = WorkflowEngine(
                agent_registry=self.agent_registry,
                tool_system=self.tool_system,
                state_manager=self.state_manager,
                config=self.config.get("workflow_config", {})
            )
            await self.workflow_engine.initialize()
            
            # 5. 初始化规划Agent
            self.planning_agent = PlanningAgent(
                agent_registry=self.agent_registry,
                workflow_engine=self.workflow_engine,
                tool_system=self.tool_system,
                config=self.config.get("planning_config", {})
            )
            await self.planning_agent.initialize()
            
            self.is_initialized = True
            logger.info("MediaAgent V2 Engine initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize engine: {e}")
            raise
    
    async def process_request(
        self, 
        request: Dict[str, Any], 
        user_id: Optional[str] = None
    ) -> AgentResponse:
        """处理用户请求"""
        
        if not self.is_initialized:
            await self.initialize()
        
        # 创建会话状态
        state = UnifiedState(
            user_id=user_id,
            topic=request.get("topic", ""),
            requirements=request.get("requirements", ""),
            keywords=request.get("keywords", []),
            target_length=request.get("target_length", 3500),
            language=request.get("language", "zh-CN"),
            tone=request.get("tone", "professional")
        )
        
        try:
            # 更新状态
            state.update_status(AgentStatus.INITIALIZING, "开始处理请求")
            
            # 保存状态
            await self.state_manager.save_state(state)
            self.active_sessions[state.session_id] = state
            
            # 使用规划Agent分析请求
            logger.info(f"Processing request for session {state.session_id}")
            response = await self.planning_agent.process_request(request, state)
            
            # 更新最终状态
            if response.success:
                state.update_status(AgentStatus.COMPLETED, "请求处理完成")
            else:
                state.update_status(AgentStatus.FAILED, "请求处理失败")
            
            # 保存最终状态
            await self.state_manager.save_state(state)
            
            return response
            
        except Exception as e:
            logger.error(f"Error processing request: {e}")
            state.add_error("engine_processing", str(e))
            state.update_status(AgentStatus.FAILED, f"处理失败: {str(e)}")
            
            await self.state_manager.save_state(state)
            
            return AgentResponse(
                success=False,
                content="",
                errors=[f"系统处理错误: {str(e)}"]
            )
        
        finally:
            # 清理会话（如果需要）
            if state.session_id in self.active_sessions:
                # 保持会话一段时间，便于恢复
                asyncio.create_task(self._cleanup_session_later(state.session_id))
    
    async def resume_session(self, session_id: str) -> Optional[UnifiedState]:
        """恢复会话"""
        
        if session_id in self.active_sessions:
            return self.active_sessions[session_id]
        
        # 从持久化存储中恢复
        state = await self.state_manager.load_state(session_id)
        if state:
            self.active_sessions[session_id] = state
            logger.info(f"Resumed session {session_id}")
        
        return state
    
    async def pause_session(self, session_id: str) -> bool:
        """暂停会话"""
        
        if session_id not in self.active_sessions:
            return False
        
        state = self.active_sessions[session_id]
        state.update_status(AgentStatus.PAUSED, "会话已暂停")
        
        await self.state_manager.save_state(state)
        
        # 通知工作流引擎暂停
        if self.workflow_engine:
            await self.workflow_engine.pause_session(session_id)
        
        logger.info(f"Paused session {session_id}")
        return True
    
    async def get_session_status(self, session_id: str) -> Optional[Dict[str, Any]]:
        """获取会话状态"""
        
        state = await self.resume_session(session_id)
        if not state:
            return None
        
        return {
            "session_id": session_id,
            "status": state.status.value,
            "current_stage": state.current_stage,
            "completed_stages": state.completed_stages,
            "progress": len(state.completed_stages) / max(1, len(state.planned_stages)) if hasattr(state, 'planned_stages') else 0,
            "created_at": state.created_at,
            "updated_at": state.updated_at,
            "errors": state.errors,
            "warnings": state.warnings
        }
    
    async def get_system_health(self) -> Dict[str, Any]:
        """获取系统健康状态"""
        
        health_info = {
            "status": "healthy",
            "uptime": time.time() - self.start_time,
            "active_sessions": len(self.active_sessions),
            "initialized": self.is_initialized,
            "components": {}
        }
        
        if self.is_initialized:
            # 检查各组件健康状态
            components = [
                ("planning_agent", self.planning_agent),
                ("agent_registry", self.agent_registry),
                ("workflow_engine", self.workflow_engine),
                ("state_manager", self.state_manager),
                ("tool_system", self.tool_system)
            ]
            
            for name, component in components:
                if component and hasattr(component, 'health_check'):
                    try:
                        health_info["components"][name] = await component.health_check()
                    except Exception as e:
                        health_info["components"][name] = {
                            "status": "unhealthy",
                            "error": str(e)
                        }
                        health_info["status"] = "degraded"
        
        return health_info
    
    async def shutdown(self):
        """关闭系统"""
        
        logger.info("Shutting down MediaAgent V2 Engine...")
        
        try:
            # 保存所有活跃会话
            for session_id, state in self.active_sessions.items():
                await self.state_manager.save_state(state)
            
            # 关闭各组件
            components = [
                self.workflow_engine,
                self.planning_agent,
                self.agent_registry,
                self.tool_system,
                self.state_manager
            ]
            
            for component in components:
                if component and hasattr(component, 'shutdown'):
                    try:
                        await component.shutdown()
                    except Exception as e:
                        logger.error(f"Error shutting down component: {e}")
            
            self.is_initialized = False
            self.active_sessions.clear()
            
            logger.info("MediaAgent V2 Engine shutdown complete")
            
        except Exception as e:
            logger.error(f"Error during shutdown: {e}")
    
    async def _cleanup_session_later(self, session_id: str, delay: int = 3600):
        """延迟清理会话"""
        
        await asyncio.sleep(delay)  # 默认1小时后清理
        
        if session_id in self.active_sessions:
            state = self.active_sessions[session_id]
            
            # 只清理已完成或失败的会话
            if state.status in [AgentStatus.COMPLETED, AgentStatus.FAILED]:
                del self.active_sessions[session_id]
                logger.info(f"Cleaned up session {session_id}")
    
    def get_metrics(self) -> Dict[str, Any]:
        """获取系统指标"""
        
        return {
            "uptime": time.time() - self.start_time,
            "active_sessions": len(self.active_sessions),
            "session_statuses": {
                status.value: sum(1 for s in self.active_sessions.values() if s.status == status)
                for status in AgentStatus
            },
            "system_metrics": self.system_metrics
        }