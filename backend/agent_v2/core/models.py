"""
Core data models for MediaAgent V2
"""

from typing import Dict, Any, List, Optional, Union
from enum import Enum
from dataclasses import dataclass, field
from datetime import datetime
import uuid
import time


class AgentStatus(str, Enum):
    """Agent执行状态"""
    IDLE = "idle"
    INITIALIZING = "initializing"
    PROCESSING = "processing"
    WAITING_INPUT = "waiting_input"
    COMPLETED = "completed"
    FAILED = "failed"
    PAUSED = "paused"


class TaskType(str, Enum):
    """任务类型"""
    ARTICLE_GENERATION = "article_generation"
    CONTENT_ANALYSIS = "content_analysis"
    RESEARCH = "research"
    EDITING = "editing"


@dataclass
class UnifiedState:
    """统一状态模型 - 融合现有两套系统的状态"""
    
    # 基础信息
    session_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    task_type: TaskType = TaskType.ARTICLE_GENERATION
    status: AgentStatus = AgentStatus.IDLE
    created_at: float = field(default_factory=time.time)
    updated_at: float = field(default_factory=time.time)
    
    # 用户和项目信息
    user_id: Optional[str] = None
    project_id: Optional[str] = None
    thread_id: Optional[str] = None
    
    # 任务参数 (来自Generator系统优化)
    topic: str = ""
    keywords: List[str] = field(default_factory=list)
    requirements: str = ""
    target_length: int = 3500
    language: str = "zh-CN"
    tone: str = "professional"
    
    # 执行上下文 (来自Backend/Agent系统)
    tools_used: List[str] = field(default_factory=list)
    sandbox_info: Dict[str, Any] = field(default_factory=dict)
    
    # 共享上下文 (借鉴Agno的session state概念)
    shared_context: Dict[str, Any] = field(default_factory=dict)
    
    # 工作流状态
    current_stage: str = ""
    completed_stages: List[str] = field(default_factory=list)
    stage_outputs: Dict[str, Any] = field(default_factory=dict)
    
    # 内容数据
    input_data: Dict[str, Any] = field(default_factory=dict)
    intermediate_data: Dict[str, Any] = field(default_factory=dict)
    output_data: Dict[str, Any] = field(default_factory=dict)
    
    # 质量和性能指标
    quality_metrics: Dict[str, float] = field(default_factory=dict)
    performance_metrics: Dict[str, float] = field(default_factory=dict)
    
    # 错误和警告
    errors: List[Dict[str, Any]] = field(default_factory=list)
    warnings: List[str] = field(default_factory=list)
    
    # 元数据
    metadata: Dict[str, Any] = field(default_factory=dict)
    timeline: List[Dict[str, Any]] = field(default_factory=list)
    
    def update_status(self, status: AgentStatus, message: str = ""):
        """更新状态"""
        self.status = status
        self.updated_at = time.time()
        
        # 记录状态变更到时间线
        self.timeline.append({
            "timestamp": self.updated_at,
            "event": "status_change",
            "old_status": getattr(self, '_previous_status', 'unknown'),
            "new_status": status.value,
            "message": message
        })
        
        self._previous_status = status.value
    
    def add_error(self, step: str, error_msg: str, error_details: Any = None):
        """添加错误记录"""
        self.errors.append({
            "timestamp": time.time(),
            "step": step,
            "message": error_msg,
            "details": error_details
        })
    
    def add_warning(self, warning_msg: str):
        """添加警告"""
        self.warnings.append(warning_msg)
    
    def mark_stage_completed(self, stage: str, output: Any = None):
        """标记阶段完成"""
        if stage not in self.completed_stages:
            self.completed_stages.append(stage)
        
        if output is not None:
            self.stage_outputs[stage] = output
        
        self.timeline.append({
            "timestamp": time.time(),
            "event": "stage_completed",
            "stage": stage,
            "total_completed": len(self.completed_stages)
        })


@dataclass 
class AgentResponse:
    """Agent响应模型"""
    success: bool
    content: str = ""
    data: Dict[str, Any] = field(default_factory=dict)
    errors: List[str] = field(default_factory=list)
    warnings: List[str] = field(default_factory=list)
    execution_time: float = 0.0
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    # 流式响应支持
    is_partial: bool = False
    progress: float = 0.0
    current_stage: str = ""


@dataclass
class ToolResult:
    """工具执行结果"""
    success: bool
    data: Any = None
    error: Optional[str] = None
    execution_time: float = 0.0
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    # 工具调用信息
    tool_name: str = ""
    parameters: Dict[str, Any] = field(default_factory=dict)


@dataclass
class QualityMetrics:
    """质量指标"""
    overall_score: float = 0.0
    content_quality: float = 0.0
    structure_quality: float = 0.0
    keyword_coverage: float = 0.0
    readability_score: float = 0.0
    length_compliance: float = 0.0
    
    # 详细指标
    word_count: int = 0
    paragraph_count: int = 0
    section_count: int = 0
    
    # 问题和建议
    issues: List[str] = field(default_factory=list)
    suggestions: List[str] = field(default_factory=list)


@dataclass
class UserContext:
    """用户上下文信息"""
    user_id: str
    preferences: Dict[str, Any] = field(default_factory=dict)
    history: List[Dict[str, Any]] = field(default_factory=list)
    learned_patterns: Dict[str, Any] = field(default_factory=dict)
    
    # 最近的交互
    recent_topics: List[str] = field(default_factory=list)
    recent_quality_scores: List[float] = field(default_factory=list)
    
    def update_preferences(self, **kwargs):
        """更新用户偏好"""
        self.preferences.update(kwargs)
    
    def add_history_entry(self, entry: Dict[str, Any]):
        """添加历史记录"""
        entry["timestamp"] = time.time()
        self.history.append(entry)
        
        # 保持最近50条记录
        if len(self.history) > 50:
            self.history = self.history[-50:]
    
    def get_preferred_length(self) -> int:
        """获取偏好的文章长度"""
        return self.preferences.get("preferred_length", 3500)
    
    def get_preferred_tone(self) -> str:
        """获取偏好的语调"""
        return self.preferences.get("preferred_tone", "professional")