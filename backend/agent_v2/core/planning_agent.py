"""
Planning Agent - MediaAgent V2 规划协调Agent
"""

import asyncio
import logging
from typing import Dict, Any, List, Optional, TYPE_CHECKING
import time

from .models import UnifiedState, AgentResponse, AgentStatus, TaskType
from .base_agent import BaseAgentV2

if TYPE_CHECKING:
    from .agent_registry import AgentRegistry
    from .workflow_engine import WorkflowEngine
    from .tool_system import ToolSystem

logger = logging.getLogger(__name__)


class PlanningAgent(BaseAgentV2):
    """
    规划协调Agent - 系统的核心协调者
    
    职责：
    - 用户意图分析和理解
    - 任务规划和分解
    - Agent调度和协调
    - 用户交互管理
    """
    
    def __init__(
        self,
        agent_registry: 'AgentRegistry',
        workflow_engine: 'WorkflowEngine', 
        tool_system: 'ToolSystem',
        config: Dict[str, Any] = None
    ):
        super().__init__(name="planning_agent", config=config)
        
        self.agent_registry = agent_registry
        self.workflow_engine = workflow_engine
        self.tool_system = tool_system
        
        # 规划配置
        self.planning_config = config or {}
        self.max_planning_depth = self.planning_config.get("max_planning_depth", 5)
        self.enable_user_interaction = self.planning_config.get("enable_user_interaction", True)
        
        # 任务模板
        self.task_templates = {
            TaskType.ARTICLE_GENERATION: self._get_article_generation_template(),
            TaskType.CONTENT_ANALYSIS: self._get_content_analysis_template(),
            TaskType.RESEARCH: self._get_research_template(),
            TaskType.EDITING: self._get_editing_template()
        }
        
        logger.info("Planning Agent initialized")
    
    async def initialize(self):
        """初始化Planning Agent"""
        logger.info("Initializing Planning Agent...")
        # 可以在这里加载预训练的规划模型或规则
        pass
    
    async def process_request(self, request: Dict[str, Any], state: UnifiedState) -> AgentResponse:
        """处理用户请求的主入口"""
        
        try:
            # 1. 分析用户意图
            intent_analysis = await self.analyze_user_intent(request, state)
            
            # 2. 制定执行计划
            execution_plan = await self.create_execution_plan(intent_analysis, state)
            
            # 3. 如果启用用户交互，请求确认
            if self.enable_user_interaction:
                plan_confirmed = await self.request_plan_confirmation(execution_plan, state)
                if not plan_confirmed:
                    return AgentResponse(
                        success=False,
                        content="用户取消了执行计划",
                        data={"plan": execution_plan}
                    )
            
            # 4. 执行计划
            execution_result = await self.execute_plan(execution_plan, state)
            
            return execution_result
            
        except Exception as e:
            logger.error(f"Planning Agent processing failed: {e}")
            state.add_error("planning", str(e))
            
            return AgentResponse(
                success=False,
                content="规划处理失败",
                errors=[str(e)]
            )
    
    async def analyze_user_intent(self, request: Dict[str, Any], state: UnifiedState) -> Dict[str, Any]:
        """分析用户意图"""
        
        logger.info(f"Analyzing user intent for session {state.session_id}")
        
        # 从请求中提取关键信息
        topic = request.get("topic", "")
        requirements = request.get("requirements", "")
        keywords = request.get("keywords", [])
        
        # 智能意图分析
        intent_analysis = {
            "primary_task": self._determine_primary_task(topic, requirements),
            "complexity_level": self._assess_complexity(topic, requirements, keywords),
            "required_agents": [],
            "estimated_steps": [],
            "user_preferences": self._extract_user_preferences(request),
            "constraints": self._identify_constraints(request)
        }
        
        # 根据任务类型确定需要的Agent
        task_type = intent_analysis["primary_task"]
        if task_type == TaskType.ARTICLE_GENERATION:
            intent_analysis["required_agents"] = ["article_agent", "research_agent", "analysis_agent"]
            intent_analysis["estimated_steps"] = [
                "研究和信息收集",
                "内容规划和大纲创建", 
                "文章撰写",
                "质量检查和优化",
                "最终审核"
            ]
        elif task_type == TaskType.RESEARCH:
            intent_analysis["required_agents"] = ["research_agent", "analysis_agent"]
            intent_analysis["estimated_steps"] = [
                "定义研究范围",
                "信息收集和搜索",
                "数据分析和整理",
                "研究报告生成"
            ]
        
        # 更新状态
        state.shared_context["intent_analysis"] = intent_analysis
        state.task_type = task_type
        
        logger.info(f"Intent analysis completed: {intent_analysis['primary_task']}")
        return intent_analysis
    
    async def create_execution_plan(self, intent_analysis: Dict[str, Any], state: UnifiedState) -> Dict[str, Any]:
        """创建执行计划"""
        
        logger.info(f"Creating execution plan for session {state.session_id}")
        
        task_type = intent_analysis["primary_task"]
        template = self.task_templates.get(task_type, {})
        
        execution_plan = {
            "plan_id": f"plan_{state.session_id}_{int(time.time())}",
            "task_type": task_type.value,
            "complexity_level": intent_analysis["complexity_level"],
            "estimated_duration": self._estimate_duration(intent_analysis),
            "stages": [],
            "agent_assignments": {},
            "success_criteria": template.get("success_criteria", []),
            "quality_thresholds": template.get("quality_thresholds", {}),
            "checkpoints": []
        }
        
        # 构建执行阶段
        stages = self._build_execution_stages(intent_analysis, template)
        execution_plan["stages"] = stages
        
        # 分配Agent
        for stage in stages:
            agent_name = stage.get("assigned_agent")
            if agent_name:
                if agent_name not in execution_plan["agent_assignments"]:
                    execution_plan["agent_assignments"][agent_name] = []
                execution_plan["agent_assignments"][agent_name].append(stage["stage_id"])
        
        # 设置检查点
        execution_plan["checkpoints"] = self._define_checkpoints(stages)
        
        # 保存计划到状态
        state.shared_context["execution_plan"] = execution_plan
        state.planned_stages = [stage["stage_id"] for stage in stages]
        
        logger.info(f"Execution plan created with {len(stages)} stages")
        return execution_plan
    
    async def request_plan_confirmation(self, execution_plan: Dict[str, Any], state: UnifiedState) -> bool:
        """请求用户确认计划"""
        
        # 这里应该通过WebSocket或其他方式与前端交互
        # 目前简化为自动确认，实际实现中需要等待用户响应
        
        confirmation_request = {
            "type": "plan_confirmation",
            "plan_summary": {
                "task_type": execution_plan["task_type"],
                "estimated_duration": execution_plan["estimated_duration"],
                "stages": [
                    {
                        "name": stage["name"],
                        "description": stage["description"],
                        "estimated_time": stage.get("estimated_time", "未知")
                    }
                    for stage in execution_plan["stages"]
                ],
                "agents_involved": list(execution_plan["agent_assignments"].keys())
            }
        }
        
        # 保存确认请求到状态
        state.shared_context["pending_confirmation"] = confirmation_request
        state.update_status(AgentStatus.WAITING_INPUT, "等待用户确认执行计划")
        
        # 模拟用户确认（在实际实现中，这里会等待真实的用户响应）
        await asyncio.sleep(0.1)  # 模拟网络延迟
        
        logger.info(f"Plan confirmation requested for session {state.session_id}")
        return True  # 目前总是返回确认
    
    async def execute_plan(self, execution_plan: Dict[str, Any], state: UnifiedState) -> AgentResponse:
        """执行计划"""
        
        logger.info(f"Executing plan {execution_plan['plan_id']}")
        
        state.update_status(AgentStatus.PROCESSING, "开始执行计划")
        
        try:
            # 使用工作流引擎执行计划
            workflow_result = await self.workflow_engine.execute_plan(execution_plan, state)
            
            if workflow_result.success:
                logger.info(f"Plan execution completed successfully for session {state.session_id}")
                return AgentResponse(
                    success=True,
                    content=workflow_result.content,
                    data=workflow_result.data,
                    metadata={
                        "plan_id": execution_plan["plan_id"],
                        "execution_time": workflow_result.execution_time,
                        "stages_completed": len(state.completed_stages)
                    }
                )
            else:
                logger.error(f"Plan execution failed for session {state.session_id}")
                return workflow_result
                
        except Exception as e:
            logger.error(f"Plan execution error: {e}")
            return AgentResponse(
                success=False,
                content="计划执行失败",
                errors=[str(e)]
            )
    
    def _determine_primary_task(self, topic: str, requirements: str) -> TaskType:
        """确定主要任务类型"""
        
        # 简单的关键词匹配逻辑，实际中可以使用更复杂的NLP分析
        content = f"{topic} {requirements}".lower()
        
        if any(word in content for word in ["文章", "写作", "创作", "article", "write"]):
            return TaskType.ARTICLE_GENERATION
        elif any(word in content for word in ["研究", "调研", "搜索", "research", "investigate"]):
            return TaskType.RESEARCH
        elif any(word in content for word in ["分析", "评估", "analyze", "evaluate"]):
            return TaskType.CONTENT_ANALYSIS
        elif any(word in content for word in ["编辑", "修改", "优化", "edit", "improve"]):
            return TaskType.EDITING
        
        # 默认为文章生成
        return TaskType.ARTICLE_GENERATION
    
    def _assess_complexity(self, topic: str, requirements: str, keywords: List[str]) -> str:
        """评估任务复杂度"""
        
        complexity_score = 0
        
        # 基于文本长度
        text_length = len(topic) + len(requirements)
        if text_length > 500:
            complexity_score += 2
        elif text_length > 200:
            complexity_score += 1
        
        # 基于关键词数量
        if len(keywords) > 10:
            complexity_score += 2
        elif len(keywords) > 5:
            complexity_score += 1
        
        # 基于需求复杂性（简单关键词检测）
        complex_indicators = ["深入分析", "详细研究", "综合对比", "多角度", "专业", "技术"]
        complexity_score += sum(1 for indicator in complex_indicators if indicator in requirements)
        
        if complexity_score >= 5:
            return "high"
        elif complexity_score >= 3:
            return "medium"
        else:
            return "low"
    
    def _extract_user_preferences(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """提取用户偏好"""
        
        return {
            "language": request.get("language", "zh-CN"),
            "tone": request.get("tone", "professional"), 
            "target_length": request.get("target_length", 3500),
            "format_preferences": request.get("format_preferences", {}),
            "quality_requirements": request.get("quality_requirements", {})
        }
    
    def _identify_constraints(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """识别约束条件"""
        
        return {
            "time_constraints": request.get("deadline"),
            "length_constraints": {
                "min_length": request.get("min_length"),
                "max_length": request.get("max_length"),
                "target_length": request.get("target_length", 3500)
            },
            "content_constraints": request.get("content_restrictions", []),
            "quality_constraints": request.get("quality_requirements", {})
        }
    
    def _build_execution_stages(self, intent_analysis: Dict[str, Any], template: Dict[str, Any]) -> List[Dict[str, Any]]:
        """构建执行阶段"""
        
        stages = []
        base_stages = template.get("stages", [])
        
        for i, stage_template in enumerate(base_stages):
            stage = {
                "stage_id": f"stage_{i+1}",
                "name": stage_template["name"],
                "description": stage_template["description"],
                "assigned_agent": stage_template.get("agent"),
                "dependencies": stage_template.get("dependencies", []),
                "estimated_time": stage_template.get("estimated_time", "10-15分钟"),
                "success_criteria": stage_template.get("success_criteria", []),
                "tools_required": stage_template.get("tools", []),
                "user_interaction": stage_template.get("user_interaction", False)
            }
            stages.append(stage)
        
        return stages
    
    def _define_checkpoints(self, stages: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """定义检查点"""
        
        checkpoints = []
        
        # 在每个需要用户交互的阶段后设置检查点
        for stage in stages:
            if stage.get("user_interaction", False):
                checkpoints.append({
                    "checkpoint_id": f"checkpoint_after_{stage['stage_id']}",
                    "stage_id": stage["stage_id"],
                    "type": "user_confirmation",
                    "description": f"等待用户确认{stage['name']}的结果"
                })
        
        # 在关键阶段后设置质量检查点
        key_stages = ["research", "draft", "final"]
        for stage in stages:
            if any(key in stage["stage_id"].lower() for key in key_stages):
                checkpoints.append({
                    "checkpoint_id": f"quality_check_{stage['stage_id']}",
                    "stage_id": stage["stage_id"],
                    "type": "quality_check",
                    "description": f"质量检查：{stage['name']}"
                })
        
        return checkpoints
    
    def _estimate_duration(self, intent_analysis: Dict[str, Any]) -> str:
        """估算执行时间"""
        
        complexity = intent_analysis["complexity_level"]
        base_time = {
            "low": 15,      # 15分钟
            "medium": 30,   # 30分钟  
            "high": 60      # 60分钟
        }.get(complexity, 30)
        
        # 根据需要的Agent数量调整
        agent_count = len(intent_analysis.get("required_agents", []))
        if agent_count > 3:
            base_time *= 1.5
        elif agent_count > 2:
            base_time *= 1.2
        
        return f"{int(base_time)}-{int(base_time * 1.3)}分钟"
    
    def _get_article_generation_template(self) -> Dict[str, Any]:
        """获取文章生成任务模板"""
        
        return {
            "stages": [
                {
                    "name": "需求分析和研究准备",
                    "description": "分析用户需求，制定研究计划",
                    "agent": "research_agent",
                    "estimated_time": "5-8分钟",
                    "tools": ["web_search", "content_analyzer"],
                    "success_criteria": ["研究计划明确", "关键词确定"]
                },
                {
                    "name": "信息收集和研究",
                    "description": "收集相关资料和信息",
                    "agent": "research_agent", 
                    "dependencies": ["stage_1"],
                    "estimated_time": "10-15分钟",
                    "tools": ["web_search", "file_tool"],
                    "success_criteria": ["收集足够的参考资料", "信息质量良好"]
                },
                {
                    "name": "文章大纲创建",
                    "description": "基于研究结果创建详细大纲",
                    "agent": "article_agent",
                    "dependencies": ["stage_2"],
                    "estimated_time": "8-12分钟",
                    "user_interaction": True,
                    "success_criteria": ["大纲结构清晰", "覆盖所有要点"]
                },
                {
                    "name": "文章撰写",
                    "description": "根据大纲撰写完整文章",
                    "agent": "article_agent",
                    "dependencies": ["stage_3"],
                    "estimated_time": "15-25分钟",
                    "tools": ["content_generator", "quality_checker"],
                    "success_criteria": ["文章完整", "符合长度要求", "关键词覆盖"]
                },
                {
                    "name": "质量检查和优化",
                    "description": "检查文章质量并进行优化",
                    "agent": "analysis_agent",
                    "dependencies": ["stage_4"],
                    "estimated_time": "8-12分钟",
                    "user_interaction": True,
                    "tools": ["quality_checker", "seo_tool"],
                    "success_criteria": ["质量分数达标", "SEO优化完成"]
                }
            ],
            "success_criteria": [
                "文章完整且连贯",
                "达到目标长度要求",
                "关键词合理分布",
                "质量分数≥0.8",
                "用户满意度良好"
            ],
            "quality_thresholds": {
                "overall_score": 0.8,
                "content_quality": 0.75,
                "structure_quality": 0.8,
                "keyword_coverage": 0.7
            }
        }
    
    def _get_content_analysis_template(self) -> Dict[str, Any]:
        """获取内容分析任务模板"""
        
        return {
            "stages": [
                {
                    "name": "内容预处理",
                    "description": "清理和预处理待分析内容",
                    "agent": "analysis_agent",
                    "estimated_time": "3-5分钟",
                    "tools": ["file_tool", "content_analyzer"]
                },
                {
                    "name": "深度分析",
                    "description": "进行详细的内容分析",
                    "agent": "analysis_agent",
                    "dependencies": ["stage_1"],
                    "estimated_time": "10-15分钟",
                    "tools": ["content_analyzer", "quality_checker"]
                },
                {
                    "name": "报告生成",
                    "description": "生成分析报告",
                    "agent": "analysis_agent",
                    "dependencies": ["stage_2"],
                    "estimated_time": "5-8分钟",
                    "user_interaction": True
                }
            ]
        }
    
    def _get_research_template(self) -> Dict[str, Any]:
        """获取研究任务模板"""
        
        return {
            "stages": [
                {
                    "name": "研究规划",
                    "description": "制定详细的研究计划",
                    "agent": "research_agent",
                    "estimated_time": "5-8分钟"
                },
                {
                    "name": "信息收集",
                    "description": "系统性收集相关信息",
                    "agent": "research_agent",
                    "dependencies": ["stage_1"],
                    "estimated_time": "15-25分钟",
                    "tools": ["web_search", "file_tool"]
                },
                {
                    "name": "数据整理分析",
                    "description": "整理和分析收集的数据",
                    "agent": "analysis_agent",
                    "dependencies": ["stage_2"],
                    "estimated_time": "10-15分钟"
                },
                {
                    "name": "研究报告",
                    "description": "生成研究报告",
                    "agent": "analysis_agent",
                    "dependencies": ["stage_3"],
                    "estimated_time": "8-12分钟",
                    "user_interaction": True
                }
            ]
        }
    
    def _get_editing_template(self) -> Dict[str, Any]:
        """获取编辑任务模板"""
        
        return {
            "stages": [
                {
                    "name": "内容评估",
                    "description": "评估现有内容质量",
                    "agent": "analysis_agent",
                    "estimated_time": "5-8分钟",
                    "tools": ["content_analyzer", "quality_checker"]
                },
                {
                    "name": "编辑优化",
                    "description": "进行内容编辑和优化",
                    "agent": "article_agent",
                    "dependencies": ["stage_1"],
                    "estimated_time": "10-20分钟",
                    "user_interaction": True
                },
                {
                    "name": "质量验证",
                    "description": "验证编辑后的质量",
                    "agent": "analysis_agent",
                    "dependencies": ["stage_2"],
                    "estimated_time": "5-8分钟"
                }
            ]
        }
    
    async def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        
        return {
            "status": "healthy",
            "agent_name": self.name,
            "config": {
                "max_planning_depth": self.max_planning_depth,
                "enable_user_interaction": self.enable_user_interaction
            },
            "templates_loaded": len(self.task_templates)
        }