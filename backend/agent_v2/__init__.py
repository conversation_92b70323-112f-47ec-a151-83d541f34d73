"""
MediaAgent V2 - Enhanced Agent System

This is a new implementation of the MediaAgent system with improved architecture,
designed to coexist with the existing system for smooth migration.

Key improvements:
- Unified state management
- Enhanced memory system  
- Quality monitoring
- Better tool integration
"""

__version__ = "2.0.0"
__author__ = "MediaAgent Team"

from .core.base_agent import BaseAgentV2
from .adapters.enhanced_article_adapter import EnhancedArticleAdapter
from .memory.context_manager import SharedContextManager
from .api.agent_api_v2 import AgentAPIV2

__all__ = [
    "BaseAgentV2",
    "EnhancedArticleAdapter", 
    "SharedContextManager",
    "AgentAPIV2"
]