"""
URL内容分析沙盒工具
专门用于分析URL内容并提供推荐内容方向
从原article_adapter.py中抽取URL分析功能
"""

import os
import json
import uuid
import time
import asyncio
from typing import Dict, Any, List, Optional
from pathlib import Path
import logging
from datetime import datetime

from agentpress.tool import Tool, ToolResult, openapi_schema, xml_schema, cost_tracked
from agentpress.thread_manager import ThreadManager
from sandbox.tool_base import SandboxToolsBase

# 导入内容提取分析模块
import sys
# 路径设置：src在MediaAgent根目录下
current_dir = Path(__file__).parent.absolute()  # tools目录
project_root = current_dir.parent.parent  # MediaAgent根目录

# 添加MediaAgent根目录到sys.path，这样可以访问src包
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))

try:
    # 必须使用src前缀导入，因为src是一个包名
    from src.services.extractor import extract_and_analyze
    MODULES_AVAILABLE = True
    print(f"成功导入URL分析模块")
except ImportError as e:
    MODULES_AVAILABLE = False
    extract_and_analyze = None
    print(f"URL分析模块导入失败: {e}")

logger = logging.getLogger(__name__)


class SandboxContentAnalyzerTool(SandboxToolsBase):
    """URL内容分析沙盒工具 - 专门用于URL分析和推荐内容方向"""
    
    def __init__(self, project_id: str, thread_manager: ThreadManager):
        super().__init__(project_id, thread_manager)
        self._analysis_states: Dict[str, Dict] = {}
        
        # 本地路径配置
        self.local_workspace = "/tmp/content_analysis"
        
        # 沙盒路径配置 
        self.sandbox_workspace = "/workspace"
        self.sandbox_results_path = f"{self.sandbox_workspace}/results"
        
        # 确保本地工作目录存在
        os.makedirs(self.local_workspace, exist_ok=True)

    async def _ensure_sandbox_directories(self, analysis_id: str) -> Dict[str, str]:
        """确保沙盒目录结构存在"""
        try:
            await self._ensure_sandbox()
            
            # 创建沙盒目录
            sandbox_analysis_dir = f"{self.sandbox_results_path}/content_analysis_{analysis_id}"
            try:
                self.sandbox.fs.create_folder(self.sandbox_results_path, "755")
                self.sandbox.fs.create_folder(sandbox_analysis_dir, "755")
            except Exception as e:
                logger.debug(f"沙盒目录创建失败或已存在: {e}")
            
            # 本地工作目录
            local_analysis_dir = f"{self.local_workspace}/content_analysis_{analysis_id}"
            os.makedirs(local_analysis_dir, exist_ok=True)
            
            # 返回路径配置
            return {
                "local_dir": local_analysis_dir,
                "sandbox_dir": sandbox_analysis_dir,
                "local_status": f"{local_analysis_dir}/status.json",
                "local_log": f"{local_analysis_dir}/analysis.log",
                "result_file": f"{local_analysis_dir}/analysis_result.json",
                "sandbox_status": f"{sandbox_analysis_dir}/status.json",
                "sandbox_log": f"{sandbox_analysis_dir}/analysis.log",
                "sandbox_result": f"{sandbox_analysis_dir}/analysis_result.json"
            }
            
        except Exception as e:
            logger.error(f"创建目录失败: {e}")
            raise

    async def _upload_file_to_sandbox(self, local_path: str, sandbox_path: str):
        """上传单个文件到沙盒"""
        try:
            if os.path.exists(local_path):
                file_size = os.path.getsize(local_path)
                logger.debug(f"准备上传文件: {local_path} ({file_size} bytes) -> {sandbox_path}")
                
                with open(local_path, 'rb') as f:
                    content = f.read()
                
                # 确保沙盒目录存在
                sandbox_dir = os.path.dirname(sandbox_path)
                try:
                    self.sandbox.fs.create_folder(sandbox_dir, "755")
                except Exception as dir_error:
                    logger.debug(f"创建沙盒目录失败或已存在 {sandbox_dir}: {dir_error}")
                
                # 上传文件
                self.sandbox.fs.upload_file(sandbox_path, content)
                logger.debug(f"成功上传: {local_path} -> {sandbox_path} ({file_size} bytes)")
            else:
                logger.warning(f"本地文件不存在，跳过上传: {local_path}")
        except Exception as e:
            logger.error(f"上传文件失败 {local_path} -> {sandbox_path}: {type(e).__name__}: {e}")
            raise

    async def _sync_files_to_sandbox(self, paths: Dict[str, str]):
        """同步所有相关文件到沙盒"""
        file_mappings = [
            (paths['local_status'], paths['sandbox_status']),
            (paths['local_log'], paths['sandbox_log']),
            (paths['result_file'], paths['sandbox_result'])
        ]
        
        logger.info(f"开始同步 {len(file_mappings)} 个文件到沙盒")
        
        sync_results = []
        for local_path, sandbox_path in file_mappings:
            try:
                if not os.path.exists(local_path):
                    logger.debug(f"本地文件不存在，跳过同步: {local_path}")
                    sync_results.append({"file": local_path, "status": "skipped", "reason": "file_not_found"})
                    continue
                
                file_size = os.path.getsize(local_path)
                logger.debug(f"准备同步文件: {local_path} ({file_size} bytes) -> {sandbox_path}")
                
                await self._upload_file_to_sandbox(local_path, sandbox_path)
                
                # 验证上传是否成功
                try:
                    verification_content = self.sandbox.fs.download_file(sandbox_path)
                    if len(verification_content) > 0:
                        logger.debug(f"同步验证成功: {sandbox_path} ({len(verification_content)} bytes)")
                        sync_results.append({"file": local_path, "status": "success", "size": file_size})
                    else:
                        logger.warning(f"同步验证失败: {sandbox_path} 文件为空")
                        sync_results.append({"file": local_path, "status": "failed", "reason": "empty_file"})
                except Exception as verify_error:
                    logger.warning(f"同步验证出错: {sandbox_path} - {verify_error}")
                    sync_results.append({"file": local_path, "status": "failed", "reason": str(verify_error)})
                
            except Exception as e:
                logger.error(f"同步文件失败: {local_path} -> {sandbox_path}, 错误: {e}")
                sync_results.append({"file": local_path, "status": "error", "reason": str(e)})
        
        # 统计同步结果
        successful_syncs = len([r for r in sync_results if r["status"] == "success"])
        logger.info(f"文件同步完成: {successful_syncs}/{len(file_mappings)} 个文件成功同步")
        
        return sync_results

    def _write_status_file(self, status_file: str, status_data: Dict[str, Any]):
        """写入状态文件"""
        try:
            with open(status_file, 'w', encoding='utf-8') as f:
                json.dump(status_data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"写入状态文件失败: {e}")

    def _write_log_file(self, log_file: str, message: str):
        """写入日志文件"""
        try:
            with open(log_file, 'a', encoding='utf-8') as f:
                timestamp = datetime.now().isoformat()
                f.write(f"[{timestamp}] {message}\n")
                f.flush()
        except Exception as e:
            logger.error(f"写入日志文件失败: {e}")

    def _format_analysis_options(self, analysis_result: List) -> List[Dict]:
        """格式化分析结果选项"""
        formatted_options = []
        
        if not analysis_result:
            logger.warning("分析结果为空")
            return formatted_options
            
        logger.info(f"开始格式化 {len(analysis_result)} 个分析结果")
        
        for i, item in enumerate(analysis_result):
            try:
                if isinstance(item, dict):
                    # 提取各种可能的字段名
                    title = (item.get("recommended_title") or 
                            item.get("title") or 
                            item.get("name") or 
                            f"选项{i+1}")
                    
                    direction = (item.get("content_direction") or 
                               item.get("direction") or 
                               item.get("description") or 
                               "")
                    
                    keywords = item.get("keywords", [])
                    if isinstance(keywords, str):
                        keywords = [keywords]
                    
                    formatted_option = {
                        "index": i,
                        "title": title,
                        "direction": direction,
                        "keywords": keywords[:5] if keywords else [],  # 只显示前5个关键词
                        "raw_data": item  # 保留原始数据供调试
                    }
                    
                    formatted_options.append(formatted_option)
                    logger.debug(f"格式化选项 {i}: {title}")
                    
                else:
                    logger.warning(f"选项 {i} 不是字典类型: {type(item)}")
                    # 尝试处理非字典类型
                    formatted_options.append({
                        "index": i,
                        "title": f"选项{i+1}",
                        "direction": str(item),
                        "keywords": [],
                        "raw_data": item
                    })
                    
            except Exception as e:
                logger.error(f"格式化选项 {i} 时发生错误: {e}")
                # 添加一个错误占位选项
                formatted_options.append({
                    "index": i,
                    "title": f"选项{i+1} (解析错误)",
                    "direction": f"解析错误: {str(e)}",
                    "keywords": [],
                    "raw_data": item
                })
        
        logger.info(f"成功格式化 {len(formatted_options)} 个选项")
        return formatted_options

    def success_response(self, data: Any) -> ToolResult:
        """创建成功响应"""
        return ToolResult(success=True, output=data)
    
    def fail_response(self, message: str) -> ToolResult:
        """创建失败响应"""
        return ToolResult(success=False, output=message)

    @openapi_schema({
        "type": "function",
        "function": {
            "name": "analyze_url_content",
            "description": "Analyze URL content immediately and provide complete analysis results including recommended content directions. This tool executes synchronously and returns the full analysis directly in the response. No additional calls are needed as the complete analysis is included in the 'options' field.",
            "parameters": {
                "type": "object",
                "properties": {
                    "url": {
                        "type": "string",
                        "description": "URL to analyze",
                    }
                },
                "required": ["url"]
            }
        }
    })
    @xml_schema(
        tag_name="analyze-url-content",
        mappings=[
            {"param_name": "url", "node_type": "content", "path": "."}
        ],
        example='''
        <analyze-url-content>
        https://example.com/article
        </analyze-url-content>
        '''
    )
    @cost_tracked("analyze_url_content")
    async def analyze_url_content(self, url: str) -> ToolResult:
        """分析URL内容并直接返回完整分析结果"""
        try:
            analysis_id = str(uuid.uuid4())
            timestamp = int(time.time())
            
            logger.info(f"🔍 开始URL内容分析: {analysis_id}")
            logger.info(f"分析URL: {url}")
            
            # 验证模块可用性
            if not MODULES_AVAILABLE or not extract_and_analyze:
                return self.fail_response("URL分析功能未可用，请检查模块导入")
            
            # 准备目录和路径
            paths = await self._ensure_sandbox_directories(analysis_id)
            
            # 写入初始状态
            initial_status = {
                "analysis_id": analysis_id,
                "status": "analyzing",
                "stage": "URL分析中",
                "progress": 0.1,
                "created_at": timestamp,
                "url": url,
                "last_updated": datetime.now().isoformat()
            }
            self._write_status_file(paths['local_status'], initial_status)
            self._write_log_file(paths['local_log'], f"=== URL内容分析日志 ===")
            self._write_log_file(paths['local_log'], f"URL: {url}")
            
            # 同步初始文件到沙盒
            await self._sync_files_to_sandbox(paths)
            
            # 使用分析器提取内容选项
            try:
                self._write_log_file(paths['local_log'], "开始分析URL内容...")
                
                # 更新进度
                progress_status = initial_status.copy()
                progress_status.update({
                    "progress": 0.3,
                    "stage": "提取内容",
                    "last_updated": datetime.now().isoformat()
                })
                self._write_status_file(paths['local_status'], progress_status)
                await self._upload_file_to_sandbox(paths['local_status'], paths['sandbox_status'])
                
                analysis_result = await extract_and_analyze(url)
                
                if not analysis_result:
                    raise Exception("URL分析未返回有效结果")
                
                # 检查返回的数据结构
                if isinstance(analysis_result, dict):
                    # 如果返回的是字典，提取其中的analysis_result数组
                    actual_options = analysis_result.get("analysis_result", [])
                    if not actual_options:
                        actual_options = []
                        # 尝试从其他可能的字段提取选项
                        for key in ["options", "results", "items"]:
                            if key in analysis_result and isinstance(analysis_result[key], list):
                                actual_options = analysis_result[key]
                                break
                    self._write_log_file(paths['local_log'], f"提取到 {len(actual_options)} 个分析选项")
                else:
                    # 如果直接返回数组
                    actual_options = analysis_result
                    analysis_result = {"analysis_result": actual_options}
                
                # 格式化分析选项
                formatted_options = self._format_analysis_options(actual_options)
                
                # 保存分析结果到文件
                result_data = {
                    "analysis_id": analysis_id,
                    "url": url,
                    "raw_analysis_result": analysis_result,
                    "formatted_options": formatted_options,
                    "created_at": timestamp,
                    "completion_time": datetime.now().isoformat()
                }
                
                with open(paths['result_file'], 'w', encoding='utf-8') as f:
                    json.dump(result_data, f, ensure_ascii=False, indent=2)
                
                # 更新最终状态
                final_status = {
                    "analysis_id": analysis_id,
                    "status": "completed",
                    "stage": "分析完成",
                    "progress": 1.0,
                    "created_at": timestamp,
                    "url": url,
                    "options_count": len(formatted_options),
                    "completion_time": datetime.now().isoformat(),
                    "last_updated": datetime.now().isoformat()
                }
                self._write_status_file(paths['local_status'], final_status)
                self._write_log_file(paths['local_log'], f"SUCCESS: URL分析完成，找到 {len(formatted_options)} 个推荐方向")
                
                # 同步所有文件到沙盒
                await self._sync_files_to_sandbox(paths)
                
                # 构建响应数据 - 突出显示完整分析结果
                response_data = {
                    "analysis_id": analysis_id,
                
                    "message": "✅ URL内容分析已完成 - 完整分析结果已包含在下方options字段中",
                
                    "options": formatted_options,

                    "raw_analysis_data": analysis_result,
                    
                    "local_dir": paths['sandbox_dir'],
                    "created_at": timestamp,
                    
                    
                    # 明确指示不需要额外调用
                    "note": "完整分析结果已包含在此响应中，无需调用其他工具获取结果"
                }
                
                return self.success_response(response_data)
                
            except Exception as e:
                logger.exception(f"URL分析失败: {e}")
                error_status = {
                    "analysis_id": analysis_id,
                    "status": "failed",
                    "stage": "URL分析失败",
                    "error": str(e),
                    "completion_time": datetime.now().isoformat(),
                    "last_updated": datetime.now().isoformat()
                }
                self._write_status_file(paths['local_status'], error_status)
                self._write_log_file(paths['local_log'], f"ERROR: URL分析失败: {e}")
                await self._sync_files_to_sandbox(paths)
                return self.fail_response(f"URL分析失败: {str(e)}")
                
        except Exception as e:
            logger.exception(f"分析URL内容失败: {e}")
            return self.fail_response(f"分析URL内容失败: {str(e)}") 