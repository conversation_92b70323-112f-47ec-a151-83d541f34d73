import hashlib
import string
import json
import fcntl
from loguru import logger
import threading
import os
import datetime
import copy
from copy import deepcopy

def string_to_md5(string):
    md5 = hashlib.md5()
    md5.update(string.encode('utf-8'))
    return md5.hexdigest()


def json_default_dumps(data):
    if isinstance(data, set):
        return sorted(data, key=lambda _x: str(_x))

    if hasattr(data, 'to_json') and callable(data.to_json):
        return data.to_json()
    
    if hasattr(data, '__dict__') and hasattr(data, '__class__'):
        ret = dict(
            cls_name=str(data.__class__),
            attr=data.__dict__,
        )
        return ret

    if hasattr(data, '__class__'):
        return str(data.__class__)
    
    return str(data)


def obj_to_hash(obj):
    s = json.dumps(obj, sort_keys=True, ensure_ascii=False, default=json_default_dumps)
    ret = string_to_md5(s)
    return ret

def get_data_list_from_jsonl(fn):
    with open(fn) as f:
        data_list = [json.loads(line) for line in f]
    
    return data_list

def append_jsonl(fn, data):
    with open(fn, 'a') as f:
        f.write(json.dumps(data, ensure_ascii=False) + '\n')
        
def get_datatime(mode=0):
    current_date = datetime.datetime.now()
    if mode == 0:
        formatted_date = current_date.strftime("%Y%m%d%H%M%S")
    elif mode == 1:
        formatted_date = current_date.strftime("%Y-%m-%d_%H:%M:%S")

    return formatted_date


class FileLock:
    def __init__(self, file_path):
        self.file_path = file_path
        self.file = None

    def __enter__(self):
        self.file = open(self.file_path, 'a')
        fcntl.flock(self.file, fcntl.LOCK_EX)
        return self.file

    def __exit__(self, exc_type, exc_value, traceback):
        fcntl.flock(self.file, fcntl.LOCK_UN)
        self.file.close()
  
def get_omit_json(data, max_str_len=100, max_list_len=10, to_str=True):
    data = copy.deepcopy(data)
    max_str_len = 10000
    def dfs(cur_data):
        if isinstance(cur_data, (list, tuple)):
            ret = [dfs(e) for e in cur_data[:max_list_len]]
            omit_cnt = len(cur_data) - max_list_len
            if omit_cnt > 0:
                ret.append(f'... Omiting #{omit_cnt} data')
            
            return ret

        if isinstance(cur_data, str):
            omit_cnt = len(cur_data) - max_str_len
            ret = cur_data[:max_str_len]
            if omit_cnt > 0:
                ret += f'...Omiting #{omit_cnt} chars'

            return ret
        
        if isinstance(cur_data, dict):
            return {key: dfs(value) for key, value in cur_data.items()}
        
        return cur_data
    
    data = dfs(data)
    ret = json.dumps(data, indent=2, ensure_ascii=False) if to_str else data
    return ret      
        
class Cache:
    name_mode_to_cache = {}
    cache_lock = threading.Lock()
    def __init__(self, fn, mode='rw'):
        self.fn = fn
        self.info_fn = f'{fn}_info.jsonl'
        self.mode = mode
        if 'r' in mode:
            try:
                self.cache_kv = self.read_cache()
            except Exception as e:
                print(f'Fail to fetch in cache {fn=}, {e=}')
                self.cache_kv = {}
        else:
            self.cache_kv = {}

        if 'w' in mode:
            os.makedirs(os.path.dirname(fn), exist_ok=True)

        # print(f'cache_size: {len(self.cache_kv)}')
    
    @staticmethod
    def get_cache(fn, mode='rw'):
        os.makedirs(os.path.dirname(fn), exist_ok=True)
        with FileLock(f'{fn}.lock'):
            with Cache.cache_lock:
                key = (fn, mode)
                d = Cache.name_mode_to_cache
                if key not in d:
                    value = Cache(fn, mode)
                    d[key] = value

        return d[key]

    def read_cache(self):
        if not os.path.isfile(self.fn):
            return {}

        data_list = get_data_list_from_jsonl(self.fn)
        cache_kv = {}
        for data in data_list:
            key = data['key']
            value = data['value']
            cache_kv[key] = value
        
        return cache_kv

    def add(self, key, value, hint=None):
        if 'w' not in self.mode:
            return

        with FileLock(f'{self.fn}.lock'):
            with Cache.cache_lock:
                # Overwrite
                # if self.has(key):
                #     return
                self.cache_kv[key] = value
                if value is not None:
                    data = dict(key=key, value=value)
                    data['add_time'] = get_datatime(mode=1)
                    if hint is not None:
                        data['hint'] = hint

                    append_jsonl(self.fn, data)
    
    def get(self, key):
        return self.cache_kv.get(key)
    
    def has(self, key):
        return key in self.cache_kv
    
    def get_cache(self, name, call_args_dict):
        obj = deepcopy(call_args_dict)
        obj["cache_name"] = name
        key = obj_to_hash(obj)
        if self.has(key):
            logger.debug(f'HIT cache：{name=}: {key=}')
            return self.get(key) 
        else:
            return None
    
    def save_cache(self, name, call_args_dict, value):
        obj = deepcopy(call_args_dict)
        obj["cache_name"] = name
        show_obj = get_omit_json(obj)
        key = obj_to_hash(obj)
        
        if value is not None:
            logger.debug(f'ADD cache：{name=}: {key=}')
            self.add(key, value, hint=show_obj)
            