#!/bin/bash

# 设置工作目录为recursive目录
cd "$(dirname "$0")"

# 设置Python环境变量
export PYTHONPATH="/root/workspace/MediaAgent/backend/agent/tools:$PYTHONPATH"

# 使用已配置的模型而不是硬编码gpt-4o
MODEL=google/gemini-2.5-flash-preview
# 使用专门的故事测试数据
task_input_file=../test_data/story_test.jsonl
output_folder=/root/workspace/MediaAgent/backend/agent/tools/recursive/example_story/$MODEL/story_test
mkdir -p ${output_folder}
task_output_file=${output_folder}/result.jsonl
done_file=${output_folder}/done.txt

# 检查测试数据文件是否存在，如果不存在则创建一个
if [ ! -f "$task_input_file" ]; then
    echo "创建故事测试数据文件: $task_input_file"
    mkdir -p "$(dirname "$task_input_file")"
    cat > "$task_input_file" << 'EOF'
{"id": "story_test_001", "ori": {"inputs": "创作一个关于2050年智慧城市的科幻故事，描述AI、物联网、绿色科技如何改变人们的生活，包含人与技术和谐共存的温暖故事情节"}}
EOF
fi

echo "🎭 开始故事生成测试"
echo "📝 模型: $MODEL"
echo "📄 输入文件: $task_input_file"
echo "📁 输出目录: $output_folder"

# 在recursive目录下运行engine.py，移除不支持的--language参数
timeout 2000 python engine.py \
    --filename "$task_input_file" \
    --output-filename "$task_output_file" \
    --done-flag-file "$done_file" \
    --model "$MODEL" \
    --mode story

# 检查结果
if [ -f "$done_file" ]; then
    echo "✅ 故事生成完成！"
    echo "📄 结果文件: $task_output_file"
    if [ -f "$task_output_file" ]; then
        echo "📊 生成内容预览:"
        head -c 500 "$task_output_file"
        echo "..."
    fi
else
    echo "❌ 故事生成未完成，检查日志..."
    echo "📁 输出目录内容:"
    ls -la "$output_folder"
fi
