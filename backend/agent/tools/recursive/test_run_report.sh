#!/bin/bash

# 设置工作目录为recursive目录
cd "$(dirname "$0")"

# 设置Python环境变量
export PYTHONPATH="/root/workspace/MediaAgent:$PYTHONPATH"

# MODEL=claude-3-5-sonnet-20241022
# MODEL=gpt-4o
# MODEL=gemini-2.5-pro-exp-03-25
MODEL=google/gemini-2.5-flash-preview
task_input_file=../test_data/qa_test.jsonl
output_folder=/root/workspace/MediaAgent/recursive/example_gemini/$MODEL/gemini-2.5

mkdir -p ${output_folder}
task_output_file=${output_folder}/result.jsonl
done_file=${output_folder}/done.txt

# 在recursive目录下运行engine.py
python engine.py --filename $task_input_file --output-filename $task_output_file --done-flag-file $done_file --model ${MODEL} --engine-backend google --mode report