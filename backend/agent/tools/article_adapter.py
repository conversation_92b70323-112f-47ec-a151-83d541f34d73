"""
文章生成沙盒工具（简化版）
专门用于通过topic、keywords、requirements生成文章
去掉URL分析和图像相关功能，保持纯文章生成接口
"""

import os
import json
import uuid
import asyncio
from typing import Dict, Any, List, Optional
from pathlib import Path
import logging
from datetime import datetime
import time

from agentpress.tool import Tool, ToolResult, openapi_schema, xml_schema, cost_tracked
from agentpress.thread_manager import ThreadManager
from sandbox.tool_base import SandboxToolsBase

# 导入ArticleProcessor相关模块
import sys
# 路径设置：src在MediaAgent根目录下
current_dir = Path(__file__).parent.absolute()  # tools目录
project_root = current_dir.parent.parent  # MediaAgent根目录

# 添加MediaAgent根目录到sys.path，这样可以访问src包
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))

try:
    # 必须使用src前缀导入，因为src是一个包名
    from src.services.generator.article_processor import ArticleProcessor
    from src.config.api_config import get_deepseek_api_key, get_qianwen_api_key
    MODULES_AVAILABLE = True
    print(f"成功导入src模块")
except ImportError as e:
    MODULES_AVAILABLE = False
    ArticleProcessor = None
    get_deepseek_api_key = None
    get_qianwen_api_key = None
    print(f"src模块导入失败: {e}")

logger = logging.getLogger(__name__)


class SandboxArticleTool(SandboxToolsBase):
    """文章生成沙盒工具（简化版） - 本地执行，文件同步到沙盒"""
    
    def __init__(self, project_id: str, thread_manager: ThreadManager):
        super().__init__(project_id, thread_manager)
        self._workflow_states: Dict[str, Dict] = {}
        
        # 本地路径配置
        self.local_workspace = "/tmp/article_workflows"
        
        # 沙盒路径配置 
        self.sandbox_workspace = "/workspace"
        self.sandbox_results_path = f"{self.sandbox_workspace}/results"
        
        # 确保本地工作目录存在
        os.makedirs(self.local_workspace, exist_ok=True)
        
        # 初始化ArticleProcessor
        self.article_processor = None
        self._init_article_processor()
    
    def _init_article_processor(self):
        """初始化ArticleProcessor"""
        try:
            if not MODULES_AVAILABLE or ArticleProcessor is None:
                logger.error("ArticleProcessor类未可用")
                return
                
            # 获取API密钥
            deepseek_key = ""
            qianwen_key = ""
            if get_deepseek_api_key:
                deepseek_key = get_deepseek_api_key()
            if get_qianwen_api_key:
                qianwen_key = get_qianwen_api_key()
                
            # 配置ArticleProcessor
            config = {
                "api_keys": {
                    "deepseek_api_key": deepseek_key,
                    "qianwen_api_key": qianwen_key,
                },
                "content_extractor_config": {
                    "article_api_key": deepseek_key,
                },
                "cache_dir": "/tmp/article_cache"
            }
            
            # 确保缓存目录存在
            os.makedirs(config["cache_dir"], exist_ok=True)
            
            self.article_processor = ArticleProcessor(config)
            logger.info("ArticleProcessor初始化成功")
            
        except Exception as e:
            logger.error(f"初始化ArticleProcessor失败: {e}")
            import traceback
            logger.debug(f"详细错误信息: {traceback.format_exc()}")
            self.article_processor = None

    async def _ensure_sandbox_directories(self, workflow_id: str) -> Dict[str, str]:
        """确保沙盒目录结构存在"""
        try:
            await self._ensure_sandbox()
            
            # 创建沙盒目录
            sandbox_workflow_dir = f"{self.sandbox_results_path}/article_workflow_{workflow_id}"
            try:
                self.sandbox.fs.create_folder(self.sandbox_results_path, "755")
                self.sandbox.fs.create_folder(sandbox_workflow_dir, "755")
            except Exception as e:
                logger.debug(f"沙盒目录创建失败或已存在: {e}")
            
            # 本地工作目录
            local_workflow_dir = f"{self.local_workspace}/article_workflow_{workflow_id}"
            os.makedirs(local_workflow_dir, exist_ok=True)
            
            # 返回路径配置
            return {
                "local_dir": local_workflow_dir,
                "sandbox_dir": sandbox_workflow_dir,
                "local_status": f"{local_workflow_dir}/status.json",
                "local_log": f"{local_workflow_dir}/execution.log",
                "result_file": f"{local_workflow_dir}/article.md",
                "sandbox_status": f"{sandbox_workflow_dir}/status.json",
                "sandbox_log": f"{sandbox_workflow_dir}/execution.log",
                "sandbox_result": f"{sandbox_workflow_dir}/article.md"
            }
            
        except Exception as e:
            logger.error(f"创建目录失败: {e}")
            raise

    async def _upload_file_to_sandbox(self, local_path: str, sandbox_path: str):
        """上传单个文件到沙盒"""
        try:
            if os.path.exists(local_path):
                file_size = os.path.getsize(local_path)
                logger.debug(f"准备上传文件: {local_path} ({file_size} bytes) -> {sandbox_path}")
                
                with open(local_path, 'rb') as f:
                    content = f.read()
                
                # 确保沙盒目录存在
                sandbox_dir = os.path.dirname(sandbox_path)
                try:
                    self.sandbox.fs.create_folder(sandbox_dir, "755")
                except Exception as dir_error:
                    logger.debug(f"创建沙盒目录失败或已存在 {sandbox_dir}: {dir_error}")
                
                # 上传文件
                self.sandbox.fs.upload_file(sandbox_path, content)
                logger.debug(f"成功上传: {local_path} -> {sandbox_path} ({file_size} bytes)")
            else:
                logger.warning(f"本地文件不存在，跳过上传: {local_path}")
        except Exception as e:
            logger.error(f"上传文件失败 {local_path} -> {sandbox_path}: {type(e).__name__}: {e}")
            raise

    async def _sync_files_to_sandbox(self, paths: Dict[str, str]):
        """同步所有相关文件到沙盒"""
        file_mappings = [
            (paths['local_status'], paths['sandbox_status']),
            (paths['local_log'], paths['sandbox_log']),
            (paths['result_file'], paths['sandbox_result'])
        ]
        
        logger.info(f"开始同步 {len(file_mappings)} 个文件到沙盒")
        
        sync_results = []
        for local_path, sandbox_path in file_mappings:
            try:
                if not os.path.exists(local_path):
                    logger.debug(f"本地文件不存在，跳过同步: {local_path}")
                    sync_results.append({"file": local_path, "status": "skipped", "reason": "file_not_found"})
                    continue
                
                file_size = os.path.getsize(local_path)
                logger.debug(f"准备同步文件: {local_path} ({file_size} bytes) -> {sandbox_path}")
                
                await self._upload_file_to_sandbox(local_path, sandbox_path)
                
                # 验证上传是否成功
                try:
                    verification_content = self.sandbox.fs.download_file(sandbox_path)
                    if len(verification_content) > 0:
                        logger.debug(f"同步验证成功: {sandbox_path} ({len(verification_content)} bytes)")
                        sync_results.append({"file": local_path, "status": "success", "size": file_size})
                    else:
                        logger.warning(f"同步验证失败: {sandbox_path} 文件为空")
                        sync_results.append({"file": local_path, "status": "failed", "reason": "empty_file"})
                except Exception as verify_error:
                    logger.warning(f"同步验证出错: {sandbox_path} - {verify_error}")
                    sync_results.append({"file": local_path, "status": "failed", "reason": str(verify_error)})
                
            except Exception as e:
                logger.error(f"同步文件失败: {local_path} -> {sandbox_path}, 错误: {e}")
                sync_results.append({"file": local_path, "status": "error", "reason": str(e)})
        
        # 统计同步结果
        successful_syncs = len([r for r in sync_results if r["status"] == "success"])
        logger.info(f"文件同步完成: {successful_syncs}/{len(file_mappings)} 个文件成功同步")
        
        return sync_results

    def _write_status_file(self, status_file: str, status_data: Dict[str, Any]):
        """写入状态文件"""
        try:
            with open(status_file, 'w', encoding='utf-8') as f:
                json.dump(status_data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"写入状态文件失败: {e}")

    def _write_log_file(self, log_file: str, message: str):
        """写入日志文件"""
        try:
            with open(log_file, 'a', encoding='utf-8') as f:
                timestamp = datetime.now().isoformat()
                f.write(f"[{timestamp}] {message}\n")
                f.flush()
        except Exception as e:
            logger.error(f"写入日志文件失败: {e}")

    async def _execute_article_generation_async(self, paths: Dict[str, str], topic: str, keywords: List, requirements: str, workflow_id: str):
        """异步执行文章生成的后台任务"""
        try:
            if not self.article_processor:
                error_msg = "ArticleProcessor未初始化"
                self._write_log_file(paths['local_log'], f"ERROR: {error_msg}")
                
                error_status = {
                    "status": "error",
                    "progress": 0.0,
                    "stage": "初始化错误",
                    "error": error_msg,
                    "end_time": datetime.now().isoformat(),
                    "last_updated": datetime.now().isoformat()
                }
                self._write_status_file(paths['local_status'], error_status)
                await self._sync_files_to_sandbox(paths)
                return

            # 写入初始状态
            initial_status = {
                "workflow_id": workflow_id,
                "status": "processing", 
                "progress": 0.1,
                "stage": "启动ArticleProcessor",
                "topic": topic,
                "keywords": keywords,
                "requirements": requirements,
                "start_time": datetime.now().isoformat(),
                "last_updated": datetime.now().isoformat(),
                "mode": "article_generation"
            }
            self._write_status_file(paths['local_status'], initial_status)
            self._write_log_file(paths['local_log'], f"=== 文章生成工作流日志 ===")
            self._write_log_file(paths['local_log'], f"主题: {topic}")
            self._write_log_file(paths['local_log'], f"关键词: {keywords}")
            self._write_log_file(paths['local_log'], f"要求: {requirements}")
            
            # 立即同步初始文件到沙盒
            await self._sync_files_to_sandbox(paths)
            
            # 使用ArticleProcessor处理文章
            self._write_log_file(paths['local_log'], "启动ArticleProcessor...")
            result = await self.article_processor.process_article(
                topic=topic,
                keywords=keywords,
                selected_direction=requirements
            )
            
            article_id = result.get("article_id")
            if not article_id:
                raise Exception("ArticleProcessor未返回有效的article_id")
            
            self._write_log_file(paths['local_log'], f"ArticleProcessor已启动，ID: {article_id}")
            
            # 更新状态
            processing_status = initial_status.copy()
            processing_status.update({
                "article_id": article_id,
                "progress": 0.2,
                "stage": "处理中",
                "last_updated": datetime.now().isoformat()
            })
            self._write_status_file(paths['local_status'], processing_status)
            
            # 立即同步状态更新到沙盒
            await self._sync_files_to_sandbox(paths)
            
            # 开始监控ArticleProcessor进度，每20秒检查一次
            max_wait_time = 900  # 最多等待15分钟
            wait_interval = 20   # 每20秒检查一次
            wait_time = 0
            last_sync_time = 0
            
            while wait_time < max_wait_time:
                # 获取ArticleProcessor状态
                article_data = await self.article_processor.generate_article(article_id)
                
                if not article_data:
                    self._write_log_file(paths['local_log'], "WARNING: 无法获取文章状态")
                    await asyncio.sleep(wait_interval)
                    wait_time += wait_interval
                    continue
                
                current_status = article_data.get("status", "unknown")
                current_progress = article_data.get("progress", 0)
                
                # 记录状态变化
                self._write_log_file(paths['local_log'], f"[{wait_time}s] 状态: {current_status}, 进度: {current_progress}%")
                
                # 更新状态文件
                progress_status = processing_status.copy()
                progress_status.update({
                    "progress": current_progress / 100.0 if current_progress else 0.2,
                    "stage": self._map_article_status_to_stage(current_status),
                    "processor_status": current_status,
                    "wait_time": wait_time,
                    "last_updated": datetime.now().isoformat()
                })
                self._write_status_file(paths['local_status'], progress_status)
                
                # 更频繁地同步到沙盒（每次状态更新都同步）
                if wait_time - last_sync_time >= 30:  # 每30秒强制同步一次
                    await self._sync_files_to_sandbox(paths)
                    last_sync_time = wait_time
                else:
                    # 只同步状态文件，提高响应速度
                    await self._upload_file_to_sandbox(paths['local_status'], paths['sandbox_status'])
                
                # 检查是否完成
                if current_status in ["completed", "failed"]:
                    break
                
                # 检查是否有错误
                if "errors" in article_data and article_data["errors"]:
                    errors = article_data["errors"]
                    self._write_log_file(paths['local_log'], f"WARNING: 处理中存在错误: {errors}")
                
                await asyncio.sleep(wait_interval)
                wait_time += wait_interval
            
            # 获取最终结果
            final_article_data = await self.article_processor.generate_article(article_id)
            final_status = final_article_data.get("status", "timeout") if final_article_data else "timeout"
            
            if final_status == "completed" and final_article_data:
                self._write_log_file(paths['local_log'], "SUCCESS: 文章生成成功完成")
                
                # 生成最终结果文件（无图像版本）
                await self._generate_final_article_result_no_images(paths, final_article_data)
                
                # 更新最终状态
                final_status_data = {
                    "workflow_id": workflow_id,
                    "status": "completed",
                    "progress": 1.0,
                    "stage": "完成",
                    "article_id": article_id,
                    "completion_time": datetime.now().isoformat(),
                    "last_updated": datetime.now().isoformat()
                }
                self._write_status_file(paths['local_status'], final_status_data)
                self._write_log_file(paths['local_log'], "SUCCESS: 工作流已完成")
                
                # 返回最终文章数据
                return final_article_data
                
            else:
                error_msg = f"文章生成失败或超时: {final_status}"
                self._write_log_file(paths['local_log'], f"ERROR: {error_msg}")
                
                error_status_data = {
                    "workflow_id": workflow_id,
                    "status": "failed",
                    "progress": 0.0,
                    "stage": "生成失败",
                    "error": error_msg,
                    "completion_time": datetime.now().isoformat(),
                    "last_updated": datetime.now().isoformat()
                }
                self._write_status_file(paths['local_status'], error_status_data)
                
                # 返回错误信息
                return {"status": "failed", "error": error_msg}
            
        except Exception as e:
            logger.error(f"文章生成异步任务失败: {e}")
            
            error_msg = f"执行异常: {str(e)}"
            self._write_log_file(paths['local_log'], f"ERROR: {error_msg}")
            
            error_status = {
                "workflow_id": workflow_id,
                "status": "error",
                "progress": 0.0,
                "stage": "执行异常",
                "error": error_msg,
                "completion_time": datetime.now().isoformat(),
                "last_updated": datetime.now().isoformat()
            }
            self._write_status_file(paths['local_status'], error_status)
            
            # 返回异常信息
            return {"status": "error", "error": error_msg}
        finally:
            # 确保最终同步所有文件到沙盒
            try:
                await self._sync_files_to_sandbox(paths)
            except Exception as sync_error:
                logger.error(f"最终文件同步失败: {sync_error}")

    def _map_article_status_to_stage(self, status: str) -> str:
        """将ArticleProcessor状态映射到用户友好的阶段名称"""
        status_mapping = {
            "processing": "处理中",
            "initialized": "初始化",
            "planning": "制定计划",
            "searching": "搜索资料",
            "writing": "生成内容",
            "evaluating": "评估质量",
            "editing": "编辑优化",
            "finalizing": "最终处理",
            "completed": "已完成",
            "failed": "生成失败",
            "error": "发生错误"
        }
        
        return status_mapping.get(status, status)

    async def _generate_final_article_result_no_images(self, paths: Dict[str, str], article_data: Dict):
        """生成最终文章结果文件（无图像版本）"""
        try:
            # 获取final_content
            final_content = article_data.get("final_content", {})
            
            if not final_content:
                logger.warning(f"Article missing final_content")
                return {
                    "success": False,
                    "preview": "Article content generation failed",
                    "article_file": "",
                    "error": "Missing article content"
                }
            
            # 生成Markdown格式文章（无图像版本）
            markdown_content = self._generate_markdown_output_no_images(article_data)
            
            # 保存到本地
            with open(paths['result_file'], 'w', encoding='utf-8') as f:
                f.write(markdown_content)
            
            # 上传到沙盒
            await self._upload_file_to_sandbox(paths['result_file'], paths['sandbox_result'])
            
            logger.info(f"✅ Article result saved: {paths['result_file']}")
            
            # 生成预览（前500字符）
            preview = markdown_content[:5000] + "..." if len(markdown_content) > 5000 else markdown_content
            
            return {
                "success": True,
                "preview": preview,
                "article_file": paths['result_file'],
                "sandbox_file": paths['sandbox_result'],
                "content_length": len(markdown_content),
                "title": self._extract_title_from_article_data(article_data)
            }
            
        except Exception as e:
            logger.error(f"Failed to generate final article result: {e}")
            return {
                "success": False,
                "preview": f"Article generation failed: {str(e)}",
                "article_file": "",
                "error": str(e)
            }

    def _extract_title_from_article_data(self, article_data: Dict) -> str:
        """从文章数据中提取标题"""
        try:
            final_content = article_data.get("final_content", {})
            if isinstance(final_content, str):
                try:
                    final_content = json.loads(final_content)
                except json.JSONDecodeError:
                    return "Generated Article"
            
            if isinstance(final_content, dict):
                return final_content.get("title", "Generated Article")
            
            return "Generated Article"
        except Exception:
            return "Generated Article"

    def _generate_markdown_output_no_images(self, article_data: Dict) -> str:
        """生成Markdown格式输出（无图像版本）"""
        try:
            # 获取final content
            content = article_data.get("final_content", {})
            
            # 处理字符串类型的JSON content
            if isinstance(content, str):
                try:
                    content = json.loads(content)
                except json.JSONDecodeError:
                    return f"# Generated Article\n\n{content}"
            
            if not isinstance(content, dict):
                return f"# Generated Article\n\n{str(content)}"
            
            # 提取标题和内容
            title = content.get("title", "Generated Article")
            markdown_lines = [f"# {title}\n"]
            
            # 获取工作流信息
            workflow_id = article_data.get("id", "unknown")
            created_at = article_data.get("created_at", "")
            
            # 添加元数据
            markdown_lines.append(f"**Generated Time**: {datetime.fromtimestamp(created_at).strftime('%Y-%m-%d %H:%M:%S') if isinstance(created_at, int) else created_at}")
            markdown_lines.append(f"**Workflow ID**: {workflow_id}")
            markdown_lines.append("---\n")
            
            # 添加介绍
            introduction = content.get("introduction", "")
            if introduction:
                markdown_lines.append(f"{introduction}\n")
            
            # 处理不同内容结构
            if "content" in content and content["content"]:
                # 流式结构
                markdown_lines.append(f"{content['content']}\n")
            elif isinstance(content.get("sections"), list):
                # 基于章节的结构
                for section in content.get("sections", []):
                    if isinstance(section, dict):
                        section_title = section.get("title", "")
                        if section_title:
                            markdown_lines.append(f"## {section_title}\n")
                        
                        section_content = section.get("content", "")
                        if section_content:
                            markdown_lines.append(f"{section_content}\n")
            
            # 添加结论
            conclusion = content.get("conclusion", "")
            if conclusion:
                markdown_lines.append(f"## Conclusion\n\n{conclusion}\n")
            
            # 注意：不再添加图像部分
            
            markdown_lines.append("\n---\n*This article was automatically generated by AI*")
            
            return "\n".join(markdown_lines)
            
        except Exception as e:
            logger.error(f"Failed to generate Markdown output: {e}")
            return f"# Article Generation Error\n\nError occurred while generating Markdown output: {str(e)}"

    def success_response(self, data: Any) -> ToolResult:
        """创建成功响应"""
        return ToolResult(success=True, output=data)
    
    def fail_response(self, message: str) -> ToolResult:
        """创建失败响应"""
        return ToolResult(success=False, output=message)

    @openapi_schema({
        "type": "function",
        "function": {
            "name": "generate_article",
            "description": "Generate a complete article immediately based on topic, keywords and requirements. This tool executes synchronously and returns the full article content directly in the response. No additional calls are needed as the complete article is included in the 'FINAL_ARTICLE_CONTENT' field.",
            "parameters": {
                "type": "object",
                "properties": {
                    "topic": {
                        "type": "string",
                        "description": "Article topic or title"
                    },
                    "keywords": {
                        "type": "array",
                        "items": {"type": "string"},
                        "description": "Keywords list for SEO optimization",
                        "default": []
                    },
                    "requirements": {
                        "type": "string", 
                        "description": "Article requirements, e.g.: technical depth, target audience, writing style",
                        "default": "Create a high-quality article"
                    }
                },
                "required": ["topic"]
            }
        }
    })
    @xml_schema(
        tag_name="generate-article",
        mappings=[
            {"param_name": "topic", "node_type": "attribute", "path": ".", "required": True},
            {"param_name": "requirements", "node_type": "attribute", "path": ".", "required": False},
            {"param_name": "keywords", "node_type": "element", "path": "keywords", "required": False}
        ],
        example='''
        <generate-article topic="AI Technology Development Trends" requirements="In-depth analysis for technical professionals">
            <keywords>["artificial intelligence", "machine learning", "technology trends"]</keywords>
        </generate-article>
        '''
    )
    @cost_tracked("generate_article")
    async def generate_article(self, topic: str, keywords: List[str] = None, 
                             requirements: str = "创作一篇高质量的文章") -> ToolResult:
        """直接生成完整文章（同步执行）"""
        try:
            workflow_id = str(uuid.uuid4())
            timestamp = int(time.time())
            
            logger.info(f"📰 开始生成文章: {workflow_id}")
            logger.info(f"主题: {topic}")
            
            # 处理keywords参数
            if keywords is None:
                keywords = []
            
            # 准备目录和路径
            paths = await self._ensure_sandbox_directories(workflow_id)
            
            # 同步执行文章生成任务，等待完成
            article_data = await self._execute_article_generation_async(paths, topic, keywords, requirements, workflow_id)
            
            # 检查执行结果
            if not article_data or article_data.get("status") in ["failed", "error"]:
                return self.fail_response(f"文章生成失败: {article_data.get('error', '未知错误')}")
            
            # 生成完成后，获取最终结果
            final_result = await self._generate_final_article_result_no_images(paths, article_data)
            
            if not final_result.get("success", False):
                return self.fail_response(f"文章结果处理失败: {final_result.get('error', '未知错误')}")
            
            return self.success_response({
                "workflow_id": workflow_id,
                "status": "completed",
                "message": "✅ 文章生成已完成 - 完整文章文件已经创建在local_article_dir字段中可以直接浏览展示, 文章内容已包含在下方FINAL_ARTICLE_CONTENT字段中",
                "created_at": timestamp,
             
                "local_article_dir": paths['sandbox_dir'],
                "FINAL_ARTICLE_CONTENT": final_result.get("preview", ""),
               
                # 明确指示不需要额外调用
                "note": "完整文章内容已生成并包含在此响应中，无需调用其他工具获取输出"
            })
            
        except Exception as e:
            logger.exception(f"生成文章失败: {e}")
            return self.fail_response(f"生成文章失败: {str(e)}") 