from typing import Optional
from agentpress.tool import Too<PERSON><PERSON><PERSON><PERSON>, openapi_schema, xml_schema, cost_tracked
from sandbox.tool_base import SandboxToolsBase
from agentpress.thread_manager import ThreadManager
import httpx
from io import BytesIO
import uuid
import base64
import os
import logging
import json
import boto3
from botocore.exceptions import ClientError
from utils.config import config
from supabase import create_client, Client
import datetime

# Set up logging
logger = logging.getLogger(__name__)

class SandboxImageGenerateTool(SandboxToolsBase):
    """Tool for generating images using AWS Bedrock image generation models."""

    def __init__(self, project_id: str, thread_id: str, thread_manager: ThreadManager):
        super().__init__(project_id, thread_manager)
        self.thread_id = thread_id
        self.thread_manager = thread_manager
        
        # Initialize Supabase client
        self.supabase: Client = create_client(
            config.SUPABASE_URL,
            config.SUPABASE_SERVICE_ROLE_KEY
        )

    @openapi_schema(
        {
            "type": "function",
            "function": {
                "name": "image_generate",
                "description": "Generate a new image with proper prompt. After successful generation, you MUST use the see_image tool to view and display the generated image to the user.",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "prompt": {
                            "type": "string",
                            "description": "Text prompt describing the desired image.",
                        },
                        "size": {
                            "type": "string",
                            "enum": ["512x512", "1024x1024"],
                            "default": "1024x1024", 
                            "description": "Size of the generated image.",
                        },
                        "aspect_ratio": {
                            "type": "string",
                            "enum": ["1:1", "16:9", "21:9", "2:3", "3:2", "4:5", "5:4", "9:16", "9:21"],
                            "default": "1:1",
                            "description": "Aspect ratio of the generated image.",
                        },
                    },
                    "required": ["prompt"],
                },
            },
        }
    )
    @xml_schema(
        tag_name="image-generate",
        mappings=[
            {"param_name": "prompt", "node_type": "attribute", "path": "."},
            {"param_name": "size", "node_type": "attribute", "path": "."},
        ],
        example="""
        <image-generate prompt="A futuristic cityscape at sunset" size="1024x1024">
        </image-generate>
        """,
    )
    @cost_tracked("image_generate")
    async def image_generate(
        self,
        prompt: str,
        size: str = "1024x1024",
        aspect_ratio: str = "1:1",
    ) -> ToolResult:
        """Generate images using AWS Bedrock image generation models."""
        try:
            await self._ensure_sandbox()

            # 获取 AWS 凭证
            aws_access_key = config.AWS_ACCESS_KEY_ID
            aws_secret_key = config.AWS_SECRET_ACCESS_KEY
            aws_region = config.AWS_REGION_NAME or "us-west-2"

            if not aws_access_key or not aws_secret_key:
                return self.fail_response("AWS credentials not configured. Please set AWS_ACCESS_KEY_ID and AWS_SECRET_ACCESS_KEY in your backend configuration.")

            # 创建 AWS Bedrock Runtime 客户端
            client = boto3.client(
                "bedrock-runtime",
                region_name=aws_region,
                aws_access_key_id=aws_access_key,
                aws_secret_access_key=aws_secret_key
            )

            # 使用 Stability AI 图像生成模型
            model_id = "stability.stable-image-core-v1:0"

            # 图像生成请求体
            request_body = {
                "prompt": prompt,
                "aspect_ratio": "1:1",
                "output_format": "png",
                "seed": 42  # 可选：随机种子
            }

            # 调用 Bedrock API
            response = client.invoke_model(
                modelId=model_id,
                body=json.dumps(request_body),
                contentType="application/json",
                accept="application/json"
            )

            # 解析响应 - 先解码字节流再解析JSON
            response_body = json.loads(response['body'].read().decode('utf-8'))

            # 获取生成的图像数据（base64编码） - 新版模型使用'images'字段
            if 'images' in response_body and response_body['images']:
                base64_image = response_body['images'][0]  # 取第一张图像
                
                # 处理完成状态
                if 'finish_reasons' in response_body and response_body['finish_reasons']:
                    reason = response_body['finish_reasons'][0]
                    if reason:
                        logger.warning(f"图像生成完成原因: {reason}")
                        if reason.startswith("Filter"):
                            return self.fail_response(f"图像生成被过滤: {reason}")

                # 保存图像到沙盒（本地）
                local_filename = await self._save_image_to_sandbox(base64_image)
                if isinstance(local_filename, ToolResult):  # 错误occurred
                    return local_filename

                # 尝试上传图像到 Supabase 存储桶并获取公共链接
                public_url = await self._upload_to_supabase(base64_image, prompt)
                
                # 构建返回信息
                if isinstance(public_url, ToolResult):  # Supabase上传失败
                    logger.warning("Supabase上传失败，但本地保存成功，继续返回成功结果")
                    return self.success_response(
                        f"🚨🚨🚨 MANDATORY TOOL RESULT ECHO REQUIRED 🚨🚨🚨\n"
                        f"✅ IMAGE GENERATION SUCCESS\n"
                        f"📁 SAVED FILENAME: {local_filename}\n"
                        f"📍 LOCAL PATH: {self.workspace_path}/{local_filename}\n"
                        f"💾 STORAGE: Image saved locally to sandbox\n"
                        f"⚠️ CRITICAL: You MUST use see_image to view LOCAL PATH and display this image to the user!\n"
                    )
                else:  # Supabase上传成功
                    return self.success_response(
                        f"🚨🚨🚨 MANDATORY TOOL RESULT ECHO REQUIRED 🚨🚨🚨\n"
                        f"✅ IMAGE GENERATION SUCCESS\n"
                        f"📁 SAVED FILENAME: {local_filename}\n"
                        f"📍 LOCAL PATH: {self.workspace_path}/{local_filename}\n"
                        f"🌐 PUBLIC URL: {public_url}\n"
                        f"💾 STORAGE: Image saved to both local sandbox and Supabase\n"
                        f"⚠️ CRITICAL: You MUST use see_image to view LOCAL PATH and display this image to the user!\n"
                    )
            else:
                logger.error(f"没有找到images字段，完整响应: {response_body}")
                return self.fail_response("No image data found in response")

        except ClientError as e:
            logger.error(f"AWS Bedrock 调用失败: {str(e)}")
            return self.fail_response(f"AWS Bedrock API call failed: {str(e)}")
        except Exception as e:
            logger.error(f"图像生成失败: {str(e)}")
            return self.fail_response(f"An error occurred during image generation: {str(e)}")

    async def _save_image_to_sandbox(self, base64_image: str) -> str | ToolResult:
        """将base64图像数据保存到沙盒中."""
        try:
            # 解码 base64 图像数据
            image_data = base64.b64decode(base64_image)

            # 生成随机文件名
            random_filename = f"generated_image_{uuid.uuid4().hex[:8]}.png"
            sandbox_path = f"{self.workspace_path}/{random_filename}"

            # 保存图像到沙盒
            self.sandbox.fs.upload_file(sandbox_path, image_data)
            
            logger.info(f"图像已保存到sandbox: {random_filename}")
            return random_filename

        except Exception as e:
            logger.error(f"保存图像到沙盒失败: {str(e)}")
            return self.fail_response(f"Failed to save image to sandbox: {str(e)}")

    async def _upload_to_supabase(self, base64_image: str, prompt: str) -> str | ToolResult:
        """将base64图像数据上传到Supabase存储桶并返回公共链接."""
        try:
            # 解码 base64 图像数据
            image_data = base64.b64decode(base64_image)
            
            # 生成文件名：使用时间戳和随机ID
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            random_id = uuid.uuid4().hex[:8]
            filename = f"ai_generated_{timestamp}_{random_id}.png"
            
            # 存储桶名称
            bucket_name = "loomu-user-down"
            
            # 上传文件到 Supabase Storage
            result = self.supabase.storage.from_(bucket_name).upload(
                path=filename,
                file=image_data,
                file_options={
                    "content-type": "image/png",
                    "x-upsert": "true"  # 如果文件已存在则覆盖
                }
            )
            
            # 检查上传结果
            if result.status_code != 200:
                logger.error(f"上传到Supabase失败: {result}")
                return self.fail_response(f"Failed to upload to Supabase: {result}")
            
            # 获取公共URL
            public_url_response = self.supabase.storage.from_(bucket_name).get_public_url(filename)
            public_url = public_url_response
            
            logger.info(f"图像已上传到Supabase: {filename}, 公共URL: {public_url}")
            
            
            return public_url

        except Exception as e:
            logger.error(f"上传到Supabase失败: {str(e)}")
            return self.fail_response(f"Failed to upload to Supabase storage: {str(e)}")

