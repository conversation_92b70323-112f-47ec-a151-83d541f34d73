"""
报告生成沙盒工具
本地执行recursive引擎，然后将状态文件、日志文件、结果文件上传到沙盒供前端查看
"""

import os
import json
import uuid
import subprocess
import tempfile
from typing import Dict, Any, List, Optional
from pathlib import Path
import logging
from datetime import datetime
import time
import asyncio

from agentpress.tool import Tool, ToolResult, openapi_schema, xml_schema, cost_tracked
from agentpress.thread_manager import ThreadManager
from sandbox.tool_base import SandboxToolsBase

logger = logging.getLogger(__name__)


class SandboxReportTool(SandboxToolsBase):
    """报告生成沙盒工具 - 本地执行，文件同步到沙盒"""
    
    def __init__(self, project_id: str, thread_manager: ThreadManager):
        super().__init__(project_id, thread_manager)
        self._workflow_states: Dict[str, Dict] = {}
        
        # 本地路径配置
        self.local_recursive_path = "/root/workspace/MediaAgent/backend/agent/tools/recursive"
        self.local_workspace = "/tmp/report_workflows"
        
        # 沙盒路径配置 
        self.sandbox_workspace = "/workspace"
        self.sandbox_results_path = f"{self.sandbox_workspace}/results"
        
        # 确保本地工作目录存在
        os.makedirs(self.local_workspace, exist_ok=True)

    async def _ensure_sandbox_directories(self, workflow_id: str) -> Dict[str, str]:
        """确保沙盒目录结构存在"""
        try:
            await self._ensure_sandbox()
            
            # 创建沙盒目录
            sandbox_workflow_dir = f"{self.sandbox_results_path}/report_workflow_{workflow_id}"
            try:
                self.sandbox.fs.create_folder(self.sandbox_results_path, "755")
                self.sandbox.fs.create_folder(sandbox_workflow_dir, "755")
            except Exception as e:
                logger.debug(f"沙盒目录创建失败或已存在: {e}")
            
            # 本地工作目录
            local_workflow_dir = f"{self.local_workspace}/report_workflow_{workflow_id}"
            os.makedirs(local_workflow_dir, exist_ok=True)
            
            # 返回路径配置
            return {
                "local_dir": local_workflow_dir,
                "sandbox_dir": sandbox_workflow_dir,
                "input_file": f"{local_workflow_dir}/input.jsonl",
                "output_file": f"{local_workflow_dir}/result.jsonl", 
                "done_file": f"{local_workflow_dir}/done.txt",
                "log_file": f"{local_workflow_dir}/execution.log",
                "status_file": f"{local_workflow_dir}/status.json",
                "sandbox_input": f"{sandbox_workflow_dir}/input.jsonl",
                "sandbox_output": f"{sandbox_workflow_dir}/result.jsonl",
                "sandbox_done": f"{sandbox_workflow_dir}/done.txt",
                "sandbox_log": f"{sandbox_workflow_dir}/execution.log",
                "sandbox_status": f"{sandbox_workflow_dir}/status.json"
            }
            
        except Exception as e:
            logger.error(f"创建目录失败: {e}")
            raise

    async def _upload_file_to_sandbox(self, local_path: str, sandbox_path: str):
        """上传单个文件到沙盒"""
        try:
            if os.path.exists(local_path):
                with open(local_path, 'rb') as f:
                    content = f.read()
                self.sandbox.fs.upload_file(sandbox_path, content)
                logger.debug(f"已上传: {local_path} -> {sandbox_path}")
            else:
                logger.debug(f"本地文件不存在: {local_path}")
        except Exception as e:
            logger.error(f"上传文件失败 {local_path}: {e}")

    async def _sync_files_to_sandbox(self, paths: Dict[str, str]):
        """同步所有相关文件到沙盒"""
        file_mappings = [
            (paths['input_file'], paths['sandbox_input']),
            (paths['output_file'], paths['sandbox_output']),
            (paths['done_file'], paths['sandbox_done']),
            (paths['log_file'], paths['sandbox_log']),
            (paths['status_file'], paths['sandbox_status'])
        ]
        
        for local_path, sandbox_path in file_mappings:
            await self._upload_file_to_sandbox(local_path, sandbox_path)

    def _write_status_file(self, status_file: str, status_data: Dict[str, Any]):
        """写入状态文件"""
        try:
            with open(status_file, 'w', encoding='utf-8') as f:
                json.dump(status_data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"写入状态文件失败: {e}")

    async def _execute_local_recursive(self, paths: Dict[str, str], topic: str, requirements: str) -> bool:
        """在本地执行recursive引擎"""
        try:
            # 写入初始状态
            initial_status = {
                "workflow_id": paths['local_dir'].split('/')[-1].replace('report_workflow_', ''),
                "status": "running", 
                "progress": 0.1,
                "stage": "初始化",
                "topic": topic,
                "requirements": requirements,
                "start_time": datetime.now().isoformat(),
                "last_updated": datetime.now().isoformat()
            }
            self._write_status_file(paths['status_file'], initial_status)
            
            # 同步初始文件到沙盒
            await self._sync_files_to_sandbox(paths)
            
            # 准备命令
            cmd = [
                "python", "engine.py",
                "--filename", paths['input_file'],
                "--output-filename", paths['output_file'], 
                "--done-flag-file", paths['done_file'],
                "--model", "google/gemini-2.5-flash-preview",
                "--mode", "report"
            ]
            
            # 设置环境变量 - 关键修复
            env = os.environ.copy()
            env['PYTHONPATH'] = f"/root/workspace/MediaAgent/backend/agent/tools:{env.get('PYTHONPATH', '')}"
            
            # 更新状态为执行中
            running_status = initial_status.copy()
            running_status.update({
                "status": "processing",
                "progress": 0.2, 
                "stage": "执行引擎",
                "command": " ".join(cmd),
                "pythonpath": env['PYTHONPATH'],
                "last_updated": datetime.now().isoformat()
            })
            self._write_status_file(paths['status_file'], running_status)
            await self._upload_file_to_sandbox(paths['status_file'], paths['sandbox_status'])
            
            # 执行命令
            logger.info(f"开始本地执行: {' '.join(cmd)}")
            logger.info(f"工作目录: {self.local_recursive_path}")
            logger.info(f"PYTHONPATH: {env['PYTHONPATH']}")
            
            with open(paths['log_file'], 'w', encoding='utf-8') as log_f:
                log_f.write(f"=== 报告生成工作流日志 ===\n")
                log_f.write(f"开始时间: {datetime.now().isoformat()}\n")
                log_f.write(f"主题: {topic}\n")
                log_f.write(f"要求: {requirements}\n")
                log_f.write(f"命令: {' '.join(cmd)}\n")
                log_f.write(f"工作目录: {self.local_recursive_path}\n")
                log_f.write(f"PYTHONPATH: {env['PYTHONPATH']}\n\n")
                log_f.flush()
                
                # 启动进程 - 添加env参数
                process = subprocess.Popen(
                    cmd,
                    cwd=self.local_recursive_path,
                    env=env,  # 关键修复：传递环境变量
                    stdout=subprocess.PIPE,
                    stderr=subprocess.STDOUT,
                    text=True,
                    bufsize=1,
                    universal_newlines=True
                )
                
                # 实时读取输出并写入日志
                while True:
                    output = process.stdout.readline()
                    if output == '' and process.poll() is not None:
                        break
                    if output:
                        log_f.write(output)
                        log_f.flush()
                        logger.debug(f"引擎输出: {output.strip()}")
                        
                        # 定期同步日志到沙盒
                        if time.time() % 10 < 1:  # 大约每10秒同步一次
                            await self._upload_file_to_sandbox(paths['log_file'], paths['sandbox_log'])
                
                # 获取返回码
                return_code = process.poll()
                
                log_f.write(f"\n=== 执行完成 ===\n")
                log_f.write(f"返回码: {return_code}\n")
                log_f.write(f"结束时间: {datetime.now().isoformat()}\n")
            
            # 检查执行结果
            success = return_code == 0 and os.path.exists(paths['done_file'])
            
            # 更新最终状态
            final_status = running_status.copy()
            if success:
                final_status.update({
                    "status": "completed",
                    "progress": 1.0,
                    "stage": "已完成",
                    "return_code": return_code,
                    "end_time": datetime.now().isoformat(),
                    "last_updated": datetime.now().isoformat()
                })
            else:
                final_status.update({
                    "status": "failed", 
                    "progress": 0.0,
                    "stage": "执行失败",
                    "return_code": return_code,
                    "error": f"执行失败，返回码: {return_code}",
                    "end_time": datetime.now().isoformat(),
                    "last_updated": datetime.now().isoformat()
                })
            
            self._write_status_file(paths['status_file'], final_status)
            
            # 最终同步所有文件到沙盒
            await self._sync_files_to_sandbox(paths)
            
            logger.info(f"本地执行完成，成功: {success}, 返回码: {return_code}")
            return success
            
        except Exception as e:
            logger.error(f"本地执行失败: {e}")
            
            # 写入错误状态
            error_status = {
                "status": "error",
                "progress": 0.0,
                "stage": "执行错误", 
                "error": str(e),
                "end_time": datetime.now().isoformat(),
                "last_updated": datetime.now().isoformat()
            }
            self._write_status_file(paths['status_file'], error_status)
            await self._upload_file_to_sandbox(paths['status_file'], paths['sandbox_status'])
            
            return False

    def success_response(self, data: Any) -> ToolResult:
        """创建成功响应"""
        return ToolResult(success=True, output=data)
    
    def fail_response(self, message: str) -> ToolResult:
        """创建失败响应"""
        return ToolResult(success=False, output=message)

    @openapi_schema({
        "type": "function",
        "function": {
            "name": "create_report_workflow",
            "description": "创建报告生成工作流",
            "parameters": {
                "type": "object",
                "properties": {
                    "topic": {
                        "type": "string",
                        "description": "报告主题"
                    },
                    "requirements": {
                        "type": "string", 
                        "description": "报告要求",
                        "default": "生成一份详细的分析报告"
                    }
                },
                "required": ["topic"]
            }
        }
    })
    @xml_schema(
        tag_name="create-report-workflow",
        mappings=[
            {"param_name": "topic", "node_type": "attribute", "path": "."},
            {"param_name": "requirements", "node_type": "attribute", "path": ".", "required": False}
        ],
        example='''
        <create-report-workflow topic="人工智能发展趋势" requirements="包含市场分析、技术趋势、未来预测的综合报告">
        </create-report-workflow>
        '''
    )
    @cost_tracked("create_report_workflow")
    async def create_report_workflow(self, topic: str, requirements: str = "生成一份详细的分析报告") -> ToolResult:
        """创建报告生成工作流"""
        try:
            workflow_id = str(uuid.uuid4())
            timestamp = int(time.time())
            
            logger.info(f"📊 开始创建报告工作流: {workflow_id}")
            logger.info(f"📝 主题: {topic}")
            logger.info(f"📋 要求: {requirements}")
            
            # 1. 准备目录和路径
            paths = await self._ensure_sandbox_directories(workflow_id)
            
            # 2. 创建输入文件
            input_data = {
                "id": f"report_workflow_{workflow_id}",
                "prompt": f"{topic}。{requirements}"
                
            }
            
            with open(paths['input_file'], 'w', encoding='utf-8') as f:
                f.write(json.dumps(input_data, ensure_ascii=False) + '\n')
            
            # 3. 启动异步执行任务
            asyncio.create_task(self._execute_local_recursive(paths, topic, requirements))
            
            # 4. 立即上传输入文件到沙盒
            await self._upload_file_to_sandbox(paths['input_file'], paths['sandbox_input'])
            
            return self.success_response({
                "workflow_id": workflow_id,
                "status": "running",
                "message": "🚀 报告生成工作流已启动（本地执行）",
                "topic": topic,
                "requirements": requirements,
                "created_at": timestamp,
                "execution_mode": "local_with_sync",
                "local_dir": paths['local_dir'],
                "sandbox_dir": paths['sandbox_dir']
            })
                
        except Exception as e:
            logger.error(f"创建报告工作流失败: {e}")
            return self.fail_response(f"创建报告工作流失败: {str(e)}")

    @openapi_schema({
        "type": "function",
        "function": {
            "name": "get_report_workflow_status",
            "description": "获取报告生成工作流的状态",
            "parameters": {
                "type": "object",
                "properties": {
                    "workflow_id": {
                        "type": "string",
                        "description": "工作流ID"
                    }
                },
                "required": ["workflow_id"]
            }
        }
    })
    @xml_schema(
        tag_name="get-report-workflow-status",
        mappings=[
            {"param_name": "workflow_id", "node_type": "content", "path": "."}
        ],
        example='''
        <get-report-workflow-status>
        workflow-uuid-here
        </get-report-workflow-status>
        '''
    )
    async def get_report_workflow_status(self, workflow_id: str) -> ToolResult:
        """获取报告工作流状态"""
        try:
            await self._ensure_sandbox()
            
            # 从沙盒读取状态文件
            sandbox_status_file = f"{self.sandbox_results_path}/report_workflow_{workflow_id}/status.json"
            
            try:
                status_content = self.sandbox.fs.download_file(sandbox_status_file)
                status_data = json.loads(status_content.decode('utf-8'))
                
                # 读取日志文件的最新内容
                sandbox_log_file = f"{self.sandbox_results_path}/report_workflow_{workflow_id}/execution.log"
                try:
                    log_content = self.sandbox.fs.download_file(sandbox_log_file)
                    log_text = log_content.decode('utf-8')[-1000:]  # 最后1000字符
                except Exception:
                    log_text = "暂无日志信息"
                
                # 如果已完成，尝试获取结果预览
                report_preview = ""
                if status_data.get("status") == "completed":
                    sandbox_output_file = f"{self.sandbox_results_path}/report_workflow_{workflow_id}/result.jsonl"
                    try:
                        result_content = self.sandbox.fs.download_file(sandbox_output_file)
                        result_data = json.loads(result_content.decode('utf-8'))
                        report_output = result_data.get('output', '')
                        report_preview = report_output[:500] + "..." if len(report_output) > 500 else report_output
                    except Exception as e:
                        logger.debug(f"获取报告预览失败: {e}")
                
                return self.success_response({
                    "workflow_id": workflow_id,
                    "status": status_data.get("status", "unknown"),
                    "progress": status_data.get("progress", 0.0),
                    "stage": status_data.get("stage", "未知"),
                    "message": f"当前阶段: {status_data.get('stage', '未知')}",
                    "report_preview": report_preview,
                    "recent_logs": log_text,
                    "last_updated": status_data.get("last_updated"),
                    "topic": status_data.get("topic"),
                    "execution_mode": "local_with_sync"
                })
                
            except Exception as e:
                logger.debug(f"读取状态文件失败: {e}")
                return self.success_response({
                    "workflow_id": workflow_id,
                    "status": "unknown",
                    "message": "无法获取工作流状态",
                    "error": str(e)
                })
            
        except Exception as e:
            logger.error(f"获取工作流状态失败: {e}")
            return self.fail_response(f"获取工作流状态失败: {str(e)}")

    @openapi_schema({
        "type": "function", 
        "function": {
            "name": "get_report_workflow_output",
            "description": "获取已完成的报告内容",
            "parameters": {
                "type": "object",
                "properties": {
                    "workflow_id": {
                        "type": "string",
                        "description": "工作流ID"
                    }
                },
                "required": ["workflow_id"]
            }
        }
    })
    @xml_schema(
        tag_name="get-report-workflow-output",
        mappings=[
            {"param_name": "workflow_id", "node_type": "content", "path": "."}
        ],
        example='''
        <get-report-workflow-output>
        workflow-uuid-here
        </get-report-workflow-output>
        '''
    )
    async def get_report_workflow_output(self, workflow_id: str) -> ToolResult:
        """获取报告生成结果"""
        try:
            await self._ensure_sandbox()
            
            # 从沙盒读取状态和结果文件
            sandbox_status_file = f"{self.sandbox_results_path}/report_workflow_{workflow_id}/status.json"
            sandbox_output_file = f"{self.sandbox_results_path}/report_workflow_{workflow_id}/result.jsonl"
            
            # 检查状态
            try:
                status_content = self.sandbox.fs.download_file(sandbox_status_file)
                status_data = json.loads(status_content.decode('utf-8'))
                
                if status_data.get("status") != "completed":
                    return self.fail_response(f"工作流尚未完成，当前状态: {status_data.get('status', 'unknown')}")
                    
            except Exception:
                return self.fail_response("无法获取工作流状态")
            
            # 获取报告结果
            try:
                result_content = self.sandbox.fs.download_file(sandbox_output_file)
                result_data = json.loads(result_content.decode('utf-8'))
                
                return self.success_response({
                    "workflow_id": workflow_id,
                    "status": "completed",
                    "report_content": result_data.get('output', ''),
                    "full_result": result_data,
                    "topic": status_data.get("topic"),
                    "execution_mode": "local_with_sync",
                    "completion_time": status_data.get("end_time"),
                    "message": "✅ 报告内容获取成功"
                })
                
            except Exception as e:
                logger.error(f"读取报告结果失败: {e}")
                return self.fail_response(f"读取报告结果失败: {str(e)}")
                
        except Exception as e:
            logger.error(f"获取报告输出失败: {e}")
            return self.fail_response(f"获取报告输出失败: {str(e)}") 