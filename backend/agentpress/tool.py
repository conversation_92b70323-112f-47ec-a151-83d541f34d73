"""
Core tool system providing the foundation for creating and managing tools.

This module defines the base classes and decorators for creating tools in AgentPress:
- Tool base class for implementing tool functionality with billing support
- Schema decorators for OpenAPI and XML tool definitions
- Result containers for standardized tool outputs
"""

from typing import Dict, Any, Union, Optional, List, Tuple
from dataclasses import dataclass, field
from abc import ABC
import json
import inspect
import time
from enum import Enum
from functools import wraps
from utils.logger import logger
from utils.config import config, EnvMode

# 尝试导入计费功能
try:
    from services.tool_billing import (
        ToolCostConfig, 
        ToolBillingCalculator, 
        ToolUsageTracker,
        CostCalculationResult,
        get_tool_cost_config
    )
    TOOL_BILLING_AVAILABLE = True
except ImportError as e:
    logger.warning(f"Tool billing not available: {e}")
    TOOL_BILLING_AVAILABLE = False
    ToolCostConfig = None
    ToolBillingCalculator = None
    ToolUsageTracker = None
    CostCalculationResult = None
    get_tool_cost_config = None

class SchemaType(Enum):
    """Enumeration of supported schema types for tool definitions."""
    OPENAPI = "openapi"
    XML = "xml"
    CUSTOM = "custom"

@dataclass
class XMLNodeMapping:
    """Maps an XML node to a function parameter.
    
    Attributes:
        param_name (str): Name of the function parameter
        node_type (str): Type of node ("element", "attribute", or "content")
        path (str): XPath-like path to the node ("." means root element)
        required (bool): Whether the parameter is required (defaults to True)
    """
    param_name: str
    node_type: str = "element"
    path: str = "."
    required: bool = True

@dataclass
class XMLTagSchema:
    """Schema definition for XML tool tags.
    
    Attributes:
        tag_name (str): Root tag name for the tool
        mappings (List[XMLNodeMapping]): Parameter mappings for the tag
        example (str, optional): Example showing tag usage
        
    Methods:
        add_mapping: Add a new parameter mapping to the schema
    """
    tag_name: str
    mappings: List[XMLNodeMapping] = field(default_factory=list)
    example: Optional[str] = None
    
    def add_mapping(self, param_name: str, node_type: str = "element", path: str = ".", required: bool = True) -> None:
        """Add a new node mapping to the schema.
        
        Args:
            param_name: Name of the function parameter
            node_type: Type of node ("element", "attribute", or "content")
            path: XPath-like path to the node
            required: Whether the parameter is required
        """
        self.mappings.append(XMLNodeMapping(
            param_name=param_name,
            node_type=node_type, 
            path=path,
            required=required
        ))
        logger.debug(f"Added XML mapping for parameter '{param_name}' with type '{node_type}' at path '{path}', required={required}")

@dataclass
class ToolSchema:
    """Container for tool schemas with type information.
    
    Attributes:
        schema_type (SchemaType): Type of schema (OpenAPI, XML, or Custom)
        schema (Dict[str, Any]): The actual schema definition
        xml_schema (XMLTagSchema, optional): XML-specific schema if applicable
    """
    schema_type: SchemaType
    schema: Dict[str, Any]
    xml_schema: Optional[XMLTagSchema] = None

@dataclass
class ToolResult:
    """Container for tool execution results.
    
    Attributes:
        success (bool): Whether the tool execution succeeded
        output (str): Output message or error description
    """
    success: bool
    output: str

class Tool(ABC):
    """Abstract base class for all tools.
    
    Provides the foundation for implementing tools with schema registration
    and result handling capabilities, including automatic billing support.
    
    Attributes:
        _schemas (Dict[str, List[ToolSchema]]): Registered schemas for tool methods
        cost_config (ToolCostConfig): Tool cost configuration
        calculator (ToolBillingCalculator): Cost calculation engine
        usage_tracker (ToolUsageTracker): Usage tracking and recording
        
    Methods:
        get_schemas: Get all registered tool schemas
        success_response: Create a successful result
        fail_response: Create a failed result
        execute_with_cost_tracking: Execute method with cost tracking
        get_cost_summary: Get cost usage summary
    """
    
    def __init__(self):
        """Initialize tool with empty schema registry and billing support."""
        self._schemas: Dict[str, List[ToolSchema]] = {}
        logger.debug(f"Initializing tool class: {self.__class__.__name__}")
        self._register_schemas()
        
        # 初始化计费相关组件
        if TOOL_BILLING_AVAILABLE:
            self.calculator = ToolBillingCalculator()
            self.usage_tracker = ToolUsageTracker()
        else:
            self.calculator = None
            self.usage_tracker = None
        
        # 跟踪统计信息 - 按方法分别统计
        self.method_stats = {}  # {method_name: {"count": int, "cost": float, "time": float}}
        self.total_cost = 0.0
        self.total_execution_time = 0.0

    def _register_schemas(self):
        """Register schemas from all decorated methods."""
        for name, method in inspect.getmembers(self, predicate=inspect.ismethod):
            if hasattr(method, 'tool_schemas'):
                self._schemas[name] = method.tool_schemas
                logger.debug(f"Registered schemas for method '{name}' in {self.__class__.__name__}")

    def get_schemas(self) -> Dict[str, List[ToolSchema]]:
        """Get all registered tool schemas.
        
        Returns:
            Dict mapping method names to their schema definitions
        """
        return self._schemas

    def success_response(self, data: Union[Dict[str, Any], str]) -> ToolResult:
        """Create a successful tool result.
        
        Args:
            data: Result data (dictionary or string)
            
        Returns:
            ToolResult with success=True and formatted output
        """
        if isinstance(data, str):
            text = data
        else:
            text = json.dumps(data, indent=2)
        logger.debug(f"Created success response for {self.__class__.__name__}")
        return ToolResult(success=True, output=text)

    def fail_response(self, msg: str) -> ToolResult:
        """Create a failed tool result.
        
        Args:
            msg: Error message describing the failure
            
        Returns:
            ToolResult with success=False and error message
        """
        logger.debug(f"Tool {self.__class__.__name__} returned failed result: {msg}")
        return ToolResult(success=False, output=msg)

    def _get_method_cost_config(self, method_name: str):
        """获取指定方法的成本配置"""
        if not TOOL_BILLING_AVAILABLE:
            return None
        return get_tool_cost_config(self.__class__.__name__, method_name)

    async def execute_with_cost_tracking(self, 
                                       method_name: str,
                                       execution_func,
                                       *args, 
                                       **kwargs) -> Tuple[Any, Optional[CostCalculationResult]]:
        """
        执行工具方法并跟踪成本
        
        Args:
            method_name: 方法名称
            execution_func: 要执行的函数
            *args: 传递给执行函数的位置参数
            **kwargs: 传递给执行函数的关键字参数
            
        Returns:
            Tuple[执行结果, 成本计算结果]
        """
        start_time = time.time()
        cost_result = None
        
        try:
            # 执行原方法
            if inspect.iscoroutinefunction(execution_func):
                result = await execution_func(*args, **kwargs)
            else:
                result = execution_func(*args, **kwargs)
            execution_time = time.time() - start_time
            
            # 如果计费功能不可用或在本地开发模式，跳过成本计算
            if not TOOL_BILLING_AVAILABLE or config.ENV_MODE == EnvMode.LOCAL:
                logger.debug(f"Skipping cost tracking for {method_name} (billing disabled or local mode)")
                return result, None
            
            # 获取方法级别的成本配置并计算成本
            method_cost_config = self._get_method_cost_config(method_name)
            if method_cost_config and self.calculator:
                # 获取该方法的执行次数统计
                if method_name not in self.method_stats:
                    self.method_stats[method_name] = {"count": 0, "cost": 0.0, "time": 0.0}
                
                self.method_stats[method_name]["count"] += 1
                method_execution_count = self.method_stats[method_name]["count"]
                
                cost_result = self.calculator.calculate_cost(
                    config=method_cost_config,
                    execution_result=result if isinstance(result, dict) else {},
                    execution_time=execution_time,
                    execution_count=method_execution_count,
                    **kwargs
                )
                
                # 更新统计信息
                method_cost_dollars = cost_result.to_usd_dollars()
                self.method_stats[method_name]["cost"] += method_cost_dollars
                self.method_stats[method_name]["time"] += execution_time
                self.total_cost += method_cost_dollars
                self.total_execution_time += execution_time
                
                # 记录使用情况
                await self._record_usage(method_name, cost_result, execution_time, result)
                
                logger.info(f"Tool {self.__class__.__name__}.{method_name} executed - Cost: {cost_result.amount} cents, Time: {execution_time:.2f}s (Total calls: {method_execution_count})")
            
            return result, cost_result
            
        except Exception as e:
            execution_time = time.time() - start_time
            logger.error(f"Tool {self.__class__.__name__}.{method_name} failed after {execution_time:.2f}s: {str(e)}")
            
            # 即使执行失败，也可能产生成本（如API调用失败但仍然计费）
            method_cost_config = self._get_method_cost_config(method_name)
            if TOOL_BILLING_AVAILABLE and method_cost_config and self.calculator and config.ENV_MODE != EnvMode.LOCAL:
                try:
                    # 获取或初始化方法统计
                    if method_name not in self.method_stats:
                        self.method_stats[method_name] = {"count": 0, "cost": 0.0, "time": 0.0}
                    
                    method_execution_count = self.method_stats[method_name]["count"] + 1
                    
                    cost_result = self.calculator.calculate_cost(
                        config=method_cost_config,
                        execution_result={},
                        execution_time=execution_time,
                        execution_count=method_execution_count,
                        **kwargs
                    )
                    if cost_result.amount > 0:
                        await self._record_usage(method_name, cost_result, execution_time, {"error": str(e)})
                        logger.info(f"Recorded cost for failed tool execution: {cost_result.amount} cents")
                except Exception as cost_error:
                    logger.error(f"Error calculating cost for failed execution: {str(cost_error)}")
            
            raise e
    
    async def _record_usage(self, 
                           method_name: str, 
                           cost_result: CostCalculationResult, 
                           execution_time: float,
                           execution_result: Any):
        """记录工具使用情况到数据库"""
        if not self.usage_tracker:
            return
        
        try:
            # 获取用户ID和项目ID
            user_id = await self._get_user_id()
            project_id = await self._get_project_id()
            
            if not user_id:
                logger.warning("Cannot record tool usage: user_id not available")
                return
            
            # 记录使用情况
            await self.usage_tracker.record_tool_usage(
                user_id=user_id,
                tool_name=f"{self.__class__.__name__}.{method_name}",
                project_id=project_id or "unknown",
                cost_result=cost_result,
                execution_time=execution_time,
                execution_result=execution_result if isinstance(execution_result, dict) else {}
            )
            
        except Exception as e:
            logger.error(f"Error recording tool usage: {str(e)}")
    
    async def _get_user_id(self) -> Optional[str]:
        """获取当前用户ID - 子类应该重写此方法"""
        logger.warning(f"Tool {self.__class__.__name__} should implement _get_user_id method for billing support")
        return None
    
    async def _get_project_id(self) -> Optional[str]:
        """获取当前项目ID - 子类应该重写此方法"""
        logger.warning(f"Tool {self.__class__.__name__} should implement _get_project_id method for billing support")
        return None
    
    def get_cost_summary(self) -> Dict[str, Any]:
        """获取工具成本摘要（按方法分别统计）"""
        total_executions = sum(stats["count"] for stats in self.method_stats.values())
        
        method_summaries = {}
        for method_name, stats in self.method_stats.items():
            method_config = self._get_method_cost_config(method_name)
            method_summaries[method_name] = {
                "execution_count": stats["count"],
                "total_cost_dollars": stats["cost"],
                "total_execution_time": stats["time"],
                "average_cost_per_execution": stats["cost"] / max(stats["count"], 1),
                "average_execution_time": stats["time"] / max(stats["count"], 1),
                "cost_config": {
                    "model": method_config.model.value if method_config else None,
                    "base_cost": float(method_config.base_cost) if method_config else 0,
                    "unit_cost": float(method_config.unit_cost) if method_config else 0,
                    "free_tier_limit": method_config.free_tier_limit if method_config else None
                } if method_config else None
            }
        
        return {
            "tool_name": self.__class__.__name__,
            "total_executions": total_executions,
            "total_cost_dollars": self.total_cost,
            "total_execution_time": self.total_execution_time,
            "average_cost_per_execution": self.total_cost / max(total_executions, 1),
            "average_execution_time": self.total_execution_time / max(total_executions, 1),
            "methods": method_summaries
        }

def _add_schema(func, schema: ToolSchema):
    """Helper to add schema to a function."""
    if not hasattr(func, 'tool_schemas'):
        func.tool_schemas = []
    func.tool_schemas.append(schema)
    logger.debug(f"Added {schema.schema_type.value} schema to function {func.__name__}")
    return func

def openapi_schema(schema: Dict[str, Any]):
    """Decorator for OpenAPI schema tools."""
    def decorator(func):
        logger.debug(f"Applying OpenAPI schema to function {func.__name__}")
        return _add_schema(func, ToolSchema(
            schema_type=SchemaType.OPENAPI,
            schema=schema
        ))
    return decorator

def xml_schema(
    tag_name: str,
    mappings: List[Dict[str, Any]] = None,
    example: str = None
):
    """
    Decorator for XML schema tools with improved node mapping.
    
    Args:
        tag_name: Name of the root XML tag
        mappings: List of mapping definitions, each containing:
            - param_name: Name of the function parameter
            - node_type: "element", "attribute", or "content" 
            - path: Path to the node (default "." for root)
            - required: Whether the parameter is required (default True)
        example: Optional example showing how to use the XML tag
    
    Example:
        @xml_schema(
            tag_name="str-replace",
            mappings=[
                {"param_name": "file_path", "node_type": "attribute", "path": "."},
                {"param_name": "old_str", "node_type": "element", "path": "old_str"},
                {"param_name": "new_str", "node_type": "element", "path": "new_str"}
            ],
            example='''
            <str-replace file_path="path/to/file">
                <old_str>text to replace</old_str>
                <new_str>replacement text</new_str>
            </str-replace>
            '''
        )
    """
    def decorator(func):
        logger.debug(f"Applying XML schema with tag '{tag_name}' to function {func.__name__}")
        xml_schema = XMLTagSchema(tag_name=tag_name, example=example)
        
        # Add mappings
        if mappings:
            for mapping in mappings:
                xml_schema.add_mapping(
                    param_name=mapping["param_name"],
                    node_type=mapping.get("node_type", "element"),
                    path=mapping.get("path", "."),
                    required=mapping.get("required", True)
                )
                
        return _add_schema(func, ToolSchema(
            schema_type=SchemaType.XML,
            schema={},  # OpenAPI schema could be added here if needed
            xml_schema=xml_schema
        ))
    return decorator

def custom_schema(schema: Dict[str, Any]):
    """Decorator for custom schema tools."""
    def decorator(func):
        logger.debug(f"Applying custom schema to function {func.__name__}")
        return _add_schema(func, ToolSchema(
            schema_type=SchemaType.CUSTOM,
            schema=schema
        ))
    return decorator


def cost_tracked(method_name: Optional[str] = None):
    """
    成本跟踪装饰器，用于装饰工具方法以启用成本跟踪
    
    Args:
        method_name: 可选的方法名称，如果不提供则使用函数名
        
    Usage:
        @cost_tracked("my_method")
        async def my_tool_method(self, ...):
            return self.success_response({"result": "data"})
    """
    def decorator(func):
        @wraps(func)
        async def wrapper(self, *args, **kwargs):
            # 如果工具不是Tool的实例，直接执行原方法
            if not isinstance(self, Tool):
                if inspect.iscoroutinefunction(func):
                    return await func(self, *args, **kwargs)
                else:
                    return func(self, *args, **kwargs)
            
            # 使用成本跟踪执行方法
            actual_method_name = method_name or func.__name__
            result, cost_result = await self.execute_with_cost_tracking(
                actual_method_name, 
                func, 
                self, 
                *args, 
                **kwargs
            )
            
            return result
        
        return wrapper
    return decorator
