#!/usr/bin/env python
"""
独立的Dramatiq Worker启动器

此脚本解决AsyncIO事件循环冲突问题，确保Dramatiq Worker能够正常启动。

使用方法:
    cd /root/workspace/MediaAgent/backend
    PYTHONPATH=/root/workspace/MediaAgent/backend python worker_start.py
"""

import asyncio
import sys
import os
import shutil
import glob
from pathlib import Path

def clean_python_cache():
    """清理Python字节码缓存，确保代码更新生效"""
    print("🧹 清理Python字节码缓存...")
    
    backend_path = Path(__file__).parent.absolute()
    
    try:
        # 删除所有 .pyc 文件
        pyc_files = list(backend_path.rglob("*.pyc"))
        for pyc_file in pyc_files:
            try:
                pyc_file.unlink()
            except Exception as e:
                print(f"   删除 {pyc_file} 失败: {e}")
        
        # 删除所有 __pycache__ 目录
        pycache_dirs = list(backend_path.rglob("__pycache__"))
        for pycache_dir in pycache_dirs:
            try:
                shutil.rmtree(pycache_dir)
            except Exception as e:
                print(f"   删除 {pycache_dir} 失败: {e}")
        
        print(f"✅ 清理完成: 删除了 {len(pyc_files)} 个.pyc文件和 {len(pycache_dirs)} 个__pycache__目录")
        
    except Exception as e:
        print(f"⚠️ 缓存清理出现问题: {e}")

def setup_environment():
    """设置环境变量和路径"""
    # 确保PYTHONPATH包含backend目录
    backend_path = str(Path(__file__).parent.absolute())
    if backend_path not in sys.path:
        sys.path.insert(0, backend_path)
    
    # 设置环境变量
    os.environ['PYTHONPATH'] = backend_path
    
    # 禁用字节码缓存（可选，确保始终使用最新代码）
    os.environ['PYTHONDONTWRITEBYTECODE'] = '1'
    
    # 加载环境变量
    from dotenv import load_dotenv
    env_files = ['.env_cusor', '.env']
    for env_file in env_files:
        if os.path.exists(env_file):
            load_dotenv(env_file)
            print(f"✅ 加载环境变量文件: {env_file}")
            break

def clean_event_loop():
    """清理可能存在的事件循环冲突"""
    try:
        loop = asyncio.get_event_loop()
        if loop.is_running():
            print("⚠️ 检测到运行中的事件循环，正在关闭...")
            loop.close()
    except RuntimeError as e:
        print(f"📝 事件循环状态: {e}")
    
    # 设置新的事件循环
    new_loop = asyncio.new_event_loop()
    asyncio.set_event_loop(new_loop)
    print("✅ 创建新的事件循环")

def start_worker():
    """启动Dramatiq Worker"""
    print("🚀 启动MediaAgent Dramatiq Worker...")
    
    # 清理缓存（确保代码更新生效）
    clean_python_cache()
    
    # 设置环境
    setup_environment()
    
    # 清理事件循环
    clean_event_loop()
    
    try:
        # 导入必要模块
        from dramatiq.cli import main
        from dramatiq import set_broker
        from dramatiq.brokers.rabbitmq import RabbitmqBroker
        
        # 验证环境变量
        rabbitmq_host = os.getenv('RABBITMQ_HOST', 'localhost')
        rabbitmq_port = int(os.getenv('RABBITMQ_PORT', 5672))
        
        print(f"🔌 连接到RabbitMQ: {rabbitmq_host}:{rabbitmq_port}")
        
        # 设置broker
        broker = RabbitmqBroker(host=rabbitmq_host, port=rabbitmq_port)
        set_broker(broker)
        
        # 设置dramatiq命令行参数 - 优化并发配置
        sys.argv = [
            'dramatiq',
            'run_agent_background',
            '--processes', '1',  # 1个进程
            '--threads', '1'     # 1个线程
        ]
        
        print("🔄 启动Dramatiq主进程...")
        print("📝 Worker配置: 1个进程, 1个线程")
        print("💡 代码更新已生效（缓存已清理）")
        print("⏸️ 按 Ctrl+C 停止Worker")
        
        # 启动dramatiq
        main()
        
    except KeyboardInterrupt:
        print("\n🛑 收到停止信号，正在关闭Worker...")
    except Exception as e:
        print(f"❌ Worker启动失败: {str(e)}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    start_worker() 