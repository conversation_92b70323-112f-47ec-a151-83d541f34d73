"""
开发环境沙盒配置和管理工具

此模块提供：
1. 开发环境专用的轻量级沙盒配置
2. 自动清理和重用机制
3. 资源优化策略
"""

from daytona_sdk import CreateSandboxParams
from typing import Optional, Dict, Any
from utils.logger import logger
from utils.config import Configuration
import os

class DevSandboxConfig:
    """开发环境沙盒配置管理器"""
    
    # 开发环境资源配置（减少磁盘使用）
    DEV_RESOURCES = {
        "cpu": 1,      # 降低CPU使用
        "memory": 2,   # 降低内存使用  
        "disk": 2,     # 大幅降低磁盘使用：5GB -> 2GB
    }
    
    # 生产环境资源配置
    PROD_RESOURCES = {
        "cpu": 2,
        "memory": 4,
        "disk": 5,
    }
    
    @classmethod
    def get_sandbox_params(cls, password: str, project_id: str = None, is_dev: bool = None) -> CreateSandboxParams:
        """
        获取适配环境的沙盒参数
        
        Args:
            password: VNC密码
            project_id: 项目ID
            is_dev: 是否为开发环境，None时自动检测
            
        Returns:
            CreateSandboxParams: 沙盒创建参数
        """
        # 自动检测环境
        if is_dev is None:
            is_dev = os.getenv('ENV_MODE', 'local') == 'local'
            
        # 选择资源配置
        resources = cls.DEV_RESOURCES if is_dev else cls.PROD_RESOURCES
        
        # 构建标签
        labels = {}
        if project_id:
            labels['id'] = project_id
            
        # 添加环境标签用于后续清理
        labels['env'] = 'dev' if is_dev else 'prod'
        labels['auto_cleanup'] = 'true' if is_dev else 'false'
        
        logger.info(f"Creating {'development' if is_dev else 'production'} sandbox with resources: {resources}")
        
        return CreateSandboxParams(
            image=Configuration.SANDBOX_IMAGE_NAME,
            public=True,
            labels=labels,
            env_vars={
                "CHROME_PERSISTENT_SESSION": "true",
                "RESOLUTION": "1024x768x24",
                "RESOLUTION_WIDTH": "1024", 
                "RESOLUTION_HEIGHT": "768",
                "VNC_PASSWORD": password,
                "ANONYMIZED_TELEMETRY": "false",
                "CHROME_PATH": "",
                "CHROME_USER_DATA": "",
                "CHROME_DEBUGGING_PORT": "9222",
                "CHROME_DEBUGGING_HOST": "localhost",
                "CHROME_CDP": "",
                # 开发环境专用变量
                "DEV_MODE": "true" if is_dev else "false",
                "AUTO_CLEANUP": "true" if is_dev else "false"
            },
            resources=resources
        )
    
    @classmethod
    def should_auto_cleanup(cls, sandbox_labels: Dict[str, str]) -> bool:
        """
        判断沙盒是否应该自动清理
        
        Args:
            sandbox_labels: 沙盒标签
            
        Returns:
            bool: 是否应该自动清理
        """
        return sandbox_labels.get('auto_cleanup') == 'true'
    
    @classmethod
    def is_dev_sandbox(cls, sandbox_labels: Dict[str, str]) -> bool:
        """
        判断是否为开发环境沙盒
        
        Args:
            sandbox_labels: 沙盒标签
            
        Returns:
            bool: 是否为开发环境沙盒
        """
        return sandbox_labels.get('env') == 'dev'


class SandboxPool:
    """沙盒池管理器 - 开发环境沙盒重用机制"""
    
    def __init__(self):
        self._cached_sandbox_id: Optional[str] = None
        self._last_project_id: Optional[str] = None
    
    def get_reusable_sandbox_id(self, project_id: str) -> Optional[str]:
        """
        获取可重用的沙盒ID（开发环境优化）
        
        Args:
            project_id: 当前项目ID
            
        Returns:
            Optional[str]: 可重用的沙盒ID，如果没有则返回None
        """
        # 开发环境下，如果是连续的同一项目，可以重用沙盒
        if (os.getenv('ENV_MODE') == 'local' and 
            self._cached_sandbox_id and 
            self._last_project_id == project_id):
            logger.info(f"Reusing cached sandbox {self._cached_sandbox_id} for project {project_id}")
            return self._cached_sandbox_id
        
        return None
    
    def cache_sandbox(self, sandbox_id: str, project_id: str):
        """
        缓存沙盒ID用于重用
        
        Args:
            sandbox_id: 沙盒ID
            project_id: 项目ID
        """
        if os.getenv('ENV_MODE') == 'local':
            self._cached_sandbox_id = sandbox_id
            self._last_project_id = project_id
            logger.debug(f"Cached sandbox {sandbox_id} for project {project_id}")
    
    def clear_cache(self):
        """清除沙盒缓存"""
        self._cached_sandbox_id = None
        self._last_project_id = None
        logger.debug("Cleared sandbox cache")


# 全局沙盒池实例
sandbox_pool = SandboxPool() 