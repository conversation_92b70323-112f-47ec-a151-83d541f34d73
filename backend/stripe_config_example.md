# Stripe 测试环境配置指南

## 环境变量配置

在 `.env` 文件中添加以下配置：

```bash
# 环境模式
ENV_MODE=staging

# Stripe 测试环境配置
STRIPE_SECRET_KEY=sk_test_your_test_secret_key_here
STRIPE_WEBHOOK_SECRET=whsec_your_test_webhook_secret_here

# 其他必需配置
SUPABASE_URL=your_supabase_url
SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

REDIS_HOST=your_redis_host
REDIS_PORT=6379
REDIS_PASSWORD=your_redis_password
REDIS_SSL=true

DAYTONA_API_KEY=your_daytona_api_key
DAYTONA_SERVER_URL=your_daytona_server_url
DAYTONA_TARGET=your_daytona_target

TAVILY_API_KEY=your_tavily_api_key
RAPID_API_KEY=your_rapid_api_key
FIRECRAWL_API_KEY=your_firecrawl_api_key

# LLM API Keys
ANTHROPIC_API_KEY=your_anthropic_api_key
OPENAI_API_KEY=your_openai_api_key
GROQ_API_KEY=your_groq_api_key
OPENROUTER_API_KEY=your_openrouter_api_key
```

## Stripe 产品配置

### 测试环境产品

系统已配置以下三个订阅等级：

1. **Basic 会员**
   - 价格：$19.9/月
   - 积分：1000 积分/月
   - 产品 ID：`prod_SZNfVNMlQVveHV`

2. **Plus 会员**
   - 价格：$39.9/月
   - 积分：2500 积分/月
   - 产品 ID：`prod_SZNf4kvOkVbrlW`

3. **Pro 会员**
   - 价格：$199/月
   - 积分：10000 积分/月
   - 产品 ID：`prod_SZNezqnbf0dYgT`

### 价格 ID 配置

在 `utils/config.py` 中，需要将以下价格 ID 替换为实际的 Stripe 价格 ID：

```python
# 当前配置（需要替换为实际的价格 ID）
STRIPE_PRICE_ID_BASIC: str = 'price_basic_subscription'
STRIPE_PRICE_ID_PLUS: str = 'price_plus_subscription'
STRIPE_PRICE_ID_PRO: str = 'price_pro_subscription'
```

## Webhook 配置

### 设置 Webhook 端点

在 Stripe Dashboard 中设置 webhook 端点：

1. 进入 Stripe Dashboard
2. 导航到 Developers > Webhooks
3. 添加端点：`https://your-domain.com/api/billing/webhook`
4. 选择以下事件：
   - `customer.subscription.created`
   - `invoice.payment_succeeded`
   - `customer.subscription.updated`
   - `customer.subscription.deleted`

### 本地测试

使用 Stripe CLI 进行本地测试：

```bash
# 安装 Stripe CLI
# 登录 Stripe
stripe login

# 转发 webhook 到本地
stripe listen --forward-to localhost:8000/api/billing/webhook
```

## API 端点

### 获取订阅计划
```
GET /api/billing/subscription-plans
```

### 创建订阅
```
POST /api/billing/create-checkout-session
{
  "price_id": "price_basic_subscription",
  "success_url": "https://your-domain.com/success",
  "cancel_url": "https://your-domain.com/cancel"
}
```

### 检查积分余额
```
GET /api/billing/credits
```

### 使用积分
```
POST /api/billing/use-credits?model_name=openai/gpt-4o
```

## 数据库表结构

确保数据库中有以下表：

### user_credits 表
```sql
CREATE TABLE user_credits (
  user_id UUID PRIMARY KEY,
  total_credits INTEGER DEFAULT 0,
  used_credits INTEGER DEFAULT 0,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);
```

### credit_purchases 表
```sql
CREATE TABLE credit_purchases (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id),
  credits_purchased INTEGER,
  product_id TEXT,
  amount_paid DECIMAL(10,2),
  created_at TIMESTAMP DEFAULT NOW()
);
```

### credit_usage 表
```sql
CREATE TABLE credit_usage (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id),
  model_name TEXT,
  credits_used INTEGER,
  created_at TIMESTAMP DEFAULT NOW()
);
```

## 测试流程

1. **设置测试环境**
   - 配置 `.env` 文件
   - 启动应用

2. **创建订阅**
   - 调用 `/api/billing/subscription-plans` 获取可用计划
   - 调用 `/api/billing/create-checkout-session` 创建订阅

3. **测试积分系统**
   - 检查用户积分余额
   - 测试模型调用时的积分扣除

4. **验证 Webhook**
   - 确保订阅创建时自动添加积分
   - 确保每月续费时自动添加积分 