[tool.poetry]
name = "suna"
version = "1.0.0"
description = "AI Agent with py311 dependencies"
authors = ["Your Name <<EMAIL>>"]
readme = "README.md"
license = "MIT"
package-mode = false 

[tool.poetry.dependencies]
python = ">=3.11,<3.14"
agentops = "^0.4.0"
aiofiles = "^24.1.0"
aiohappyeyeballs = "^2.6.0"
aiohttp = "^3.11.18"
aiosignal = "^1.3.0"
altair = "^4.2.0"
amqp = "^5.3.0"
annotated-types = "^0.7.0"
anthropic = "^0.52.0"
anyio = "^4.9.0"
async-timeout = "^4.0.0"
asyncio = "^3.4.0"
attrs = "^25.3.0"
automat = "^24.8.0"
autoprompt = "^1.0.0"
babel = "^2.17.0"
backoff = "^2.2.0"
beautifulsoup4 = "^4.13.0"
bidict = "^0.23.0"
billiard = "^4.2.0"
boto3 = "^1.38.0"
botocore = "^1.38.0"
brotli = "^1.1.0"
cachetools = "^5.5.0"
celery = "^5.5.0"
certifi = "^2024.2.0"
charset-normalizer = "^3.4.0"
class-registry = "^2.1.0"
click = "^8.1.0"
click-didyoumean = "^0.3.0"
click-plugins = "^1.1.0"
click-repl = "^0.3.0"
colorama = "^0.4.0"
constantly = "^23.10.0"
courlan = "^1.3.0"
cssselect = "^1.3.0"
dataclasses-json = "^0.6.0"
datarecorder = "^3.6.0"
dateparser = "^1.2.0"
daytona-api-client = "^0.19.0"
daytona-sdk = "^0.14.0"
deprecated = "^1.2.0"
deprecation = "^2.1.0"
dill = "^0.4.0"
distro = "^1.9.0"
dotenv = "^0.9.0"
downloadkit = "^2.0.0"
dramatiq = "^1.18.0"
drissionpage = "^4.1.0"
duckduckgo-search = "^8.0.0"
e2b = "^1.5.0"
e2b-code-interpreter = "^1.5.0"
entrypoints = "^0.4.0"
env = "^0.1.0"
environs = "^9.5.0"
et-xmlfile = "^2.0.0"
exa-py = "^1.13.0"
exceptiongroup = "^1.3.0"
fastapi = "^0.115.0"
ffmpy = "^0.5.0"
filelock = "^3.18.0"
flask = "^2.0.0"
flask-cors = "^3.0.0"
flask-socketio = "^5.5.0"
frozenlist = "^1.5.0"
fsspec = "^2025.3.0"
google-ai-generativelanguage = "^0.6.0"
google-api-core = "^2.25.0"
google-api-python-client = "^2.170.0"
google-auth = "^2.40.0"
google-auth-httplib2 = "^0.2.0"
google-generativeai = "^0.8.0"
googleapis-common-protos = "^1.70.0"
gotrue = "^2.12.0"
gradio = "^5.32.0"
gradio-client = "^1.10.0"
greenlet = "^3.2.0"
griffe = "^1.7.0"
groovy = "^0.1.0"
grpcio = "^1.71.0"
grpcio-status = "^1.71.0"
gunicorn = "^20.1.0"
h11 = "^0.14.0"
h2 = "^4.2.0"
hf-xet = "^1.1.0"
hpack = "^4.1.0"
htmldate = "^1.9.0"
httpcore = "^1.0.0"
httplib2 = "^0.22.0"
httpx = "^0.28.0"
httpx-sse = "^0.4.0"
huggingface-hub = "^0.32.0"
hyperframe = "^6.1.0"
hyperlink = "^21.0.0"
idna = "^3.10.0"
importlib-metadata = "^8.6.0"
incremental = "^24.7.0"
iniconfig = "^2.1.0"
itsdangerous = "^2.2.0"
jieba = "^0.42.0"
jinja2 = "^3.1.0"
jiter = "^0.9.0"
jmespath = "^1.0.0"
joblib = "^1.5.0"
json-repair = "^0.44.0"
jsonpatch = "^1.33.0"
jsonpointer = "^3.0.0"
jsonschema = "^4.24.0"
jsonschema-specifications = "^2024.10.0"
justext = "^3.0.0"
kombu = "^5.5.0"
langchain = "^0.3.25"
langchain-community = "^0.3.24"
langchain-core = "^0.3.60"
langchain-deepseek = "^0.1.0"
langchain-mcp-adapters = "^0.1.0"
langchain-openai = "^0.3.17"
langchain-text-splitters = "^0.3.0"
langfuse = "^2.60.0"
langgraph = "^0.4.0"
langgraph-checkpoint = "^2.0.0"
langgraph-prebuilt = "^0.1.0"
langgraph-sdk = "^0.1.0"
langsmith = "^0.3.42"
litellm = "^1.66.0"
loguru = "^0.7.0"
lxml = "^5.4.0"
lxml-html-clean = "^0.4.0"
markdown-it-py = "^3.0.0"
markupsafe = "^3.0.0"
marshmallow = "^3.26.0"
mcp = "^1.9.0"
mdurl = "^0.1.0"
multidict = "^6.4.0"
mypy-extensions = "^1.1.0"
nest-asyncio = "^1.6.0"
nodeenv = "^1.9.0"
numpy = "^2.0.0"
openai = "^1.79.0"
openpyxl = "^3.1.0"
opentelemetry-api = "^1.33.0"
opentelemetry-exporter-otlp-proto-common = "^1.33.0"
opentelemetry-exporter-otlp-proto-http = "^1.33.0"
opentelemetry-instrumentation = "^0.54b1"
opentelemetry-proto = "^1.33.0"
opentelemetry-sdk = "^1.33.0"
opentelemetry-semantic-conventions = "^0.54b1"
ordered-set = "^4.1.0"
orjson = "^3.10.0"
ormsgpack = "^1.9.0"
overrides = "^7.7.0"
packaging = "^24.1.0"
pandas = "^2.2.0"
pika = "^1.3.0"
pillow = "^11.2.0"
pluggy = "^1.5.0"
postgrest = "^1.0.0"
primp = "^0.15.0"
prisma = "^0.15.0"
prometheus-client = "^0.22.0"
prompt-toolkit = "^3.0.0"
propcache = "^0.3.0"
proto-plus = "^1.26.0"
protobuf = "^5.29.0"
psutil = "^6.0.0"
pyasn1 = "^0.6.0"
pyasn1-modules = "^0.4.0"
pycryptodomex = "^3.23.0"
pydantic = "^2.7.4"
pydantic-core = "^2.33.0"
pydantic-settings = "^2.9.0"
pydub = "^0.25.0"
pygments = "^2.19.0"
pyjwt = "^2.10.0"
pyparsing = "^3.2.0"
pytesseract = "^0.3.0"
pytest = "^8.3.0"
pytest-asyncio = "^0.24.0"
pytest-mock = "^3.14.0"
python-dateutil = "^2.9.0"
python-dotenv = "^1.0.0"
python-engineio = "^4.12.0"
python-multipart = "^0.0.20"
python-ripgrep = "^0.0.6"
python-socketio = "^5.13.0"
pytz = "^2025.2.0"
pyyaml = "^6.0.0"
questionary = "^2.0.0"
realtime = "^2.4.0"
redis = "^5.2.0"
referencing = "^0.36.0"
regex = "^2024.11.0"
requests = "^2.32.0"
requests-file = "^2.1.0"
requests-toolbelt = "^1.0.0"
rich = "^14.0.0"
rpds-py = "^0.24.0"
rsa = "^4.9.0"
ruff = "^0.11.0"
s3transfer = "^0.13.0"
safehttpx = "^0.1.0"
scikit-learn = "^1.6.0"
scipy = "^1.15.0"
semantic-version = "^2.10.0"
shellingham = "^1.5.0"
simple-websocket = "^1.1.0"
six = "^1.17.0"
sniffio = "^1.3.0"
soupsieve = "^2.7.0"
sqlalchemy = "^2.0.0"
sse-starlette = "^2.3.0"
starlette = "^0.46.0"
storage3 = "^0.11.0"
strenum = "^0.4.0"
stripe = "^12.2.0"
supabase = "^2.15.0"
supafunc = "^0.9.0"
tavily-python = "^0.7.0"
tenacity = "^9.1.0"
termcolor = "^2.4.0"
threadpoolctl = "^3.6.0"
tiktoken = "^0.9.0"
tld = "^0.13.0"
tldextract = "^5.3.0"
tokenizers = "^0.21.0"
tomli = "^2.2.0"
tomlkit = "^0.13.0"
toolz = "^1.0.0"
tqdm = "^4.67.0"
trafilatura = "^2.0.0"
twisted = "^24.11.0"
typer = "^0.16.0"
typing-extensions = "^4.13.0"
typing-inspect = "^0.9.0"
typing-inspection = "^0.4.0"
tzdata = "^2025.2.0"
tzlocal = "^5.3.0"
upstash-redis = "^1.3.0"
uritemplate = "^4.1.0"
urllib3 = "^2.4.0"
uvicorn = "^0.34.0"
vine = "^5.1.0"
vncdotool = "^1.2.0"
wcwidth = "^0.2.0"
websocket-client = "^1.8.0"
websockets = "^14.2.0"
werkzeug = "^2.0.0"
wrapt = "^1.17.0"
wsproto = "^1.2.0"
xxhash = "^3.5.0"
yarl = "^1.19.0"
zipp = "^3.21.0"
zope-interface = "^7.2.0"
zstandard = "^0.23.0"

[tool.poetry.group.dev.dependencies]
# 开发依赖可以在这里添加

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"
