# MediaAgent V2 实用集成方案

## 概述

基于MediaAgent V2统一架构设计，本方案专注于实现一个支持交互式工作流的Agent系统，采用渐进式实施策略，确保与现有系统的平滑迁移和兼容。

## 核心设计原则

1. **统一架构**: 基于Core Agent Engine的统一框架
2. **交互优先**: 支持用户参与的交互式工作流
3. **渐进实施**: 分阶段实施，每阶段都可独立部署
4. **质量保障**: 内置质量监控和用户确认机制
5. **平滑迁移**: 与现有系统并行运行，逐步切换

## 系统架构

### 整体架构
```
MediaAgent V2 架构
├── Core Agent Engine (统一引擎)
│   ├── Planning Agent (规划协调)
│   ├── Agent Registry (代理注册)
│   ├── Workflow Engine (工作流引擎)
│   ├── State Manager (状态管理)
│   └── Tool System (工具系统)
├── Sub Agents (子Agent)
│   ├── Article Agent (文章代理)
│   ├── Research Agent (研究代理)
│   ├── Analysis Agent (分析代理)
│   └── Custom Agents (自定义代理)
├── Tool Ecosystem (工具生态)
├── Infrastructure (基础设施)
└── API Gateway (API网关)
```

### 目录结构
```
backend/
├── agent/                          # 现有系统（保持不变）
└── agent_v2/                       # 新V2系统
    ├── core/                       # Core Agent Engine
    │   ├── engine.py              # 核心引擎
    │   ├── planning_agent.py      # 规划协调Agent
    │   ├── agent_registry.py      # Agent注册中心
    │   ├── workflow_engine.py     # 工作流引擎
    │   ├── state_manager.py       # 状态管理
    │   ├── tool_system.py         # 工具系统
    │   └── models.py              # 数据模型
    ├── agents/                     # Sub Agents
    │   ├── base_agent.py          # Agent基类
    │   ├── article_agent.py       # 文章Agent
    │   ├── research_agent.py      # 研究Agent
    │   └── analysis_agent.py      # 分析Agent
    ├── tools/                      # 工具生态
    ├── infrastructure/             # 基础设施
    └── config/                     # 配置
```

## 核心组件设计

### 1. Planning Agent (规划协调Agent)

Planning Agent是系统的核心协调者，负责：
- 用户意图分析和理解
- 任务规划和分解
- Agent调度和协调
- 用户交互管理

```python
class PlanningAgent:
    """规划协调Agent - 系统的核心协调者"""
    
    async def analyze_user_intent(self, user_input: str) -> TaskPlan:
        """分析用户意图，生成任务计划"""
        
    async def coordinate_execution(self, plan: TaskPlan) -> ExecutionResult:
        """协调Agent执行任务计划"""
        
    async def handle_user_interaction(self, feedback: UserFeedback) -> PlanUpdate:
        """处理用户交互和反馈"""
```

### 2. Interactive Workflow Engine (交互式工作流引擎)

支持用户参与的工作流执行：

```python
class InteractiveWorkflow:
    """交互式工作流"""
    
    def __init__(self):
        self.current_step = 0
        self.user_confirmations = []
        self.can_interrupt = True
        self.checkpoints = {}
    
    async def execute_with_user_interaction(self):
        """执行支持用户交互的工作流"""
        
    async def pause_for_user_confirmation(self, result):
        """暂停等待用户确认"""
        
    async def resume_from_checkpoint(self, checkpoint_id):
        """从检查点恢复执行"""
```

### 3. Unified State Manager (统一状态管理)

管理整个系统的状态：

```python
class UnifiedState:
    """统一状态模型"""
    
    # 基础信息
    session_id: str
    user_id: Optional[str]
    current_task: str
    status: TaskStatus
    
    # 工作流状态
    current_step: str
    completed_steps: List[str]
    step_outputs: Dict[str, Any]
    
    # 用户交互
    pending_confirmations: List[Dict]
    user_feedback_history: List[Dict]
    
    # 质量和上下文
    quality_metrics: Dict[str, float]
    shared_context: Dict[str, Any]
```

## 交互式工作流设计

### 用户交互模式

#### 1. 计划确认模式
```python
# 用户输入: "我想创建一个关于AI技术的视频脚本"

# Planning Agent分析并返回计划
plan = {
    "task_type": "video_script_creation",
    "steps": [
        {"step": "story_creation", "agent": "story_agent", "description": "创建故事大纲"},
        {"step": "script_conversion", "agent": "script_agent", "description": "转换为脚本格式"},
        {"step": "quality_review", "agent": "analysis_agent", "description": "质量检查"}
    ],
    "estimated_time": "15-20分钟"
}

# 等待用户确认或调整
user_response = await wait_for_user_confirmation(plan)
```

#### 2. 中间结果确认模式
```python
# Agent执行完一个步骤后
story_result = await story_agent.create_story(topic="AI技术")

# 返回给用户确认
confirmation_request = {
    "step": "story_creation",
    "result": story_result,
    "options": ["继续", "修改", "重新生成"],
    "feedback_prompt": "请确认故事大纲是否符合您的要求"
}

user_feedback = await request_user_feedback(confirmation_request)
```

#### 3. 动态调整模式
```python
# 用户在执行过程中提出调整
user_adjustment = {
    "type": "modify_requirement",
    "content": "请加入更多技术细节",
    "apply_to": "remaining_steps"
}

# Planning Agent调整后续计划
updated_plan = await planning_agent.adjust_plan(current_plan, user_adjustment)
```

## 文章生成Agent设计

### Article Agent实现

```python
class ArticleAgent(BaseAgent):
    """文章生成Agent - 融合现有Generator系统能力"""
    
    def __init__(self):
        super().__init__(name="article_agent")
        self.research_agent = ResearchAgent()
        self.analysis_agent = AnalysisAgent()
    
    async def generate_article(self, requirements: ArticleRequirements, state: UnifiedState):
        """生成文章的主流程"""
        
        # Step 1: 规划阶段
        plan = await self._create_article_plan(requirements)
        await state.request_user_confirmation("article_plan", plan)
        
        # Step 2: 研究阶段  
        research_result = await self.research_agent.conduct_research(
            topic=requirements.topic,
            keywords=requirements.keywords
        )
        await state.request_user_confirmation("research_result", research_result)
        
        # Step 3: 写作阶段
        draft = await self._write_article_draft(plan, research_result)
        await state.request_user_confirmation("article_draft", draft)
        
        # Step 4: 优化阶段
        final_article = await self.analysis_agent.optimize_content(draft)
        
        return final_article
```

### 质量保障机制

```python
class QualityAssurance:
    """质量保障系统"""
    
    async def check_content_quality(self, content: str, requirements: Dict) -> QualityReport:
        """检查内容质量"""
        
        quality_checks = [
            self._check_length_compliance(content, requirements),
            self._check_keyword_coverage(content, requirements.get('keywords', [])),
            self._check_structure_quality(content),
            self._check_readability(content)
        ]
        
        results = await asyncio.gather(*quality_checks)
        return QualityReport(
            overall_score=sum(results) / len(results),
            detailed_scores=results,
            suggestions=self._generate_improvement_suggestions(results)
        )
    
    async def auto_retry_with_adjustment(self, agent, requirements, quality_report):
        """基于质量报告自动重试"""
        
        if quality_report.overall_score < 0.7:
            # 调整参数
            adjusted_requirements = self._adjust_requirements(requirements, quality_report)
            return await agent.generate_article(adjusted_requirements)
        
        return None
```

## 实施路径

### Phase 1: 核心框架 (2-3周)

#### Week 1: 基础架构
```bash
# 1. 创建V2目录结构
mkdir -p backend/agent_v2/{core,agents,tools,infrastructure,config}

# 2. 实现核心数据模型
touch backend/agent_v2/core/models.py

# 3. 实现Planning Agent基础框架
touch backend/agent_v2/core/planning_agent.py

# 4. 实现State Manager
touch backend/agent_v2/core/state_manager.py
```

#### Week 2: 工作流引擎
```bash
# 1. 实现交互式工作流引擎
touch backend/agent_v2/core/workflow_engine.py

# 2. 实现Agent Registry
touch backend/agent_v2/core/agent_registry.py

# 3. 基础测试框架
mkdir backend/agent_v2/tests
```

#### Week 3: Article Agent
```bash
# 1. 实现Article Agent
touch backend/agent_v2/agents/article_agent.py

# 2. 实现Research Agent
touch backend/agent_v2/agents/research_agent.py

# 3. 集成测试
python -m pytest backend/agent_v2/tests/
```

### Phase 2: 功能完善 (2-3周)

#### Week 4-5: 工具生态
- 实现核心工具适配
- 集成现有沙盒系统
- 添加质量监控

#### Week 6: API集成
- 实现V2 API接口
- 添加兼容层
- 性能优化

### Phase 3: 部署上线 (1-2周)

#### Week 7-8: 生产准备
- 完善监控日志
- 压力测试
- 生产部署

## 关键实现细节

### 1. 用户交互接口

```python
class UserInteractionManager:
    """用户交互管理器"""
    
    async def request_confirmation(self, 
                                 step_name: str, 
                                 result: Any, 
                                 options: List[str]) -> UserResponse:
        """请求用户确认"""
        
        confirmation_data = {
            "type": "confirmation_request",
            "step": step_name,
            "result": self._format_result_for_user(result),
            "options": options,
            "timestamp": time.time()
        }
        
        # 通过WebSocket或HTTP长轮询发送给前端
        await self._send_to_frontend(confirmation_data)
        
        # 等待用户响应
        user_response = await self._wait_for_user_response(timeout=300)  # 5分钟超时
        
        return user_response
    
    async def handle_interruption(self, interruption: UserInterruption):
        """处理用户中断"""
        
        current_workflow = self._get_current_workflow()
        if current_workflow.can_interrupt:
            await current_workflow.pause()
            await self._process_user_adjustment(interruption)
            await current_workflow.resume()
```

### 2. 状态持久化

```python
class StatePersistence:
    """状态持久化管理"""
    
    def __init__(self, storage_backend="redis"):
        self.storage = self._init_storage(storage_backend)
    
    async def save_checkpoint(self, state: UnifiedState) -> str:
        """保存状态检查点"""
        
        checkpoint_data = {
            "state": state.to_dict(),
            "timestamp": time.time(),
            "version": "1.0"
        }
        
        checkpoint_id = f"checkpoint_{state.session_id}_{time.time()}"
        await self.storage.save(checkpoint_id, checkpoint_data)
        
        return checkpoint_id
    
    async def restore_checkpoint(self, checkpoint_id: str) -> UnifiedState:
        """恢复状态检查点"""
        
        checkpoint_data = await self.storage.load(checkpoint_id)
        return UnifiedState.from_dict(checkpoint_data["state"])
```

### 3. 错误处理和恢复

```python
class ErrorRecoveryManager:
    """错误恢复管理器"""
    
    async def handle_agent_error(self, error: Exception, state: UnifiedState):
        """处理Agent执行错误"""
        
        error_info = {
            "type": type(error).__name__,
            "message": str(error),
            "step": state.current_step,
            "timestamp": time.time()
        }
        
        # 记录错误
        state.add_error("agent_execution", error_info)
        
        # 尝试恢复策略
        recovery_strategies = [
            self._retry_with_different_parameters,
            self._fallback_to_simpler_approach,
            self._request_user_intervention
        ]
        
        for strategy in recovery_strategies:
            try:
                result = await strategy(error, state)
                if result.success:
                    return result
            except Exception:
                continue
        
        # 所有策略都失败，返回错误
        return ExecutionResult(success=False, error=error_info)
```

## API接口设计

### RESTful API

```python
# V2 API路由
@app.post("/api/v2/tasks/start")
async def start_interactive_task(request: TaskRequest):
    """启动交互式任务"""
    
    planning_agent = get_planning_agent()
    
    # 分析意图并生成计划
    plan = await planning_agent.analyze_user_intent(request.description)
    
    # 创建会话状态
    state = UnifiedState(
        session_id=str(uuid.uuid4()),
        user_id=request.user_id,
        current_task=request.description
    )
    
    return {
        "session_id": state.session_id,
        "plan": plan,
        "status": "waiting_confirmation"
    }

@app.post("/api/v2/tasks/{session_id}/confirm")
async def confirm_plan(session_id: str, confirmation: PlanConfirmation):
    """确认执行计划"""
    
    state = await load_session_state(session_id)
    workflow_engine = get_workflow_engine()
    
    # 开始执行工作流
    execution_task = asyncio.create_task(
        workflow_engine.execute_interactive_workflow(state, confirmation.plan)
    )
    
    return {"status": "execution_started"}

@app.get("/api/v2/tasks/{session_id}/status")
async def get_task_status(session_id: str):
    """获取任务状态"""
    
    state = await load_session_state(session_id)
    
    return {
        "session_id": session_id,
        "status": state.status,
        "current_step": state.current_step,
        "completed_steps": state.completed_steps,
        "pending_confirmations": state.pending_confirmations
    }
```

### WebSocket支持

```python
@app.websocket("/api/v2/tasks/{session_id}/ws")
async def websocket_endpoint(websocket: WebSocket, session_id: str):
    """WebSocket连接用于实时交互"""
    
    await websocket.accept()
    
    try:
        while True:
            # 监听用户消息
            message = await websocket.receive_json()
            
            if message["type"] == "user_response":
                await handle_user_response(session_id, message["data"])
            
            elif message["type"] == "interruption":
                await handle_user_interruption(session_id, message["data"])
            
            # 发送状态更新
            state = await load_session_state(session_id)
            await websocket.send_json({
                "type": "state_update",
                "data": {
                    "status": state.status,
                    "current_step": state.current_step,
                    "progress": len(state.completed_steps) / len(state.planned_steps)
                }
            })
            
    except WebSocketDisconnect:
        # 处理连接断开
        await handle_websocket_disconnect(session_id)
```

## 监控和质量保障

### 执行监控

```python
class ExecutionMonitor:
    """执行监控器"""
    
    def __init__(self):
        self.metrics_collector = MetricsCollector()
        self.quality_monitor = QualityMonitor()
    
    async def monitor_agent_execution(self, agent_name: str, execution_func):
        """监控Agent执行"""
        
        start_time = time.time()
        
        try:
            result = await execution_func()
            
            # 记录成功指标
            execution_time = time.time() - start_time
            self.metrics_collector.record_success(agent_name, execution_time)
            
            # 质量检查
            if hasattr(result, 'content'):
                quality_score = await self.quality_monitor.evaluate(result.content)
                self.metrics_collector.record_quality(agent_name, quality_score)
            
            return result
            
        except Exception as e:
            # 记录失败指标
            execution_time = time.time() - start_time
            self.metrics_collector.record_failure(agent_name, str(e), execution_time)
            raise
```

### 用户体验监控

```python
class UserExperienceMonitor:
    """用户体验监控"""
    
    async def track_user_interaction(self, session_id: str, interaction_type: str, data: Dict):
        """跟踪用户交互"""
        
        interaction_record = {
            "session_id": session_id,
            "type": interaction_type,
            "data": data,
            "timestamp": time.time()
        }
        
        # 分析用户行为模式
        await self.analyze_user_patterns(interaction_record)
        
        # 记录到数据库
        await self.store_interaction(interaction_record)
    
    async def generate_ux_insights(self, time_period: str = "7d"):
        """生成用户体验洞察"""
        
        interactions = await self.get_interactions(time_period)
        
        insights = {
            "avg_task_completion_time": self._calculate_avg_completion_time(interactions),
            "user_satisfaction_score": self._calculate_satisfaction_score(interactions),
            "common_interruption_points": self._identify_interruption_patterns(interactions),
            "quality_feedback_trends": self._analyze_quality_feedback(interactions)
        }
        
        return insights
```

## 总结

MediaAgent V2的实用集成方案具有以下关键特点：

### 核心优势
1. **交互式设计**: 用户可全程参与和调整任务执行
2. **智能协调**: Planning Agent提供智能的任务规划和调度
3. **状态恢复**: 支持中断恢复和检查点机制
4. **质量保障**: 内置多层质量检查和自动优化
5. **平滑迁移**: 与现有系统并行运行，逐步切换

### 实施优势
- **渐进式**: 分阶段实施，风险可控
- **兼容性**: 保持与现有系统的兼容
- **可测试**: 每个阶段都可独立测试验证
- **可扩展**: 模块化设计支持未来功能扩展

该方案为MediaAgent系统提供了一个现代化、用户友好且技术先进的架构升级路径，能够显著提升用户体验和系统能力。