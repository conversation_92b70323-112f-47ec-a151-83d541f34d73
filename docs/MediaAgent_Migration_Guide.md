# MediaAgent V2 迁移指南

## 概述

本指南将帮助您从MediaAgent V1平滑迁移到MediaAgent V2系统。V2系统采用了全新的统一Agent架构，提供了更好的性能、可扩展性和用户体验。

## 迁移策略

### 1. 渐进式迁移（推荐）

采用渐进式迁移可以最大程度降低风险，确保业务连续性：

```
阶段1: 部署V2系统（并行运行）
阶段2: 新功能使用V2，旧功能保持V1
阶段3: 逐步迁移现有功能到V2
阶段4: 完全切换到V2，下线V1系统
```

### 2. 立即切换

适用于测试环境或对停机时间容忍度高的场景：

```
1. 数据备份
2. 部署V2系统
3. 数据迁移
4. 切换流量
5. 验证功能
```

## 系统差异对比

### 架构差异

| 特性 | V1系统 | V2系统 |
|------|--------|--------|
| Agent架构 | 分散式 | 统一架构 |
| 状态管理 | 分离的状态 | 统一状态(UnifiedState) |
| 工作流 | 固定流程 | 交互式工作流 |
| 用户交互 | 有限支持 | 全程交互支持 |
| 质量控制 | 基础检查 | 多层质量保障 |
| 可扩展性 | 模块化 | 高度模块化 |

### API接口差异

#### V1 API格式
```json
{
  "workflow_id": "string",
  "thread_id": "string", 
  "topic": "string",
  "requirements": "string",
  "keywords": ["string"],
  "stream": true
}
```

#### V2 API格式
```json
{
  "session_id": "string",
  "task_type": "article_generation",
  "topic": "string",
  "requirements": "string", 
  "keywords": ["string"],
  "target_length": 3500,
  "language": "zh-CN",
  "tone": "professional",
  "user_id": "string"
}
```

## 迁移步骤详解

### 阶段1: 环境准备

#### 1.1 安装V2系统依赖

```bash
# 安装新的Python依赖
pip install -r backend/agent_v2/requirements.txt

# 安装Redis（用于状态管理）
# Ubuntu/Debian
sudo apt-get install redis-server

# macOS
brew install redis

# 启动Redis服务
redis-server
```

#### 1.2 配置文件准备

创建V2系统配置文件：

```python
# backend/agent_v2/config/config.py
V2_CONFIG = {
    "engine": {
        "max_concurrent_sessions": 10,
        "default_timeout": 3600
    },
    "state_manager": {
        "storage_backend": "redis",
        "redis": {
            "host": "localhost",
            "port": 6379,
            "db": 0
        }
    },
    "planning_agent": {
        "max_planning_depth": 5,
        "enable_user_interaction": True
    },
    "workflow_engine": {
        "max_concurrent_workflows": 10,
        "checkpoint_interval": 300
    }
}
```

#### 1.3 数据库迁移准备

```sql
-- 创建V2相关表
CREATE TABLE IF NOT EXISTS agent_v2_sessions (
    session_id VARCHAR(255) PRIMARY KEY,
    user_id VARCHAR(255),
    task_type VARCHAR(100),
    status VARCHAR(50),
    created_at TIMESTAMP,
    updated_at TIMESTAMP,
    state_data TEXT
);

CREATE TABLE IF NOT EXISTS agent_v2_user_contexts (
    user_id VARCHAR(255) PRIMARY KEY,
    preferences JSON,
    history JSON,
    updated_at TIMESTAMP
);
```

### 阶段2: 系统部署

#### 2.1 部署V2核心组件

```python
# backend/agent_v2/deploy.py
import asyncio
from .core.engine import MediaAgentV2Engine
from .infrastructure.compatibility_layer import CompatibilityLayer

async def deploy_v2_system():
    """部署V2系统"""
    
    # 初始化核心引擎
    engine = MediaAgentV2Engine(V2_CONFIG)
    await engine.initialize()
    
    # 初始化兼容性层
    compatibility = CompatibilityLayer({
        "enable_v1_compatibility": True,
        "default_system": "v2",
        "auto_migration": True
    })
    await compatibility.initialize()
    
    print("MediaAgent V2 deployed successfully")
    return engine, compatibility

if __name__ == "__main__":
    asyncio.run(deploy_v2_system())
```

#### 2.2 配置负载均衡

```nginx
# nginx配置示例
upstream mediaagent_v1 {
    server localhost:8001;
}

upstream mediaagent_v2 {
    server localhost:8002;
}

server {
    listen 80;
    server_name your-domain.com;
    
    # V2系统路由
    location /api/v2/ {
        proxy_pass http://mediaagent_v2;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
    
    # V1系统路由（兼容性）
    location /api/v1/ {
        proxy_pass http://mediaagent_v1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
    
    # 默认路由到兼容性层
    location /api/ {
        proxy_pass http://mediaagent_v2/api/compatibility/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

### 阶段3: 数据迁移

#### 3.1 迁移脚本

```python
# scripts/migrate_v1_to_v2.py
import asyncio
import json
from backend.agent_v2.infrastructure.compatibility_layer import CompatibilityLayer

async def migrate_data():
    """迁移V1数据到V2"""
    
    compatibility = CompatibilityLayer()
    await compatibility.initialize()
    
    # 读取V1数据
    with open('v1_data_export.json', 'r') as f:
        v1_data = json.load(f)
    
    print(f"Found {len(v1_data.get('workflows', []))} workflows to migrate")
    
    # 执行迁移
    migrated_data = await compatibility.migrate_v1_data(v1_data)
    
    # 保存迁移结果
    with open('v2_migrated_data.json', 'w') as f:
        json.dump(migrated_data, f, indent=2)
    
    print(f"Migration completed:")
    print(f"- Sessions: {len(migrated_data['sessions'])}")
    print(f"- User contexts: {len(migrated_data['user_contexts'])}")
    
    return migrated_data

if __name__ == "__main__":
    asyncio.run(migrate_data())
```

#### 3.2 数据验证脚本

```python
# scripts/validate_migration.py
import asyncio
import json

async def validate_migration():
    """验证迁移结果"""
    
    with open('v1_data_export.json', 'r') as f:
        v1_data = json.load(f)
    
    with open('v2_migrated_data.json', 'r') as f:
        v2_data = json.load(f)
    
    # 验证数据完整性
    v1_workflow_count = len(v1_data.get('workflows', []))
    v2_session_count = len(v2_data.get('sessions', []))
    
    print(f"V1 workflows: {v1_workflow_count}")
    print(f"V2 sessions: {v2_session_count}")
    
    if v1_workflow_count == v2_session_count:
        print("✅ Session count matches")
    else:
        print("❌ Session count mismatch")
    
    # 验证关键字段
    for i, (v1_workflow, v2_session) in enumerate(zip(
        v1_data.get('workflows', []), 
        v2_data.get('sessions', [])
    )):
        if v1_workflow.get('workflow_id') != v2_session.get('session_id'):
            print(f"❌ ID mismatch at index {i}")
            break
    else:
        print("✅ All IDs match")
    
    print("Migration validation completed")

if __name__ == "__main__":
    asyncio.run(validate_migration())
```

### 阶段4: 功能测试

#### 4.1 自动化测试套件

```python
# tests/test_migration.py
import pytest
import asyncio
from backend.agent_v2.core.engine import MediaAgentV2Engine
from backend.agent_v2.infrastructure.compatibility_layer import CompatibilityLayer

class TestMigration:
    
    @pytest.fixture
    async def v2_engine(self):
        engine = MediaAgentV2Engine()
        await engine.initialize()
        yield engine
        await engine.shutdown()
    
    @pytest.fixture
    async def compatibility_layer(self):
        layer = CompatibilityLayer()
        await layer.initialize()
        yield layer
        await layer.shutdown()
    
    @pytest.mark.asyncio
    async def test_v1_request_compatibility(self, compatibility_layer):
        """测试V1请求兼容性"""
        
        v1_request = {
            "workflow_id": "test-workflow-123",
            "topic": "AI技术发展",
            "requirements": "需要深入分析AI技术的发展趋势",
            "keywords": ["人工智能", "机器学习", "深度学习"],
            "stream": True
        }
        
        result = await compatibility_layer.handle_request(v1_request)
        
        assert result["success"] == True
        assert "content" in result
        assert "compatibility_info" in result
    
    @pytest.mark.asyncio
    async def test_v2_native_request(self, v2_engine):
        """测试V2原生请求"""
        
        v2_request = {
            "topic": "AI技术发展", 
            "requirements": "需要深入分析AI技术的发展趋势",
            "keywords": ["人工智能", "机器学习", "深度学习"],
            "target_length": 3500,
            "language": "zh-CN",
            "tone": "professional"
        }
        
        result = await v2_engine.process_request(v2_request)
        
        assert result.success == True
        assert result.content is not None
    
    @pytest.mark.asyncio
    async def test_data_migration(self, compatibility_layer):
        """测试数据迁移"""
        
        v1_data = {
            "workflows": [
                {
                    "workflow_id": "test-123",
                    "user_id": "user-456", 
                    "topic": "测试主题",
                    "status": "completed",
                    "result": {"content": "测试内容"}
                }
            ]
        }
        
        migrated = await compatibility_layer.migrate_v1_data(v1_data)
        
        assert len(migrated["sessions"]) == 1
        assert migrated["sessions"][0]["session_id"] == "test-123"
        assert migrated["sessions"][0]["user_id"] == "user-456"
```

#### 4.2 性能测试

```python
# tests/test_performance.py
import asyncio
import time
import statistics
from backend.agent_v2.core.engine import MediaAgentV2Engine

async def performance_test():
    """性能测试"""
    
    engine = MediaAgentV2Engine()
    await engine.initialize()
    
    # 测试参数
    test_requests = [
        {
            "topic": f"测试主题{i}",
            "requirements": "简单的文章生成测试",
            "keywords": ["测试", "性能"],
            "target_length": 1000
        }
        for i in range(10)
    ]
    
    # 执行性能测试
    response_times = []
    
    for request in test_requests:
        start_time = time.time()
        
        result = await engine.process_request(request)
        
        end_time = time.time()
        response_time = end_time - start_time
        response_times.append(response_time)
        
        print(f"Request processed in {response_time:.2f}s")
    
    # 统计结果
    avg_time = statistics.mean(response_times)
    median_time = statistics.median(response_times)
    max_time = max(response_times)
    min_time = min(response_times)
    
    print(f"\nPerformance Summary:")
    print(f"Average response time: {avg_time:.2f}s")
    print(f"Median response time: {median_time:.2f}s")
    print(f"Max response time: {max_time:.2f}s")
    print(f"Min response time: {min_time:.2f}s")
    
    await engine.shutdown()

if __name__ == "__main__":
    asyncio.run(performance_test())
```

### 阶段5: 逐步切换

#### 5.1 流量切换策略

```python
# backend/routing_controller.py
import random
from typing import Dict, Any

class TrafficRouter:
    """流量路由控制器"""
    
    def __init__(self):
        self.v2_traffic_percentage = 0  # 初始0%流量到V2
        self.user_whitelist = set()     # V2用户白名单
        self.feature_flags = {
            "enable_v2_for_new_users": False,
            "enable_v2_for_complex_tasks": False
        }
    
    def should_use_v2(self, request: Dict[str, Any]) -> bool:
        """判断是否使用V2系统"""
        
        user_id = request.get("user_id")
        
        # 白名单用户优先使用V2
        if user_id in self.user_whitelist:
            return True
        
        # 新用户使用V2
        if (self.feature_flags["enable_v2_for_new_users"] and 
            self._is_new_user(user_id)):
            return True
        
        # 复杂任务使用V2
        if (self.feature_flags["enable_v2_for_complex_tasks"] and 
            self._is_complex_task(request)):
            return True
        
        # 按比例分流
        return random.random() < (self.v2_traffic_percentage / 100)
    
    def _is_new_user(self, user_id: str) -> bool:
        """判断是否为新用户"""
        # 实现新用户判断逻辑
        return False
    
    def _is_complex_task(self, request: Dict[str, Any]) -> bool:
        """判断是否为复杂任务"""
        # 基于请求特征判断任务复杂度
        target_length = request.get("target_length", 0)
        keyword_count = len(request.get("keywords", []))
        
        return target_length > 5000 or keyword_count > 10
    
    def set_v2_traffic_percentage(self, percentage: int):
        """设置V2流量比例"""
        self.v2_traffic_percentage = max(0, min(100, percentage))
    
    def add_to_whitelist(self, user_id: str):
        """添加到V2白名单"""
        self.user_whitelist.add(user_id)
    
    def enable_feature(self, feature_name: str, enabled: bool = True):
        """启用/禁用功能特性"""
        if feature_name in self.feature_flags:
            self.feature_flags[feature_name] = enabled
```

#### 5.2 切换时间表

```python
# scripts/migration_schedule.py
import asyncio
import schedule
import time
from backend.routing_controller import TrafficRouter

router = TrafficRouter()

def phase_1():
    """阶段1: 5%流量到V2"""
    router.set_v2_traffic_percentage(5)
    router.enable_feature("enable_v2_for_new_users", True)
    print("Phase 1: 5% traffic to V2, new users enabled")

def phase_2():
    """阶段2: 25%流量到V2"""
    router.set_v2_traffic_percentage(25)
    router.enable_feature("enable_v2_for_complex_tasks", True)
    print("Phase 2: 25% traffic to V2, complex tasks enabled")

def phase_3():
    """阶段3: 50%流量到V2"""
    router.set_v2_traffic_percentage(50)
    print("Phase 3: 50% traffic to V2")

def phase_4():
    """阶段4: 80%流量到V2"""
    router.set_v2_traffic_percentage(80)
    print("Phase 4: 80% traffic to V2")

def phase_5():
    """阶段5: 100%流量到V2"""
    router.set_v2_traffic_percentage(100)
    print("Phase 5: 100% traffic to V2 - Migration Complete!")

# 调度切换时间表
schedule.every(1).days.do(phase_1)      # 第1天
schedule.every(3).days.do(phase_2)      # 第3天
schedule.every(7).days.do(phase_3)      # 第7天  
schedule.every(14).days.do(phase_4)     # 第14天
schedule.every(21).days.do(phase_5)     # 第21天

def run_migration_schedule():
    """运行迁移调度"""
    while True:
        schedule.run_pending()
        time.sleep(3600)  # 每小时检查一次

if __name__ == "__main__":
    run_migration_schedule()
```

## 监控和回滚

### 1. 监控指标

```python
# monitoring/metrics.py
import time
from dataclasses import dataclass
from typing import Dict, List

@dataclass
class MigrationMetrics:
    """迁移监控指标"""
    
    # 系统性能指标
    v1_response_time: float = 0.0
    v2_response_time: float = 0.0
    v1_success_rate: float = 0.0
    v2_success_rate: float = 0.0
    
    # 用户体验指标
    user_satisfaction_v1: float = 0.0
    user_satisfaction_v2: float = 0.0
    
    # 错误指标
    v1_error_count: int = 0
    v2_error_count: int = 0
    migration_errors: int = 0
    
    # 业务指标
    total_requests: int = 0
    v1_requests: int = 0
    v2_requests: int = 0

class MigrationMonitor:
    """迁移监控器"""
    
    def __init__(self):
        self.metrics = MigrationMetrics()
        self.alerts = []
    
    def record_request(self, system: str, response_time: float, success: bool):
        """记录请求指标"""
        
        self.metrics.total_requests += 1
        
        if system == "v1":
            self.metrics.v1_requests += 1
            self.metrics.v1_response_time = self._update_avg(
                self.metrics.v1_response_time, response_time, self.metrics.v1_requests
            )
            if success:
                self.metrics.v1_success_rate = self._update_success_rate(
                    self.metrics.v1_success_rate, True, self.metrics.v1_requests
                )
            else:
                self.metrics.v1_error_count += 1
        
        elif system == "v2":
            self.metrics.v2_requests += 1
            self.metrics.v2_response_time = self._update_avg(
                self.metrics.v2_response_time, response_time, self.metrics.v2_requests
            )
            if success:
                self.metrics.v2_success_rate = self._update_success_rate(
                    self.metrics.v2_success_rate, True, self.metrics.v2_requests
                )
            else:
                self.metrics.v2_error_count += 1
        
        # 检查告警条件
        self._check_alerts()
    
    def _update_avg(self, current_avg: float, new_value: float, count: int) -> float:
        """更新平均值"""
        return (current_avg * (count - 1) + new_value) / count
    
    def _update_success_rate(self, current_rate: float, success: bool, count: int) -> float:
        """更新成功率"""
        return (current_rate * (count - 1) + (1.0 if success else 0.0)) / count
    
    def _check_alerts(self):
        """检查告警条件"""
        
        # V2性能显著下降
        if (self.metrics.v2_response_time > self.metrics.v1_response_time * 1.5 and
            self.metrics.v2_requests > 10):
            self.alerts.append({
                "type": "performance_degradation",
                "message": "V2 response time significantly higher than V1",
                "timestamp": time.time()
            })
        
        # V2错误率过高
        if (self.metrics.v2_requests > 0 and 
            self.metrics.v2_success_rate < 0.95):
            self.alerts.append({
                "type": "high_error_rate",
                "message": f"V2 success rate is {self.metrics.v2_success_rate:.2%}",
                "timestamp": time.time()
            })
    
    def get_dashboard_data(self) -> Dict:
        """获取监控面板数据"""
        
        return {
            "metrics": self.metrics,
            "alerts": self.alerts[-10:],  # 最近10个告警
            "recommendations": self._generate_recommendations()
        }
    
    def _generate_recommendations(self) -> List[str]:
        """生成建议"""
        
        recommendations = []
        
        if self.metrics.v2_response_time > self.metrics.v1_response_time * 1.2:
            recommendations.append("考虑优化V2系统性能或降低流量比例")
        
        if self.metrics.v2_success_rate < self.metrics.v1_success_rate:
            recommendations.append("调查V2系统错误原因")
        
        if len(self.alerts) > 5:
            recommendations.append("考虑回滚到V1系统")
        
        return recommendations
```

### 2. 自动回滚机制

```python
# rollback/auto_rollback.py
import asyncio
import logging
from typing import Dict, Any
from monitoring.metrics import MigrationMonitor

class AutoRollbackManager:
    """自动回滚管理器"""
    
    def __init__(self, monitor: MigrationMonitor):
        self.monitor = monitor
        self.rollback_triggers = {
            "high_error_rate": 0.05,      # 错误率>5%触发回滚
            "response_time_ratio": 2.0,    # 响应时间>2倍触发回滚
            "consecutive_failures": 10     # 连续失败>10次触发回滚
        }
        self.rollback_executed = False
    
    async def check_rollback_conditions(self) -> bool:
        """检查回滚条件"""
        
        if self.rollback_executed:
            return False
        
        metrics = self.monitor.metrics
        
        # 检查错误率
        if metrics.v2_requests > 0:
            error_rate = metrics.v2_error_count / metrics.v2_requests
            if error_rate > self.rollback_triggers["high_error_rate"]:
                await self._execute_rollback("High error rate", error_rate)
                return True
        
        # 检查响应时间
        if (metrics.v1_response_time > 0 and metrics.v2_response_time > 0):
            time_ratio = metrics.v2_response_time / metrics.v1_response_time
            if time_ratio > self.rollback_triggers["response_time_ratio"]:
                await self._execute_rollback("Response time degradation", time_ratio)
                return True
        
        return False
    
    async def _execute_rollback(self, reason: str, metric_value: float):
        """执行回滚"""
        
        logging.critical(f"Executing automatic rollback: {reason} (value: {metric_value})")
        
        try:
            # 1. 立即停止V2流量
            from backend.routing_controller import TrafficRouter
            router = TrafficRouter()
            router.set_v2_traffic_percentage(0)
            
            # 2. 通知管理员
            await self._send_rollback_notification(reason, metric_value)
            
            # 3. 记录回滚事件
            self._log_rollback_event(reason, metric_value)
            
            self.rollback_executed = True
            
            logging.info("Automatic rollback completed successfully")
            
        except Exception as e:
            logging.error(f"Rollback execution failed: {e}")
    
    async def _send_rollback_notification(self, reason: str, metric_value: float):
        """发送回滚通知"""
        
        notification = {
            "type": "automatic_rollback",
            "reason": reason,
            "metric_value": metric_value,
            "timestamp": time.time(),
            "action_required": "Investigate V2 system issues"
        }
        
        # 这里可以集成邮件、Slack、钉钉等通知系统
        logging.info(f"Rollback notification: {notification}")
    
    def _log_rollback_event(self, reason: str, metric_value: float):
        """记录回滚事件"""
        
        with open("rollback_log.txt", "a") as f:
            f.write(f"{time.time()},{reason},{metric_value}\n")
```

## 最佳实践

### 1. 迁移前准备

- ✅ 完整备份V1系统数据和配置
- ✅ 在测试环境完整验证迁移流程
- ✅ 准备详细的回滚计划
- ✅ 培训团队成员熟悉V2系统
- ✅ 建立完善的监控和告警机制

### 2. 迁移过程中

- ✅ 采用渐进式迁移策略
- ✅ 持续监控系统性能和错误率
- ✅ 及时响应用户反馈
- ✅ 保持V1和V2系统同步更新
- ✅ 定期评估迁移进度和质量

### 3. 迁移完成后

- ✅ 保持V1系统一段时间作为备份
- ✅ 优化V2系统性能
- ✅ 收集用户反馈并持续改进
- ✅ 更新文档和培训材料
- ✅ 制定长期维护计划

## 常见问题和解决方案

### Q1: V2系统性能不如V1怎么办？

**解决方案：**
1. 检查系统配置是否优化
2. 分析性能瓶颈点
3. 考虑降低V2流量比例
4. 优化数据库查询和缓存策略

### Q2: 用户反馈V2功能不符合预期？

**解决方案：**
1. 收集详细的用户反馈
2. 对比V1和V2的功能差异
3. 调整V2系统配置
4. 必要时临时回滚到V1

### Q3: 数据迁移后出现数据不一致？

**解决方案：**
1. 使用数据验证脚本检查
2. 比对关键业务数据
3. 修复数据不一致问题
4. 重新运行迁移流程

### Q4: V2系统出现V1没有的错误？

**解决方案：**
1. 检查错误日志和堆栈跟踪
2. 分析错误原因和触发条件
3. 修复代码或配置问题
4. 增加相应的错误处理

## 技术支持

如需技术支持，请联系：

- 📧 Email: <EMAIL>
- 💬 Slack: #mediaagent-v2-migration
- 📝 Issues: https://github.com/your-org/mediaagent/issues

## 更新日志

- **v2.0.0** - 初始版本发布
- **v2.0.1** - 修复兼容性问题
- **v2.0.2** - 性能优化和错误修复

---

*本指南将随着系统更新持续维护，请关注最新版本。*