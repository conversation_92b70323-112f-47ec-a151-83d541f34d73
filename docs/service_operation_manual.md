# MediaAgent 服务操作手册

## 1. 服务停止操作

### 1.1 停止所有前端和后端服务
```bash
# 1. 停止所有MediaAgent相关进程
sudo pkill -f "uvicorn"
sudo pkill -f "next"
sudo pkill -f "npm"
sudo pkill -f "node.*next"
sudo pkill -f "python.*api.py"

# 2. 检查并强制杀死残留进程
ps aux | grep -E "(uvicorn|next|api.py|npm)" | grep -v grep
# 如果还有进程，记录PID并强制杀死
# sudo kill -9 [PID]

# 3. 释放端口（如果被占用）
sudo netstat -tlnp | grep -E ":3000|:8000"
# 如果端口被占用，找到PID并杀死进程
# sudo kill -9 [PID]

# 4. 清理前端缓存和临时文件
cd /root/workspace/MediaAgent/frontend
rm -rf .next
rm -rf node_modules/.cache
rm -rf *.log

# 5. 清理后端日志和缓存
cd ../backend
rm -rf logs/*
rm -rf __pycache__
find . -name "*.pyc" -delete
```

### 1.2 验证服务已完全停止
```bash
# 检查进程
ps aux | grep -E "(uvicorn|next|api.py|npm)" | grep -v grep

# 检查端口
sudo netstat -tlnp | grep -E ":3000|:8000"

# 应该没有任何输出，表示服务已完全停止
```

## 2. 服务启动操作

### 2.1 环境检查和准备
```bash
# 1. 检查Redis和RabbitMQ服务状态
sudo systemctl status redis
sudo systemctl status rabbitmq-server

# 2. 如果服务未运行，启动它们
sudo systemctl start redis
sudo systemctl start rabbitmq-server

# 3. 验证服务连接
redis-cli ping  # 应返回 PONG
sudo rabbitmqctl status  # 应显示运行状态

# 4. 激活Python环境
cd MediaAgent
source activate_py311.sh  # 或者手动激活虚拟环境
```

### 2.2 启动后端服务 (uvicorn)
```bash
# 1. 进入后端目录
cd MediaAgent/backend

# 2. 检查环境变量配置
ls -la .env
cat .env  # 确认配置正确

# 3. 使用uvicorn启动后端
uvicorn api:app --host 0.0.0.0 --port 8000 --workers 2 --reload

# 或者使用单进程模式（开发调试）
# uvicorn api:app --host 0.0.0.0 --port 8000 --reload

# 4. 验证后端启动
# 打开新终端窗口
curl http://localhost:8000/api/health
# 应返回: {"status":"ok","timestamp":"...","instance_id":"single"}
```

### 2.3 启动前端服务 (npm run dev)
```bash
# 1. 进入前端目录
cd MediaAgent/frontend

# 2. 检查环境配置
ls -la .env.local
cat .env.local  # 确认配置正确

# 3. 确保依赖已安装
npm install

# 4. 使用npm启动前端
npm run dev

# 5. 验证前端启动
# 打开浏览器访问: http://localhost:3000
# 或使用curl测试: curl http://localhost:3000
```

## 3. 服务状态检查

### 3.1 完整系统状态检查脚本
```bash
#!/bin/bash
echo "=== MediaAgent 系统状态检查 ==="

echo "1. 检查进程状态..."
echo "后端进程:"
ps aux | grep -E "uvicorn.*api:app" | grep -v grep || echo "  ❌ 后端未运行"

echo "前端进程:"
ps aux | grep -E "npm.*dev|next.*dev" | grep -v grep || echo "  ❌ 前端未运行"

echo ""
echo "2. 检查端口监听..."
echo "端口8000 (后端):"
sudo netstat -tlnp | grep ":8000" || echo "  ❌ 端口8000未监听"

echo "端口3000 (前端):"
sudo netstat -tlnp | grep ":3000" || echo "  ❌ 端口3000未监听"

echo ""
echo "3. 检查依赖服务..."
echo "Redis服务:"
redis-cli ping 2>/dev/null || echo "  ❌ Redis连接失败"

echo "RabbitMQ服务:"
sudo rabbitmqctl status >/dev/null 2>&1 && echo "  ✅ RabbitMQ正常" || echo "  ❌ RabbitMQ状态异常"

echo ""
echo "4. 检查API健康状态..."
curl -s http://localhost:8000/api/health >/dev/null 2>&1 && echo "  ✅ 后端API正常" || echo "  ❌ 后端API无响应"

curl -s http://localhost:3000 >/dev/null 2>&1 && echo "  ✅ 前端服务正常" || echo "  ❌ 前端服务无响应"

echo ""
echo "=== 检查完成 ==="
```

### 3.2 保存状态检查脚本
```bash
# 创建状态检查脚本
cat > MediaAgent/check_system_status.sh << 'EOF'
#!/bin/bash
echo "=== MediaAgent 系统状态检查 ==="

echo "1. 检查进程状态..."
echo "后端进程:"
ps aux | grep -E "uvicorn.*api:app" | grep -v grep || echo "  ❌ 后端未运行"

echo "前端进程:"
ps aux | grep -E "npm.*dev|next.*dev" | grep -v grep || echo "  ❌ 前端未运行"

echo ""
echo "2. 检查端口监听..."
echo "端口8000 (后端):"
sudo netstat -tlnp | grep ":8000" || echo "  ❌ 端口8000未监听"

echo "端口3000 (前端):"
sudo netstat -tlnp | grep ":3000" || echo "  ❌ 端口3000未监听"

echo ""
echo "3. 检查依赖服务..."
echo "Redis服务:"
redis-cli ping 2>/dev/null || echo "  ❌ Redis连接失败"

echo "RabbitMQ服务:"
sudo rabbitmqctl status >/dev/null 2>&1 && echo "  ✅ RabbitMQ正常" || echo "  ❌ RabbitMQ状态异常"

echo ""
echo "4. 检查API健康状态..."
curl -s http://localhost:8000/api/health >/dev/null 2>&1 && echo "  ✅ 后端API正常" || echo "  ❌ 后端API无响应"

curl -s http://localhost:3000 >/dev/null 2>&1 && echo "  ✅ 前端服务正常" || echo "  ❌ 前端服务无响应"

echo ""
echo "=== 检查完成 ==="
EOF

# 设置执行权限
chmod +x MediaAgent/check_system_status.sh
```

## 4. 常见问题排查

### 4.1 后端启动失败
```bash
# 检查端口占用
sudo netstat -tlnp | grep ":8000"
# 如果被占用，杀死占用进程

# 检查环境变量
cd MediaAgent/backend
cat .env
# 确保Redis、RabbitMQ等配置正确

# 检查Python环境
python --version  # 应该是3.11+
pip list | grep -E "(fastapi|uvicorn|redis|pika)"
```

### 4.2 前端启动失败  
```bash
# 检查Node.js版本
node --version  # 应该是18+
npm --version

# 重新安装依赖
cd MediaAgent/frontend
rm -rf node_modules package-lock.json
npm install

# 检查环境配置
cat .env.local
# 确保OPENROUTER_API_KEY等配置正确
```

### 4.3 依赖服务问题
```bash
# Redis问题
sudo systemctl restart redis
redis-cli ping

# RabbitMQ问题  
sudo systemctl restart rabbitmq-server
sudo rabbitmqctl status

# 如果是Docker部署的Redis/RabbitMQ
docker ps | grep -E "(redis|rabbitmq)"
docker restart mediaagent-redis mediaagent-rabbitmq
```

## 5. 开发模式vs生产模式

### 5.1 开发模式启动（推荐）
```bash
# 后端开发模式 - 支持热重载
uvicorn api:app --host 0.0.0.0 --port 8000 --reload

# 前端开发模式 - 支持热重载
npm run dev
```

### 5.2 生产模式启动
```bash
# 后端生产模式 - 多进程
uvicorn api:app --host 0.0.0.0 --port 8000 --workers 4

# 前端生产模式 - 构建后启动
npm run build
npm start
```

## 6. 日志查看

### 6.1 实时查看服务日志
```bash
# 后端日志（如果有日志文件）
tail -f MediaAgent/backend/logs/app.log

# 系统日志中的服务信息
sudo journalctl -f | grep -E "(uvicorn|redis|rabbitmq)"
```

### 6.2 调试模式启动
```bash
# 后端调试模式
cd MediaAgent/backend
export LOG_LEVEL=DEBUG
uvicorn api:app --host 0.0.0.0 --port 8000 --reload --log-level debug

# 前端调试模式（已默认包含详细日志）
cd MediaAgent/frontend
npm run dev
```

## 7. 快速启动脚本模板

### 7.1 创建启动脚本
```bash
# 创建快速启动脚本
cat > MediaAgent/quick_start.sh << 'EOF'
#!/bin/bash
set -e

echo "🚀 MediaAgent 快速启动脚本"

# 检查依赖服务
echo "1. 检查依赖服务..."
sudo systemctl start redis || echo "Redis启动失败"
sudo systemctl start rabbitmq-server || echo "RabbitMQ启动失败"

# 激活Python环境
echo "2. 激活Python环境..."
source activate_py311.sh 2>/dev/null || echo "请手动激活Python环境"

# 启动后端
echo "3. 启动后端服务..."
cd backend
uvicorn api:app --host 0.0.0.0 --port 8000 --workers 2 --reload &
BACKEND_PID=$!
echo "后端已启动，PID: $BACKEND_PID"

# 等待后端启动
sleep 5

# 启动前端
echo "4. 启动前端服务..."
cd ../frontend
npm run dev &
FRONTEND_PID=$!
echo "前端已启动，PID: $FRONTEND_PID"

echo ""
echo "✅ 服务启动完成！"
echo "后端地址: http://localhost:8000"
echo "前端地址: http://localhost:3000"
echo ""
echo "要停止服务，请运行:"
echo "kill $BACKEND_PID $FRONTEND_PID"
EOF

chmod +x MediaAgent/quick_start.sh
```

使用此操作手册，你可以完全控制MediaAgent系统的启动和停止，确保服务以正确的方式运行。 

# MediaAgent 服务运维操作手册

## 🚨 核心问题分析：前端调用工具失败

### ❌ 问题现象
- 前端创建agent run成功，状态显示"running"
- 但Redis中没有响应数据，前端无法看到工具被调用
- 数据库显示agent run处于"running"状态但实际未执行

### 🔍 根本原因研究

#### 1. **环境变量加载不一致问题**
**发现**：`.env_cusor`文件存在但系统未正确加载所有环境变量

```bash
# 环境变量检查结果
RABBITMQ_HOST: localhost (✅ 正确配置)
RABBITMQ_PORT: 5672 (✅ 正确配置)
REDIS_HOST: localhost (✅ 正确配置)
REDIS_PORT: 6379 (✅ 正确配置)
```

**影响**：RabbitMQ配置正确，问题不在环境变量层面

#### 2. **Dramatiq任务队列机制失效**

**架构分析**：
- **FastAPI服务** (端口8000) - 负责接收前端请求并创建agent run
- **Dramatiq Worker** - 负责实际处理agent任务的后台进程
- **RabbitMQ** (端口5672) - 消息队列broker
- **Redis** (端口6379) - 存储agent响应和状态

**工作流程**：
```
1. 前端请求 → 2. FastAPI创建agent_run → 3. 调用run_agent_background.send() 
   ↓
4. Dramatiq排队任务 → 5. Worker处理任务 → 6. 响应存储到Redis → 7. 前端获取响应
```

**问题定位**：第4-5步之间断裂

#### 3. **Dramatiq Worker状态分析**

**发现**：
- ✅ RabbitMQ正在运行 (端口5672监听正常)
- ✅ Dramatiq Worker进程已启动
- ❌ 队列中没有任务被处理

**Worker状态**：
```bash
# 正在运行的Dramatiq进程
root     2683305  0.9% python -m dramatiq run_agent_background
root     2683315 48.4% python -m dramatiq run_agent_background  
root     2683316 47.8% python -m dramatiq run_agent_background
```

#### 4. **任务排队失败分析**

**关键代码**：`MediaAgent/backend/agent/api.py`
```python
# agent run创建成功后的关键调用
run_agent_background.send(
    agent_run_id=agent_run_id, 
    thread_id=thread_id, 
    instance_id=instance_id,
    project_id=project_id,
    model_name=model_name,
    enable_thinking=body.enable_thinking, 
    reasoning_effort=body.reasoning_effort,
    stream=body.stream, 
    enable_context_manager=body.enable_context_manager
)
```

**问题推测**：`send()`调用未成功将任务加入RabbitMQ队列

### 🛠️ 解决方案

#### 方案一：服务重启解决（临时方案）
**观察**：用户反馈"重启后端是正常的"
**推测**：重启解决了Dramatiq broker连接问题

#### 方案二：根本性解决方案

##### 1. **Dramatiq连接状态检查机制**
创建连接健康检查：
```python
# 在run_agent_background.py中添加
async def check_broker_health():
    """检查RabbitMQ broker连接状态"""
    try:
        # 测试broker连接
        test_message = rabbitmq_broker.declare_queue("health_check")
        logger.info("Dramatiq broker连接正常")
        return True
    except Exception as e:
        logger.error(f"Dramatiq broker连接失败: {e}")
        return False
```

##### 2. **环境变量统一加载机制**
**发现**：不同模块加载环境变量的方式不一致

**标准化加载**：
```python
# 统一环境变量加载顺序
from dotenv import load_dotenv

# 1. 优先加载.env文件（如果存在）
if os.path.exists('.env'):
    load_dotenv('.env')

```

##### 3. **Agent工具正确加载验证**

**深度创作工具确认已正确集成**：
```python
# MediaAgent/backend/agent/run.py (第129-132行)
# 沙盒工作流和意图识别工具 - 提供隔离环境和增强安全性
thread_manager.add_tool(SandboxUnifiedWorkflowTool, project_id=project_id, thread_manager=thread_manager)
thread_manager.add_tool(SandboxConversationIntentAgent, project_id=project_id, thread_manager=thread_manager)
```

**工具功能验证**：
- ✅ `SandboxUnifiedWorkflowTool` - 文章/报告/故事生成工具
- ✅ `SandboxConversationIntentAgent` - 智能意图识别和工作流路由

##### 4. **任务队列监控和自动恢复**
```python
# 添加任务排队失败重试机制
@dramatiq.actor(max_retries=3, min_backoff=1000, max_backoff=5000)
async def run_agent_background_with_retry(...):
    """带重试机制的agent任务"""
    pass
```

### 🎯 下一步行动计划

#### 立即行动（优先级：高）
1. **诊断当前Dramatiq状态**
   - 检查worker日志中的错误信息
   - 验证RabbitMQ连接状态
   - 确认任务是否进入队列

2. **实施环境变量标准化**
   - 创建统一的环境变量加载函数
   - 确保所有模块使用相同的加载机制

3. **添加连接健康检查**
   - 在API启动时验证Dramatiq broker连接
   - 定期健康检查，自动重连

#### 中期优化（优先级：中）
1. **增强错误处理**
   - 任务排队失败时的错误报告
   - 自动重试机制
   - 优雅降级策略

2. **监控和告警系统**
   - 队列状态监控
   - Worker性能指标
   - 自动故障恢复

### 📋 故障排查清单

#### 前端工具调用失败时的检查步骤：
1. ✅ 检查FastAPI服务状态 (端口8000)
2. ✅ 检查RabbitMQ服务状态 (端口5672)
3. ✅ 检查Redis服务状态 (端口6379)
4. ⚠️ 检查Dramatiq Worker进程状态
5. ❌ 检查队列中是否有待处理任务
6. ❌ 检查worker日志中的错误信息
7. ❌ 验证broker连接状态

#### 快速恢复操作：
```bash
# 重启Dramatiq worker
pkill -f "dramatiq run_agent_background"
cd /root/workspace/MediaAgent/backend
PYTHONPATH=/root/workspace/MediaAgent/backend python -m dramatiq run_agent_background &

# 检查服务状态
netstat -tlnp | grep -E "(5672|6379|8000)"
ps aux | grep -E "(dramatiq|uvicorn)"
```

---

## 📚 技术架构说明

### Agent工具体系
MediaAgent已集成完整的深度创作工具链：
- **意图识别**: 自动识别用户的创作需求
- **工作流路由**: 智能路由到专业工作流（文章/报告/故事）
- **沙盒执行**: 隔离环境确保安全可靠
- **结果回传**: 通过Redis流式返回创作结果

### 环境要求
- Python 3.11+
- RabbitMQ (消息队列)
- Redis (状态存储)
- FastAPI (API服务)
- Dramatiq (任务队列处理)

---

*最后更新: 2025-06-03 23:15*  
*维护人员: AI Assistant* 

## 🎯 **核心问题确认**：Dramatiq Worker启动失败

### ❌ 根本原因
**从用户日志发现的关键错误**：
```bash
[2025-06-03 23:19:59,940] [PID 2692114] [MainThread] [dramatiq.broker.RabbitmqBroker] [CRITICAL] 
Unexpected failure in before_worker_boot
RuntimeError: Event loop failed to start.
```

**技术分析**：
- **AsyncIO事件循环冲突**：dramatiq的AsyncIO中间件无法启动事件循环
- **服务依赖问题**：FastAPI和Dramatiq Worker必须同时运行
- **环境隔离问题**：可能存在多个Python环境的事件循环冲突

## 🚀 **完整解决方案**

### 方案一：修复Dramatiq启动问题（立即执行）

#### 1. **正确的服务启动序列**
```bash
# 1. 确保基础服务运行
sudo systemctl status redis
sudo systemctl status rabbitmq-server

# 2. 启动FastAPI服务（端口8000）
cd /root/workspace/MediaAgent/backend
PYTHONPATH=/root/workspace/MediaAgent/backend uvicorn api:app --host 0.0.0.0 --port 8000 --reload

# 3. 启动Dramatiq Worker（新终端）
cd /root/workspace/MediaAgent/backend
PYTHONPATH=/root/workspace/MediaAgent/backend python -m dramatiq run_agent_background --processes 1 --threads 1
```

#### 2. **修复AsyncIO冲突**
创建独立的worker启动脚本，避免事件循环冲突：

```python
# worker_start.py - 独立的worker启动器
import asyncio
import sys
import os
from dramatiq import set_broker
from dramatiq.brokers.rabbitmq import RabbitmqBroker
from dramatiq.cli import main

def start_worker():
    # 清理可能存在的事件循环
    try:
        loop = asyncio.get_event_loop()
        if loop.is_running():
            loop.close()
    except RuntimeError:
        pass
    
    # 设置新的事件循环
    asyncio.set_event_loop(asyncio.new_event_loop())
    
    # 启动dramatiq worker
    sys.argv = ['dramatiq', 'run_agent_background', '--processes', '1', '--threads', '1']
    main()

if __name__ == "__main__":
    start_worker()
```

### 方案二：Docker容器化部署（推荐生产环境）

```yaml
# docker-compose.override.yml
version: '3.8'
services:
  mediaagent-backend:
    build: ./backend
    ports:
      - "8000:8000"
    environment:
      - RABBITMQ_HOST=rabbitmq
      - REDIS_HOST=redis
    depends_on:
      - redis
      - rabbitmq
    command: uvicorn api:app --host 0.0.0.0 --port 8000
  
  mediaagent-worker:
    build: ./backend
    environment:
      - RABBITMQ_HOST=rabbitmq
      - REDIS_HOST=redis
    depends_on:
      - redis
      - rabbitmq
    command: python -m dramatiq run_agent_background --processes 2
```

### 方案三：进程管理器部署（最稳定）

```bash
# 使用supervisor管理服务
sudo apt install supervisor

# /etc/supervisor/conf.d/mediaagent.conf
[program:mediaagent-api]
command=/root/miniconda3/envs/py311/bin/python -m uvicorn api:app --host 0.0.0.0 --port 8000
directory=/root/workspace/MediaAgent/backend
environment=PYTHONPATH="/root/workspace/MediaAgent/backend"
autostart=true
autorestart=true
user=root

[program:mediaagent-worker]
command=/root/miniconda3/envs/py311/bin/python -m dramatiq run_agent_background --processes 1
directory=/root/workspace/MediaAgent/backend
environment=PYTHONPATH="/root/workspace/MediaAgent/backend"
autostart=true
autorestart=true
user=root
```

## 📋 **立即执行清单**

### 步骤1：停止现有进程
```bash
# 杀死所有相关进程
pkill -f "uvicorn api:app"
pkill -f "dramatiq run_agent_background"
```

### 步骤2：创建Worker修复脚本
```bash
cd /root/workspace/MediaAgent/backend
cat > worker_start.py << 'EOF'
[上面的worker_start.py内容]
EOF
```

### 步骤3：启动服务（两个终端）
```bash
# 终端1：启动FastAPI
cd /root/workspace/MediaAgent/backend
PYTHONPATH=/root/workspace/MediaAgent/backend uvicorn api:app --host 0.0.0.0 --port 8000 --reload

# 终端2：启动Worker
cd /root/workspace/MediaAgent/backend
PYTHONPATH=/root/workspace/MediaAgent/backend python worker_start.py
```

### 步骤4：验证服务状态
```bash
# 检查进程
ps aux | grep -E "(uvicorn|dramatiq)"

# 检查端口
netstat -tlnp | grep -E "(8000|5672|6379)"

# 测试API
curl -X GET http://localhost:8000/health
```

## 🔍 **故障排查命令**

```bash
# 1. 检查环境变量
env | grep -E "(RABBITMQ|REDIS|PYTHONPATH)"

# 2. 检查Python环境
which python
python --version
pip list | grep -E "(dramatiq|uvicorn|asyncio)"

# 3. 检查依赖服务
sudo systemctl status redis rabbitmq-server

# 4. 检查网络连接
telnet localhost 5672
telnet localhost 6379
```

## ✅ **成功验证标准**

系统正常运行时应该看到：
1. ✅ FastAPI服务响应（8000端口）
2. ✅ Dramatiq Worker处理任务
3. ✅ Redis中有响应数据
4. ✅ 前端显示工具调用过程
5. ✅ 故事/文章/报告成功生成 

## 📋 目录
- [沙盒管理与清理](#沙盒管理与清理)
- [磁盘空间管理](#磁盘空间管理)
- [服务重启指南](#服务重启指南)
- [监控与故障排除](#监控与故障排除)
- [定期维护任务](#定期维护任务)

---

## 🗂️ 沙盒管理与清理

### 问题现象
- **磁盘配额超出**：`Total disk quota exceeded (51GB > 50GB)`
- **沙盒创建失败**：`Failed to create sandbox: Total disk quota exceeded`
- **服务响应缓慢**：大量僵尸沙盒占用资源

### 🔧 快速清理方案

#### 1. 自动清理开发沙盒（推荐）
```bash
cd /root/workspace/MediaAgent/backend

# 试运行 - 查看将要清理的沙盒
python utils/scripts/auto_cleanup_dev_sandboxes.py --dry-run --max-age 2

# 实际清理 - 清理2小时以上的开发沙盒
python utils/scripts/auto_cleanup_dev_sandboxes.py --max-age 2

# 激进清理 - 清理1小时以上的开发沙盒
python utils/scripts/auto_cleanup_dev_sandboxes.py --max-age 1
```

**清理规则**：
- 只清理标记为开发环境的沙盒
- 保护正在运行的活跃沙盒
- 根据项目名称智能识别：包含"测试"、"test"、"故事"等关键词
- 短项目名（<10字符）也被认为是测试项目

#### 2. 手动清理特定用户沙盒
```bash
cd /root/workspace/MediaAgent/backend

# 查看特定用户的沙盒
python utils/scripts/delete_user_sandboxes.py <account_id>

# 示例
python utils/scripts/delete_user_sandboxes.py ********-1234-1234-1234-********9abc
```

#### 3. 清理长期未使用沙盒
```bash
cd /root/workspace/MediaAgent/backend

# 清理7天以上的停止状态沙盒
python utils/scripts/archive_old_sandboxes.py --days 7 --dry-run

# 实际清理
python utils/scripts/archive_old_sandboxes.py --days 7
```

### 🔍 沙盒状态检查命令

#### 查看当前沙盒使用情况
```bash
# 进入后端目录
cd /root/workspace/MediaAgent/backend

# 查看Python环境
python -c "
from sandbox.sandbox import daytona
sandboxes = daytona.list()
total_size = 0
for sb in sandboxes:
    print(f'沙盒: {sb.id[:8]}... 状态: {sb.state}')
    if hasattr(sb, 'resources'):
        disk = sb.resources.get('disk', 0)
        total_size += disk
        print(f'  磁盘使用: {disk}GB')
print(f'总磁盘使用: {total_size}GB')
"
```

#### 查看项目数据库信息
```bash
cd /root/workspace/MediaAgent/backend

python -c "
import asyncio
from services.supabase import DBConnection

async def check_projects():
    db = DBConnection()
    client = await db.client
    
    # 查询最近的项目
    result = await client.table('projects').select('name,created_at,sandbox').order('created_at', desc=True).limit(10).execute()
    
    print('最近10个项目:')
    for project in result.data:
        sandbox_id = project.get('sandbox', {}).get('id', 'N/A')
        print(f'{project[\"name\"][:30]}... 沙盒: {sandbox_id[:8] if sandbox_id != \"N/A\" else \"N/A\"}...')
    
    await DBConnection.disconnect()

asyncio.run(check_projects())
"
```

---

## 💾 磁盘空间管理

### 沙盒资源配置优化

#### 开发环境配置（在dev_sandbox_config.py中）
```python
DEV_RESOURCES = {
    "cpu": 1,      # 1核CPU
    "memory": 2,   # 2GB内存  
    "disk": 2,     # 2GB磁盘（从5GB降低）
}
```

#### 生产环境配置
```python
PROD_RESOURCES = {
    "cpu": 2,      # 2核CPU
    "memory": 4,   # 4GB内存
    "disk": 5,     # 5GB磁盘
}
```

### 紧急清理命令集
```bash
# 1. 快速清理开发沙盒（1小时以上）
cd /root/workspace/MediaAgent/backend
python auto_cleanup_dev_sandboxes.py --max-age 1

# 2. 清理停止状态的沙盒
python archive_old_sandboxes.py --days 1

# 3. 如果还不够，清理更多
python archive_old_sandboxes.py --days 0

# 4. 查看系统磁盘使用
df -h

# 5. 查看MediaAgent目录大小
du -sh /root/workspace/MediaAgent/
```

---

## 🔄 服务重启指南

### 清理沙盒后是否需要重启？

**答案：通常不需要，但建议重启以确保稳定性**

#### 情况分析：
1. **轻量清理（< 10个沙盒）**：不需要重启，服务会自动更新状态
2. **大量清理（> 20个沙盒）**：建议重启前后端服务
3. **紧急清理（磁盘满）**：**必须重启**，确保资源正确回收

### 🚀 前端服务重启
```bash
# 1. 进入前端目录
cd /root/workspace/MediaAgent/frontend

# 2. 停止当前服务（如果在运行）
pkill -f "npm.*dev" || true
pkill -f "node.*next" || true

# 3. 清理依赖和缓存
npm ci
rm -rf .next

# 4. 重新启动开发服务
npm run dev
```

### 🛠️ 后端服务重启
```bash
# 1. 进入后端目录
cd /root/workspace/MediaAgent/backend

# 2. 停止所有Python相关进程
pkill -f "python.*api.py" || true
pkill -f "python.*worker" || true
pkill -f "python.*agent" || true

# 3. 检查进程是否完全停止
ps aux | grep python | grep -v grep

# 4. 使用启动脚本重启
chmod +x start_services.sh
./start_services.sh

# 或者手动启动主要服务
python api.py &
python worker_start.py &
```

### 🔄 完整重启流程（推荐）
```bash
#!/bin/bash
# 完整重启MediaAgent服务

echo "🛑 停止所有服务..."

# 停止前端
cd /root/workspace/MediaAgent/frontend
pkill -f "npm.*dev" || true
pkill -f "node.*next" || true

# 停止后端  
cd /root/workspace/MediaAgent/backend
pkill -f "python.*api.py" || true
pkill -f "python.*worker" || true
pkill -f "python.*agent" || true

echo "⏳ 等待进程完全停止..."
sleep 5

echo "🧹 清理缓存..."
cd /root/workspace/MediaAgent/frontend
rm -rf .next node_modules/.cache

echo "🚀 重启后端服务..."
cd /root/workspace/MediaAgent/backend
python api.py &
sleep 3
python worker_start.py &

echo "🚀 重启前端服务..."
cd /root/workspace/MediaAgent/frontend
npm run dev &

echo "✅ 服务重启完成！"
echo "请等待30秒让服务完全启动..."
```

---

## 📊 监控与故障排除

### 关键指标监控
```bash
# 1. 磁盘使用监控
watch -n 10 'df -h | grep -E "(Filesystem|/dev/)"'

# 2. 沙盒数量监控
watch -n 30 'cd /root/workspace/MediaAgent/backend && python -c "
from sandbox.sandbox import daytona
sandboxes = daytona.list()
print(f\"活跃沙盒数量: {len([s for s in sandboxes if s.state == \"running\"])}\")
print(f\"总沙盒数量: {len(sandboxes)}\")
"'

# 3. 服务状态监控
watch -n 10 'ps aux | grep -E "(python.*api|npm.*dev)" | grep -v grep'
```

### 常见错误解决

#### 1. 磁盘配额超出
```bash
# 立即执行紧急清理
cd /root/workspace/MediaAgent/backend
python utils/scripts/auto_cleanup_dev_sandboxes.py --max-age 0
```

#### 2. 沙盒创建失败
```bash
# 检查Daytona连接
cd /root/workspace/MediaAgent/backend
python -c "
from sandbox.sandbox import daytona
try:
    sandboxes = daytona.list()
    print(f'Daytona连接正常，找到 {len(sandboxes)} 个沙盒')
except Exception as e:
    print(f'Daytona连接失败: {e}')
"
```

#### 3. 数据库连接问题
```bash
# 检查Supabase连接
cd /root/workspace/MediaAgent/backend
python -c "
import asyncio
from services.supabase import DBConnection

async def test_db():
    try:
        db = DBConnection()
        client = await db.client
        result = await client.table('projects').select('count').execute()
        print('数据库连接正常')
        await DBConnection.disconnect()
    except Exception as e:
        print(f'数据库连接失败: {e}')

asyncio.run(test_db())
"
```

---

## ⏰ 定期维护任务

### 每日维护（推荐自动化）
```bash
#!/bin/bash
# 每日维护脚本

cd /root/workspace/MediaAgent/backend

echo "📅 $(date): 开始每日维护..."

# 1. 清理开发沙盒（2小时以上）
echo "🧹 清理开发沙盒..."
python utils/scripts/auto_cleanup_dev_sandboxes.py --max-age 2

# 2. 清理旧沙盒（3天以上）
echo "🧹 清理旧沙盒..."
python utils/scripts/archive_old_sandboxes.py --days 3

# 3. 检查磁盘使用
echo "📊 磁盘使用情况:"
df -h | grep -E "(Filesystem|/dev/)"

echo "✅ 每日维护完成"
```

### 设置Cron定时任务
```bash
# 编辑crontab
crontab -e

# 添加以下内容（每天凌晨2点执行清理）
0 2 * * * cd /root/workspace/MediaAgent/backend && python utils/scripts/auto_cleanup_dev_sandboxes.py --max-age 3 >> /var/log/sandbox_cleanup.log 2>&1

# 每小时检查磁盘，如果使用率>90%则紧急清理
0 * * * * df / | awk 'NR==2 {if($5+0 > 90) print "Disk usage:", $5}' | grep "Disk usage" && cd /root/workspace/MediaAgent/backend && python utils/scripts/auto_cleanup_dev_sandboxes.py --max-age 1
```

### 周维护任务
```bash
#!/bin/bash
# 周维护脚本（每周日执行）

cd /root/workspace/MediaAgent/backend

echo "📅 $(date): 开始周维护..."

# 1. 清理所有停止状态沙盒
echo "🧹 清理停止状态沙盒..."
python utils/scripts/archive_old_sandboxes.py --days 1

# 2. 重启服务确保资源回收
echo "🔄 重启服务..."
pkill -f "python.*api.py" || true
pkill -f "python.*worker" || true
sleep 5

python api.py &
sleep 3
python worker_start.py &

echo "✅ 周维护完成"
```

---

## 🚨 应急预案

### 磁盘空间紧急处理
```bash
#!/bin/bash
# 紧急磁盘清理脚本

echo "🚨 磁盘空间紧急处理开始..."

cd /root/workspace/MediaAgent/backend

# 1. 立即清理所有开发沙盒
echo "🧹 紧急清理所有开发沙盒..."
python utils/scripts/auto_cleanup_dev_sandboxes.py --max-age 0

# 2. 清理所有停止状态沙盒
echo "🧹 清理停止状态沙盒..."
python utils/scripts/archive_old_sandboxes.py --days 0

# 3. 清理缓存和临时文件
echo "🧹 清理系统缓存..."
rm -rf /root/workspace/MediaAgent/frontend/.next
rm -rf /root/workspace/MediaAgent/backend/__pycache__
rm -rf /root/workspace/MediaAgent/backend/**/__pycache__

# 4. 检查结果
echo "📊 清理后磁盘状态:"
df -h

echo "✅ 紧急处理完成"
```

### 服务无响应处理
```bash
#!/bin/bash
# 服务无响应紧急处理

echo "🚨 服务无响应紧急处理..."

# 1. 强制停止所有相关进程
pkill -9 -f "python.*api"
pkill -9 -f "python.*worker"
pkill -9 -f "npm.*dev"
pkill -9 -f "node.*next"

# 2. 清理可能的锁文件
rm -f /tmp/.sandbox_lock
rm -f /tmp/.db_lock

# 3. 重启服务
cd /root/workspace/MediaAgent/backend
python api.py &
sleep 5
python worker_start.py &

cd /root/workspace/MediaAgent/frontend
npm run dev &

echo "✅ 服务重启完成"
```

---

## 📞 联系信息

**维护负责人**: 开发团队
**紧急联系**: 查看项目README.md

**最后更新**: 2025-01-03
**文档版本**: v1.0 