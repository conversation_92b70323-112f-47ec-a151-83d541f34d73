# MediaAgent V3 统一架构设计方案

## 🎯 执行摘要

本文档基于对MediaAgent现有两套Agent系统的深入分析，结合12-factor-agents最佳实践，提出了MediaAgent V3统一架构设计方案。该方案旨在融合现有系统的优势，解决架构分离问题，并支持交互式工作流和未来功能扩展。

## 📊 现状分析

### 现有系统架构

MediaAgent项目目前存在两套并行的Agent系统：

1. **Backend/Agent系统**（核心Agent架构）
   - 基于AgentPress框架
   - 强大的沙盒工具生态
   - 复杂的Recursive框架支持
   - 架构复杂度高，维护困难

2. **Src/Services/Generator系统**（文章生成专用）
   - 基于LangGraph的专业化设计
   - 清晰的状态管理（ArticleState）
   - 专门优化的文章生成能力
   - 扩展性有限，与核心系统割裂

### 核心问题识别

1. **架构分离**：两套系统无法有效协作
2. **Planning Agent定位不当**：被归类为Sub Agent，与协调职责不符
3. **缺乏交互式支持**：无法支持用户全程参与的工作流
4. **状态管理分散**：执行状态和业务状态分离
5. **工具系统不统一**：两套工具调用机制

## 🏗️ V3统一架构设计

### 整体架构图

```
MediaAgent V3 统一架构
├── 🧠 Core Agent Engine (核心引擎)
│   ├── Planning Coordinator (规划协调器) - 重新定位为协调者
│   ├── Agent Registry (代理注册表)
│   ├── Interactive Workflow Engine (交互式工作流引擎)
│   ├── Unified State Manager (统一状态管理)
│   ├── Context Manager (上下文管理)
│   └── Unified Tool System (统一工具系统)
├── 🎭 Specialized Agents (专业化Agent)
│   ├── Enhanced Article Agent (增强文章代理) - 融合Generator
│   ├── Story Agent (故事代理)
│   ├── Script Agent (脚本代理)
│   ├── Research Agent (研究代理)
│   └── Analysis Agent (分析代理)
├── 🔧 Tool Ecosystem (工具生态) - 保持现有优势
│   ├── Sandbox Tools (沙盒工具)
│   ├── Content Tools (内容工具)
│   └── Integration Tools (集成工具)
└── 🏢 Infrastructure (基础设施)
    ├── State Persistence (状态持久化)
    ├── API Gateway (API网关)
    └── Monitoring & Logging (监控日志)
```

### 核心设计原则

1. **统一但专业化**：统一的核心引擎 + 专业化的Agent实现
2. **交互式优先**：全程支持用户参与和调整
3. **状态统一管理**：执行状态与业务状态融合（12-factor Factor 5）
4. **工具生态保持**：复用现有丰富的沙盒工具
5. **渐进式迁移**：平滑从现有系统迁移

## 🧠 Planning Coordinator重新设计

### 角色转变

**从Sub Agent到System Coordinator**

- **原有问题**：Planning Agent被归类为Sub Agent，与协调职责不符
- **新定位**：系统协调中心，负责用户意图理解、任务规划、Agent调度
- **层级提升**：从Sub Agent提升到Core Engine层

### 核心职责

1. **用户意图分析**：理解用户需求，识别任务类型
2. **智能任务规划**：制定可执行的任务计划
3. **Agent协调调度**：选择合适的Agent并协调执行
4. **用户交互管理**：处理用户确认、调整、中断等交互
5. **执行监控**：监控执行过程，确保质量

### 工作流程

```mermaid
sequenceDiagram
    participant U as User
    participant PC as Planning Coordinator
    participant SA as Specialized Agent
    participant TS as Tool System

    U->>PC: 用户请求
    PC->>PC: 意图分析
    PC->>U: 计划确认
    U->>PC: 确认/调整
    
    loop 执行阶段
        PC->>SA: 调度Agent
        SA->>TS: 调用工具
        TS->>SA: 工具结果
        SA->>PC: 阶段结果
        PC->>U: 中间确认
        
        opt 用户调整
            U->>PC: 反馈调整
            PC->>PC: 动态调整计划
        end
    end
    
    PC->>U: 最终结果
```

## 🔄 交互式工作流设计

### 核心交互模式

1. **实时参与决策**：在关键节点支持用户确认或调整
2. **中断和恢复**：支持随时暂停，稍后继续
3. **动态调整计划**：根据中间结果调整后续步骤
4. **多轮输入确认**：支持多次输入和确认

### 交互点设计

- **执行前确认**：每个重要步骤执行前用户确认
- **中间结果确认**：关键输出需要用户确认满意度
- **错误处理交互**：出现错误时询问用户处理方式
- **计划调整交互**：支持用户随时调整执行计划

## 📊 统一状态管理

### UnifiedState设计

基于12-factor-agents Factor 5原则，统一执行状态和业务状态：

```python
class UnifiedState:
    # 会话基础信息
    session_id: str
    user_id: str
    task_type: TaskType
    
    # 执行状态
    current_stage: str
    execution_history: List[ExecutionStep]
    pending_confirmations: List[UserConfirmation]
    
    # 业务状态
    business_context: Dict[str, Any]
    user_preferences: Dict[str, Any]
    
    # 交互状态
    interaction_history: List[UserInteraction]
    context_window: List[ContextItem]
```

### 状态持久化策略

- **实时持久化**：关键状态变更立即保存
- **缓存优化**：热点数据缓存，提升性能
- **恢复机制**：支持从任意中断点恢复

## 🎭 Enhanced Article Agent设计

### 融合Generator优势

将Generator系统的专业文章生成能力集成到统一架构：

1. **保持专业能力**：复用Generator的文章生成逻辑
2. **统一状态管理**：适配到UnifiedState模型
3. **交互式支持**：增加用户交互和确认机制
4. **工具系统集成**：与统一工具系统对接

### 工作流集成

```python
async def execute_article_workflow(self, state: UnifiedState):
    workflow_steps = [
        ("research", "信息搜集", True),      # 需要用户确认
        ("planning", "文章规划", True),      # 需要用户确认
        ("writing", "内容写作", False),     # 自动执行
        ("evaluation", "质量评估", True),   # 需要用户确认
        ("finalizing", "最终处理", False)   # 自动执行
    ]
    
    for step_name, step_desc, requires_confirmation in workflow_steps:
        if requires_confirmation:
            # 等待用户确认
            await self.wait_for_user_confirmation(step_desc)
        
        # 执行步骤
        result = await self.execute_step(step_name, state)
        
        # 更新状态
        state.update_business_context(f"{step_name}_result", result)
```

## 🔧 统一工具系统

### 工具生态保持

- **沙盒工具**：保持现有的SandboxShellTool、SandboxFilesTool等
- **Generator工具**：适配Generator系统的专用工具
- **新增工具**：支持新功能的工具扩展

### 统一调用接口

```python
class UnifiedToolSystem:
    async def execute_tool(self, tool_name: str, parameters: Dict, state: UnifiedState) -> ToolResult:
        tool = self.get_tool(tool_name)
        
        # 添加状态上下文
        enhanced_params = {
            **parameters,
            "session_context": state.get_context_for_tool(tool_name)
        }
        
        result = await tool.execute(enhanced_params)
        
        # 更新状态
        state.add_execution_step(ExecutionStep(
            type="tool_execution",
            tool_name=tool_name,
            result=result
        ))
        
        return result
```

## 📋 实施路线图

### Phase 1: 核心架构搭建 (2-3周)

1. **统一状态模型设计**
   - 设计UnifiedState数据结构
   - 实现状态持久化机制
   - 建立状态迁移适配器

2. **Planning Coordinator实现**
   - 重新设计Planning Agent为Coordinator
   - 实现意图分析和计划生成
   - 建立Agent调度机制

3. **交互式工作流引擎**
   - 设计用户交互协议
   - 实现确认和调整机制
   - 支持暂停和恢复功能

### Phase 2: Agent系统集成 (3-4周)

1. **Enhanced Article Agent开发**
   - 集成Generator系统能力
   - 适配统一状态管理
   - 增加交互式支持

2. **工具系统统一**
   - 建立统一工具调用接口
   - 适配现有沙盒工具
   - 集成Generator工具

3. **Agent Registry建设**
   - 实现Agent注册和发现
   - 建立Agent能力描述
   - 支持动态Agent选择

### Phase 3: 高级特性 (2-3周)

1. **智能优化**
   - 实现智能Agent选择
   - 优化执行计划生成
   - 增加质量评估机制

2. **监控和日志**
   - 建立执行监控系统
   - 实现结构化日志
   - 添加性能分析

3. **API和集成**
   - 设计统一API接口
   - 实现向后兼容
   - 建立迁移工具

## 🎯 预期收益

### 技术收益

1. **架构统一**：消除系统分离，提升维护效率
2. **功能增强**：支持交互式工作流，提升用户体验
3. **扩展性提升**：统一架构便于新功能集成
4. **质量保证**：内置质量检查和用户确认机制

### 业务收益

1. **用户体验提升**：支持全程参与和实时调整
2. **功能覆盖扩展**：统一平台支持多种内容创作
3. **开发效率提升**：统一架构降低开发复杂度
4. **维护成本降低**：单一架构减少维护负担

## 🚀 下一步行动

1. **架构方案确认**：请确认统一架构设计是否符合预期
2. **技术细节讨论**：深入讨论关键技术实现细节
3. **实施计划制定**：制定详细的开发和迁移计划
4. **原型开发**：开始核心组件的原型开发

---

*本文档将随着项目进展持续更新和完善。*
