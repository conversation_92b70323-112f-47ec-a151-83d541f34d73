# MediaAgent V2 测试方案

## 测试概述

本测试方案旨在确保MediaAgent V2系统的质量、性能和可靠性，以及与V1系统的兼容性。测试覆盖功能测试、性能测试、集成测试、用户验收测试等多个方面。

## 测试策略

### 1. 测试目标

- ✅ 验证V2系统核心功能正确性
- ✅ 确保系统性能满足要求
- ✅ 验证V1到V2迁移的完整性
- ✅ 确保系统稳定性和可靠性
- ✅ 验证用户体验改进效果

### 2. 测试范围

| 测试类型 | 覆盖范围 | 优先级 |
|----------|----------|--------|
| 单元测试 | 所有核心组件 | 高 |
| 集成测试 | 组件间交互 | 高 |
| 功能测试 | 用户场景 | 高 |
| 性能测试 | 响应时间、吞吐量 | 高 |
| 兼容性测试 | V1/V2兼容性 | 中 |
| 安全测试 | 数据安全、访问控制 | 中 |
| 用户体验测试 | 界面交互、易用性 | 低 |

### 3. 测试环境

```yaml
# 测试环境配置
environments:
  unit_test:
    description: "单元测试环境"
    python_version: "3.11+"
    dependencies: "requirements-test.txt"
    database: "sqlite_memory"
    
  integration_test:
    description: "集成测试环境"
    python_version: "3.11+"
    database: "postgresql_test"
    redis: "redis_test"
    external_services: "mocked"
    
  performance_test:
    description: "性能测试环境"
    resources:
      cpu: "4 cores"
      memory: "8GB"
      database: "postgresql_performance"
      redis: "redis_performance"
    
  staging:
    description: "预发布环境"
    configuration: "production-like"
    data: "anonymized_production_data"
```

## 详细测试计划

### 1. 单元测试

#### 1.1 核心组件测试

```python
# tests/unit/test_core_engine.py
import pytest
import asyncio
from backend.agent_v2.core.engine import MediaAgentV2Engine
from backend.agent_v2.core.models import UnifiedState, TaskType

class TestMediaAgentV2Engine:
    
    @pytest.fixture
    async def engine(self):
        """测试引擎实例"""
        engine = MediaAgentV2Engine({
            "state_config": {"storage_backend": "memory"},
            "agent_config": {"max_agents": 5}
        })
        await engine.initialize()
        yield engine
        await engine.shutdown()
    
    @pytest.mark.asyncio
    async def test_engine_initialization(self, engine):
        """测试引擎初始化"""
        assert engine.is_initialized == True
        assert engine.planning_agent is not None
        assert engine.agent_registry is not None
        assert engine.workflow_engine is not None
        assert engine.state_manager is not None
        assert engine.tool_system is not None
    
    @pytest.mark.asyncio
    async def test_process_simple_request(self, engine):
        """测试简单请求处理"""
        request = {
            "topic": "AI技术发展",
            "requirements": "简单介绍",
            "keywords": ["人工智能"],
            "target_length": 1000
        }
        
        response = await engine.process_request(request)
        
        assert response.success == True
        assert response.content is not None
        assert len(response.content) > 0
    
    @pytest.mark.asyncio
    async def test_session_management(self, engine):
        """测试会话管理"""
        request = {
            "topic": "测试主题",
            "requirements": "测试需求"
        }
        
        # 处理请求
        response = await engine.process_request(request, user_id="test_user")
        session_id = response.metadata.get("session_id")
        
        # 获取会话状态
        status = await engine.get_session_status(session_id)
        assert status is not None
        assert status["session_id"] == session_id
        
        # 暂停会话
        paused = await engine.pause_session(session_id)
        assert paused == True
        
        # 恢复会话
        state = await engine.resume_session(session_id)
        assert state is not None
    
    @pytest.mark.asyncio
    async def test_error_handling(self, engine):
        """测试错误处理"""
        # 无效请求
        invalid_request = {}
        
        response = await engine.process_request(invalid_request)
        
        assert response.success == False
        assert len(response.errors) > 0
```

#### 1.2 Agent测试

```python
# tests/unit/test_agents.py
import pytest
from backend.agent_v2.agents.article_agent import ArticleAgent
from backend.agent_v2.agents.research_agent import ResearchAgent
from backend.agent_v2.agents.analysis_agent import AnalysisAgent
from backend.agent_v2.core.models import UnifiedState, TaskType

class TestArticleAgent:
    
    @pytest.fixture
    def agent(self):
        return ArticleAgent()
    
    @pytest.fixture
    def test_state(self):
        return UnifiedState(
            topic="AI技术发展",
            requirements="详细介绍AI技术的发展历程",
            keywords=["人工智能", "机器学习"],
            target_length=2000,
            task_type=TaskType.ARTICLE_GENERATION
        )
    
    @pytest.mark.asyncio
    async def test_article_generation(self, agent, test_state):
        """测试文章生成"""
        responses = []
        async for response in agent.process(test_state):
            responses.append(response)
        
        final_response = responses[-1]
        assert final_response.success == True
        assert "article" in final_response.data
        assert final_response.progress == 1.0
    
    @pytest.mark.asyncio
    async def test_agent_health_check(self, agent):
        """测试Agent健康检查"""
        health = await agent.health_check()
        
        assert health["status"] in ["healthy", "degraded"]
        assert "agent_name" in health
        assert health["agent_name"] == "article_agent"

class TestResearchAgent:
    
    @pytest.fixture
    def agent(self):
        return ResearchAgent()
    
    @pytest.mark.asyncio
    async def test_research_process(self, agent):
        """测试研究过程"""
        test_state = UnifiedState(
            topic="区块链技术",
            keywords=["区块链", "比特币", "智能合约"],
            task_type=TaskType.RESEARCH
        )
        
        final_response = None
        async for response in agent.process(test_state):
            final_response = response
        
        assert final_response.success == True
        assert "research_report" in final_response.data
    
    @pytest.mark.asyncio
    async def test_conduct_research_method(self, agent):
        """测试独立研究方法"""
        result = await agent.conduct_research(
            topic="量子计算",
            keywords=["量子", "计算", "算法"]
        )
        
        assert isinstance(result, dict)
        if result:  # 如果有结果
            assert "research_report" in result or "sources_count" in result

class TestAnalysisAgent:
    
    @pytest.fixture
    def agent(self):
        return AnalysisAgent()
    
    @pytest.mark.asyncio
    async def test_content_analysis(self, agent):
        """测试内容分析"""
        test_content = """
        # AI技术发展现状
        
        人工智能技术在近年来发展迅速，特别是在机器学习和深度学习领域取得了重大突破。
        
        ## 主要发展方向
        
        1. 自然语言处理
        2. 计算机视觉  
        3. 语音识别
        
        ## 总结
        
        AI技术将继续推动社会进步。
        """
        
        test_state = UnifiedState(
            task_type=TaskType.CONTENT_ANALYSIS,
            keywords=["人工智能", "机器学习"],
            target_length=500
        )
        test_state.output_data["content"] = test_content
        
        final_response = None
        async for response in agent.process(test_state):
            final_response = response
        
        assert final_response.success == True
        assert "analysis_report" in final_response.data
        assert final_response.data["overall_score"] > 0
    
    @pytest.mark.asyncio  
    async def test_optimize_content_method(self, agent):
        """测试内容优化方法"""
        test_content = "这是一个简单的测试内容。"
        
        result = await agent.optimize_content(test_content, {
            "keywords": ["测试"],
            "target_length": 100
        })
        
        assert "optimized_content" in result
        assert "quality_score" in result
        assert isinstance(result["quality_score"], float)
```

#### 1.3 工具系统测试

```python
# tests/unit/test_tools.py
import pytest
from backend.agent_v2.tools.base_tool import BaseToolV2, ToolRegistry
from backend.agent_v2.core.tool_system import ToolSystem
from backend.agent_v2.core.models import UnifiedState, ToolResult

class MockTool(BaseToolV2):
    """模拟工具用于测试"""
    
    def __init__(self):
        super().__init__(name="mock_tool", description="Mock tool for testing")
    
    async def execute(self, parameters, state):
        return ToolResult(
            success=True,
            data={"result": "mock_result", "parameters": parameters}
        )
    
    def _get_metadata(self):
        return {"capabilities": ["testing"], "version": "1.0.0"}

class TestToolRegistry:
    
    @pytest.fixture
    def registry(self):
        return ToolRegistry()
    
    def test_register_tool(self, registry):
        """测试工具注册"""
        tool = MockTool()
        registry.register_tool(tool)
        
        assert "mock_tool" in registry.tools
        assert registry.get_tool("mock_tool") == tool
    
    def test_list_tools(self, registry):
        """测试工具列表"""
        tool1 = MockTool()
        tool1.name = "tool1"
        tool2 = MockTool()
        tool2.name = "tool2"
        
        registry.register_tool(tool1)
        registry.register_tool(tool2)
        
        tools = registry.list_tools()
        assert "tool1" in tools
        assert "tool2" in tools
    
    @pytest.mark.asyncio
    async def test_execute_tool_chain(self, registry):
        """测试工具链执行"""
        tool1 = MockTool()
        tool1.name = "tool1"
        tool2 = MockTool()
        tool2.name = "tool2"
        
        registry.register_tool(tool1)
        registry.register_tool(tool2)
        registry.create_tool_chain("test_chain", ["tool1", "tool2"])
        
        state = UnifiedState()
        results = await registry.execute_tool_chain(
            "test_chain", 
            [{"param1": "value1"}, {"param2": "value2"}],
            state
        )
        
        assert len(results) == 2
        assert all(result.success for result in results)

class TestToolSystem:
    
    @pytest.fixture
    async def tool_system(self):
        system = ToolSystem()
        await system.initialize()
        
        # 注册测试工具
        mock_tool = MockTool()
        system.register_tool(mock_tool)
        
        yield system
        await system.shutdown()
    
    @pytest.mark.asyncio
    async def test_execute_tool(self, tool_system):
        """测试工具执行"""
        state = UnifiedState()
        result = await tool_system.execute_tool("mock_tool", {"test": "value"}, state)
        
        assert result.success == True
        assert result.data["result"] == "mock_result"
    
    @pytest.mark.asyncio
    async def test_suggest_tools_for_task(self, tool_system):
        """测试任务工具推荐"""
        suggestions = await tool_system.suggest_tools_for_task("测试任务")
        
        assert isinstance(suggestions, list)
    
    @pytest.mark.asyncio
    async def test_health_check(self, tool_system):
        """测试工具系统健康检查"""
        health = await tool_system.health_check()
        
        assert health["status"] in ["healthy", "degraded"]
        assert "total_tools" in health
```

### 2. 集成测试

#### 2.1 端到端测试

```python
# tests/integration/test_end_to_end.py
import pytest
import asyncio
from backend.agent_v2.core.engine import MediaAgentV2Engine

class TestEndToEnd:
    
    @pytest.fixture
    async def engine(self):
        """完整配置的引擎"""
        config = {
            "state_config": {
                "storage_backend": "redis",
                "redis": {"host": "localhost", "port": 6379, "db": 1}
            },
            "workflow_config": {
                "max_concurrent_workflows": 5
            }
        }
        
        engine = MediaAgentV2Engine(config)
        await engine.initialize()
        yield engine
        await engine.shutdown()
    
    @pytest.mark.asyncio
    async def test_complete_article_generation_flow(self, engine):
        """测试完整的文章生成流程"""
        
        # 1. 提交请求
        request = {
            "topic": "深度学习在医疗领域的应用",
            "requirements": "需要包含技术原理、实际应用案例、未来发展方向",
            "keywords": ["深度学习", "医疗AI", "神经网络", "诊断"],
            "target_length": 3000,
            "language": "zh-CN",
            "tone": "professional",
            "user_id": "test_user_001"
        }
        
        response = await engine.process_request(request)
        
        # 2. 验证响应
        assert response.success == True
        assert response.content is not None
        assert len(response.content) > 100
        
        # 3. 验证生成的文章质量
        if "article" in response.data:
            article = response.data["article"]
            
            # 检查文章结构
            content = article.get("content", "")
            assert "深度学习" in content
            assert "医疗" in content
            
            # 检查长度
            word_count = len(content.split())
            assert 2000 <= word_count <= 4000  # 允许一定偏差
            
            # 检查关键词覆盖
            for keyword in request["keywords"]:
                assert keyword in content
    
    @pytest.mark.asyncio
    async def test_interactive_workflow(self, engine):
        """测试交互式工作流"""
        
        request = {
            "topic": "区块链技术发展趋势",
            "requirements": "需要用户参与确认每个阶段的结果",
            "keywords": ["区块链", "比特币", "智能合约"],
            "target_length": 2000,
            "enable_interaction": True
        }
        
        # 由于交互式工作流需要用户输入，这里模拟自动确认
        response = await engine.process_request(request)
        
        # 验证至少启动了工作流
        assert response is not None
        # 由于是交互式流程，可能需要额外的确认步骤
    
    @pytest.mark.asyncio
    async def test_concurrent_requests(self, engine):
        """测试并发请求处理"""
        
        # 创建多个并发请求
        requests = [
            {
                "topic": f"测试主题{i}",
                "requirements": f"测试需求{i}",
                "keywords": [f"关键词{i}"],
                "target_length": 1000,
                "user_id": f"user_{i}"
            }
            for i in range(5)
        ]
        
        # 并发执行
        tasks = [
            engine.process_request(request) 
            for request in requests
        ]
        
        responses = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 验证所有请求都成功处理
        successful_responses = [
            r for r in responses 
            if not isinstance(r, Exception) and r.success
        ]
        
        assert len(successful_responses) >= 3  # 至少60%成功率
    
    @pytest.mark.asyncio
    async def test_session_persistence(self, engine):
        """测试会话持久化"""
        
        request = {
            "topic": "会话持久化测试",
            "requirements": "测试会话状态的保存和恢复",
            "user_id": "persistence_test_user"
        }
        
        # 处理请求
        response = await engine.process_request(request)
        session_id = response.metadata.get("session_id")
        
        # 暂停会话
        await engine.pause_session(session_id)
        
        # 模拟系统重启 - 重新初始化引擎
        await engine.shutdown()
        await engine.initialize()
        
        # 恢复会话
        restored_state = await engine.resume_session(session_id)
        
        assert restored_state is not None
        assert restored_state.topic == request["topic"]
```

#### 2.2 兼容性测试

```python
# tests/integration/test_compatibility.py
import pytest
from backend.agent_v2.infrastructure.compatibility_layer import CompatibilityLayer

class TestCompatibility:
    
    @pytest.fixture
    async def compatibility_layer(self):
        config = {
            "enable_v1_compatibility": True,
            "enable_v2_features": True,
            "default_system": "v2"
        }
        
        layer = CompatibilityLayer(config)
        await layer.initialize()
        yield layer
        await layer.shutdown()
    
    @pytest.mark.asyncio
    async def test_v1_request_format_handling(self, compatibility_layer):
        """测试V1请求格式处理"""
        
        v1_request = {
            "workflow_id": "test_workflow_123",
            "thread_id": "test_thread_456", 
            "topic": "V1格式测试",
            "prompt": "这是V1格式的提示内容",
            "keywords": ["V1", "兼容性"],
            "stream": True,
            "tool_choice": "auto"
        }
        
        result = await compatibility_layer.handle_request(v1_request, "v1")
        
        assert result["success"] == True
        assert "content" in result
        assert "compatibility_info" in result
    
    @pytest.mark.asyncio
    async def test_v2_request_format_handling(self, compatibility_layer):
        """测试V2请求格式处理"""
        
        v2_request = {
            "session_id": "test_session_789",
            "task_type": "article_generation",
            "topic": "V2格式测试",
            "requirements": "测试V2格式的处理能力",
            "keywords": ["V2", "新架构"],
            "target_length": 1500,
            "language": "zh-CN"
        }
        
        result = await compatibility_layer.handle_request(v2_request, "v2")
        
        assert result["success"] == True
        assert "content" in result
        assert result["metadata"]["system_used"] == "v2"
    
    @pytest.mark.asyncio
    async def test_format_conversion(self, compatibility_layer):
        """测试格式转换"""
        
        # V1格式请求自动转换为V2处理
        v1_request = {
            "thread_id": "conversion_test",
            "prompt": "测试格式转换功能",
            "messages": [
                {"role": "user", "content": "请写一篇关于AI的文章"}
            ]
        }
        
        result = await compatibility_layer.handle_request(v1_request)
        
        assert result["success"] == True
        if "compatibility_info" in result:
            assert result["compatibility_info"].get("migrated_from_v1") == True
    
    @pytest.mark.asyncio
    async def test_data_migration(self, compatibility_layer):
        """测试数据迁移"""
        
        v1_data = {
            "workflows": [
                {
                    "workflow_id": "migrate_test_001",
                    "user_id": "user_migrate",
                    "topic": "迁移测试主题",
                    "status": "completed",
                    "created_at": "2024-01-01T00:00:00Z",
                    "result": {
                        "content": "这是迁移测试的内容"
                    }
                }
            ],
            "users": [
                {
                    "user_id": "user_migrate",
                    "language": "zh-CN",
                    "preferred_tone": "professional"
                }
            ]
        }
        
        migrated_data = await compatibility_layer.migrate_v1_data(v1_data)
        
        assert "sessions" in migrated_data
        assert "user_contexts" in migrated_data
        assert len(migrated_data["sessions"]) == 1
        assert len(migrated_data["user_contexts"]) == 1
        
        # 验证数据完整性
        session = migrated_data["sessions"][0]
        assert session["session_id"] == "migrate_test_001"
        assert session["user_id"] == "user_migrate"
```

### 3. 性能测试

#### 3.1 负载测试

```python
# tests/performance/test_load.py
import asyncio
import time
import statistics
import pytest
from concurrent.futures import ProcessPoolExecutor
from backend.agent_v2.core.engine import MediaAgentV2Engine

class TestPerformance:
    
    @pytest.fixture(scope="class")
    async def engine(self):
        """性能测试专用引擎"""
        config = {
            "workflow_config": {"max_concurrent_workflows": 20},
            "agent_config": {"max_agents": 10}
        }
        
        engine = MediaAgentV2Engine(config)
        await engine.initialize()
        yield engine
        await engine.shutdown()
    
    @pytest.mark.performance
    @pytest.mark.asyncio
    async def test_response_time_under_load(self, engine):
        """测试负载下的响应时间"""
        
        # 测试参数
        concurrent_requests = 10
        total_requests = 50
        
        async def single_request(request_id):
            """单个请求"""
            start_time = time.time()
            
            request = {
                "topic": f"性能测试主题{request_id}",
                "requirements": "简单的性能测试内容",
                "keywords": ["性能", "测试"],
                "target_length": 1000
            }
            
            try:
                response = await engine.process_request(request)
                end_time = time.time()
                
                return {
                    "success": response.success,
                    "response_time": end_time - start_time,
                    "request_id": request_id
                }
            except Exception as e:
                end_time = time.time()
                return {
                    "success": False,
                    "response_time": end_time - start_time,
                    "request_id": request_id,
                    "error": str(e)
                }
        
        # 执行并发测试
        all_results = []
        
        for batch_start in range(0, total_requests, concurrent_requests):
            batch_end = min(batch_start + concurrent_requests, total_requests)
            batch_tasks = [
                single_request(i) for i in range(batch_start, batch_end)
            ]
            
            batch_results = await asyncio.gather(*batch_tasks)
            all_results.extend(batch_results)
            
            # 批次间间隔
            await asyncio.sleep(1)
        
        # 分析结果
        successful_results = [r for r in all_results if r["success"]]
        failed_results = [r for r in all_results if not r["success"]]
        
        response_times = [r["response_time"] for r in successful_results]
        
        # 性能指标
        success_rate = len(successful_results) / len(all_results)
        avg_response_time = statistics.mean(response_times) if response_times else 0
        median_response_time = statistics.median(response_times) if response_times else 0
        p95_response_time = sorted(response_times)[int(len(response_times) * 0.95)] if response_times else 0
        max_response_time = max(response_times) if response_times else 0
        
        # 输出测试结果
        print(f"\n性能测试结果:")
        print(f"总请求数: {len(all_results)}")
        print(f"成功率: {success_rate:.2%}")
        print(f"平均响应时间: {avg_response_time:.2f}s")
        print(f"中位数响应时间: {median_response_time:.2f}s")
        print(f"95%响应时间: {p95_response_time:.2f}s")
        print(f"最大响应时间: {max_response_time:.2f}s")
        print(f"失败请求数: {len(failed_results)}")
        
        # 断言性能要求
        assert success_rate >= 0.95  # 成功率不低于95%
        assert avg_response_time <= 30.0  # 平均响应时间不超过30秒
        assert p95_response_time <= 60.0  # 95%响应时间不超过60秒
    
    @pytest.mark.performance
    @pytest.mark.asyncio
    async def test_memory_usage(self, engine):
        """测试内存使用情况"""
        import psutil
        import os
        
        process = psutil.Process(os.getpid())
        
        # 获取初始内存使用
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        # 执行多个请求
        requests = [
            {
                "topic": f"内存测试{i}",
                "requirements": "测试内存使用情况",
                "target_length": 2000
            }
            for i in range(20)
        ]
        
        for request in requests:
            await engine.process_request(request)
        
        # 获取结束内存使用
        final_memory = process.memory_info().rss / 1024 / 1024  # MB
        memory_increase = final_memory - initial_memory
        
        print(f"\n内存使用测试结果:")
        print(f"初始内存: {initial_memory:.2f} MB")
        print(f"结束内存: {final_memory:.2f} MB")
        print(f"内存增长: {memory_increase:.2f} MB")
        
        # 内存增长不应该过大
        assert memory_increase <= 500  # 内存增长不超过500MB
    
    @pytest.mark.performance
    @pytest.mark.asyncio
    async def test_throughput(self, engine):
        """测试系统吞吐量"""
        
        start_time = time.time()
        completed_requests = 0
        test_duration = 60  # 测试60秒
        
        async def continuous_requests():
            """持续发送请求"""
            nonlocal completed_requests
            request_id = 0
            
            while time.time() - start_time < test_duration:
                request = {
                    "topic": f"吞吐量测试{request_id}",
                    "requirements": "简单测试",
                    "target_length": 500  # 使用较短长度以提高速度
                }
                
                try:
                    response = await engine.process_request(request)
                    if response.success:
                        completed_requests += 1
                except Exception:
                    pass
                
                request_id += 1
                await asyncio.sleep(0.1)  # 短暂间隔
        
        # 启动多个并发任务
        tasks = [continuous_requests() for _ in range(5)]
        await asyncio.gather(*tasks)
        
        actual_duration = time.time() - start_time
        throughput = completed_requests / actual_duration
        
        print(f"\n吞吐量测试结果:")
        print(f"测试时长: {actual_duration:.2f}s")
        print(f"完成请求数: {completed_requests}")
        print(f"吞吐量: {throughput:.2f} requests/second")
        
        # 吞吐量要求
        assert throughput >= 0.5  # 至少每秒处理0.5个请求
```

#### 3.2 压力测试

```python
# tests/performance/test_stress.py
import asyncio
import pytest
from backend.agent_v2.core.engine import MediaAgentV2Engine

class TestStress:
    
    @pytest.mark.stress
    @pytest.mark.asyncio
    async def test_system_stability_under_stress(self):
        """测试压力下的系统稳定性"""
        
        engine = MediaAgentV2Engine()
        await engine.initialize()
        
        try:
            # 逐步增加负载
            load_levels = [5, 10, 20, 30, 40, 50]
            
            for load_level in load_levels:
                print(f"\n测试负载级别: {load_level} 并发请求")
                
                # 创建大量并发请求
                tasks = []
                for i in range(load_level):
                    request = {
                        "topic": f"压力测试{i}",
                        "requirements": "压力测试内容",
                        "target_length": 800
                    }
                    task = engine.process_request(request)
                    tasks.append(task)
                
                # 执行并发请求
                results = await asyncio.gather(*tasks, return_exceptions=True)
                
                # 分析结果
                successes = sum(1 for r in results if not isinstance(r, Exception) and r.success)
                failures = len(results) - successes
                success_rate = successes / len(results)
                
                print(f"成功: {successes}, 失败: {failures}, 成功率: {success_rate:.2%}")
                
                # 如果成功率低于80%，停止增加负载
                if success_rate < 0.8:
                    print(f"系统在负载级别 {load_level} 时开始不稳定")
                    break
                
                # 短暂休息让系统恢复
                await asyncio.sleep(5)
            
            # 系统健康检查
            health = await engine.get_system_health()
            assert health["status"] in ["healthy", "degraded"]
            
        finally:
            await engine.shutdown()
    
    @pytest.mark.stress
    @pytest.mark.asyncio
    async def test_resource_exhaustion_recovery(self):
        """测试资源耗尽后的恢复能力"""
        
        engine = MediaAgentV2Engine({
            "workflow_config": {"max_concurrent_workflows": 5}  # 限制并发数
        })
        await engine.initialize()
        
        try:
            # 超出系统容量的请求
            overload_requests = 20
            
            # 发送大量请求快速耗尽资源
            tasks = []
            for i in range(overload_requests):
                request = {
                    "topic": f"资源耗尽测试{i}",
                    "requirements": "测试资源耗尽情况",
                    "target_length": 2000  # 较长内容增加资源消耗
                }
                task = engine.process_request(request)
                tasks.append(task)
            
            # 不等待完成，立即检查系统状态
            await asyncio.sleep(1)
            
            # 检查系统是否还能处理新请求
            test_request = {
                "topic": "恢复测试",
                "requirements": "测试系统恢复能力",
                "target_length": 500
            }
            
            recovery_response = await engine.process_request(test_request)
            
            # 系统应该能够优雅处理资源不足的情况
            assert recovery_response is not None
            
            # 清理任务
            for task in tasks:
                if not task.done():
                    task.cancel()
            
        finally:
            await engine.shutdown()
```

### 4. 用户验收测试

#### 4.1 用户场景测试

```python
# tests/acceptance/test_user_scenarios.py
import pytest
from backend.agent_v2.core.engine import MediaAgentV2Engine

class TestUserScenarios:
    
    @pytest.fixture
    async def engine(self):
        engine = MediaAgentV2Engine()
        await engine.initialize()
        yield engine
        await engine.shutdown()
    
    @pytest.mark.acceptance
    @pytest.mark.asyncio
    async def test_business_user_article_creation(self, engine):
        """测试商务用户文章创建场景"""
        
        # 场景：商务用户需要创建一篇产品介绍文章
        request = {
            "topic": "智能客服系统产品介绍",
            "requirements": """
            需要包含以下内容：
            1. 产品概述和核心功能
            2. 技术优势和特色
            3. 应用场景和案例
            4. 客户收益和价值
            5. 联系方式和获取信息
            
            语调要专业但易懂，面向企业决策者。
            """,
            "keywords": [
                "智能客服", "AI客服", "自动化客服", 
                "企业服务", "客户体验", "成本优化"
            ],
            "target_length": 2500,
            "language": "zh-CN",
            "tone": "professional",
            "user_id": "business_user_001"
        }
        
        response = await engine.process_request(request)
        
        # 验证响应满足商务需求
        assert response.success == True
        assert response.content is not None
        
        if "article" in response.data:
            content = response.data["article"]["content"]
            
            # 检查必要的商务内容
            business_keywords = ["产品", "功能", "优势", "应用", "客户", "价值"]
            for keyword in business_keywords:
                assert keyword in content, f"缺少商务关键词: {keyword}"
            
            # 检查文章结构
            assert "# " in content or "## " in content, "文章应该有标题结构"
            
            # 检查长度符合要求
            word_count = len(content.split())
            assert 2000 <= word_count <= 3000, f"文章长度不符合要求: {word_count}"
    
    @pytest.mark.acceptance
    @pytest.mark.asyncio
    async def test_content_creator_workflow(self, engine):
        """测试内容创作者工作流场景"""
        
        # 场景：内容创作者需要创建多种类型的内容
        scenarios = [
            {
                "name": "技术博客",
                "request": {
                    "topic": "Python异步编程最佳实践",
                    "requirements": "包含代码示例和实际案例",
                    "keywords": ["Python", "asyncio", "异步编程", "并发"],
                    "target_length": 3000,
                    "tone": "technical"
                }
            },
            {
                "name": "产品评测",
                "request": {
                    "topic": "iPhone 15 Pro全面评测",
                    "requirements": "客观评价优缺点，包含使用体验",
                    "keywords": ["iPhone 15", "苹果", "智能手机", "评测"],
                    "target_length": 2000,
                    "tone": "neutral"
                }
            },
            {
                "name": "教程文章",
                "request": {
                    "topic": "机器学习入门指南",
                    "requirements": "适合初学者，循序渐进",
                    "keywords": ["机器学习", "AI", "算法", "数据科学"],
                    "target_length": 2500,
                    "tone": "educational"
                }
            }
        ]
        
        for scenario in scenarios:
            print(f"\n测试场景: {scenario['name']}")
            
            response = await engine.process_request(scenario["request"])
            
            assert response.success == True, f"{scenario['name']} 场景失败"
            
            if "article" in response.data:
                content = response.data["article"]["content"]
                
                # 基本质量检查
                assert len(content) > 100, f"{scenario['name']} 内容过短"
                
                # 关键词检查
                for keyword in scenario["request"]["keywords"][:2]:  # 检查前两个关键词
                    assert keyword in content, f"{scenario['name']} 缺少关键词: {keyword}"
    
    @pytest.mark.acceptance
    @pytest.mark.asyncio  
    async def test_multilingual_support(self, engine):
        """测试多语言支持场景"""
        
        languages = [
            {
                "code": "zh-CN",
                "name": "中文",
                "topic": "人工智能的发展历程",
                "check_chars": ["人工智能", "发展", "技术"]
            },
            {
                "code": "en-US", 
                "name": "English",
                "topic": "The Development History of Artificial Intelligence",
                "check_chars": ["artificial", "intelligence", "development"]
            }
        ]
        
        for lang in languages:
            print(f"\n测试语言: {lang['name']}")
            
            request = {
                "topic": lang["topic"],
                "requirements": "简要介绍历史发展过程",
                "keywords": ["AI", "技术", "历史"],
                "target_length": 1500,
                "language": lang["code"]
            }
            
            response = await engine.process_request(request)
            
            assert response.success == True, f"{lang['name']} 语言支持失败"
            
            if "article" in response.data:
                content = response.data["article"]["content"]
                
                # 检查语言特征词汇
                found_chars = sum(1 for char in lang["check_chars"] if char in content.lower())
                assert found_chars >= 1, f"{lang['name']} 语言特征不明显"
    
    @pytest.mark.acceptance
    @pytest.mark.asyncio
    async def test_quality_requirements(self, engine):
        """测试质量要求场景"""
        
        # 高质量要求的请求
        high_quality_request = {
            "topic": "区块链技术在金融领域的应用与挑战",
            "requirements": """
            要求：
            1. 内容必须准确、权威
            2. 引用最新的研究和数据
            3. 分析要深入、客观
            4. 结构清晰、逻辑严谨
            5. 语言专业、准确
            """,
            "keywords": [
                "区块链", "金融科技", "数字货币", "智能合约",
                "监管", "风险管理", "创新", "安全"
            ],
            "target_length": 4000,
            "language": "zh-CN",
            "tone": "professional",
            "quality_requirements": {
                "min_score": 0.85,
                "include_sources": True,
                "fact_check": True
            }
        }
        
        response = await engine.process_request(high_quality_request)
        
        assert response.success == True
        
        # 检查质量指标
        if "quality_metrics" in response.metadata:
            quality = response.metadata["quality_metrics"]
            
            if hasattr(quality, 'overall_score'):
                assert quality.overall_score >= 0.8, "质量分数不满足要求"
            
            # 检查内容质量特征
            if "article" in response.data:
                content = response.data["article"]["content"]
                
                # 检查专业词汇密度
                professional_terms = ["技术", "应用", "挑战", "分析", "发展"]
                term_count = sum(1 for term in professional_terms if term in content)
                assert term_count >= 3, "专业词汇密度不足"
                
                # 检查结构完整性
                assert "## " in content or "### " in content, "缺少子标题结构"
```

### 5. 自动化测试脚本

```bash
#!/bin/bash
# scripts/run_all_tests.sh

echo "开始执行MediaAgent V2完整测试套件..."

# 设置测试环境
export PYTHONPATH=$PWD/backend:$PYTHONPATH
export TESTING=true

# 创建测试报告目录
mkdir -p test_reports

# 1. 单元测试
echo "执行单元测试..."
pytest tests/unit/ -v --tb=short --cov=backend.agent_v2 --cov-report=html:test_reports/coverage_unit > test_reports/unit_test_report.txt 2>&1

if [ $? -eq 0 ]; then
    echo "✅ 单元测试通过"
else
    echo "❌ 单元测试失败"
    exit 1
fi

# 2. 集成测试  
echo "执行集成测试..."
pytest tests/integration/ -v --tb=short > test_reports/integration_test_report.txt 2>&1

if [ $? -eq 0 ]; then
    echo "✅ 集成测试通过"
else
    echo "❌ 集成测试失败"
    exit 1
fi

# 3. 性能测试
echo "执行性能测试..."
pytest tests/performance/ -v -m performance --tb=short > test_reports/performance_test_report.txt 2>&1

if [ $? -eq 0 ]; then
    echo "✅ 性能测试通过"
else
    echo "⚠️ 性能测试有问题，请检查报告"
fi

# 4. 验收测试
echo "执行验收测试..."
pytest tests/acceptance/ -v -m acceptance --tb=short > test_reports/acceptance_test_report.txt 2>&1

if [ $? -eq 0 ]; then
    echo "✅ 验收测试通过"
else
    echo "❌ 验收测试失败"
    exit 1
fi

# 5. 压力测试（可选）
read -p "是否执行压力测试？(y/N): " run_stress
if [[ $run_stress =~ ^[Yy]$ ]]; then
    echo "执行压力测试..."
    pytest tests/performance/ -v -m stress --tb=short > test_reports/stress_test_report.txt 2>&1
    
    if [ $? -eq 0 ]; then
        echo "✅ 压力测试通过"
    else
        echo "⚠️ 压力测试发现问题，请检查报告"
    fi
fi

# 生成测试摘要报告
echo "生成测试摘要报告..."
python scripts/generate_test_summary.py

echo "所有测试完成！详细报告请查看 test_reports/ 目录"
echo "测试摘要："
cat test_reports/test_summary.txt
```

### 6. 持续集成配置

```yaml
# .github/workflows/test.yml
name: MediaAgent V2 Tests

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    
    services:
      redis:
        image: redis:7-alpine
        ports:
          - 6379:6379
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
      
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: testpass
          POSTGRES_DB: mediaagent_test
        ports:
          - 5432:5432
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r backend/agent_v2/requirements.txt
        pip install -r backend/agent_v2/requirements-test.txt
    
    - name: Run unit tests
      run: |
        pytest tests/unit/ -v --cov=backend.agent_v2 --cov-report=xml
    
    - name: Run integration tests
      run: |
        pytest tests/integration/ -v
    
    - name: Run performance tests
      run: |
        pytest tests/performance/ -v -m "performance and not stress"
    
    - name: Run acceptance tests
      run: |
        pytest tests/acceptance/ -v -m acceptance
    
    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage.xml
        flags: unittests
        name: codecov-umbrella

  stress-test:
    runs-on: ubuntu-latest
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r backend/agent_v2/requirements.txt
        pip install -r backend/agent_v2/requirements-test.txt
    
    - name: Run stress tests
      run: |
        pytest tests/performance/ -v -m stress --tb=short
```

## 测试报告和监控

### 测试覆盖率要求

- **单元测试覆盖率**: ≥ 90%
- **集成测试覆盖率**: ≥ 80%
- **关键路径覆盖率**: 100%

### 性能基准

- **平均响应时间**: ≤ 15秒
- **95%响应时间**: ≤ 30秒
- **系统可用性**: ≥ 99.5%
- **并发处理能力**: ≥ 10个请求/分钟

### 质量门禁

- 所有单元测试必须通过
- 集成测试成功率 ≥ 95%
- 性能测试不能出现显著退化
- 代码质量检查必须通过
- 安全扫描必须通过

通过这个全面的测试方案，可以确保MediaAgent V2系统的质量、性能和可靠性，为生产环境部署提供充分的信心保障。