# 产品目标
帮我实现符合我产品的Agent，代码写入backend/agent_v3中，并注重配套文档的记录。这个需求融合了清晰的业务场景（视频脚本创作、文章创作、故事创作等业务场景）、具有现代化的Agent架构、以及先进的设计哲学（12-Factor-Agents），并且强调了迭代开发的重要性。整个研发过程不要用模拟数据，请都替换成真实生产活动过程中使用的工具。

# 场景叙事
让我们来演练一下您提到的两种场景。
场景1：输入信息不足，需要补充信息
用户输入 (User Input): "帮我写一个关于‘未来’的视频脚本。" (信息非常模糊)
Planning Coordinator (PC) 分析: PC接收到请求，通过内部逻辑（可以是基于规则，也可以是LLM判断）识别出“未来”这个词过于宽泛，无法制定出具体的Story Agent或Script Agent调用计划。
PC 生成澄清计划: PC不生成完整计划，而是生成一个只包含“人类交互”步骤的初始计划。
plan = [{ "type": "HUMAN_INPUT_CLARIFICATION", "question": "当然可以！为了更好地帮您创作，您能具体说明是哪个领域的'未来'吗？例如：科技、社会、环境，还是个人成长？" }]
Workflow Engine (WE) 执行:
WE启动，读取到计划的第一步是HUMAN_INPUT_CLARIFICATION。
它立刻将工作流状态更新为PAUSED_FOR_USER_INPUT，并将question的内容存入状态。
API层返回: API网关检测到PAUSED状态，于是将存储在状态中的问题返回给前端，呈现给用户。
用户补充信息: 用户回复："我想要关于科技，特别是AI对社会影响的未来。"
工作流恢复 (Resume Workflow): 前端将用户的补充信息通过一个resume API端点提交，并指定工作流ID。
重新规划: WE被唤醒，状态变为RUNNING。它将新的补充信息与原始请求合并，然后再次调用PC。
生成完整计划: 这一次，PC获得了足够的信息（"关于AI对社会影响的未来"），于是它可以生成您在Mermaid图中描绘的完整计划：1.创作故事大纲 -> 2.完善故事内容 -> 3.转换为脚本格式。
继续执行: WE开始按部就班地执行新的、完整的计划。
场景2：返回初步结果让用户确认
这正是您在Mermaid图中描绘的流程，我们的架构完美支持。
PC生成带确认点的计划: PC在初始规划时，就在计划中插入了确认点。
plan = [
{ "agent_name": "StoryAgent", "task": "create_outline", ... },
{ "type": "HUMAN_INPUT_CONFIRMATION", "prompt": "故事大纲已生成，请确认：{last_step_output}" },
{ "agent_name": "StoryAgent", "task": "enhance_story", ... },
...
]
WE执行:
WE成功调用StoryAgent生成大纲。last_step_output现在包含了大纲内容。
WE执行到第二步HUMAN_INPUT_CONFIRMATION。
它将工作流状态设置为 PAUSED_FOR_USER_INPUT，并将格式化后的prompt（包含大纲内容）返回给用户。
用户确认/调整:
如果用户满意 ("很好，继续"): 用户通过resume API发送确认。WE唤醒，继续执行计划的下一步。
如果用户要求调整 ("请调整大纲，增加技术细节"): 用户在resume API中提供了修改意见。WE唤醒，并将这个反馈传递给PC，让PC**基于反馈动态调整（re-plan）**后续的步骤，例如重新调用StoryAgent并附上新的指令。

# Agent架构
Agent开发的路径在backend/agent_v3，参考如下优秀的架构
/agent_v3
├── docs/                      # 新的文档目录
│   ├── architecture.md        # 整体架构设计
│   ├── guides/
│   │   ├── getting-started.md # 如何运行第一个Agent
│   │   └── new-agent-guide.md # 如何开发一个新的Agent
│   └── README.md              # 项目总览
│── core_engine/           # 🧠 核心引擎
│   │   ├── __init__.py
│   │   ├── planning_coordinator.py # 规划协调器
│   │   ├── workflow_engine.py      # 交互式工作流引擎
│   │   ├── agent_registry.py       # 代理注册表
│   │   ├── state_manager.py        # 统一状态管理
│   │   └── prompt_manager.py       # 提示词管理
│   │
│── sub_agents/    # 🎭 专业化Agent
│   │   ├── __init__.py
│   │   ├── base_agent.py           # 所有Agent的基类 (接口)
│   │   └── enhanced_article_agent.py # 增强文章代理 (迁移自Generator)
│   │
│── tools/                   # 🔧 工具生态
│   │   └── __init__.py
│   │
│── llms/                   # 🔧 LLM基础模型
│   │   └── __init__.py
│   │   ├── bedrock.py           # bedrock模型使用
│   │   ├── sd_image.py          # stabily image
│   │
│── infrastructure/          # 🏢 基础设施 (暂时可为空)
│   │   └── __init__.py
│   │
│── main.py                  # 启动入口/API服务 (例如用FastAPI)，暂时不用实现，等我们将Agent全部测通了再实现
│
├── prompts/                   # 存放所有Prompt模板
    └── article_prompts.yaml   # 文章创作相关的Prompt│
├── tests/                   # 存放所有测试代码
    └── 

# Agent研发原则
### Natural Language to Tool Calls
将自然语言转换为结构化的工具调用，由Planning Coordinator负责意图分析。
### Own Your Prompts
通过PromptManager完全控制提示词，支持版本化和优化。
### Own Your Context Window
ContextManager智能管理上下文窗口，确保关键信息不丢失。
### Tools Are Just Structured Outputs
Agent响应结构化的工具调用请求。
### Unify Execution State and Business State
UnifiedStateManager融合执行状态和业务状态，避免状态分离。
### Launch/Pause/Resume with Simple APIs
支持工作流的启动、暂停和恢复操作。
### Contact Humans with Tool Calls
支持人机交互确认点。
### Own Your Control Flow
完全自主控制执行流程，不依赖框架。

# Agent研发计划
1. 按照上述设计实现Agent必须的架构。
2. 迁移文章生成的功能，文章生成脚本在路径src中，借鉴设计思路，迁移功能，放弃langgragh框架，兼容当前的Agent框架。
3. 迁移故事/报告生成功能，对应的代码在backend/agent/tools/recursive中，借鉴设计思路，迁移功能，兼容当前的Agent框架。
4. 依赖的工具都从backend/agent/tools获取，而不是采取模拟数据方式实现。

# 文档记录
1. 包括Agent架构的框架设计，以便于保持前后的代码设计，和框架一致性。
2. 工作计划、执行计划文档的设计，以便于一直根据项目的研发进度。
上述是项目必须的文档，修改完就需要更新对应的文档，保证文档和Agent_v2的代码保持一致。