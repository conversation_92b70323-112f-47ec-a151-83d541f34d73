# MediaGpt项目ID系统详细说明

## 概述

MediaGpt项目使用了多种UUID来标识和管理不同层级的资源。每个ID都有特定的作用域和生命周期，用于实现多用户、多项目、多对话的隔离和管理。

## 1. 核心ID类型及其关系

### 1.1 数据库表结构关系图

```mermaid
graph TD
    A[account_id<br/>用户账户ID<br/>🔑 Primary Key] --> B[project_id<br/>项目ID<br/>📁 Project Container]
    B --> C[thread_id<br/>对话线程ID<br/>💬 Conversation Thread]
    C --> D[message_id<br/>消息ID<br/>📝 Individual Message]
    C --> E[agent_run_id<br/>Agent运行实例ID<br/>🤖 Agent Execution]

    B --> F[sandbox_id<br/>沙盒ID<br/>🐳 Docker Container]
    E --> G[instance_id<br/>Worker实例ID<br/>⚙️ Worker Process]

    style A fill:#e1f5fe
    style B fill:#f3e5f5
    style C fill:#e8f5e8
    style D fill:#fff3e0
    style E fill:#fce4ec
    style F fill:#f1f8e9
    style G fill:#e0f2f1
```

### 1.2 ID层级关系和作用域

```mermaid
graph LR
    subgraph "全局作用域"
        A1[account_id]
    end

    subgraph "用户作用域"
        B1[project_id]
        B2[project_id]
        B3[project_id]
    end

    subgraph "项目作用域"
        C1[thread_id]
        C2[thread_id]
    end

    subgraph "线程作用域"
        D1[message_id]
        D2[message_id]
        D3[message_id]
        E1[agent_run_id]
        E2[agent_run_id]
    end

    A1 --> B1
    A1 --> B2
    A1 --> B3
    B1 --> C1
    B1 --> C2
    C1 --> D1
    C1 --> D2
    C1 --> D3
    C1 --> E1
    C1 --> E2
```

## 2. 各种ID详细说明

### 2.1 account_id (用户账户ID)

**定义**: 用户账户的唯一标识符
**类型**: UUID
**创建时机**: 用户注册时由Supabase Auth自动创建
**作用域**: 全局唯一
**生命周期**: 用户账户存在期间永久有效

**用途**:
- 用户身份识别和认证
- 数据权限控制的根基础
- 所有用户资源的顶级归属标识

**在代码中的体现**:
```sql
-- basejump.accounts表中的主键
account_id UUID PRIMARY KEY

-- 其他表中的外键引用
account_id UUID REFERENCES basejump.accounts(id)
```

### 2.2 project_id (项目ID)

**定义**: 项目的唯一标识符
**类型**: UUID
**创建时机**: 用户创建新项目时
**作用域**: 全局唯一
**生命周期**: 项目存在期间永久有效

**用途**:
- 项目级别的资源隔离
- 沙盒环境的绑定标识
- 多项目管理的基础单位
- 权限控制的中间层

**创建过程**:
```python
# 在 /api/agent/initiate 接口中创建
project = await client.table('projects').insert({
    "project_id": str(uuid.uuid4()),  # 新生成的UUID
    "account_id": account_id,         # 关联到用户账户
    "name": placeholder_name,
    "created_at": datetime.now(timezone.utc).isoformat()
}).execute()
project_id = project.data[0]['project_id']
```

**数据库结构**:
```sql
CREATE TABLE projects (
    project_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name TEXT NOT NULL,
    description TEXT,
    account_id UUID NOT NULL REFERENCES basejump.accounts(id),
    sandbox JSONB DEFAULT '{}'::jsonb,  -- 沙盒信息
    is_public BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE,
    updated_at TIMESTAMP WITH TIME ZONE
);
```

### 2.3 thread_id (对话线程ID)

**定义**: 对话线程的唯一标识符
**类型**: UUID
**创建时机**: 用户开始新对话时
**作用域**: 全局唯一
**生命周期**: 对话线程存在期间永久有效

**用途**:
- 对话上下文的隔离和管理
- 消息归属的标识
- Agent运行实例的绑定
- 实时通信的频道标识

**创建过程**:
```python
# 在 /api/agent/initiate 接口中创建
thread = await client.table('threads').insert({
    "thread_id": str(uuid.uuid4()),  # 新生成的UUID
    "project_id": project_id,        # 关联到项目
    "account_id": account_id,        # 关联到用户账户
    "created_at": datetime.now(timezone.utc).isoformat()
}).execute()
thread_id = thread.data[0]['thread_id']
```

**数据库结构**:
```sql
CREATE TABLE threads (
    thread_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    account_id UUID REFERENCES basejump.accounts(id),
    project_id UUID REFERENCES projects(project_id),
    is_public BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE,
    updated_at TIMESTAMP WITH TIME ZONE
);
```

### 2.4 message_id (消息ID)

**定义**: 单条消息的唯一标识符
**类型**: UUID
**创建时机**: 每次添加消息时（用户消息或AI响应）
**作用域**: 全局唯一
**生命周期**: 消息存在期间永久有效

**用途**:
- 消息的唯一标识和检索
- 消息顺序和关联关系管理
- 消息编辑和删除的目标标识

**创建过程**:
```python
# 在添加用户消息时
message_id = str(uuid.uuid4())
await client.table('messages').insert({
    "message_id": message_id,
    "thread_id": thread_id,
    "type": "user",
    "is_llm_message": True,
    "content": json.dumps(message_payload),
    "created_at": datetime.now(timezone.utc).isoformat()
}).execute()
```

**数据库结构**:
```sql
CREATE TABLE messages (
    message_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    thread_id UUID NOT NULL REFERENCES threads(thread_id),
    type TEXT NOT NULL,  -- 'user', 'assistant', 'tool', 'summary'等
    is_llm_message BOOLEAN NOT NULL DEFAULT TRUE,
    content JSONB NOT NULL,
    metadata JSONB DEFAULT '{}'::jsonb,
    created_at TIMESTAMP WITH TIME ZONE,
    updated_at TIMESTAMP WITH TIME ZONE
);
```

### 2.5 agent_run_id (Agent运行实例ID)

**定义**: Agent运行实例的唯一标识符
**类型**: UUID
**创建时机**: 启动Agent执行任务时
**作用域**: 全局唯一
**生命周期**: Agent运行期间有效，完成后保留用于历史记录

**用途**:
- Agent执行状态的跟踪
- 实时通信频道的标识
- 任务队列中的任务标识
- Redis中状态缓存的键值

**创建过程**:
```python
# 在启动Agent时
agent_run = await client.table('agent_runs').insert({
    "thread_id": thread_id,
    "status": "running",
    "started_at": datetime.now(timezone.utc).isoformat()
}).execute()
agent_run_id = agent_run.data[0]['id']  # 注意：这里字段名是'id'而不是'agent_run_id'
```

**数据库结构**:
```sql
CREATE TABLE agent_runs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),  -- 注意字段名是'id'
    thread_id UUID NOT NULL REFERENCES threads(thread_id),
    status TEXT NOT NULL DEFAULT 'running',  -- 'running', 'completed', 'failed', 'stopped'
    started_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    responses JSONB NOT NULL DEFAULT '[]'::jsonb,
    error TEXT,
    created_at TIMESTAMP WITH TIME ZONE,
    updated_at TIMESTAMP WITH TIME ZONE
);
```

## 3. ID的使用场景和流程

### 3.1 用户创建新对话的完整流程

1. **用户认证**: 获取 `account_id`
2. **创建项目**: 生成新的 `project_id`
3. **创建线程**: 生成新的 `thread_id`
4. **添加消息**: 生成新的 `message_id`
5. **启动Agent**: 生成新的 `agent_run_id`

### 3.2 实时通信中的ID使用

```python
# Redis键值命名规范
response_list_key = f"agent_run:{agent_run_id}:responses"
response_channel = f"agent_run:{agent_run_id}:new_response"
instance_control_channel = f"agent_run:{agent_run_id}:control:{instance_id}"
instance_active_key = f"active_run:{instance_id}:{agent_run_id}"
```

### 3.3 前端路由中的ID使用

```typescript
// URL路径中的ID
/agents/[threadId]           // thread_id作为路由参数
/share/[threadId]           // 公开分享的thread_id
/projects/[projectId]       // project_id作为路由参数

// API调用中的ID传递
GET /api/threads/{thread_id}/messages
POST /api/threads/{thread_id}/agent/start
GET /api/agent-run/{agent_run_id}/stream
```

## 4. ID的权限控制机制

### 4.1 行级安全策略 (Row Level Security)

MediaGpt使用Supabase的RLS机制，基于ID实现细粒度的权限控制：

**项目级权限**:
```sql
-- 用户只能访问自己的项目或公开项目
CREATE POLICY project_select_policy ON projects
    FOR SELECT
    USING (
        is_public = TRUE OR
        basejump.has_role_on_account(account_id) = true
    );
```

**线程级权限**:
```sql
-- 基于项目权限继承线程访问权限
CREATE POLICY thread_select_policy ON threads
    FOR SELECT
    USING (
        basejump.has_role_on_account(account_id) = true OR
        EXISTS (
            SELECT 1 FROM projects
            WHERE projects.project_id = threads.project_id
            AND (projects.is_public = TRUE OR basejump.has_role_on_account(projects.account_id) = true)
        )
    );
```

### 4.2 API层面的权限验证

```python
# 验证用户对线程的访问权限
async def verify_thread_access(client, thread_id: str, user_id: str):
    thread_result = await client.table('threads').select('*').eq('thread_id', thread_id).execute()
    if not thread_result.data:
        raise HTTPException(status_code=404, detail="Thread not found")

    thread = thread_result.data[0]
    if thread['account_id'] != user_id:
        # 检查是否为公开项目
        project_result = await client.table('projects').select('is_public').eq('project_id', thread['project_id']).execute()
        if not project_result.data or not project_result.data[0]['is_public']:
            raise HTTPException(status_code=403, detail="Access denied")
```

## 5. ID在不同组件中的传递和使用

### 5.1 前端状态管理

```typescript
// React Query中的查询键
const threadQuery = useQuery(['thread', threadId], () => getThread(threadId));
const messagesQuery = useQuery(['messages', threadId], () => getMessages(threadId));
const agentRunsQuery = useQuery(['agentRuns', threadId], () => getAgentRuns(threadId));

// Zustand状态存储
interface AppState {
  currentProjectId: string | null;
  currentThreadId: string | null;
  activeAgentRunId: string | null;
}
```

### 5.2 后端任务队列

```python
# Dramatiq任务中的ID传递
@dramatiq.actor
async def run_agent_background(
    agent_run_id: str,    # Agent运行实例ID
    thread_id: str,       # 对话线程ID
    instance_id: str,     # Worker实例ID
    project_id: str,      # 项目ID
    model_name: str,
    # ... 其他参数
):
    # 使用这些ID进行资源定位和状态管理
```

### 5.3 Redis缓存键命名规范

```python
# 基于ID的缓存键命名
response_list_key = f"agent_run:{agent_run_id}:responses"           # Agent响应列表
response_channel = f"agent_run:{agent_run_id}:new_response"         # 新响应通知频道
instance_control_channel = f"agent_run:{agent_run_id}:control:{instance_id}"  # 实例控制频道
global_control_channel = f"agent_run:{agent_run_id}:control"        # 全局控制频道
instance_active_key = f"active_run:{instance_id}:{agent_run_id}"    # 实例活跃状态
```

## 6. ID的生命周期管理

### 6.1 创建顺序

```mermaid
sequenceDiagram
    participant U as User
    participant Auth as Supabase Auth
    participant API as Backend API
    participant DB as Database
    participant Sandbox as Daytona
    participant Worker as Background Worker

    %% 用户注册
    U->>Auth: Register/Login
    Auth->>DB: Create account_id
    Auth-->>U: Authentication token

    %% 用户发起对话
    U->>API: POST /api/agent/initiate

    %% 创建项目
    API->>API: Generate project_id = uuid4()
    API->>DB: INSERT INTO projects (project_id, account_id, name)
    DB-->>API: Project created

    %% 创建线程
    API->>API: Generate thread_id = uuid4()
    API->>DB: INSERT INTO threads (thread_id, project_id, account_id)
    DB-->>API: Thread created

    %% 创建沙盒
    API->>Sandbox: Create sandbox for project_id
    Sandbox-->>API: Return sandbox_id
    API->>DB: UPDATE projects SET sandbox = {...}

    %% 添加用户消息
    API->>API: Generate message_id = uuid4()
    API->>DB: INSERT INTO messages (message_id, thread_id, content)
    DB-->>API: Message saved

    %% 启动Agent
    API->>API: Generate agent_run_id = uuid4()
    API->>DB: INSERT INTO agent_runs (id=agent_run_id, thread_id)
    DB-->>API: Agent run created

    %% 后台任务
    API->>Worker: Send task with all IDs
    Worker->>Worker: Generate instance_id

    API-->>U: Return {thread_id, agent_run_id}
```

**创建顺序说明**:
1. **account_id**: 用户注册时创建，由Supabase Auth管理
2. **project_id**: 用户发起对话时创建
3. **thread_id**: 紧接着project_id创建
4. **sandbox_id**: 项目创建时同步创建沙盒环境
5. **message_id**: 每次添加消息时创建
6. **agent_run_id**: 启动Agent时创建
7. **instance_id**: Worker进程处理任务时生成

### 6.2 清理机制

**Redis缓存清理**:
```python
# Agent运行完成后设置TTL
REDIS_RESPONSE_LIST_TTL = 3600 * 24  # 24小时
await redis.expire(response_list_key, REDIS_RESPONSE_LIST_TTL)

# 清理实例活跃状态
await redis.delete(f"active_run:{instance_id}:{agent_run_id}")
```

**数据库级联删除**:
```sql
-- 删除项目时自动删除相关线程、消息和Agent运行记录
project_id UUID REFERENCES projects(project_id) ON DELETE CASCADE
thread_id UUID REFERENCES threads(thread_id) ON DELETE CASCADE
```

## 7. 特殊ID说明

### 7.1 instance_id (Worker实例ID)

**定义**: Worker进程实例的标识符
**类型**: 字符串 (通常为"single"或UUID的前8位)
**创建时机**: Worker进程启动时
**作用域**: Worker进程内唯一
**生命周期**: Worker进程运行期间

**用途**:
- 多Worker实例的区分
- 任务分配和负载均衡
- 实例级别的控制信号

```python
# Worker实例ID的生成和使用
instance_id = "single"  # 单实例模式
# 或者
instance_id = str(uuid.uuid4())[:8]  # 多实例模式
```

### 7.2 sandbox_id (沙盒ID)

**定义**: Docker沙盒容器的标识符
**类型**: 字符串 (由Daytona生成)
**创建时机**: 创建项目时同时创建沙盒
**作用域**: Daytona平台内唯一
**生命周期**: 项目存在期间

**用途**:
- 代码执行环境的隔离
- 文件系统的访问控制
- 工具执行的安全边界

```python
# 沙盒创建和ID获取
sandbox = create_sandbox(sandbox_pass, project_id)
sandbox_id = sandbox.id  # 由Daytona返回的ID

# 存储在项目的sandbox字段中
'sandbox': {
    'id': sandbox_id,
    'pass': sandbox_pass,
    'vnc_preview': vnc_url,
    'sandbox_url': website_url
}
```

## 8. 常见问题和注意事项

### 8.1 ID字段命名不一致

**注意**: `agent_runs`表中的主键字段名是`id`而不是`agent_run_id`
```sql
-- 正确的字段名
CREATE TABLE agent_runs (
    id UUID PRIMARY KEY,  -- 注意这里是'id'
    thread_id UUID NOT NULL,
    -- ...
);
```

### 8.2 UUID生成方式

**数据库默认生成**:
```sql
project_id UUID PRIMARY KEY DEFAULT gen_random_uuid()
```

**应用层生成**:
```python
project_id = str(uuid.uuid4())
```

### 8.3 ID的传递和验证

**前端URL参数**:
```typescript
// 从URL获取ID
const { threadId } = useParams<{ threadId: string }>();

// 验证ID格式
const isValidUUID = (id: string) => {
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  return uuidRegex.test(id);
};
```

**后端参数验证**:
```python
from pydantic import UUID4

class ThreadRequest(BaseModel):
    thread_id: UUID4  # 自动验证UUID格式
```

这个ID系统设计确保了MediaGpt项目的多租户架构、数据隔离、权限控制和资源管理的有效实现。
