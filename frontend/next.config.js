/** @type {import('next').NextConfig} */
const nextConfig = {
  // 优化构建性能
  experimental: {
    // 减少内存使用
    workerThreads: false,
    // 启用 SWC 编译器优化
    swcMinify: true,
  },
  
  // 构建优化
  compiler: {
    // 移除 console.log (生产环境)
    removeConsole: process.env.NODE_ENV === 'production',
  },
  
  // 图片优化配置
  images: {
    // 允许的图片域名
    domains: ['localhost', '127.0.0.1'],
    // 图片格式优化
    formats: ['image/webp', 'image/avif'],
  },
  
  // 输出配置
  output: 'standalone',
  
  // 禁用 telemetry
  telemetry: false,
  
  // ESLint 配置
  eslint: {
    // 在构建时忽略 ESLint 错误（警告仍会显示）
    ignoreDuringBuilds: false,
  },
  
  // TypeScript 配置
  typescript: {
    // 在构建时忽略 TypeScript 错误
    ignoreBuildErrors: false,
  },
  
  // Webpack 配置
  webpack: (config, { buildId, dev, isServer, defaultLoaders, webpack }) => {
    // 优化内存使用
    if (!dev && !isServer) {
      config.optimization.splitChunks = {
        chunks: 'all',
        cacheGroups: {
          default: {
            minChunks: 1,
            priority: -20,
            reuseExistingChunk: true,
          },
          vendor: {
            test: /[\\/]node_modules[\\/]/,
            name: 'vendors',
            priority: -10,
            chunks: 'all',
          },
        },
      };
    }
    
    return config;
  },
};

module.exports = nextConfig;
