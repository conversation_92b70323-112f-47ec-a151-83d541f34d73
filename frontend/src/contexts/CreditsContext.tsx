import { createContext, useContext, useState, useEffect, ReactNode } from "react";
import { toast } from "sonner";
import { useAuth } from "@/components/AuthProvider";

export type SubscriptionTier = 'basic' | 'plus' | 'pro';

interface SubscriptionPlan {
  tier: SubscriptionTier;
  name: string;
  price: number;
  monthlyCredits: number;
  features: string[];
}

export const SUBSCRIPTION_PLANS: SubscriptionPlan[] = [
  {
    tier: 'basic',
    name: 'Basic',
    price: 19.9,
    monthlyCredits: 1000,
    features: [
      '每月 1000 积分',
      '基础 AI 模型访问',
      '标准客服支持'
    ]
  },
  {
    tier: 'plus',
    name: 'Plus',
    price: 36.9,
    monthlyCredits: 2500,
    features: [
      '每月 2500 积分',
      '高级 AI 模型访问',
      '优先客服支持',
      '更多工具访问权限'
    ]
  },
  {
    tier: 'pro',
    name: 'Pro',
    price: 199,
    monthlyCredits: 10000,
    features: [
      '每月 10000 积分',
      '最新 AI 模型访问',
      '24/7 专属客服支持',
      '所有工具访问权限',
      'API 访问权限'
    ]
  }
];

interface CreditsContextType {
  credits: number;
  monthlyCredits: number;
  usedCredits: number;
  subscriptionTier: SubscriptionTier;
  isLoading: boolean;
  error: Error | null;
  addCredits: (amount: number) => Promise<void>;
  deductCredits: (amount: number) => Promise<boolean>;
  refreshCredits: () => Promise<void>;
  upgradeSubscription: (tier: SubscriptionTier) => Promise<void>;
}

const CreditsContext = createContext<CreditsContextType | undefined>(undefined);

export function CreditsProvider({ children }: { children: ReactNode }) {
  const [credits, setCredits] = useState<number>(0);
  const [monthlyCredits, setMonthlyCredits] = useState<number>(1000);
  const [usedCredits, setUsedCredits] = useState<number>(0);
  const [subscriptionTier, setSubscriptionTier] = useState<SubscriptionTier>('basic');
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  const { user } = useAuth();

  const fetchSubscriptionData = async () => {
    try {
      setIsLoading(true);
      await new Promise(resolve => setTimeout(resolve, 500));
      
      const currentPlan = SUBSCRIPTION_PLANS.find(plan => plan.tier === subscriptionTier) || SUBSCRIPTION_PLANS[0];
      setMonthlyCredits(currentPlan.monthlyCredits);
      setCredits(currentPlan.monthlyCredits - usedCredits);
      setError(null);
    } catch (err) {
      setError(err instanceof Error ? err : new Error('Failed to fetch subscription data'));
      toast.error("获取订阅信息失败", {
        description: "无法获取您的订阅信息，请稍后重试。"
      });
    } finally {
      setIsLoading(false);
    }
  };

  const upgradeSubscription = async (tier: SubscriptionTier) => {
    try {
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const newPlan = SUBSCRIPTION_PLANS.find(plan => plan.tier === tier);
      if (!newPlan) {
        throw new Error('Invalid subscription tier');
      }

      setSubscriptionTier(tier);
      setMonthlyCredits(newPlan.monthlyCredits);
      setCredits(newPlan.monthlyCredits - usedCredits);
      
      toast.success("订阅升级成功", {
        description: `已成功升级到 ${newPlan.name} 订阅`
      });
    } catch (err) {
      toast.error("订阅升级失败", {
        description: "无法升级订阅，请稍后重试。"
      });
      throw err;
    }
  };

  const addCredits = async (amount: number) => {
    try {
      await new Promise(resolve => setTimeout(resolve, 500));
      setCredits(prev => prev + amount);
      toast.success("积分添加成功", {
        description: `已成功添加 ${amount} 积分`
      });
    } catch (err) {
      toast.error("积分添加失败", {
        description: "无法添加积分，请稍后重试。"
      });
      throw err;
    }
  };

  const deductCredits = async (amount: number): Promise<boolean> => {
    try {
      if (credits < amount) {
        toast.error("积分不足", {
          description: "您的积分不足以完成此操作。请考虑升级订阅或等待下月重置。"
        });
        return false;
      }

      await new Promise(resolve => setTimeout(resolve, 500));
      setCredits(prev => prev - amount);
      setUsedCredits(prev => prev + amount);
      return true;
    } catch (err) {
      toast.error("积分扣除失败", {
        description: "无法扣除积分，请稍后重试。"
      });
      return false;
    }
  };

  useEffect(() => {
    if (user) {
      fetchSubscriptionData();
    }
  }, [user, subscriptionTier, usedCredits]);

  return (
    <CreditsContext.Provider
      value={{
        credits,
        monthlyCredits,
        usedCredits,
        subscriptionTier,
        isLoading,
        error,
        addCredits,
        deductCredits,
        refreshCredits: fetchSubscriptionData,
        upgradeSubscription,
      }}
    >
      {children}
    </CreditsContext.Provider>
  );
}

export function useCredits() {
  const context = useContext(CreditsContext);
  if (context === undefined) {
    throw new Error("useCredits must be used within a CreditsProvider");
  }
  return context;
} 