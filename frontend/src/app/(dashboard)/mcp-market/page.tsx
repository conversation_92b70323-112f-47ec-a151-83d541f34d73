'use client';

import React, { useState, useMemo } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useLanguage } from '@/contexts/LanguageContext';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription
} from '@/components/ui/card';
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
  PaginationEllipsis
} from "@/components/ui/pagination";
import {
  Search,
  ChevronDown,
  LayoutGrid,
  Cloud,
  Shield,
  Brain,
  BookOpen,
  Puzzle,
  ArrowLeft,
  ArrowRight,
  ChevronsLeft,
  ChevronsRight
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface MCPItem {
  id: string;
  title: string;
  developer: string;
  category: string;
  popularity: number; // 1-5 for example
  dateAdded: string; // YYYY-MM-DD
  icon: React.ElementType;
  iconBgColor: string;
  imageUrl?: string; // Optional: if specific images are used later
}

const mockMcpItems: MCPItem[] = [
  {
    id: '1',
    title: '数据分析专家',
    developer: '数据解决方案公司',
    category: 'Analytics',
    popularity: 5,
    dateAdded: '2023-10-01',
    icon: LayoutGrid,
    iconBgColor: 'bg-teal-500',
  },
  {
    id: '2',
    title: '云连接器',
    developer: '云科技解决方案',
    category: 'Infrastructure',
    popularity: 4,
    dateAdded: '2023-09-15',
    icon: Cloud,
    iconBgColor: 'bg-sky-600',
  },
  {
    id: '3',
    title: '安全哨兵',
    developer: '安全网络系统',
    category: 'Security',
    popularity: 5,
    dateAdded: '2023-10-05',
    icon: Shield,
    iconBgColor: 'bg-slate-700',
  },
  {
    id: '4',
    title: 'AI助手',
    developer: 'AI创新',
    category: 'AI/ML',
    popularity: 4,
    dateAdded: '2023-08-20',
    icon: Brain,
    iconBgColor: 'bg-indigo-500',
  },
  {
    id: '5',
    title: '工作流自动化',
    developer: '自动化动力',
    category: 'Productivity',
    popularity: 3,
    dateAdded: '2023-10-10',
    icon: BookOpen, // Changed from Settings2 to BookOpen to match image more
    iconBgColor: 'bg-emerald-600',
  },
  {
    id: '6',
    title: 'API集成器',
    developer: 'API连接',
    category: 'Development',
    popularity: 4,
    dateAdded: '2023-07-01',
    icon: Puzzle,
    iconBgColor: 'bg-rose-500',
  },
  // Add more items to test pagination
  {
    id: '7',
    title: '营销大师',
    developer: '广告向导',
    category: 'Marketing',
    popularity: 3,
    dateAdded: '2023-06-10',
    icon: LayoutGrid, // Reusing icons for additional items
    iconBgColor: 'bg-pink-500',
  },
  {
    id: '8',
    title: 'DevOps工具包',
    developer: '代码船公司',
    category: 'Development',
    popularity: 5,
    dateAdded: '2023-05-05',
    icon: Puzzle,
    iconBgColor: 'bg-cyan-500',
  },
  {
    id: '9',
    title: '安全云存储',
    developer: '保险库系统',
    category: 'Infrastructure',
    popularity: 4,
    dateAdded: '2023-04-12',
    icon: Cloud,
    iconBgColor: 'bg-blue-700',
  },
  {
    id: '10',
    title: '智能聊天机器人',
    developer: '机器人构建公司',
    category: 'AI/ML',
    popularity: 4,
    dateAdded: '2023-03-22',
    icon: Brain,
    iconBgColor: 'bg-purple-600',
  },
  {
    id: '11',
    title: '社交媒体调度器',
    developer: '发布完美',
    category: 'Marketing',
    popularity: 3,
    dateAdded: '2023-02-18',
    icon: LayoutGrid,
    iconBgColor: 'bg-orange-500',
  },
];

const ITEMS_PER_PAGE = 10; // Changed to 10

export default function MCPMarketPage() {
  const { t } = useLanguage();
  
  const categories = [
    { value: 'all', label: t('mcpMarket.categories.all') },
    { value: 'Analytics', label: t('mcpMarket.categories.analytics') },
    { value: 'Infrastructure', label: t('mcpMarket.categories.infrastructure') },
    { value: 'Security', label: t('mcpMarket.categories.security') },
    { value: 'AI/ML', label: t('mcpMarket.categories.aiMl') },
    { value: 'Productivity', label: t('mcpMarket.categories.productivity') },
    { value: 'Development', label: t('mcpMarket.categories.development') },
    { value: 'Marketing', label: t('mcpMarket.categories.marketing') },
  ];

  const popularityFilters = [
    { value: 'all', label: t('mcpMarket.popularityFilters.all') },
    { value: '5', label: '★★★★★' },
    { value: '4', label: '★★★★☆' },
    { value: '3', label: '★★★☆☆' },
  ];

  const dateFilters = [
    { value: 'all', label: t('mcpMarket.dateFilters.all') }, 
    { value: 'newest', label: t('mcpMarket.dateFilters.newest') },
    { value: 'oldest', label: t('mcpMarket.dateFilters.oldest') },
  ];
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [selectedPopularity, setSelectedPopularity] = useState('all');
  const [selectedDateSort, setSelectedDateSort] = useState('newest');
  const [currentPage, setCurrentPage] = useState(1);

  const filteredAndSortedMCPs = useMemo(() => {
    let items = mockMcpItems;

    if (searchTerm) {
      items = items.filter(
        (item) =>
          item.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
          item.developer.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    if (selectedCategory !== 'all') {
      items = items.filter((item) => item.category === selectedCategory);
    }

    if (selectedPopularity !== 'all') {
      items = items.filter((item) => item.popularity >= parseInt(selectedPopularity));
    }

    if (selectedDateSort === 'newest') {
      items = items.sort((a, b) => new Date(b.dateAdded).getTime() - new Date(a.dateAdded).getTime());
    } else if (selectedDateSort === 'oldest') {
      items = items.sort((a, b) => new Date(a.dateAdded).getTime() - new Date(b.dateAdded).getTime());
    }

    return items;
  }, [searchTerm, selectedCategory, selectedPopularity, selectedDateSort]);

  const totalPages = Math.ceil(filteredAndSortedMCPs.length / ITEMS_PER_PAGE);
  const paginatedMCPs = filteredAndSortedMCPs.slice(
    (currentPage - 1) * ITEMS_PER_PAGE,
    currentPage * ITEMS_PER_PAGE
  );

  const handlePageChange = (page: number) => {
    if (page >= 1 && page <= totalPages) {
      setCurrentPage(page);
      window.scrollTo({ top: 0, behavior: 'smooth' });
    }
  };

  const renderPageNumbers = () => {
    const pageNumbers = [];
    const maxVisiblePages = 3; 

    if (totalPages <= maxVisiblePages + 2) {
      for (let i = 1; i <= totalPages; i++) {
        pageNumbers.push(
          <PaginationItem key={i}>
            <PaginationLink 
              href="#" 
              isActive={currentPage === i} 
              onClick={(e) => { e.preventDefault(); handlePageChange(i); }}
              className={currentPage === i ? "bg-muted " : "hover:bg-muted/50"}
            >
              {i}
            </PaginationLink>
          </PaginationItem>
        );
      }
    } else {
      let startPage = Math.max(1, currentPage - Math.floor((maxVisiblePages -1) / 2));
      let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);

      if (endPage - startPage + 1 < maxVisiblePages) {
        if(currentPage < (totalPages/2)){
          endPage = Math.min(totalPages, startPage + maxVisiblePages -1);
        } else {
          startPage = Math.max(1, endPage - maxVisiblePages + 1);
        }
      }
      
      if (startPage > 1) {
        pageNumbers.push(
          <PaginationItem key="first">
            <PaginationLink href="#" onClick={(e) => { e.preventDefault(); handlePageChange(1); }} className="hover:bg-muted/50">
              <ChevronsLeft className="h-4 w-4" />
            </PaginationLink>
          </PaginationItem>
        );
        if (startPage > 2) { 
          pageNumbers.push(<PaginationEllipsis key="start-ellipsis" />);
        }
      }

      for (let i = startPage; i <= endPage; i++) {
        pageNumbers.push(
          <PaginationItem key={i}>
            <PaginationLink 
              href="#" 
              isActive={currentPage === i} 
              onClick={(e) => { e.preventDefault(); handlePageChange(i); }}
              className={currentPage === i ? "bg-muted text-primary-foreground" : "hover:bg-muted/50"}
            >
              {i}
            </PaginationLink>
          </PaginationItem>
        );
      }

      if (endPage < totalPages) {
        if (endPage < totalPages - 1) { 
          pageNumbers.push(<PaginationEllipsis key="end-ellipsis" />);
        }
        pageNumbers.push(
          <PaginationItem key="last">
            <PaginationLink href="#" onClick={(e) => { e.preventDefault(); handlePageChange(totalPages); }} className="hover:bg-muted/50">
              <ChevronsRight className="h-4 w-4" />
            </PaginationLink>
          </PaginationItem>
        );
      }
    }
    return pageNumbers;
  };

  return (
    <div className="container mx-auto py-8 px-4 md:px-6 lg:px-8 bg-background text-foreground">
      <header className="flex flex-col sm:flex-row justify-between sm:items-center mb-8">
        <h1 className="text-3xl font-bold mb-4 sm:mb-0">{t('mcpMarket.title')}</h1>
        <Button variant="default" size="lg">{t('mcpMarket.submitMCP')}</Button>
      </header>

      <div className="mb-6 relative">
        <Search className="absolute left-3.5 top-1/2 -translate-y-1/2 h-5 w-5 text-muted-foreground" />
        <Input
          type="search"
          placeholder={t('mcpMarket.searchPlaceholder')}
          className="pl-10 pr-4 py-3 w-full bg-card border-border focus:ring-primary text-base h-12 rounded-md shadow-sm"
          value={searchTerm}
          onChange={(e) => { setSearchTerm(e.target.value); setCurrentPage(1);}}
        />
      </div>

      <div className="flex flex-wrap gap-4 mb-8">
        <Select value={selectedCategory} onValueChange={(value) => { setSelectedCategory(value); setCurrentPage(1);}}>
          <SelectTrigger className="w-full sm:w-auto bg-card border-border h-11 rounded-md shadow-sm data-[placeholder]:text-muted-foreground">
            <SelectValue placeholder={t('mcpMarket.categories.all')} />
          </SelectTrigger>
          <SelectContent className="bg-card border-border">
            {categories.map(cat => (
              <SelectItem key={cat.value} value={cat.value} className="hover:bg-muted/50 focus:bg-muted/80">
                {cat.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        <Select value={selectedPopularity} onValueChange={(value) => { setSelectedPopularity(value); setCurrentPage(1);}}>
          <SelectTrigger className="w-full sm:w-auto bg-card border-border h-11 rounded-md shadow-sm data-[placeholder]:text-muted-foreground">
            <SelectValue placeholder={t('mcpMarket.popularityFilters.all')} />
          </SelectTrigger>
          <SelectContent className="bg-card border-border">
            {popularityFilters.map(pop => (
              <SelectItem key={pop.value} value={pop.value} className="hover:bg-muted/50 focus:bg-muted/80">
                {pop.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        <Select value={selectedDateSort} onValueChange={(value) => {setSelectedDateSort(value); setCurrentPage(1);}}>
          <SelectTrigger className="w-full sm:w-auto bg-card border-border h-11 rounded-md shadow-sm data-[placeholder]:text-muted-foreground">
            <SelectValue placeholder={t('mcpMarket.dateFilters.all')} />
          </SelectTrigger>
          <SelectContent className="bg-card border-border">
            {dateFilters.map(date => (
              <SelectItem key={date.value} value={date.value} className="hover:bg-muted/50 focus:bg-muted/80">
                {date.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {paginatedMCPs.length > 0 ? (
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4 mb-8">
          {paginatedMCPs.map((item) => {
            const Icon = item.icon;
            return (
              <Card key={item.id} className="bg-card border-border shadow-lg rounded-lg overflow-hidden flex flex-col group p-0">
                <CardHeader className="p-0 flex items-center justify-center relative">
                  <div className={cn("w-full aspect-[4/3] flex items-center justify-center", item.iconBgColor)}>
                    <Icon className="w-1/2 h-1/2 text-white/90" />
                  </div>
                </CardHeader>
                <CardContent className="p-3 pt-2 flex flex-col flex-grow">
                  <CardTitle className="text-sm font-semibold mb-1 leading-tight group-hover:text-primary transition-colors line-clamp-2">
                    {item.title}
                  </CardTitle>
                  <CardDescription className="text-xs text-muted-foreground line-clamp-3">
                    {t('mcpMarket.developer')} {item.developer}
                  </CardDescription>
                </CardContent>
              </Card>
            );
          })}
        </div>
      ) : (
        <div className="text-center py-12 text-muted-foreground">
          <Search className="mx-auto h-12 w-12 mb-4 opacity-50" />
          <p className="text-lg">{t('mcpMarket.noResults')}</p>
        </div>
      )}
      
      {totalPages > 1 && (
        <div className="mt-10 flex justify-center">
          <Pagination>
            <PaginationContent>
              <PaginationItem>
                <PaginationPrevious 
                    href="#" // href is present for NextLink behavior
                    onClick={(e) => { e.preventDefault(); handlePageChange(currentPage - 1); }}
                    className={currentPage === 1 ? "pointer-events-none opacity-50" : undefined}
                    title={t('mcpMarket.pagination.previous')}
                />
              </PaginationItem>
              {renderPageNumbers()}
              <PaginationItem>
                <PaginationNext 
                    href="#" // href is present for NextLink behavior
                    onClick={(e) => { e.preventDefault(); handlePageChange(currentPage + 1); }}
                    className={currentPage === totalPages ? "pointer-events-none opacity-50" : undefined}
                    title={t('mcpMarket.pagination.next')}
                />
              </PaginationItem>
            </PaginationContent>
          </Pagination>
        </div>
      )}
    </div>
  );
} 