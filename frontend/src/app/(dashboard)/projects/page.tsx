'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useLanguage } from '@/contexts/LanguageContext';
import {
  Table,
  TableHeader,
  TableBody,
  TableRow,
  TableHead,
  TableCell,
} from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Search, Settings2, Play, Share2, Trash2, Edit3 } from 'lucide-react';

interface Workflow {
  id: string;
  name: string;
  description: string;
  date: string;
  tags: string[];
  status: 'Public' | 'Private';
}

const mockWorkflows: Workflow[] = [
  {
    id: '1',
    name: 'Workflow A',
    description: 'Description of Workflow A',
    date: '2023-01-15',
    tags: ['Tag1', 'Tag2'],
    status: 'Public',
  },
  {
    id: '2',
    name: 'Workflow B',
    description: 'Description of Workflow B',
    date: '2023-02-20',
    tags: ['Tag3'],
    status: 'Private',
  },
  {
    id: '3',
    name: 'Workflow C',
    description: 'Description of Workflow C',
    date: '2023-03-25',
    tags: ['Tag1', 'Tag4'],
    status: 'Public',
  },
  {
    id: '4',
    name: 'Workflow D',
    description: 'Description of Workflow D',
    date: '2023-04-30',
    tags: ['Tag2', 'Tag3'],
    status: 'Private',
  },
  {
    id: '5',
    name: 'Workflow E',
    description: 'Description of Workflow E',
    date: '2023-05-05',
    tags: ['Tag5'],
    status: 'Public',
  },
];

export default function ProjectsPage() {
  const { t } = useLanguage();
  const [searchTerm, setSearchTerm] = useState('');

  const filteredWorkflows = mockWorkflows.filter((workflow) =>
    workflow.name.toLowerCase().includes(searchTerm.toLowerCase()) || 
    workflow.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
    workflow.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  const ActionButton = ({ children, icon: Icon, ...props }: { children: React.ReactNode, icon?: React.ElementType, [key: string]: any }) => (
    <Button variant="ghost" size="sm" className="text-xs p-1 h-auto hover:bg-transparent hover:text-primary" {...props}>
      {Icon && <Icon className="mr-1 h-3 w-3" />}
      {children}
    </Button>
  );

  return (
    <div className="container mx-auto py-8 px-4 md:px-6 lg:px-8 bg-background text-foreground">
      <header className="flex flex-col sm:flex-row justify-between sm:items-center mb-8">
        <h1 className="text-3xl font-bold mb-4 sm:mb-0">{t('projects.title')}</h1>
        <Button>
          {/* <Plus className="mr-2 h-4 w-4" /> */}
          {t('projects.createWorkflow')}
        </Button>
      </header>

      <div className="mb-6 relative">
        <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-5 w-5 text-muted-foreground" />
        <Input
          type="search"
          placeholder={t('projects.searchPlaceholder')}
          className="pl-10 w-full bg-card border-border focus:ring-primary"
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
        />
      </div>

      <h2 className="text-xl font-semibold mb-4">{t('projects.allProjects')}</h2>

      <div className="rounded-lg border border-border bg-card shadow-sm overflow-hidden">
        <Table>
          <TableHeader>
            <TableRow className="hover:bg-transparent border-b-border">
              <TableHead className="w-[200px] text-foreground/80 font-semibold">{t('projects.tableHeaders.name')}</TableHead>
              <TableHead className="text-foreground/80 font-semibold">{t('projects.tableHeaders.description')}</TableHead>
              <TableHead className="w-[120px] text-foreground/80 font-semibold">{t('projects.tableHeaders.date')}</TableHead>
              <TableHead className="w-[180px] text-foreground/80 font-semibold">{t('projects.tableHeaders.tags')}</TableHead>
              <TableHead className="w-[100px] text-foreground/80 font-semibold">{t('projects.tableHeaders.status')}</TableHead>
              <TableHead className="text-right w-[180px] text-foreground/80 font-semibold">{t('projects.tableHeaders.actions')}</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredWorkflows.length > 0 ? (
              filteredWorkflows.map((workflow) => (
                <TableRow key={workflow.id} className="hover:bg-muted/20 border-b-border last:border-b-0">
                  <TableCell className="font-medium py-3">{workflow.name}</TableCell>
                  <TableCell className="text-muted-foreground py-3">{workflow.description}</TableCell>
                  <TableCell className="text-muted-foreground py-3">{workflow.date}</TableCell>
                  <TableCell className="py-3">
                    <div className="flex flex-wrap gap-1">
                      {workflow.tags.map((tag) => (
                        <Badge key={tag} variant="secondary" className="px-2 py-0.5 text-xs bg-muted hover:bg-muted/80 text-muted-foreground font-normal">
                          {tag}
                        </Badge>
                      ))}
                    </div>
                  </TableCell>
                  <TableCell className="py-3">
                    <Badge variant={workflow.status === 'Public' ? 'default' : 'outline'} className={
                        workflow.status === 'Public' 
                        ? "bg-green-500/20 text-green-400 border-green-500/30 hover:bg-green-500/30 text-xs px-2 py-0.5"
                        : "border-amber-500/30 text-amber-400 bg-amber-500/10 hover:bg-amber-500/20 text-xs px-2 py-0.5"
                    }>
                      {workflow.status === 'Public' ? t('projects.status.public') : t('projects.status.private')}
                    </Badge>
                  </TableCell>
                  <TableCell className="text-right py-3">
                    <div className="flex justify-end items-center space-x-0.5">
                        <ActionButton>{t('projects.actions.run')}</ActionButton>
                        <ActionButton>{t('projects.actions.edit')}</ActionButton>
                        <ActionButton>{t('projects.actions.share')}</ActionButton>
                        <ActionButton>{t('projects.actions.delete')}</ActionButton>
                        {/* For a version with icons: */}
                        {/* <ActionButton title="Run Workflow"><Play /></ActionButton>
                        <ActionButton title="Edit Workflow"><Edit3 /></ActionButton>
                        <ActionButton title="Share Workflow"><Share2 /></ActionButton>
                        <ActionButton title="Delete Workflow"><Trash2 /></ActionButton> */}
                    </div>
                  </TableCell>
                </TableRow>
              ))
            ) : (
              <TableRow className="hover:bg-transparent">
                <TableCell colSpan={6} className="h-24 text-center text-muted-foreground">
                  {t('projects.noWorkflows')}
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
    </div>
  );
} 