import EditPersonalAccountName from '@/components/basejump/edit-personal-account-name';
import { createClient } from '@/lib/supabase/server';
import { Alert, AlertTitle, AlertDescription } from '@/components/ui/alert';

export default async function PersonalAccountSettingsPage() {
  const supabaseClient = await createClient();
  
  // Check if Supabase is available
  if (!supabaseClient) {
    return (
      <div className="space-y-6">
        <div>
          <h3 className="text-lg font-medium text-card-title">个人设置</h3>
          <p className="text-sm text-foreground/70">
            管理您的个人账户设置。
          </p>
        </div>
        
        <Alert className="border-orange-300 dark:border-orange-800">
          <AlertTitle>服务不可用</AlertTitle>
          <AlertDescription>
            个人设置功能当前不可用。请联系管理员配置 Supabase 服务。
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  let personalAccount = null;
  let error = null;

  try {
    const { data, error: rpcError } = await supabaseClient.rpc('get_personal_account');
    if (rpcError) {
      throw rpcError;
    }
    personalAccount = data;
  } catch (err) {
    error = err;
    console.error('Failed to load personal account:', err);
  }

  if (error || !personalAccount) {
    return (
      <div className="space-y-6">
        <div>
          <h3 className="text-lg font-medium text-card-title">个人设置</h3>
          <p className="text-sm text-foreground/70">
            管理您的个人账户设置。
          </p>
        </div>
        
        <Alert variant="destructive" className="border-red-300 dark:border-red-800">
          <AlertTitle>加载失败</AlertTitle>
          <AlertDescription>
            无法加载账户信息。请稍后重试或联系客服。
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div>
      <EditPersonalAccountName account={personalAccount} />
    </div>
  );
}
