'use client';
import React, { useState, useMemo } from 'react';
import { motion } from 'framer-motion';
import { ContentCard } from '@/components/discover/ContentCard';
import { ContentListItem } from '@/components/discover/ContentListItem';
import { ContentModal } from '@/components/discover/ContentModal';
import { FilterBar } from '@/components/discover/FilterBar';
import { useLanguage } from '@/contexts/LanguageContext';
import { 
  Pagination, 
  PaginationContent, 
  PaginationEllipsis, 
  PaginationItem, 
  PaginationLink, 
  PaginationNext, 
  PaginationPrevious 
} from '@/components/ui/pagination';
import { ContentCase, FilterOptions } from '@/types/content';
import { mockContentCases } from '@/data/mockData';

export default function DiscoverPage() {
  const { t } = useLanguage();
  const [selectedContent, setSelectedContent] = useState<ContentCase | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [filters, setFilters] = useState<FilterOptions>({
    type: 'all',
    dateRange: 'all',
    sortBy: 'popular',
    tags: [],
    layout: 'grid',
  });

  const [contents, setContents] = useState(mockContentCases);
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 12;

  const filteredContents = useMemo(() => {
    let filtered = [...contents];

    // Search filter
    if (searchQuery) {
      filtered = filtered.filter(content =>
        content.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        content.description?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        content.author.name.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    // Type filter
    if (filters.type !== 'all') {
      filtered = filtered.filter(content => content.type === filters.type);
    }

    // Date filter
    if (filters.dateRange !== 'all') {
      const now = new Date();
      const cutoffDate = new Date();
      
      switch (filters.dateRange) {
        case 'week':
          cutoffDate.setDate(now.getDate() - 7);
          break;
        case 'month':
          cutoffDate.setMonth(now.getMonth() - 1);
          break;
        case 'year':
          cutoffDate.setFullYear(now.getFullYear() - 1);
          break;
      }
      
      filtered = filtered.filter(content => 
        new Date(content.publishedAt) >= cutoffDate
      );
    }

    // Sort
    switch (filters.sortBy) {
      case 'popular':
        filtered.sort((a, b) => b.views - a.views);
        break;
      case 'recent':
        filtered.sort((a, b) => 
          new Date(b.publishedAt).getTime() - new Date(a.publishedAt).getTime()
        );
        break;
      case 'favorites':
        filtered.sort((a, b) => b.favorites - a.favorites);
        break;
    }

    return filtered;
  }, [contents, searchQuery, filters]);

  // Pagination logic
  const totalPages = Math.ceil(filteredContents.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentContents = filteredContents.slice(startIndex, endIndex);

  // Reset to first page when filters change
  React.useEffect(() => {
    setCurrentPage(1);
  }, [searchQuery, filters.type, filters.dateRange, filters.sortBy, filters.tags]);

  const handleContentClick = (content: ContentCase) => {
    setSelectedContent(content);
    setIsModalOpen(true);
  };

  const handleToggleFavorite = (id: string) => {
    setContents(prev => prev.map(content => 
      content.id === id 
        ? { 
            ...content, 
            isFavorited: !content.isFavorited,
            favorites: content.isFavorited ? content.favorites - 1 : content.favorites + 1
          }
        : content
    ));
  };

  return (
    <div className="min-h-screen bg-background">
      <div className=" mx-auto py-8 px-4 md:px-6 lg:px-8 bg-background text-foreground">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-foreground mb-2">{t('discover.title')}</h1>
          <p className="text-muted-foreground">{t('discover.subtitle')}</p>
        </div>

        {/* Filter Bar */}
        <FilterBar
          filters={filters}
          onFiltersChange={setFilters}
          searchQuery={searchQuery}
          onSearchChange={setSearchQuery}
        />

        {/* Results Summary */}
        <div className="mb-6 flex items-center justify-between">
          <p className="text-sm text-muted-foreground">
            {t('discover.resultsSummary').replace('{count}', filteredContents.length.toString())}
            {totalPages > 1 && (
              <span className="ml-2">
                （{t('discover.pageInfo').replace('{current}', currentPage.toString()).replace('{total}', totalPages.toString())}）
              </span>
            )}
          </p>
        </div>

        {/* Content Display */}
        <motion.div 
          className={filters.layout === 'grid' 
            ? "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6"
            : "space-y-4"
          }
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.3 }}
        >
          {currentContents.map((content, index) => (
            <motion.div
              key={content.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: index * 0.05 }}
            >
              {filters.layout === 'grid' ? (
                <ContentCard
                  content={content}
                  onClick={handleContentClick}
                  onToggleFavorite={handleToggleFavorite}
                />
              ) : (
                <ContentListItem
                  content={content}
                  onClick={handleContentClick}
                  onToggleFavorite={handleToggleFavorite}
                />
              )}
            </motion.div>
          ))}
        </motion.div>

        {/* Empty State */}
        {currentContents.length === 0 && (
          <div className="text-center py-16">
            <div className="text-muted-foreground mb-4">
              <svg className="mx-auto h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9.172 16.172a4 4 0 015.656 0M9 12h6m-6-4h6m2 5.291A7.962 7.962 0 0112 15c-2.34 0-4.47-.881-6.08-2.33" />
              </svg>
            </div>
            <h3 className="text-lg font-medium text-foreground mb-2">{t('discover.noResults.title')}</h3>
            <p className="text-muted-foreground">{t('discover.noResults.description')}</p>
          </div>
        )}

        {/* Pagination */}
        {filteredContents.length > 0 && (
          <div className="mt-8">
            <Pagination>
              <PaginationContent>
                <PaginationItem>
                  <PaginationPrevious 
                    onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                    className={currentPage === 1 ? 'pointer-events-none opacity-50' : 'cursor-pointer'}
                    title={t('discover.pagination.previous')}
                  />
                </PaginationItem>
                
                {/* First page */}
                {currentPage > 3 && (
                  <>
                    <PaginationItem>
                      <PaginationLink 
                        onClick={() => setCurrentPage(1)}
                        className="cursor-pointer"
                      >
                        1
                      </PaginationLink>
                    </PaginationItem>
                    {currentPage > 4 && (
                      <PaginationItem>
                        <PaginationEllipsis />
                      </PaginationItem>
                    )}
                  </>
                )}

                {/* Page numbers around current page */}
                {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                  const pageNum = Math.max(1, Math.min(totalPages, currentPage - 2 + i));
                  if (pageNum < 1 || pageNum > totalPages) return null;
                  
                  return (
                    <PaginationItem key={pageNum}>
                      <PaginationLink 
                        onClick={() => setCurrentPage(pageNum)}
                        isActive={pageNum === currentPage}
                        className="cursor-pointer"
                      >
                        {pageNum}
                      </PaginationLink>
                    </PaginationItem>
                  );
                })}

                {/* Last page */}
                {currentPage < totalPages - 2 && (
                  <>
                    {currentPage < totalPages - 3 && (
                      <PaginationItem>
                        <PaginationEllipsis />
                      </PaginationItem>
                    )}
                    <PaginationItem>
                      <PaginationLink 
                        onClick={() => setCurrentPage(totalPages)}
                        className="cursor-pointer"
                      >
                        {totalPages}
                      </PaginationLink>
                    </PaginationItem>
                  </>
                )}

                <PaginationItem>
                  <PaginationNext 
                    onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                    className={currentPage === totalPages ? 'pointer-events-none opacity-50' : 'cursor-pointer'}
                    title={t('discover.pagination.next')}
                  />
                </PaginationItem>
              </PaginationContent>
            </Pagination>
          </div>
        )}

        {/* Content Modal */}
        <ContentModal
          content={selectedContent}
          isOpen={isModalOpen}
          onClose={() => setIsModalOpen(false)}
          onToggleFavorite={handleToggleFavorite}
        />
      </div>
    </div>
  );
};