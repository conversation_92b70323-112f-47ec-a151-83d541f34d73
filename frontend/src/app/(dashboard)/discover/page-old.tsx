'use client';

import React, { useState } from 'react';
import Image from 'next/image';
import { Input } from '@/components/ui/input';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Pagination, PaginationContent, PaginationItem, PaginationLink, PaginationNext, PaginationPrevious, PaginationEllipsis } from '@/components/ui/pagination';
import { Search, ChevronsLeft, ChevronsRight } from 'lucide-react'; // Re-added Chevrons

const categories = [
  { id: 'tools', name: '一起用' },
  { id: 'entertainment', name: '一起玩' },
  { id: 'office', name: '工作计划' }
];

const allItems = [
  // Marketing (Ensure at least 10 for pagination demo)
  { id: 'm1', category: 'tools', title: '账号定位', description: '明确账号定位方向，塑造专属人设开启流量密码', imageUrl: '/discover/m1.png' },
  { id: 'm2', category: 'tools', title: 'logo生成', description: '智能生成专属 logo，快速塑造账号视觉识别符号', imageUrl: '/discover/m2.png' },
  { id: 'm3', category: 'tools', title: '账号起名', description: '自动化邮件活动', imageUrl: '/discover/m3.png' },
  { id: 'm4', category: 'tools', title: '成熟账号分析', description: '深度剖析成熟账号，拆解运营逻辑与爆款内容方法论', imageUrl: '/discover/m4.png' },
  { id: 'm5', category: 'tools', title: '成熟内容分析', description: '解析爆款内容逻辑，提炼高传播性创作技巧与运营策略', imageUrl: '/discover/m5.png' },
  { id: 'm6', category: 'tools', title: '生成同款图片', description: '跟踪社交媒体表智能生成同款风格图片，快速复制高流量视觉创作思路现', imageUrl: '/discover/m6.png' },
  { id: 'm7', category: 'tools', title: '生成同款文章', description: '智能解析爆款框架，一键生成同主题高共鸣优质文章', imageUrl: '/discover/m7.png' },
  { id: 'm8', category: 'tools', title: '生成同款视频', description: '拆解爆款视频逻辑，智能生成同风格高流量创意视频', imageUrl: '/discover/m8.png' },
  { id: 'm9', category: 'tools', title: '图片生成', description: '一键智能生成图片，为账号增添多元视觉吸引力', imageUrl: '/discover/m9.png' },
  { id: 'm10', category: 'tools', title: '文章生成', description: '智能生成优质文章，助力账号高效产出高价值内容', imageUrl: '/discover/m10.png' },
  { id: 'm11', category: 'tools', title: '视频生成', description: '智能生成创意视频，轻松打造高流量账号内容矩阵', imageUrl: '/discover/m11.png' }, 
  { id: 'm12', category: 'tools', title: '近期热点抓取', description: '实时抓取各平台热点，为创作精准锚定流量密码', imageUrl: '/discover/m12.png' }, 
  // Entertainment
  { id: 'e1', category: 'entertainment',  title: '测测你的前世故事', description: '趣味前世故事测试，探寻灵魂深处的神秘记忆', imageUrl: '/discover/e1.png' },
  { id: 'e2', category: 'entertainment',  title: '测测你的修仙灵根', description: '趣味修仙灵根测试！速查你的天赋属性与修炼方向', imageUrl: '/discover/e2.png' },
  { id: 'e3', category: 'entertainment', title: '测测你的未来伴侣', description: '趣味占卜！测未来伴侣特征，解锁情感奇妙联结',imageUrl: '/discover/e3.png' },
  { id: 'e4', category: 'entertainment', title: '测测平行世界的你', description: '趣味前世身份测试，探索神秘前世身份背后的故事', imageUrl: '/discover/e4.png' },
  { id: 'e5', category: 'entertainment', title: '一封情书', description: '定制专属浪漫情书，用文字传递心底的温柔与爱意', imageUrl: '/discover/e5.png' },

  // Office
  { id: 'o1', category: 'office', title: '从0到1打造你的个人小红书账号', description: '从 0 开启小红书账号打造之旅，教你全流程涨粉秘诀', imageUrl: '/discover/o1.png' },
  { id: 'o2', category: 'office', title: '从0到1打造你的个人知乎账号', description: '从 0 搭建知乎账号，解锁精准内容创作与流量密码', imageUrl: '/discover/o2.png' },
  { id: 'o3', category: 'office', title: '从0到1打造你的个人公众号账号', description: '从 0 起步打造公众号，掌握内容运营与粉丝增长法则', imageUrl: '/discover/o3.png' },
  { id: 'o4', category: 'office', title: '从0到1打造你的个人b站账号', description: '从 0 搭建 B 站账号，解锁视频创作、流量获取全攻略', imageUrl: '/discover/o4.png' },
  { id: 'o5', category: 'office', title: '从0到1打造你的个人视频号账号', description: '从 0 开始搭建视频号，掌握内容创作与流量运营技巧', imageUrl: '/discover/o5.png' },
  { id: 'o6', category: 'office', title: '从0到1打造你的个人抖音账号', description: '从 0 起步构建抖音账号，解锁爆款内容与粉丝增长密码', imageUrl: '/discover/o6.png' },
];

const ITEMS_PER_PAGE = 10;

export default function DiscoverPage() {
  const [searchTerm, setSearchTerm] = useState('');
  const [activeTab, setActiveTab] = useState(categories[0].id);
  const [currentPage, setCurrentPage] = useState(1);

  const filteredItemsByTab = allItems.filter(item => item.category === activeTab);
  
  const searchedItems = filteredItemsByTab.filter(item => 
    item.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    item.description.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const totalPages = Math.ceil(searchedItems.length / ITEMS_PER_PAGE);
  const paginatedItems = searchedItems.slice(
    (currentPage - 1) * ITEMS_PER_PAGE,
    currentPage * ITEMS_PER_PAGE
  );

  const handlePageChange = (page: number) => {
    if (page >= 1 && page <= totalPages) {
      setCurrentPage(page);
      window.scrollTo({ top: 0, behavior: 'smooth' });
    }
  };

  // Reinstated renderPageNumbers function
  const renderPageNumbers = () => {
    const pageNumbers = [];
    const maxVisiblePages = 3; // Show up to 3 page numbers, plus prev/next and first/last/ellipsis

    if (totalPages <= maxVisiblePages + 2) { // Show all if not many pages
      for (let i = 1; i <= totalPages; i++) {
        pageNumbers.push(
          <PaginationItem key={i}>
            <PaginationLink 
              href="#" 
              isActive={currentPage === i} 
              onClick={(e) => { e.preventDefault(); handlePageChange(i); }}
              title={`Go to page ${i}`}
            >
              {i}
            </PaginationLink>
          </PaginationItem>
        );
      }
    } else {
      let startPage = Math.max(1, currentPage - Math.floor((maxVisiblePages -1) / 2));
      let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);

      if (endPage - startPage + 1 < maxVisiblePages) {
        if(currentPage < (totalPages/2)){
          endPage = Math.min(totalPages, startPage + maxVisiblePages -1);
        } else {
          startPage = Math.max(1, endPage - maxVisiblePages + 1);
        }
      }
      
      // Always show first page and ellipsis if needed
      if (startPage > 1) {
        pageNumbers.push(
          <PaginationItem key="first">
            <PaginationLink 
              href="#" 
              onClick={(e) => { e.preventDefault(); handlePageChange(1); }}
              title="Go to first page (Page 1)"
            >
              <ChevronsLeft className="h-4 w-4" />
            </PaginationLink>
          </PaginationItem>
        );
        if (startPage > 2) { // Show ellipsis if there's a gap after first page
          pageNumbers.push(<PaginationEllipsis key="start-ellipsis" />);
        }
      }

      for (let i = startPage; i <= endPage; i++) {
        pageNumbers.push(
          <PaginationItem key={i}>
            <PaginationLink 
              href="#" 
              isActive={currentPage === i} 
              onClick={(e) => { e.preventDefault(); handlePageChange(i); }}
              title={`Go to page ${i}`}
            >
              {i}
            </PaginationLink>
          </PaginationItem>
        );
      }

      // Always show last page and ellipsis if needed
      if (endPage < totalPages) {
        if (endPage < totalPages - 1) { // Show ellipsis if there's a gap before last page
          pageNumbers.push(<PaginationEllipsis key="end-ellipsis" />);
        }
        pageNumbers.push(
          <PaginationItem key="last">
            <PaginationLink 
              href="#" 
              onClick={(e) => { e.preventDefault(); handlePageChange(totalPages); }}
              title={`Go to last page (Page ${totalPages})`}
            >
              <ChevronsRight className="h-4 w-4" />
            </PaginationLink>
          </PaginationItem>
        );
      }
    }
    return pageNumbers;
  };

  return (
    <div className="container mx-auto py-8 px-4 md:px-6 lg:px-8">
      {/* Search Bar */}
      <div className="mb-8 relative">
        <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-5 w-5 text-muted-foreground" />
        <Input
          type="search"
          placeholder="搜索..."
          className="pl-10 w-full bg-background"
          value={searchTerm}
          onChange={(e) => {
            setSearchTerm(e.target.value);
            setCurrentPage(1);
          }}
        />
      </div>

      {/* Tabs */}
      <Tabs value={activeTab} onValueChange={(value) => { setActiveTab(value); setCurrentPage(1); }} className="w-full">
        <TabsList className="grid w-full grid-cols-2 sm:grid-cols-3 md:grid-cols-6 mb-6 bg-muted/30 rounded-lg">
          {categories.map((category) => (
            <TabsTrigger 
              key={category.id} 
              value={category.id}
              className="py-2 px-3 rounded-md text-muted-foreground data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm data-[state=active]:font-medium hover:text-foreground hover:bg-background/60 transition-all duration-200"
            >
              {category.name}
            </TabsTrigger>
          ))}
        </TabsList>

        {categories.map((category) => (
          <TabsContent key={category.id} value={category.id}>
            {paginatedItems.length > 0 ? (
                <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-6">
                {paginatedItems.map((item) => (
                  <Card key={item.id} className="pt-0 overflow-hidden flex flex-col group hover:shadow-lg transition-shadow duration-200 bg-card" >
                    <CardHeader className="p-0 space-y-0">
                      <div className="relative pt-[100%] w-full">
                        <Image 
                          src={item.imageUrl} 
                          alt={item.title} 
                          fill
                          className="object-cover group-hover:scale-105 transition-transform duration-300" 
                        /> 
                      </div>
                    </CardHeader>
                    <CardContent className="p-4 flex-grow flex flex-col gap-2">
                      <CardTitle className="text-sm font-medium leading-tight line-clamp-2">{item.title}</CardTitle>
                      <CardDescription className="text-xs text-muted-foreground line-clamp-2">{item.description}</CardDescription>
                    </CardContent>
                  </Card>
                ))}
              </div>
            ) : (
              <div className="text-center py-12 text-muted-foreground">
                <Search className="mx-auto h-12 w-12 mb-4 opacity-50" />
                <p className="text-lg">在 {categories.find(c=>c.id === activeTab)?.name} 中未找到 "{searchTerm}" 相关项目。</p>
                <p className="text-sm">尝试不同的搜索词或类别。</p>
              </div>
            )}
          </TabsContent>
        ))}
      </Tabs>

      {/* Reinstated Pagination UI */}
      {totalPages > 1 && (
        <div className="mt-10 flex justify-center">
          <Pagination>
            <PaginationContent>
              <PaginationItem>
                <PaginationPrevious 
                    href="#" // href is present for NextLink behavior
                    onClick={(e) => { e.preventDefault(); handlePageChange(currentPage - 1); }}
                    className={currentPage === 1 ? "pointer-events-none opacity-50" : undefined}
                    title="上一页"
                />
              </PaginationItem>
              {renderPageNumbers()}
              <PaginationItem>
                <PaginationNext 
                    href="#" // href is present for NextLink behavior
                    onClick={(e) => { e.preventDefault(); handlePageChange(currentPage + 1); }}
                    className={currentPage === totalPages ? "pointer-events-none opacity-50" : undefined}
                    title="下一页"
                />
              </PaginationItem>
            </PaginationContent>
          </Pagination>
        </div>
      )}
    </div>
  );
} 