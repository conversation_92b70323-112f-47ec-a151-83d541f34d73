import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Agent Conversation | LoomuAI',
  description: 'Interactive agent conversation powered by LoomuAI',
  openGraph: {
    title: 'Agent Conversation | LoomuAI',
    description: 'Interactive agent conversation powered by LoomuAI',
    type: 'website',
  },
};

export default function AgentsLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return <>{children}</>;
}
