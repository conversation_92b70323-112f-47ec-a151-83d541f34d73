'use client';

import React, { useState } from 'react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogFooter, 
  DialogHeader, 
  DialogTitle, 
  DialogTrigger 
} from '@/components/ui/dialog';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import { 
  Users, 
  Plus, 
  Copy, 
  ExternalLink, 
  Trash2, 
  Check, 
  X 
} from 'lucide-react';
import { toast } from 'sonner';

// 模拟数据类型
interface InviteCode {
  id: string;
  code: string;
  createdAt: string;
  usageLimit: number;
  usedCount: number;
  status: 'active' | 'expired' | 'disabled';
  expiresAt?: string;
}

// 模拟数据
const mockInviteCodes: InviteCode[] = [
  {
    id: '1',
    code: 'WELCOME2024',
    createdAt: '2024-01-15',
    usageLimit: 10,
    usedCount: 3,
    status: 'active',
    expiresAt: '2024-12-31'
  },
  {
    id: '2',
    code: 'FRIEND50',
    createdAt: '2024-01-10',
    usageLimit: 5,
    usedCount: 5,
    status: 'expired',
    expiresAt: '2024-06-30'
  },
  {
    id: '3',
    code: 'BETA2024',
    createdAt: '2024-01-01',
    usageLimit: 100,
    usedCount: 45,
    status: 'active',
    expiresAt: '2024-12-31'
  }
];

export default function InvitationsPage() {
  const [inviteCodes, setInviteCodes] = useState<InviteCode[]>(mockInviteCodes);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [newCodeData, setNewCodeData] = useState({
    usageLimit: 10,
    expiresAt: ''
  });

  // 生成邀请码
  const generateInviteCode = () => {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    let result = '';
    for (let i = 0; i < 8; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  };

  // 创建邀请码
  const handleCreateInviteCode = () => {
    const newCode: InviteCode = {
      id: Date.now().toString(),
      code: generateInviteCode(),
      createdAt: new Date().toISOString().split('T')[0],
      usageLimit: newCodeData.usageLimit,
      usedCount: 0,
      status: 'active',
      expiresAt: newCodeData.expiresAt || undefined
    };

    setInviteCodes([newCode, ...inviteCodes]);
    setIsCreateDialogOpen(false);
    setNewCodeData({ usageLimit: 10, expiresAt: '' });
    toast.success('邀请码创建成功！');
  };

  // 复制邀请链接
  const copyInviteLink = (code: string) => {
    const inviteUrl = `${window.location.origin}/auth?mode=signup&inviteCode=${code}`;
    navigator.clipboard.writeText(inviteUrl);
    toast.success('邀请链接已复制到剪贴板！');
  };

  // 删除邀请码
  const deleteInviteCode = (id: string) => {
    setInviteCodes(inviteCodes.filter(code => code.id !== id));
    toast.success('邀请码已删除！');
  };

  // 获取状态徽章
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return (
          <Badge 
            variant="outline" 
            className="bg-emerald-50 text-emerald-700 border-emerald-200 dark:bg-emerald-950/20 dark:text-emerald-400 dark:border-emerald-800/30"
          >
            有效
          </Badge>
        );
      case 'expired':
        return (
          <Badge 
            variant="outline" 
            className="bg-orange-50 text-orange-700 border-orange-200 dark:bg-orange-950/20 dark:text-orange-400 dark:border-orange-800/30"
          >
            已过期
          </Badge>
        );
      case 'disabled':
        return (
          <Badge 
            variant="outline" 
            className="bg-red-50 text-red-700 border-red-200 dark:bg-red-950/20 dark:text-red-400 dark:border-red-800/30"
          >
            已禁用
          </Badge>
        );
      default:
        return (
          <Badge 
            variant="outline" 
            className="bg-gray-50 text-gray-700 border-gray-200 dark:bg-gray-950/20 dark:text-gray-400 dark:border-gray-800/30"
          >
            未知
          </Badge>
        );
    }
  };

  return (
    <div className="min-h-screen bg-background">
      {/* 主容器，增加边距 */}
      <div className="container mx-auto py-8 px-4 md:px-6 lg:px-8">
        <div className="space-y-8">
          {/* 页面标题 */}
          <div className="flex flex-col sm:flex-row sm:justify-between sm:items-start gap-4">
            <div className="space-y-2">
              <h1 className="text-3xl font-bold tracking-tight text-foreground">邀请好友</h1>
              <p className="text-muted-foreground">
                管理您的邀请码，邀请朋友加入平台
              </p>
            </div>
            <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
              <DialogTrigger asChild>
                <Button className="shrink-0">
                  <Plus className="mr-2 h-4 w-4" />
                  创建邀请码
                </Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-md">
                <DialogHeader>
                  <DialogTitle>创建新的邀请码</DialogTitle>
                  <DialogDescription>
                    设置邀请码的使用限制和有效期
                  </DialogDescription>
                </DialogHeader>
                <div className="space-y-4">
                  <div>
                    <label className="text-sm font-medium text-foreground">使用次数限制</label>
                    <Input
                      type="number"
                      min="1"
                      max="1000"
                      value={newCodeData.usageLimit}
                      onChange={(e) => setNewCodeData({ ...newCodeData, usageLimit: parseInt(e.target.value) || 1 })}
                      className="mt-2"
                    />
                  </div>
                  <div>
                    <label className="text-sm font-medium text-foreground">过期时间（可选）</label>
                    <Input
                      type="date"
                      value={newCodeData.expiresAt}
                      onChange={(e) => setNewCodeData({ ...newCodeData, expiresAt: e.target.value })}
                      className="mt-2"
                    />
                  </div>
                </div>
                <DialogFooter>
                  <Button variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
                    取消
                  </Button>
                  <Button onClick={handleCreateInviteCode}>
                    创建邀请码
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </div>

          {/* 统计卡片 */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
            <Card className="bg-card/50 backdrop-blur-sm border-border/50">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-muted-foreground">总邀请码</CardTitle>
                <div className="h-8 w-8 bg-blue-100 dark:bg-blue-950/20 rounded-lg flex items-center justify-center">
                  <Users className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-foreground">{inviteCodes.length}</div>
                <p className="text-xs text-muted-foreground mt-1">
                  已创建的邀请码数量
                </p>
              </CardContent>
            </Card>
            <Card className="bg-card/50 backdrop-blur-sm border-border/50">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-muted-foreground">活跃邀请码</CardTitle>
                <div className="h-8 w-8 bg-emerald-100 dark:bg-emerald-950/20 rounded-lg flex items-center justify-center">
                  <Check className="h-4 w-4 text-emerald-600 dark:text-emerald-400" />
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-foreground">
                  {inviteCodes.filter(code => code.status === 'active').length}
                </div>
                <p className="text-xs text-muted-foreground mt-1">
                  当前可用的邀请码
                </p>
              </CardContent>
            </Card>
            <Card className="bg-card/50 backdrop-blur-sm border-border/50">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-muted-foreground">总使用次数</CardTitle>
                <div className="h-8 w-8 bg-purple-100 dark:bg-purple-950/20 rounded-lg flex items-center justify-center">
                  <ExternalLink className="h-4 w-4 text-purple-600 dark:text-purple-400" />
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-foreground">
                  {inviteCodes.reduce((sum, code) => sum + code.usedCount, 0)}
                </div>
                <p className="text-xs text-muted-foreground mt-1">
                  累计邀请成功次数
                </p>
              </CardContent>
            </Card>
          </div>

          {/* 邀请码列表 */}
          <Card className="bg-card/50 backdrop-blur-sm border-border/50">
            <CardHeader>
              <CardTitle className="text-foreground">邀请码管理</CardTitle>
              <CardDescription>
                查看和管理您的所有邀请码
              </CardDescription>
            </CardHeader>
            <CardContent className="p-0">
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow className="border-border/50">
                      <TableHead className="text-muted-foreground font-medium">邀请码</TableHead>
                      <TableHead className="text-muted-foreground font-medium">状态</TableHead>
                      <TableHead className="text-muted-foreground font-medium">使用情况</TableHead>
                      <TableHead className="text-muted-foreground font-medium">创建时间</TableHead>
                      <TableHead className="text-muted-foreground font-medium">过期时间</TableHead>
                      <TableHead className="text-right text-muted-foreground font-medium">操作</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {inviteCodes.map((code) => (
                      <TableRow key={code.id} className="border-border/50 hover:bg-muted/30 transition-colors">
                        <TableCell className="py-4">
                          <div className="font-mono font-medium text-sm bg-muted/50 px-2.5 py-1.5 rounded-md inline-block text-foreground">
                            {code.code}
                          </div>
                        </TableCell>
                        <TableCell className="py-4">
                          {getStatusBadge(code.status)}
                        </TableCell>
                        <TableCell className="py-4">
                          <div className="flex items-center gap-3 min-w-[120px]">
                            <span className="text-sm font-medium text-foreground shrink-0">
                              {code.usedCount} / {code.usageLimit}
                            </span>
                            <div className="flex-1 bg-muted/60 rounded-full h-2.5 overflow-hidden">
                              <div 
                                className="h-full rounded-full bg-gradient-to-r from-blue-500 to-blue-600 transition-all duration-300 ease-out" 
                                style={{ width: `${Math.min((code.usedCount / code.usageLimit) * 100, 100)}%` }}
                              />
                            </div>
                          </div>
                        </TableCell>
                        <TableCell className="py-4">
                          <span className="text-sm text-muted-foreground">{code.createdAt}</span>
                        </TableCell>
                        <TableCell className="py-4">
                          <span className="text-sm text-muted-foreground">{code.expiresAt || '永不过期'}</span>
                        </TableCell>
                        <TableCell className="text-right py-4">
                          <div className="flex justify-end gap-2">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => copyInviteLink(code.code)}
                              disabled={code.status !== 'active'}
                              className="h-8 w-8 p-0 hover:bg-blue-50 hover:border-blue-200 dark:hover:bg-blue-950/20 dark:hover:border-blue-800/30"
                            >
                              <Copy className="h-3.5 w-3.5" />
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => deleteInviteCode(code.id)}
                              className="h-8 w-8 p-0 text-red-600 hover:text-red-700 hover:bg-red-50 hover:border-red-200 dark:text-red-400 dark:hover:text-red-300 dark:hover:bg-red-950/20 dark:hover:border-red-800/30"
                            >
                              <Trash2 className="h-3.5 w-3.5" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
} 