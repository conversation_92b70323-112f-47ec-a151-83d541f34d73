'use client';

import React from 'react';
import { createClient } from '@/lib/supabase/server';
import AccountBillingStatus from '@/components/billing/account-billing-status';
import { Alert, AlertTitle, AlertDescription } from '@/components/ui/alert';

const returnUrl = process.env.NEXT_PUBLIC_URL as string;

type AccountParams = {
  accountSlug: string;
};

export default function TeamBillingPage({
  params,
}: {
  params: Promise<AccountParams>;
}) {
  const unwrappedParams = React.use(params);
  const { accountSlug } = unwrappedParams;

  // Use an effect to load team account data
  const [teamAccount, setTeamAccount] = React.useState<any>(null);
  const [error, setError] = React.useState<string | null>(null);
  const [loading, setLoading] = React.useState(true);
  const [supabaseAvailable, setSupabaseAvailable] = React.useState(true);

  React.useEffect(() => {
    async function loadData() {
      try {
        const supabaseClient = await createClient();
        
        // Check if Supabase is available
        if (!supabaseClient) {
          setSupabaseAvailable(false);
          setLoading(false);
          return;
        }

        const { data, error: rpcError } = await supabaseClient.rpc('get_account_by_slug', {
          slug: accountSlug,
        });
        
        if (rpcError) {
          throw rpcError;
        }
        
        setTeamAccount(data);
      } catch (err) {
        setError('无法加载账户数据');
        console.error(err);
      } finally {
        setLoading(false);
      }
    }

    loadData();
  }, [accountSlug]);

  if (loading) {
    return <div>加载中...</div>;
  }

  if (!supabaseAvailable) {
    return (
      <div className="space-y-6">
        <div>
          <h3 className="text-lg font-medium text-card-title">团队账单</h3>
          <p className="text-sm text-foreground/70">
            管理您团队的订阅和账单详情。
          </p>
        </div>
        
        <Alert className="border-orange-300 dark:border-orange-800">
          <AlertTitle>服务不可用</AlertTitle>
          <AlertDescription>
            账单管理功能当前不可用。请联系管理员配置 Supabase 服务。
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-6">
        <div>
          <h3 className="text-lg font-medium text-card-title">团队账单</h3>
          <p className="text-sm text-foreground/70">
            管理您团队的订阅和账单详情。
          </p>
        </div>
        
        <Alert
          variant="destructive"
          className="border-red-300 dark:border-red-800 rounded-xl"
        >
          <AlertTitle>错误</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      </div>
    );
  }

  if (!teamAccount) {
    return (
      <div className="space-y-6">
        <div>
          <h3 className="text-lg font-medium text-card-title">团队账单</h3>
          <p className="text-sm text-foreground/70">
            管理您团队的订阅和账单详情。
          </p>
        </div>
        
        <Alert
          variant="destructive"
          className="border-red-300 dark:border-red-800 rounded-xl"
        >
          <AlertTitle>未找到账户</AlertTitle>
          <AlertDescription>
            找不到指定的团队账户。
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  if (teamAccount.account_role !== 'owner') {
    return (
      <div className="space-y-6">
        <div>
          <h3 className="text-lg font-medium text-card-title">团队账单</h3>
          <p className="text-sm text-foreground/70">
            管理您团队的订阅和账单详情。
          </p>
        </div>
        
        <Alert
          variant="destructive"
          className="border-red-300 dark:border-red-800 rounded-xl"
        >
          <AlertTitle>访问被拒绝</AlertTitle>
          <AlertDescription>
            您没有权限访问此页面。
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium text-card-title">团队账单</h3>
        <p className="text-sm text-foreground/70">
          管理您团队的订阅和账单详情。
        </p>
      </div>

      <AccountBillingStatus
        accountId={teamAccount.account_id}
        returnUrl={`${returnUrl}/${accountSlug}/settings/billing`}
      />
    </div>
  );
}
