'use client';

import React from 'react';
import { createClient } from '@/lib/supabase/server';
import ManageTeamMembers from '@/components/basejump/manage-team-members';
import ManageTeamInvitations from '@/components/basejump/manage-team-invitations';
import { Alert, AlertTitle, AlertDescription } from '@/components/ui/alert';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';

type AccountParams = {
  accountSlug: string;
};

export default function TeamMembersPage({
  params,
}: {
  params: Promise<AccountParams>;
}) {
  const unwrappedParams = React.use(params);
  const { accountSlug } = unwrappedParams;

  // Use an effect to load team account data
  const [teamAccount, setTeamAccount] = React.useState<any>(null);
  const [loading, setLoading] = React.useState(true);
  const [error, setError] = React.useState<string | null>(null);
  const [supabaseAvailable, setSupabaseAvailable] = React.useState(true);

  React.useEffect(() => {
    async function loadData() {
      try {
        const supabaseClient = await createClient();
        
        // Check if Supabase is available
        if (!supabaseClient) {
          setSupabaseAvailable(false);
          setLoading(false);
          return;
        }

        const { data, error: rpcError } = await supabaseClient.rpc('get_account_by_slug', {
          slug: accountSlug,
        });
        
        if (rpcError) {
          throw rpcError;
        }
        
        setTeamAccount(data);
      } catch (err) {
        setError('无法加载团队数据');
        console.error(err);
      } finally {
        setLoading(false);
      }
    }

    loadData();
  }, [accountSlug]);

  if (loading) {
    return <div>加载中...</div>;
  }

  if (!supabaseAvailable) {
    return (
      <div className="space-y-6">
        <div>
          <h3 className="text-lg font-medium text-card-title">团队成员</h3>
          <p className="text-sm text-foreground/70">
            管理您的团队成员和邀请。
          </p>
        </div>
        
        <Alert className="border-orange-300 dark:border-orange-800">
          <AlertTitle>服务不可用</AlertTitle>
          <AlertDescription>
            团队成员管理功能当前不可用。请联系管理员配置 Supabase 服务。
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-6">
        <div>
          <h3 className="text-lg font-medium text-card-title">团队成员</h3>
          <p className="text-sm text-foreground/70">
            管理您的团队成员和邀请。
          </p>
        </div>
        
        <Alert
          variant="destructive"
          className="border-red-300 dark:border-red-800 rounded-xl"
        >
          <AlertTitle>错误</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      </div>
    );
  }

  if (!teamAccount || teamAccount.account_role !== 'owner') {
    return (
      <div className="space-y-6">
        <div>
          <h3 className="text-lg font-medium text-card-title">团队成员</h3>
          <p className="text-sm text-foreground/70">
            管理您的团队成员和邀请。
          </p>
        </div>
        
        <Alert
          variant="destructive"
          className="border-red-300 dark:border-red-800 rounded-xl"
        >
          <AlertTitle>访问被拒绝</AlertTitle>
          <AlertDescription>
            您没有权限访问此页面。
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium text-card-title">团队成员</h3>
        <p className="text-sm text-foreground/70">
          管理您的团队成员和邀请。
        </p>
      </div>

      <Card className="border-subtle dark:border-white/10 bg-white dark:bg-background-secondary shadow-none">
        <CardHeader>
          <CardTitle className="text-base text-card-title">
            邀请
          </CardTitle>
          <CardDescription>邀请新成员加入您的团队。</CardDescription>
        </CardHeader>
        <CardContent>
          <ManageTeamInvitations accountId={teamAccount.account_id} />
        </CardContent>
      </Card>

      <Card className="border-subtle dark:border-white/10 bg-white dark:bg-background-secondary shadow-none">
        <CardHeader>
          <CardTitle className="text-base text-card-title">成员</CardTitle>
          <CardDescription>管理现有团队成员。</CardDescription>
        </CardHeader>
        <CardContent>
          <ManageTeamMembers accountId={teamAccount.account_id} />
        </CardContent>
      </Card>
    </div>
  );
}
