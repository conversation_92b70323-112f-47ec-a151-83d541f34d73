'use client';

import React from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from '@/components/ui/tabs';
import EditTeamName from '@/components/basejump/edit-team-name';
import EditTeamSlug from '@/components/basejump/edit-team-slug';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { createClient } from '@/lib/supabase/server';
import { Alert, AlertTitle, AlertDescription } from '@/components/ui/alert';

type AccountParams = {
  accountSlug: string;
};

export default function TeamSettingsPage({
  params,
}: {
  params: Promise<AccountParams>;
}) {
  const unwrappedParams = React.use(params);
  const { accountSlug } = unwrappedParams;

  // Use an effect to load team account data
  const [teamAccount, setTeamAccount] = React.useState<any>(null);
  const [loading, setLoading] = React.useState(true);
  const [error, setError] = React.useState<string | null>(null);
  const [supabaseAvailable, setSupabaseAvailable] = React.useState(true);

  React.useEffect(() => {
    async function loadData() {
      try {
        const supabaseClient = await createClient();
        
        // Check if Supabase is available
        if (!supabaseClient) {
          setSupabaseAvailable(false);
          setLoading(false);
          return;
        }

        const { data, error: rpcError } = await supabaseClient.rpc('get_account_by_slug', {
          slug: accountSlug,
        });
        
        if (rpcError) {
          throw rpcError;
        }
        
        setTeamAccount(data);
      } catch (err) {
        setError('无法加载账户数据');
        console.error(err);
      } finally {
        setLoading(false);
      }
    }

    loadData();
  }, [accountSlug]);

  if (loading) {
    return <div>加载中...</div>;
  }

  if (!supabaseAvailable) {
    return (
      <div className="space-y-6">
        <div>
          <h3 className="text-lg font-medium text-card-title">团队设置</h3>
          <p className="text-sm text-foreground/70">
            管理您的团队账户详情。
          </p>
        </div>
        
        <Alert className="border-orange-300 dark:border-orange-800">
          <AlertTitle>服务不可用</AlertTitle>
          <AlertDescription>
            团队设置功能当前不可用。请联系管理员配置 Supabase 服务。
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-6">
        <div>
          <h3 className="text-lg font-medium text-card-title">团队设置</h3>
          <p className="text-sm text-foreground/70">
            管理您的团队账户详情。
          </p>
        </div>
        
        <Alert variant="destructive" className="border-red-300 dark:border-red-800">
          <AlertTitle>错误</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      </div>
    );
  }

  if (!teamAccount) {
    return (
      <div className="space-y-6">
        <div>
          <h3 className="text-lg font-medium text-card-title">团队设置</h3>
          <p className="text-sm text-foreground/70">
            管理您的团队账户详情。
          </p>
        </div>
        
        <Alert variant="destructive" className="border-red-300 dark:border-red-800">
          <AlertTitle>未找到账户</AlertTitle>
          <AlertDescription>找不到指定的团队账户。</AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium text-card-title">团队设置</h3>
        <p className="text-sm text-foreground/70">
          管理您的团队账户详情。
        </p>
      </div>

      <Card className="border-subtle dark:border-white/10 bg-white dark:bg-background-secondary shadow-none">
        <CardHeader>
          <CardTitle className="text-base text-card-title">团队名称</CardTitle>
          <CardDescription>更新您的团队名称。</CardDescription>
        </CardHeader>
        <CardContent>
          <EditTeamName account={teamAccount} />
        </CardContent>
      </Card>

      <Card className="border-subtle dark:border-white/10 bg-white dark:bg-background-secondary shadow-none">
        <CardHeader>
          <CardTitle className="text-base text-card-title">团队 URL</CardTitle>
          <CardDescription>更新您的团队 URL 标识。</CardDescription>
        </CardHeader>
        <CardContent>
          <EditTeamSlug account={teamAccount} />
        </CardContent>
      </Card>
    </div>
  );
}
