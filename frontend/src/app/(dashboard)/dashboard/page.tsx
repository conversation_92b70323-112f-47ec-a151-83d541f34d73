'use client';

import React, { useState, Suspense, useEffect, useRef, useMemo, useCallback, lazy } from 'react';
import { Skeleton } from '@/components/ui/skeleton';
import { useRouter } from 'next/navigation';
import { Menu, Sparkles, Sun, RefreshCw, MessageSquare, Compass as CompassIcon } from 'lucide-react';
import {
  ChatInput,
  ChatInputHandles,
} from '@/components/thread/chat-input/chat-input';
import {
  initiateAgent,
  createThread,
  addUserMessage,
  startAgent,
  createProject,
  BillingError,
} from '@/lib/api';
import { generateThreadName } from '@/lib/actions/threads';
import { useIsMobile } from '@/hooks/use-mobile';
import { useSidebar } from '@/components/ui/sidebar';
import { Button } from '@/components/ui/button';
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { useBillingError } from '@/hooks/useBillingError';
import { useAccounts } from '@/hooks/use-accounts';
import { isLocalMode, config } from '@/lib/config';
import { toast } from 'sonner';
import { cn } from '@/lib/utils';
import { useLanguage } from '@/contexts/LanguageContext';

// 懒加载组件
const BillingErrorAlert = lazy(() => 
  import('@/components/billing/usage-limit-alert').then(module => ({
    default: module.BillingErrorAlert
  }))
);

// Constant for localStorage key to ensure consistency
const PENDING_PROMPT_KEY = 'pendingAgentPrompt';

// Memoized components for better performance
const ExampleCard = React.memo(({ item, onClick }: { 
  item: { text: string; icon: any }; 
  onClick: () => void 
}) => (
  <button
    onClick={onClick}
    className="bg-background border border-border rounded-lg p-3 text-left hover:bg-muted/50 transition-colors duration-150 flex items-start space-x-2 h-full text-sm"
  >
    <item.icon className="h-4 w-4 text-muted-foreground mt-0.5 flex-shrink-0" />
    <span>{item.text}</span>
  </button>
));
ExampleCard.displayName = 'ExampleCard';

const CapabilityCard = React.memo(({ item }: { item: { text: string; icon: any } }) => (
  <div className="bg-background border border-border rounded-lg p-3 text-left flex items-start space-x-2 h-full text-sm">
    <item.icon className="h-4 w-4 text-muted-foreground mt-0.5 flex-shrink-0" />
    <span>{item.text}</span>
  </div>
));
CapabilityCard.displayName = 'CapabilityCard';

const DiscoverCard = React.memo(({ item, onNavigate }: { 
  item: { text: string; icon: any; link: string }; 
  onNavigate: (link: string) => void 
}) => (
  <button
    onClick={() => onNavigate(item.link)}
    className="bg-background border border-border rounded-lg p-3 text-left hover:bg-muted/50 transition-colors duration-150 flex items-start space-x-2 h-full text-sm"
  >
    <item.icon className="h-4 w-4 text-muted-foreground mt-0.5 flex-shrink-0" />
    <span>{item.text}</span>
  </button>
));
DiscoverCard.displayName = 'DiscoverCard';

function DashboardContent() {
  const [inputValue, setInputValue] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [autoSubmit, setAutoSubmit] = useState(false);
  const { billingError, handleBillingError, clearBillingError } =
    useBillingError();
  const router = useRouter();
  const isMobile = useIsMobile();
  const { setOpenMobile } = useSidebar();
  const { data: accounts } = useAccounts();
  const chatInputRef = useRef<ChatInputHandles>(null);
  const { t } = useLanguage();

  // Memoize computed values
  const personalAccount = useMemo(
    () => accounts?.find((account) => account.personal_account),
    [accounts]
  );

  const secondaryGradient = useMemo(
    () => 'bg-gradient-to-r from-blue-500 to-blue-500 bg-clip-text text-transparent',
    []
  );

  // Memoize static data to prevent recreation on every render
  const exampleItems = useMemo(() => [
    { text: t('dashboard.exampleItems.quantumPhysics'), icon: Sparkles },
    { text: t('dashboard.exampleItems.marketingPlan'), icon: Sparkles },
    { text: t('dashboard.exampleItems.techSummary'), icon: Sparkles },
  ], [t]);

  const capabilityItems = useMemo(() => [
    { text: t('dashboard.capabilityItems.memory'), icon: Sun },
    { text: t('dashboard.capabilityItems.corrections'), icon: RefreshCw },
    { text: t('dashboard.capabilityItems.safety'), icon: MessageSquare },
  ], [t]);

  const discoverItems = useMemo(() => [
    { text: t('dashboard.discoverItems.aiTrends'), icon: CompassIcon, link: "/discover" },
    { text: t('dashboard.discoverItems.hotTopics'), icon: CompassIcon, link: "/discover" },
    { text: t('dashboard.discoverItems.recommendations'), icon: CompassIcon, link: "/discover" },
  ], [t]);

  // Memoize callbacks to prevent recreation
  const handleExampleClick = useCallback((text: string) => {
    setInputValue(text);
    chatInputRef.current?.focusInput();
  }, []);

  const handleNavigate = useCallback((link: string) => {
    router.push(link);
  }, [router]);

  const handleSubmit = useCallback(async (
    message: string,
    options?: {
      model_name?: string;
      enable_thinking?: boolean;
      reasoning_effort?: string;
      stream?: boolean;
      enable_context_manager?: boolean;
    },
  ) => {
    if (
      (!message.trim() && !chatInputRef.current?.getPendingFiles().length) ||
      isSubmitting
    )
      return;

    setIsSubmitting(true);

    try {
      const files = chatInputRef.current?.getPendingFiles() || [];
      localStorage.removeItem(PENDING_PROMPT_KEY);

      // Always use FormData for consistency
      const formData = new FormData();
      formData.append('prompt', message);

      // Append files if present
      files.forEach((file, index) => {
        formData.append('files', file, file.name);
      });

      // Append options
      if (options?.model_name) formData.append('model_name', options.model_name);
      formData.append('enable_thinking', String(options?.enable_thinking ?? false));
      formData.append('reasoning_effort', options?.reasoning_effort ?? 'low');
      formData.append('stream', String(options?.stream ?? true));
      formData.append('enable_context_manager', String(options?.enable_context_manager ?? false));

      console.log('FormData content:', Array.from(formData.entries()));

      const result = await initiateAgent(formData);
      console.log('Agent initiated:', result);

      if (result.thread_id) {
        router.push(`/agents/${result.thread_id}`);
      } else {
        throw new Error('Agent initiation did not return a thread_id.');
      }
      chatInputRef.current?.clearPendingFiles();
    } catch (error: any) {
      console.error('提交过程中出错:', error);
      if (error instanceof BillingError) {
        console.log('处理计费错误:', error.detail);
        handleBillingError({
          message:
            error.detail.message ||
            '已达到本月使用限额。请升级您的计划以继续使用。',
          currentUsage: error.detail.currentUsage as number | undefined,
          limit: error.detail.limit as number | undefined,
          subscription: error.detail.subscription || {
            price_id: config.SUBSCRIPTION_TIERS.FREE.priceId,
            plan_name: 'Free',
          },
        });
        setIsSubmitting(false);
        return;
      }

      const isConnectionError =
        error instanceof TypeError && error.message.includes('Failed to fetch');
      if (!isLocalMode() || isConnectionError) {
        toast.error(error.message || '发生了意外错误');
      }
      setIsSubmitting(false);
    }
  }, [isSubmitting, router, handleBillingError]);

  // Check for pending prompt in localStorage on mount
  useEffect(() => {
    // Use a small delay to ensure we're fully mounted
    const timer = setTimeout(() => {
      const pendingPrompt = localStorage.getItem(PENDING_PROMPT_KEY);

      if (pendingPrompt) {
        setInputValue(pendingPrompt);
        setAutoSubmit(true); // Flag to auto-submit after mounting
      }
    }, 200);

    return () => clearTimeout(timer);
  }, []);

  // Auto-submit the form if we have a pending prompt
  useEffect(() => {
    if (autoSubmit && inputValue && !isSubmitting) {
      const timer = setTimeout(() => {
        handleSubmit(inputValue);
        setAutoSubmit(false);
      }, 500);

      return () => clearTimeout(timer);
    }
  }, [autoSubmit, inputValue, isSubmitting, handleSubmit]);

  return (
    <div className="flex flex-col items-center justify-start h-full w-full pt-8 pb-6 overflow-y-auto">
      {isMobile && (
        <div className="absolute top-4 left-4 z-10">
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="icon"
                className="h-8 w-8"
                onClick={() => setOpenMobile(true)}
              >
                <Menu className="h-4 w-4" />
                <span className="sr-only">打开菜单</span>
              </Button>
            </TooltipTrigger>
            <TooltipContent>{t('dashboard.openMenu')}</TooltipContent>
          </Tooltip>
        </div>
      )}

      <div className="w-full max-w-3xl px-4 flex flex-col flex-grow">
        <div className="flex flex-col items-center text-center mb-6 w-full">
          <h1 className={cn('tracking-tight text-4xl font-semibold leading-tight')}>
            {t('dashboard.welcome')}
          </h1>
          <p className="tracking-tight text-3xl font-normal text-muted-foreground/80 mt-2 flex items-center gap-2">
            {t('dashboard.startNewChat')}
          </p>
        </div>

        <div className="mb-6">
            <ChatInput
            ref={chatInputRef}
            onSubmit={handleSubmit}
            loading={isSubmitting}
            placeholder={t('dashboard.placeholder')}
            value={inputValue}
            onChange={setInputValue}
            hideAttachments={false}
            />
        </div>

        {/* New Sections Start Here */}
        <div className="w-full space-y-6">
          {/* Examples Section */}
          <div>
            <h2 className="text-xl font-semibold mb-3 text-left">{t('dashboard.examples')}</h2>
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-3">
              {exampleItems.map((item, index) => (
                <ExampleCard
                  key={index}
                  item={item}
                  onClick={() => handleExampleClick(item.text)}
                />
              ))}
            </div>
          </div>

          {/* Capabilities Section */}
          <div>
            <h2 className="text-xl font-semibold mb-3 text-left">{t('dashboard.capabilities')}</h2>
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-3">
              {capabilityItems.map((item, index) => (
                <CapabilityCard
                  key={index}
                  item={item}
                />
              ))}
            </div>
          </div>

          {/* Discover Section */}
          <div>
            <h2 className="text-xl font-semibold mb-3 text-left">{t('dashboard.discover')}</h2>
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-3">
              {discoverItems.map((item, index) => (
                <DiscoverCard
                  key={index}
                  item={item}
                  onNavigate={handleNavigate}
                />
              ))}
            </div>
          </div>
        </div>
        {/* New Sections End Here */}

      </div>

      {/* Billing Error Alert */}
      <Suspense fallback={null}>
        <BillingErrorAlert
          message={billingError?.message}
          currentUsage={billingError?.currentUsage}
          limit={billingError?.limit}
          accountId={personalAccount?.account_id}
          onDismiss={clearBillingError}
          isOpen={!!billingError}
        />
      </Suspense>
    </div>
  );
}

export default function DashboardPage() {
  return (
    <Suspense
      fallback={
        <div className="flex flex-col items-center justify-center h-full w-full">
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[560px] max-w-[90%]">
            <div className="flex flex-col items-center text-center mb-10">
              <Skeleton className="h-10 w-40 mb-2" />
              <Skeleton className="h-7 w-56" />
            </div>

            <Skeleton className="w-full h-[100px] rounded-xl" />
            <div className="flex justify-center mt-3">
              <Skeleton className="h-5 w-16" />
            </div>
          </div>
        </div>
      }
    >
      <DashboardContent />
    </Suspense>
  );
}
