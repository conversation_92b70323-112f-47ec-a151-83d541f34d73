'use client';

import React, { useState, useMemo } from 'react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { useLanguage } from '@/contexts/LanguageContext';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Search, Settings, CheckCircle, AlertCircle } from 'lucide-react';
import { cn } from '@/lib/utils';

interface Connector {
  id: string;
  name: string;
  status: '已连接' | '未连接';
  actionText: '连接' | '管理';
  logoText: string;
  bgColor: string;
  boundAccount?: string; // Account name if bound
  description: string;
  configFields: {
    label: string;
    key: string;
    type: 'text' | 'password' | 'url';
    placeholder: string;
    required: boolean;
  }[];
}

const mockConnectors: Connector[] = [
  {
    id: 'xia<PERSON><PERSON><PERSON>',
    name: '小红书',
    status: '已连接',
    actionText: '管理',
    logoText: '红',
    bgColor: 'bg-red-500',
    boundAccount: '@美食探索者',
    description: '连接您的小红书账号，自动发布内容和获取数据分析',
    configFields: [
      {
        label: 'API密钥',
        key: 'apiKey',
        type: 'password',
        placeholder: '请输入小红书API密钥',
        required: true,
      },
      {
        label: '用户名',
        key: 'username',
        type: 'text',
        placeholder: '请输入用户名',
        required: true,
      },
    ],
  },
  {
    id: 'douyin',
    name: '抖音',
    status: '未连接',
    actionText: '连接',
    logoText: '抖',
    bgColor: 'bg-black',
    description: '连接抖音平台，实现视频自动发布和数据同步',
    configFields: [
      {
        label: 'App ID',
        key: 'appId',
        type: 'text',
        placeholder: '请输入应用ID',
        required: true,
      },
      {
        label: 'App Secret',
        key: 'appSecret',
        type: 'password',
        placeholder: '请输入应用密钥',
        required: true,
      },
      {
        label: '回调地址',
        key: 'redirectUri',
        type: 'url',
        placeholder: 'https://your-domain.com/callback',
        required: true,
      },
    ],
  },
  {
    id: 'bilibili',
    name: 'B站',
    status: '未连接',
    actionText: '连接',
    logoText: 'B',
    bgColor: 'bg-pink-500',
    description: '集成B站功能，管理视频内容和粉丝互动',
    configFields: [
      {
        label: 'Access Token',
        key: 'accessToken',
        type: 'password',
        placeholder: '请输入访问令牌',
        required: true,
      },
      {
        label: 'UID',
        key: 'uid',
        type: 'text',
        placeholder: '请输入用户UID',
        required: true,
      },
    ],
  },
  {
    id: 'wechat',
    name: '微信',
    status: '已连接',
    actionText: '管理',
    logoText: '微',
    bgColor: 'bg-green-500',
    boundAccount: '@智能助手',
    description: '连接微信公众号，实现消息自动回复和内容推送',
    configFields: [
      {
        label: 'AppID',
        key: 'appId',
        type: 'text',
        placeholder: '请输入微信AppID',
        required: true,
      },
      {
        label: 'AppSecret',
        key: 'appSecret',
        type: 'password',
        placeholder: '请输入微信AppSecret',
        required: true,
      },
      {
        label: 'Token',
        key: 'token',
        type: 'password',
        placeholder: '请输入验证Token',
        required: true,
      },
    ],
  },
  {
    id: 'weibo',
    name: '微博',
    status: '未连接',
    actionText: '连接',
    logoText: '微',
    bgColor: 'bg-orange-500',
    description: '接入微博API，实现内容发布和社交数据分析',
    configFields: [
      {
        label: 'Client Key',
        key: 'clientKey',
        type: 'text',
        placeholder: '请输入客户端密钥',
        required: true,
      },
      {
        label: 'Client Secret',
        key: 'clientSecret',
        type: 'password',
        placeholder: '请输入客户端秘密',
        required: true,
      },
    ],
  },
  {
    id: 'zhihu',
    name: '知乎',
    status: '未连接',
    actionText: '连接',
    logoText: '知',
    bgColor: 'bg-blue-600',
    description: '连接知乎平台，自动管理问答和内容推广',
    configFields: [
      {
        label: 'Client ID',
        key: 'clientId',
        type: 'text',
        placeholder: '请输入客户端ID',
        required: true,
      },
      {
        label: 'Client Secret',
        key: 'clientSecret',
        type: 'password',
        placeholder: '请输入客户端密钥',
        required: true,
      },
    ],
  },
];

export default function ConnectorsPage() {
  const { t } = useLanguage();
  const [searchTerm, setSearchTerm] = useState('');
  const [configDialogOpen, setConfigDialogOpen] = useState(false);
  const [selectedConnector, setSelectedConnector] = useState<Connector | null>(null);
  const [configData, setConfigData] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  const filteredConnectors = useMemo(() => {
    if (!searchTerm) {
      return mockConnectors;
    }
    return mockConnectors.filter((connector) =>
      connector.name.toLowerCase().includes(searchTerm.toLowerCase())
    );
  }, [searchTerm]);

  const handleConnectorClick = (connector: Connector) => {
    setSelectedConnector(connector);
    setConfigDialogOpen(true);
    // Pre-fill existing config if available
    setConfigData({});
  };

  const handleConfigSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!selectedConnector) return;

    setIsSubmitting(true);

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      // Here you would make an actual API call to save the configuration
      console.log('Saving config for', selectedConnector.name, configData);
      
      // Show success and close dialog
      setConfigDialogOpen(false);
      setConfigData({});
      setSelectedConnector(null);
    } catch (error) {
      console.error('Error saving configuration:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleInputChange = (key: string, value: string) => {
    setConfigData(prev => ({
      ...prev,
      [key]: value
    }));
  };

  return (
    <div className="container mx-auto py-8 px-4 md:px-6 lg:px-8">
      <header className="mb-8">
        <h1 className="text-4xl font-bold mb-2 text-foreground">{t('connectors.title')}</h1>
        <p className="text-lg text-muted-foreground">
          {t('connectors.subtitle')}
        </p>
      </header>

      <div className="mb-8 relative">
        <Search className="absolute left-3.5 top-1/2 -translate-y-1/2 h-5 w-5 text-muted-foreground" />
        <Input
          type="search"
          placeholder={t('connectors.searchPlaceholder')}
          className="pl-10 pr-4 py-3 w-full bg-background border-border focus:ring-primary text-base h-12 rounded-lg shadow-sm"
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
        />
      </div>

      {filteredConnectors.length > 0 ? (
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredConnectors.map((connector) => (
            <Card key={connector.id} className="bg-card border-border hover:shadow-md transition-all duration-200 cursor-pointer group">
              <CardContent className="p-6">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center gap-3">
                    <div className={cn("w-8 h-8 rounded-lg flex items-center justify-center text-white text-sm font-bold shadow-sm", connector.bgColor)}>
                      {connector.logoText}
                    </div>
                    <div>
                      <h3 className="text-foreground font-semibold text-lg">{connector.name}</h3>
                      <p className="text-muted-foreground text-sm mt-1 line-clamp-2">
                        {connector.description}
                      </p>
                    </div>
                  </div>
                </div>
                
                <div className="mb-4">
                  <div className="flex items-center gap-2">
                    <Badge 
                      variant={connector.status === '已连接' ? 'default' : 'secondary'}
                      className={cn(
                        "text-xs",
                        connector.status === '已连接' 
                          ? 'bg-green-50 text-green-700 border-green-200 dark:bg-green-950/20 dark:text-green-400 dark:border-green-900/50' 
                          : 'bg-gray-50 text-gray-600 border-gray-200 dark:bg-gray-950/20 dark:text-gray-400 dark:border-gray-800'
                      )}
                    >
                      {connector.status === '已连接' ? (
                        <CheckCircle className="h-3 w-3 mr-1" />
                      ) : (
                        <AlertCircle className="h-3 w-3 mr-1" />
                      )}
                      {connector.status === '已连接' ? t('connectors.status.connected') : t('connectors.status.notConnected')}
                    </Badge>
                  </div>
                  
                  {connector.boundAccount && (
                    <p className="text-sm text-muted-foreground mt-2">
                      {t('connectors.boundAccount')}: <span className="font-medium text-foreground">{connector.boundAccount}</span>
                    </p>
                  )}
                  
                  {!connector.boundAccount && connector.status === '未连接' && (
                    <p className="text-sm text-muted-foreground mt-2">
                      {t('connectors.clickToConnect')}
                    </p>
                  )}
                </div>

                <Button 
                  onClick={() => handleConnectorClick(connector)}
                  variant={connector.status === '已连接' ? 'outline' : 'default'}
                  className={cn(
                    "w-full transition-all duration-200",
                    connector.status === '已连接'
                      ? 'border-border hover:bg-accent hover:text-accent-foreground'
                      : 'bg-primary hover:bg-primary/90 text-primary-foreground shadow-sm'
                  )}
                >
                  <Settings className="h-4 w-4 mr-2" />
                  {connector.actionText === '连接' ? t('connectors.actionText.connect') : t('connectors.actionText.manage')}
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : (
        <div className="text-center py-12 text-muted-foreground">
          <Search className="mx-auto h-12 w-12 mb-4 opacity-50" />
          <p className="text-lg">{t('connectors.noResults').replace('{searchTerm}', searchTerm)}</p>
        </div>
      )}

      {/* Configuration Dialog */}
      <Dialog open={configDialogOpen} onOpenChange={setConfigDialogOpen}>
        <DialogContent className="sm:max-w-md border-border bg-card rounded-xl shadow-custom">
          <DialogHeader>
            <DialogTitle className="text-card-foreground flex items-center gap-2">
              {selectedConnector && (
                <div className={cn("w-6 h-6 rounded flex items-center justify-center text-white text-sm font-bold", selectedConnector.bgColor)}>
                  {selectedConnector.logoText}
                </div>
              )}
              {t('connectors.configDialog.title').replace('{name}', selectedConnector?.name || '')}
            </DialogTitle>
            <DialogDescription className="text-muted-foreground">
              {t('connectors.configDialog.description').replace('{name}', selectedConnector?.name || '')}
            </DialogDescription>
          </DialogHeader>

          {selectedConnector && (
            <form onSubmit={handleConfigSubmit} className="space-y-4">
              {selectedConnector.configFields.map((field) => (
                <div key={field.key} className="flex flex-col gap-2">
                  <Label htmlFor={field.key} className="text-sm font-medium text-foreground/90">
                    {field.label}
                    {field.required && <span className="text-destructive ml-1">*</span>}
                  </Label>
                  <Input
                    id={field.key}
                    type={field.type}
                    placeholder={field.placeholder}
                    value={configData[field.key] || ''}
                    onChange={(e) => handleInputChange(field.key, e.target.value)}
                    required={field.required}
                    className="h-10 rounded-lg border-border bg-background"
                  />
                </div>
              ))}

              <div className="flex gap-3 pt-4">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setConfigDialogOpen(false)}
                  className="flex-1 border-border"
                >
                  {t('connectors.configDialog.cancel')}
                </Button>
                <Button
                  type="submit"
                  disabled={isSubmitting}
                  className="flex-1 bg-primary hover:bg-primary/90 text-primary-foreground"
                >
                  {isSubmitting ? t('connectors.configDialog.saving') : (selectedConnector?.status === '已连接' ? t('connectors.configDialog.updateConfig') : t('connectors.configDialog.connectAccount'))}
                </Button>
              </div>
            </form>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
} 