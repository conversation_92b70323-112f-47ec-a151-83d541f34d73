'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { FlickeringGrid } from '@/components/home/<USER>/flickering-grid';
import { useMediaQuery } from '@/hooks/use-media-query';
import { ArrowLeft, BookOpen, Play, Star, HelpCircle, CheckCircle, ArrowRight, Lightbulb, MessageCircle, Cpu, Database, Globe, Shield, Zap, Users, Mail, Phone } from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

const features = [
  {
    title: '智能对话',
    description: '基于大语言模型的自然语言交互，支持多轮对话和上下文理解',
    icon: MessageCircle,
    color: 'bg-blue-500',
    capabilities: ['多轮对话', '上下文理解', '情感分析', '意图识别']
  },
  {
    title: '数据分析',
    description: '强大的数据处理和分析能力，支持多种数据格式和可视化',
    icon: Database,
    color: 'bg-green-500',
    capabilities: ['文件解析', '数据清洗', '统计分析', '图表生成']
  },
  {
    title: '自动化任务',
    description: '执行复杂的自动化工作流，提高工作效率',
    icon: Cpu,
    color: 'bg-purple-500',
    capabilities: ['流程自动化', '定时任务', '批处理', 'API集成']
  },
  {
    title: '多平台集成',
    description: '连接主流社交媒体和办公平台，实现数据同步和内容发布',
    icon: Globe,
    color: 'bg-orange-500',
    capabilities: ['社交媒体', '办公软件', '云存储', 'API接口']
  }
];

const quickStartSteps = [
  {
    step: 1,
    title: '注册账号',
    description: '使用邮箱或第三方账号快速注册',
    action: '立即注册',
    link: '/auth?mode=signup'
  },
  {
    step: 2,
    title: '创建项目',
    description: '在控制台创建您的第一个项目',
    action: '创建项目',
    link: '/dashboard/projects'
  },
  {
    step: 3,
    title: '开始对话',
    description: '与 Loomu 开始您的第一次对话',
    action: '开始聊天',
    link: '/dashboard'
  }
];

const bestPractices = [
  {
    category: '对话技巧',
    icon: MessageCircle,
    color: 'bg-blue-500',
    tips: [
      '使用清晰、具体的问题描述',
      '提供足够的上下文信息',
      '分步骤描述复杂任务',
      '及时确认和澄清需求'
    ]
  },
  {
    category: '数据处理',
    icon: Database,
    color: 'bg-green-500',
    tips: [
      '确保数据格式正确和完整',
      '提前清理和预处理数据',
      '明确数据分析目标',
      '验证分析结果的合理性'
    ]
  },
  {
    category: '安全使用',
    icon: Shield,
    color: 'bg-red-500',
    tips: [
      '不要分享敏感个人信息',
      '定期更新API密钥',
      '使用强密码保护账号',
      '及时删除不需要的数据'
    ]
  },
  {
    category: '性能优化',
    icon: Zap,
    color: 'bg-yellow-500',
    tips: [
      '合理使用批处理功能',
      '避免频繁的重复请求',
      '选择合适的模型和参数',
      '监控使用量和成本'
    ]
  }
];

const faqs = [
  {
    question: 'Loomu 支持哪些文件格式？',
    answer: 'Loomu 支持常见的文档格式（PDF、Word、Excel、PPT）、图片格式（JPG、PNG、GIF）、数据格式（CSV、JSON、XML）以及代码文件等多种格式。'
  },
  {
    question: '如何提高对话的准确性？',
    answer: '建议使用清晰、具体的语言描述需求，提供足够的背景信息，对于复杂任务可以分步骤进行，并及时确认中间结果。'
  },
  {
    question: '数据安全如何保障？',
    answer: '我们采用企业级安全措施，包括数据加密传输和存储、严格的访问控制、定期安全审计等。用户数据仅用于提供服务，不会用于其他目的。'
  },
  {
    question: '是否有使用量限制？',
    answer: '不同套餐有不同的使用量限制。免费版每月提供60分钟使用时间，专业版提供更高的使用量。具体限制请查看定价页面。'
  },
  {
    question: '如何连接第三方平台？',
    answer: '在连接器页面选择要集成的平台，按照指引填写API密钥或授权信息即可。我们支持小红书、微信、抖音等主流平台。'
  },
  {
    question: '遇到问题如何获取帮助？',
    answer: '您可以通过以下方式获取帮助：1）查看文档和FAQ；2）发送邮件到****************；3）加入用户交流群；4）联系在线客服。'
  }
];

export default function GuideDocsPage() {
  const router = useRouter();
  const tablet = useMediaQuery('(max-width: 1024px)');
  const [mounted, setMounted] = useState(false);
  const [isScrolling, setIsScrolling] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  return (
    <main className="flex flex-col items-center justify-center min-h-screen w-full">
      <section className="w-full relative overflow-hidden pb-20">
        <div className="relative flex flex-col items-center w-full px-6 pt-10">
          {/* Left side flickering grid with gradient fades */}
          <div className="absolute left-0 top-0 h-[600px] w-1/3 -z-10 overflow-hidden">
            <div className="absolute inset-0 bg-gradient-to-r from-transparent via-transparent to-background z-10" />
            <div className="absolute inset-x-0 top-0 h-32 bg-gradient-to-b from-background via-background/90 to-transparent z-10" />
            <div className="absolute inset-x-0 bottom-0 h-48 bg-gradient-to-t from-background via-background/90 to-transparent z-10" />

            <FlickeringGrid
              className="h-full w-full"
              squareSize={mounted && tablet ? 2 : 2.5}
              gridGap={mounted && tablet ? 2 : 2.5}
              color="var(--secondary)"
              maxOpacity={0.4}
              flickerChance={isScrolling ? 0.01 : 0.03}
            />
          </div>

          {/* Right side flickering grid with gradient fades */}
          <div className="absolute right-0 top-0 h-[600px] w-1/3 -z-10 overflow-hidden">
            <div className="absolute inset-0 bg-gradient-to-l from-transparent via-transparent to-background z-10" />
            <div className="absolute inset-x-0 top-0 h-32 bg-gradient-to-b from-background via-background/90 to-transparent z-10" />
            <div className="absolute inset-x-0 bottom-0 h-48 bg-gradient-to-t from-background via-background/90 to-transparent z-10" />

            <FlickeringGrid
              className="h-full w-full"
              squareSize={mounted && tablet ? 2 : 2.5}
              gridGap={mounted && tablet ? 2 : 2.5}
              color="var(--secondary)"
              maxOpacity={0.4}
              flickerChance={isScrolling ? 0.01 : 0.03}
            />
          </div>

          {/* Center content background with rounded bottom */}
          <div className="absolute inset-x-1/4 top-0 h-[600px] -z-20 bg-background rounded-b-xl"></div>

          <div className="max-w-5xl w-full mx-auto">
            <div className="flex items-center justify-center mb-10 relative">
              <button
                onClick={() => router.back()}
                className="absolute left-0 group border border-border/50 bg-background hover:bg-accent/20 hover:border-secondary/40 rounded-full text-sm h-8 px-3 flex items-center gap-2 transition-all duration-300 shadow-sm hover:shadow-md hover:scale-105"
              >
                <ArrowLeft size={14} className="text-muted-foreground" />
                <span className="font-medium text-muted-foreground text-xs tracking-wide">
                  返回
                </span>
              </button>

              <h1 className="text-3xl md:text-4xl font-medium tracking-tighter text-center">
                使用 <span className="text-secondary">指南</span>
              </h1>
            </div>

            <div className="rounded-xl border border-border bg-[#F3F4F6] dark:bg-[#F9FAFB]/[0.02] p-8 shadow-sm">
              <div className="prose prose-sm max-w-none dark:prose-invert">
                <div className="flex items-center gap-3 mb-6">
                  <div className="p-2 bg-green-500 rounded-lg">
                    <BookOpen className="h-6 w-6 text-white" />
                  </div>
                  <div>
                    <h2 className="text-2xl font-medium tracking-tight mb-1">Loomu 使用指南</h2>
                    <p className="text-sm text-muted-foreground">
                      全面了解 Loomu 的功能特性，掌握最佳使用方法
                    </p>
                  </div>
                </div>

                <div className="bg-blue-50 dark:bg-blue-950/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4 mb-6">
                  <div className="flex items-start gap-3">
                    <Lightbulb className="h-5 w-5 text-blue-600 dark:text-blue-400 mt-0.5 flex-shrink-0" />
                    <div>
                      <h3 className="font-semibold text-blue-800 dark:text-blue-300 mb-2">新手指南</h3>
                      <p className="text-sm text-blue-700 dark:text-blue-400">
                        本指南将帮助您快速上手 Loomu AI 助手，了解核心功能并掌握最佳使用方法。无论您是新用户还是想要深入了解高级功能，都能在这里找到有用的信息。
                      </p>
                    </div>
                  </div>
                </div>

                <div className="space-y-8">
                  {/* Quick Start */}
                  <section>
                    <h3 className="text-xl font-semibold mb-6 flex items-center gap-2">
                      <Play className="h-5 w-5 text-green-500" />
                      快速开始
                    </h3>
                    
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                      {quickStartSteps.map((step, index) => (
                        <Card key={index} className="border-border relative">
                          <CardHeader>
                            <div className="flex items-center gap-3 mb-2">
                              <div className="w-8 h-8 rounded-full bg-primary text-primary-foreground flex items-center justify-center font-bold text-sm">
                                {step.step}
                              </div>
                              <CardTitle className="text-lg">{step.title}</CardTitle>
                            </div>
                            <CardDescription>{step.description}</CardDescription>
                          </CardHeader>
                          <CardContent>
                            <Button variant="outline" size="sm" asChild className="w-full">
                              <Link href={step.link}>
                                {step.action}
                                <ArrowRight className="h-4 w-4 ml-2" />
                              </Link>
                            </Button>
                          </CardContent>
                          {index < quickStartSteps.length - 1 && (
                            <ArrowRight className="absolute -right-3 top-1/2 transform -translate-y-1/2 h-6 w-6 text-muted-foreground hidden md:block" />
                          )}
                        </Card>
                      ))}
                    </div>
                  </section>

                  {/* Features */}
                  <section>
                    <h3 className="text-xl font-semibold mb-6 flex items-center gap-2">
                      <Star className="h-5 w-5 text-yellow-500" />
                      核心功能
                    </h3>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      {features.map((feature, index) => {
                        const Icon = feature.icon;
                        return (
                          <Card key={index} className="border-border">
                            <CardHeader>
                              <div className="flex items-center gap-3 mb-3">
                                <div className={`p-3 rounded-lg ${feature.color}`}>
                                  <Icon className="h-6 w-6 text-white" />
                                </div>
                                <div>
                                  <CardTitle className="text-lg">{feature.title}</CardTitle>
                                </div>
                              </div>
                              <CardDescription>{feature.description}</CardDescription>
                            </CardHeader>
                            <CardContent>
                              <div className="space-y-2">
                                {feature.capabilities.map((capability, idx) => (
                                  <div key={idx} className="flex items-center text-sm text-muted-foreground">
                                    <CheckCircle className="h-4 w-4 text-green-500 mr-2 flex-shrink-0" />
                                    {capability}
                                  </div>
                                ))}
                              </div>
                            </CardContent>
                          </Card>
                        );
                      })}
                    </div>
                  </section>

                  {/* Best Practices */}
                  <section>
                    <h3 className="text-xl font-semibold mb-6 flex items-center gap-2">
                      <Lightbulb className="h-5 w-5 text-yellow-500" />
                      最佳实践
                    </h3>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      {bestPractices.map((practice, index) => {
                        const Icon = practice.icon;
                        return (
                          <Card key={index} className="border-border">
                            <CardHeader>
                              <CardTitle className="flex items-center gap-3">
                                <div className={`p-2 rounded-lg ${practice.color}`}>
                                  <Icon className="h-5 w-5 text-white" />
                                </div>
                                {practice.category}
                              </CardTitle>
                            </CardHeader>
                            <CardContent>
                              <div className="space-y-2">
                                {practice.tips.map((tip, idx) => (
                                  <div key={idx} className="flex items-start text-sm text-muted-foreground">
                                    <div className="w-1.5 h-1.5 rounded-full bg-primary mr-2 mt-2 flex-shrink-0" />
                                    {tip}
                                  </div>
                                ))}
                              </div>
                            </CardContent>
                          </Card>
                        );
                      })}
                    </div>
                  </section>

                  {/* FAQ */}
                  <section>
                    <h3 className="text-xl font-semibold mb-6 flex items-center gap-2">
                      <HelpCircle className="h-5 w-5 text-blue-500" />
                      常见问题
                    </h3>
                    
                    <div className="space-y-4">
                      {faqs.map((faq, index) => (
                        <Card key={index} className="border-border">
                          <CardHeader className="pb-3">
                            <CardTitle className="text-lg font-medium">{faq.question}</CardTitle>
                          </CardHeader>
                          <CardContent>
                            <p className="text-muted-foreground">{faq.answer}</p>
                          </CardContent>
                        </Card>
                      ))}
                    </div>
                  </section>

                  {/* Support */}
                  <section>
                    <h3 className="text-xl font-semibold mb-6 flex items-center gap-2">
                      <Users className="h-5 w-5 text-purple-500" />
                      获取帮助
                    </h3>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <Card className="border-border">
                        <CardHeader>
                          <CardTitle className="flex items-center gap-2">
                            <Mail className="h-5 w-5 text-blue-500" />
                            邮件支持
                          </CardTitle>
                          <CardDescription>
                            发送邮件给我们的技术支持团队
                          </CardDescription>
                        </CardHeader>
                        <CardContent>
                          <Button variant="outline" asChild className="w-full">
                            <Link href="mailto:<EMAIL>">
                              <EMAIL>
                            </Link>
                          </Button>
                        </CardContent>
                      </Card>

                      <Card className="border-border">
                        <CardHeader>
                          <CardTitle className="flex items-center gap-2">
                            <MessageCircle className="h-5 w-5 text-green-500" />
                            在线客服
                          </CardTitle>
                          <CardDescription>
                            通过在线聊天获得即时帮助
                          </CardDescription>
                        </CardHeader>
                        <CardContent>
                          <Button variant="outline" className="w-full">
                            开始对话
                          </Button>
                        </CardContent>
                      </Card>
                    </div>
                  </section>
                </div>

                <div className="mt-12 pt-8 border-t border-border text-center">
                  <p className="text-sm text-muted-foreground mb-2">
                    本使用指南于2024年12月1日更新，版本号：2.0
                  </p>
                  <p className="text-xs text-muted-foreground">
                    © 2024 LoomuAI. 保留所有权利。如需更多帮助请联系 <a href="mailto:<EMAIL>" className="text-primary hover:underline"><EMAIL></a>
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </main>
  );
} 