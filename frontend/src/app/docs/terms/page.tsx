'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { FlickeringGrid } from '@/components/home/<USER>/flickering-grid';
import { useMediaQuery } from '@/hooks/use-media-query';
import { ArrowLeft } from 'lucide-react';
import { useRouter } from 'next/navigation';

export default function TermsOfServicePage() {
  const tablet = useMediaQuery('(max-width: 1024px)');
  const [mounted, setMounted] = useState(false);
  const [isScrolling, setIsScrolling] = useState(false);
  const router = useRouter();

  useEffect(() => {
    setMounted(true);
  }, []);

  return (
    <main className="flex flex-col items-center justify-center min-h-screen w-full">
      <section className="w-full relative overflow-hidden pb-20">
        <div className="relative flex flex-col items-center w-full px-6 pt-10">
          {/* Left side flickering grid with gradient fades */}
          <div className="absolute left-0 top-0 h-[600px] w-1/3 -z-10 overflow-hidden">
            <div className="absolute inset-0 bg-gradient-to-r from-transparent via-transparent to-background z-10" />
            <div className="absolute inset-x-0 top-0 h-32 bg-gradient-to-b from-background via-background/90 to-transparent z-10" />
            <div className="absolute inset-x-0 bottom-0 h-48 bg-gradient-to-t from-background via-background/90 to-transparent z-10" />

            <FlickeringGrid
              className="h-full w-full"
              squareSize={mounted && tablet ? 2 : 2.5}
              gridGap={mounted && tablet ? 2 : 2.5}
              color="var(--secondary)"
              maxOpacity={0.4}
              flickerChance={isScrolling ? 0.01 : 0.03}
            />
          </div>

          {/* Right side flickering grid with gradient fades */}
          <div className="absolute right-0 top-0 h-[600px] w-1/3 -z-10 overflow-hidden">
            <div className="absolute inset-0 bg-gradient-to-l from-transparent via-transparent to-background z-10" />
            <div className="absolute inset-x-0 top-0 h-32 bg-gradient-to-b from-background via-background/90 to-transparent z-10" />
            <div className="absolute inset-x-0 bottom-0 h-48 bg-gradient-to-t from-background via-background/90 to-transparent z-10" />

            <FlickeringGrid
              className="h-full w-full"
              squareSize={mounted && tablet ? 2 : 2.5}
              gridGap={mounted && tablet ? 2 : 2.5}
              color="var(--secondary)"
              maxOpacity={0.4}
              flickerChance={isScrolling ? 0.01 : 0.03}
            />
          </div>

          {/* Center content background with rounded bottom */}
          <div className="absolute inset-x-1/4 top-0 h-[600px] -z-20 bg-background rounded-b-xl"></div>

          <div className="max-w-4xl w-full mx-auto">
            <div className="flex items-center justify-center mb-10 relative">
              <button
                onClick={() => router.back()}
                className="absolute left-0 group border border-border/50 bg-background hover:bg-accent/20 hover:border-secondary/40 rounded-full text-sm h-8 px-3 flex items-center gap-2 transition-all duration-300 shadow-sm hover:shadow-md hover:scale-105"
              >
                <ArrowLeft size={14} className="text-muted-foreground" />
                <span className="font-medium text-muted-foreground text-xs tracking-wide">
                  返回
                </span>
              </button>

              <h1 className="text-3xl md:text-4xl font-medium tracking-tighter text-center">
                服务 <span className="text-secondary">条款</span>
              </h1>
            </div>

            <div className="bg-background p-8">
              <div className="prose prose-lg max-w-none">
                <h1 className="text-2xl font-bold mb-2">太初（LoomuAI）服务条款（中文）</h1>
                <p className="text-muted-foreground mb-8">最后更新日期：2025年06月01日</p>

                <p className="mb-6">
                  本服务条款（以下简称"本条款"）是您（"用户"或"您"）与思维涌动科技有限公司（以下简称"我们"或"公司"）之间具有法律约束力的协议。本条款规范您对太初（LoomuAI）网站、应用程序及公司提供的各项服务（统称为"服务"）的使用。
                </p>
                
                <p className="mb-6">
                  通过任何方式访问或使用本服务，即表示您确认接受本条款所述的惯例和政策，并同意我们按照隐私政策的规定收集、使用和共享您的信息。
                </p>

                <h2 className="text-xl font-semibold mb-4 mt-8">一、条款接受</h2>
                
                <p className="mb-4">1. 访问或使用本服务的任何部分，即表示您同意受本条款约束。若您不同意本协议的所有条款和条件，则不得访问本网站或使用任何服务。</p>
                
                <p className="mb-4">2. 公司保留随时更新本条款的权利，更新后的条款将通过网站公告或应用内通知发布。若您在条款更新后继续使用服务，即视为接受更新后的条款。</p>

                <h2 className="text-xl font-semibold mb-4 mt-8">二、测试版软件</h2>
                
                <p className="mb-4">1. 公司可能根据本条款提供"测试版软件"。此类产品按"现状"提供，可能包含错误或缺陷，且不提供任何形式的保证。</p>
                
                <p className="mb-4">2. 您需自担使用测试版软件的风险。公司有权随时修改或终止测试版软件，且无需提前通知用户。</p>

                <h2 className="text-xl font-semibold mb-4 mt-8">三、服务使用</h2>
                
                <p className="mb-4">1. 本服务可能包括AI对话、文本生成图像/音频、创建和管理AI工作流等功能及相关服务。</p>
                
                <p className="mb-4">2. 您需对使用本服务产生的结果负责，包括遵守适用的法律法规。禁止利用本服务从事违法活动、传播有害信息或侵犯他人权益。</p>

                <h2 className="text-xl font-semibold mb-4 mt-8">四、知识产权</h2>
                
                <p className="mb-4">1. 本服务包含的所有内容，如文本、图形、标志、图像及其汇编，以及网站使用的任何软件，均为公司或其供应商的财产，受版权、商标和其他知识产权法律保护。</p>
                
                <p className="mb-4">2. 未经公司明确书面许可，您不得复制、修改、传播或商业使用上述内容（本条款明确授权的除外）。</p>

                <h2 className="text-xl font-semibold mb-4 mt-8">五、服务可用性及限制</h2>
                
                <p className="mb-4">1. 公司不保证服务始终可用或无中断。我们有权因技术维护、升级、法律合规等原因，随时变更、暂停或终止服务。</p>
                
                <p className="mb-4">2. 若因不可抗力、网络故障、第三方行为等非公司可控因素导致服务中断或延迟，公司不承担责任。</p>

                <h2 className="text-xl font-semibold mb-4 mt-8">六、禁止行为</h2>
                
                <p className="mb-4">您同意不滥用本服务，禁止行为包括但不限于：</p>
                
                <ul className="list-disc pl-6 mb-4 space-y-2">
                  <li>干扰或破坏服务的系统完整性或安全性；</li>
                  <li>未经授权访问、篡改服务器数据或传输内容；</li>
                  <li>利用服务进行欺诈、侵权、恶意攻击或违反公序良俗的活动；</li>
                  <li>反向工程、反编译或试图获取服务的底层代码或架构。</li>
                </ul>

                <h2 className="text-xl font-semibold mb-4 mt-8">七、保证与免责声明</h2>
                
                <p className="mb-4">1. 本服务按"现状"提供，不附带任何明示或暗示的保证，包括但不限于适销性、特定用途适用性或非侵权保证。</p>
                
                <p className="mb-4">2. 公司不保证服务满足您的特定需求，或始终保持安全、无错误、不间断运行。</p>

                <h2 className="text-xl font-semibold mb-4 mt-8">八、责任限制</h2>
                
                <p className="mb-4">在法律允许的最大范围内，公司对以下情况不承担责任：</p>
                
                <ul className="list-disc pl-6 mb-4 space-y-2">
                  <li>任何间接、附带、特殊、后果性或惩罚性损害，包括利润损失、数据丢失或商誉损失；</li>
                  <li>因使用或无法使用本服务导致的任何直接损害，除非该损害因公司故意或重大过失造成。</li>
                </ul>

                <h2 className="text-xl font-semibold mb-4 mt-8">九、管辖法律</h2>
                
                <p className="mb-4">本条款受中华人民共和国法律管辖（不包括冲突法规则）。若发生争议，双方应首先通过友好协商解决；协商不成的，任何一方均有权向有管辖权的人民法院提起诉讼。</p>

                <h2 className="text-xl font-semibold mb-4 mt-8">十、地域限制</h2>
                
                <p className="mb-4">根据公司运营策略，部分服务可能对特定地区用户限制使用。用户需自行确认所在地区是否符合服务使用条件，因违规使用导致的后果由用户自行承担。</p>

                <h2 className="text-xl font-semibold mb-4 mt-8">十一、联系我们</h2>
                
                <p className="mb-4">如有任何关于本条款的疑问，请通过以下方式联系我们：</p>
                
                <ul className="list-disc pl-6 mb-4 space-y-2">
                  <li>公司名称：思维涌动科技有限公司</li>
                  <li>联系邮箱：<EMAIL></li>
                  <li>地址：[请补充公司实际办公地址]</li>
                </ul>

                <h2 className="text-xl font-semibold mb-4 mt-8">十二、生效与适用</h2>
                
                <p className="mb-4">本条款自您首次使用服务之日起生效。若您为企业用户，需确保使用服务的员工或授权代表遵守本条款。</p>
                
                <div className="mt-8 p-4 bg-yellow-50 dark:bg-yellow-950/20 border border-yellow-200 dark:border-yellow-800 rounded-lg">
                  <p className="font-semibold text-yellow-800 dark:text-yellow-300 mb-2">重要提示：</p>
                  <p className="text-sm text-yellow-700 dark:text-yellow-400">
                    请仔细阅读本条款，确保理解并同意所有内容。若您代表企业或组织使用服务，需保证您有权代表该主体接受本条款。
                  </p>
                </div>

                <div className="mt-12 pt-8 border-t border-border">
                  <p className="text-sm text-muted-foreground text-center">
                    思维涌动科技有限公司<br />
                    2025年06月01日
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </main>
  );
} 