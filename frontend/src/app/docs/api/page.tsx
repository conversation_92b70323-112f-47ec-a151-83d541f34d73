'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { FlickeringGrid } from '@/components/home/<USER>/flickering-grid';
import { useMediaQuery } from '@/hooks/use-media-query';
import { ArrowLeft, Code, Key, Zap, Shield, Globe, Download, Copy, ExternalLink, CheckCircle, AlertTriangle, ChevronRight } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';

const apiEndpoints = [
  {
    method: 'POST',
    path: '/api/v1/chat/completions',
    description: '创建聊天完成',
    status: 'stable',
    auth: true
  },
  {
    method: 'GET',
    path: '/api/v1/models',
    description: '获取可用模型列表',
    status: 'stable',
    auth: true
  },
  {
    method: 'POST',
    path: '/api/v1/projects',
    description: '创建新项目',
    status: 'beta',
    auth: true
  },
  {
    method: 'GET',
    path: '/api/v1/projects',
    description: '获取项目列表',
    status: 'stable',
    auth: true
  },
  {
    method: 'POST',
    path: '/api/v1/connectors/config',
    description: '配置连接器',
    status: 'beta',
    auth: true
  }
];

const sdks = [
  {
    language: 'JavaScript',
    description: 'Node.js 和浏览器环境的 SDK',
    version: 'v1.2.0',
    install: 'npm install @Loomu/sdk',
    docs: 'https://docs.Loomu.so/sdk/javascript',
    status: 'stable'
  },
  {
    language: 'Python',
    description: 'Python 3.7+ 的官方 SDK',
    version: 'v1.1.0', 
    install: 'pip install Loomu-sdk',
    docs: 'https://docs.Loomu.so/sdk/python',
    status: 'stable'
  },
  {
    language: 'Go',
    description: 'Go 语言的轻量级 SDK',
    version: 'v0.3.0',
    install: 'go get github.com/Loomu/go-sdk',
    docs: 'https://docs.Loomu.so/sdk/go',
    status: 'beta'
  }
];

const codeExamples = {
  javascript: `// 安装 SDK
npm install @Loomu/sdk

// 基本使用
import { LoomuClient } from '@Loomu/sdk';

const client = new LoomuClient({
  apiKey: 'your-api-key',
  baseURL: 'https://api.Loomu.so'
});

// 创建聊天完成
const response = await client.chat.completions.create({
  model: 'Loomu-v1',
  messages: [
    { role: 'user', content: '你好，请帮我分析这个数据文件' }
  ],
  stream: true
});

console.log(response.choices[0].message.content);`,

  python: `# 安装 SDK
pip install Loomu-sdk

# 基本使用
from Loomu import LoomuClient

client = LoomuClient(
    api_key="your-api-key",
    base_url="https://api.Loomu.so"
)

# 创建聊天完成
response = client.chat.completions.create(
    model="Loomu-v1",
    messages=[
        {"role": "user", "content": "你好，请帮我分析这个数据文件"}
    ],
    stream=True
)

print(response.choices[0].message.content)`,

  curl: `# 使用 cURL 调用 API
curl -X POST "https://api.Loomu.so/api/v1/chat/completions" \\
  -H "Authorization: Bearer your-api-key" \\
  -H "Content-Type: application/json" \\
  -d '{
    "model": "Loomu-v1",
    "messages": [
      {
        "role": "user",
        "content": "你好，请帮我分析这个数据文件"
      }
    ],
    "stream": false
  }'`
};

// 目录导航结构
const tocItems = [
  { id: 'overview', title: '概览', icon: Code },
  { id: 'quick-start', title: '快速开始', icon: Zap },
  { id: 'authentication', title: '身份认证', icon: Shield },
  { id: 'endpoints', title: 'API 端点', icon: Globe },
  { id: 'examples', title: '代码示例', icon: Code },
  { id: 'sdks', title: '官方 SDK', icon: Download },
  { id: 'rate-limits', title: '速率限制', icon: AlertTriangle }
];

export default function ApiDocsPage() {
  const router = useRouter();
  const tablet = useMediaQuery('(max-width: 1024px)');
  const [mounted, setMounted] = useState(false);
  const [isScrolling, setIsScrolling] = useState(false);
  const [activeSection, setActiveSection] = useState('overview');

  useEffect(() => {
    setMounted(true);
  }, []);

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
  };

  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
      setActiveSection(sectionId);
    }
  };

  // 监听滚动来更新活跃的导航项
  useEffect(() => {
    const handleScroll = () => {
      const sections = tocItems.map(item => item.id);
      const scrollPosition = window.scrollY + 100; // 偏移量

      for (let i = sections.length - 1; i >= 0; i--) {
        const element = document.getElementById(sections[i]);
        if (element && element.offsetTop <= scrollPosition) {
          setActiveSection(sections[i]);
          break;
        }
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  return (
    <main className="flex flex-col items-center justify-center min-h-screen w-full">
      <section className="w-full relative overflow-hidden pb-20">
        <div className="relative flex flex-col items-center w-full px-6 pt-10">
          {/* Left side flickering grid with gradient fades */}
          <div className="absolute left-0 top-0 h-[600px] w-1/3 -z-10 overflow-hidden">
            <div className="absolute inset-0 bg-gradient-to-r from-transparent via-transparent to-background z-10" />
            <div className="absolute inset-x-0 top-0 h-32 bg-gradient-to-b from-background via-background/90 to-transparent z-10" />
            <div className="absolute inset-x-0 bottom-0 h-48 bg-gradient-to-t from-background via-background/90 to-transparent z-10" />

            <FlickeringGrid
              className="h-full w-full"
              squareSize={mounted && tablet ? 2 : 2.5}
              gridGap={mounted && tablet ? 2 : 2.5}
              color="var(--secondary)"
              maxOpacity={0.4}
              flickerChance={isScrolling ? 0.01 : 0.03}
            />
          </div>

          {/* Right side flickering grid with gradient fades */}
          <div className="absolute right-0 top-0 h-[600px] w-1/3 -z-10 overflow-hidden">
            <div className="absolute inset-0 bg-gradient-to-l from-transparent via-transparent to-background z-10" />
            <div className="absolute inset-x-0 top-0 h-32 bg-gradient-to-b from-background via-background/90 to-transparent z-10" />
            <div className="absolute inset-x-0 bottom-0 h-48 bg-gradient-to-t from-background via-background/90 to-transparent z-10" />

            <FlickeringGrid
              className="h-full w-full"
              squareSize={mounted && tablet ? 2 : 2.5}
              gridGap={mounted && tablet ? 2 : 2.5}
              color="var(--secondary)"
              maxOpacity={0.4}
              flickerChance={isScrolling ? 0.01 : 0.03}
            />
          </div>

          {/* Center content background with rounded bottom */}
          <div className="absolute inset-x-1/4 top-0 h-[600px] -z-20 bg-background rounded-b-xl"></div>

          <div className="max-w-7xl w-full mx-auto">
            <div className="flex items-center justify-center mb-10 relative">
              <button
                onClick={() => router.back()}
                className="absolute left-0 group border border-border/50 bg-background hover:bg-accent/20 hover:border-secondary/40 rounded-full text-sm h-8 px-3 flex items-center gap-2 transition-all duration-300 shadow-sm hover:shadow-md hover:scale-105"
              >
                <ArrowLeft size={14} className="text-muted-foreground" />
                <span className="font-medium text-muted-foreground text-xs tracking-wide">
                  返回
                </span>
              </button>

              <h1 className="text-3xl md:text-4xl font-medium tracking-tighter text-center">
                API <span className="text-secondary">开发文档</span>
              </h1>
            </div>

            <div className="flex gap-8 ">
              {/* 固定的左侧目录导航 */}
              <aside className="w-64 flex-shrink-0 fixed top-40">
                <div className="sticky top-24 max-h-[calc(100vh-6rem)] overflow-y-auto">
                  <div className="p-4 border border-border rounded-lg bg-card/50 backdrop-blur-sm">
                    <h3 className="font-semibold text-sm mb-4 text-foreground">文档目录</h3>
                    <nav className="space-y-1">
                      {tocItems.map((item) => {
                        const Icon = item.icon;
                        const isActive = activeSection === item.id;
                        
                        return (
                          <button
                            key={item.id}
                            onClick={() => scrollToSection(item.id)}
                            className={`w-full flex items-center gap-2 px-3 py-2 text-sm rounded-md transition-all duration-200 text-left ${
                              isActive
                                ? 'bg-primary text-primary-foreground font-medium'
                                : 'text-muted-foreground hover:text-foreground hover:bg-accent'
                            }`}
                          >
                            <Icon className="h-4 w-4 flex-shrink-0" />
                            <span className="truncate">{item.title}</span>
                            {isActive && <ChevronRight className="h-3 w-3 ml-auto flex-shrink-0" />}
                          </button>
                        );
                      })}
                    </nav>
                  </div>
                </div>
              </aside>

              {/* 右侧主要内容区域 */}
              <main className="flex-1 min-w-0 ml-74">
                <div className="rounded-xl border border-border bg-[#F3F4F6] dark:bg-[#F9FAFB]/[0.02] p-8 shadow-sm">
                  <div className="prose prose-sm max-w-none dark:prose-invert">
                    {/* Header */}
                    <div id="overview" className="mb-8">
                      <div className="flex items-center gap-3 mb-4">
                        <div className="p-2 bg-blue-500 rounded-lg">
                          <Code className="h-6 w-6 text-white" />
                        </div>
                        <div>
                          <h2 className="text-2xl font-medium tracking-tight mb-1">Loomu API 文档</h2>
                          <p className="text-sm text-muted-foreground">
                            为开发者提供强大的 AI 能力集成 - 版本 1.0
                          </p>
                        </div>
                      </div>

                      <div className="bg-blue-50 dark:bg-blue-950/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4 mb-6">
                        <div className="flex items-start gap-3">
                          <Zap className="h-5 w-5 text-blue-600 dark:text-blue-400 mt-0.5 flex-shrink-0" />
                          <div>
                            <h3 className="font-semibold text-blue-800 dark:text-blue-300 mb-2">快速开始</h3>
                            <p className="text-sm text-blue-700 dark:text-blue-400">
                              Loomu API 为开发者提供强大的 AI 能力集成，支持聊天完成、项目管理、连接器配置等功能。通过简单的 REST API 调用即可集成到您的应用中。
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div className="space-y-8">
                      {/* Quick Start Steps */}
                      <section id="quick-start">
                        <h3 className="text-xl font-semibold mb-4 flex items-center gap-2">
                          <Zap className="h-5 w-5 text-yellow-500" />
                          快速开始
                        </h3>
                        
                        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
                          <Card className="border-border">
                            <CardHeader className="pb-4">
                              <CardTitle className="text-lg flex items-center gap-2">
                                <Key className="h-5 w-5 text-green-500" />
                                1. 获取 API 密钥
                              </CardTitle>
                            </CardHeader>
                            <CardContent>
                              <p className="text-sm text-muted-foreground mb-3">
                                在控制台的设置页面生成您的 API 密钥
                              </p>
                              <Button variant="outline" size="sm" asChild>
                                <a href="/dashboard/settings/api" target="_blank">
                                  <ExternalLink className="h-4 w-4 mr-2" />
                                  生成密钥
                                </a>
                              </Button>
                            </CardContent>
                          </Card>

                          <Card className="border-border">
                            <CardHeader className="pb-4">
                              <CardTitle className="text-lg flex items-center gap-2">
                                <Download className="h-5 w-5 text-blue-500" />
                                2. 安装 SDK
                              </CardTitle>
                            </CardHeader>
                            <CardContent>
                              <p className="text-sm text-muted-foreground mb-3">
                                选择您喜欢的编程语言和 SDK
                              </p>
                              <code className="text-xs bg-muted px-2 py-1 rounded">
                                npm install @Loomu/sdk
                              </code>
                            </CardContent>
                          </Card>

                          <Card className="border-border">
                            <CardHeader className="pb-4">
                              <CardTitle className="text-lg flex items-center gap-2">
                                <Code className="h-5 w-5 text-purple-500" />
                                3. 开始编码
                              </CardTitle>
                            </CardHeader>
                            <CardContent>
                              <p className="text-sm text-muted-foreground mb-3">
                                参考下方示例代码开始集成
                              </p>
                              <p className="text-xs text-muted-foreground">查看代码示例了解详情</p>
                            </CardContent>
                          </Card>
                        </div>
                      </section>

                      {/* Authentication */}
                      <section id="authentication">
                        <h3 className="text-xl font-semibold mb-4 flex items-center gap-2">
                          <Shield className="h-5 w-5 text-green-500" />
                          身份认证
                        </h3>
                        
                        <Card className="border-border mb-6">
                          <CardHeader>
                            <CardTitle>API 密钥认证</CardTitle>
                            <CardDescription>
                              所有 API 请求都需要在 Authorization 头中包含有效的 API 密钥
                            </CardDescription>
                          </CardHeader>
                          <CardContent>
                            <div className="bg-muted p-4 rounded-lg">
                              <code className="text-sm">
                                Authorization: Bearer your-api-key-here
                              </code>
                              <Button 
                                variant="ghost" 
                                size="sm" 
                                className="ml-2"
                                onClick={() => copyToClipboard('Authorization: Bearer your-api-key-here')}
                              >
                                <Copy className="h-4 w-4" />
                              </Button>
                            </div>
                            <div className="mt-4 p-4 bg-yellow-50 dark:bg-yellow-950/20 border border-yellow-200 dark:border-yellow-900/50 rounded-lg">
                              <div className="flex items-start gap-2">
                                <AlertTriangle className="h-5 w-5 text-yellow-600 dark:text-yellow-400 mt-0.5" />
                                <div>
                                  <p className="text-sm font-medium text-yellow-800 dark:text-yellow-300">安全提醒</p>
                                  <p className="text-sm text-yellow-700 dark:text-yellow-400">
                                    请妥善保管您的 API 密钥，不要在客户端代码中暴露。建议使用环境变量存储密钥。
                                  </p>
                                </div>
                              </div>
                            </div>
                          </CardContent>
                        </Card>
                      </section>

                      {/* API Endpoints */}
                      <section id="endpoints">
                        <h3 className="text-xl font-semibold mb-4 flex items-center gap-2">
                          <Globe className="h-5 w-5 text-blue-500" />
                          API 端点
                        </h3>

                        <div className="space-y-4">
                          {apiEndpoints.map((endpoint, index) => (
                            <Card key={index} className="border-border">
                              <CardContent className="p-6">
                                <div className="flex items-center justify-between mb-3">
                                  <div className="flex items-center gap-3">
                                    <Badge 
                                      variant={endpoint.method === 'GET' ? 'secondary' : 'default'}
                                      className="font-mono text-xs"
                                    >
                                      {endpoint.method}
                                    </Badge>
                                    <code className="text-sm font-mono">{endpoint.path}</code>
                                  </div>
                                  <div className="flex items-center gap-2">
                                    {endpoint.auth && (
                                      <Badge variant="outline" className="text-xs">
                                        <Key className="h-3 w-3 mr-1" />
                                        需要认证
                                      </Badge>
                                    )}
                                    <Badge 
                                      variant={endpoint.status === 'stable' ? 'default' : 'secondary'}
                                      className="text-xs"
                                    >
                                      {endpoint.status === 'stable' ? (
                                        <CheckCircle className="h-3 w-3 mr-1" />
                                      ) : (
                                        <AlertTriangle className="h-3 w-3 mr-1" />
                                      )}
                                      {endpoint.status}
                                    </Badge>
                                  </div>
                                </div>
                                <p className="text-sm text-muted-foreground">{endpoint.description}</p>
                              </CardContent>
                            </Card>
                          ))}
                        </div>
                      </section>

                      {/* Code Examples */}
                      <section id="examples">
                        <h3 className="text-xl font-semibold mb-4 flex items-center gap-2">
                          <Code className="h-5 w-5 text-purple-500" />
                          代码示例
                        </h3>

                        <Tabs defaultValue="javascript" className="w-full">
                          <TabsList className="grid w-full grid-cols-3">
                            <TabsTrigger value="javascript">JavaScript</TabsTrigger>
                            <TabsTrigger value="python">Python</TabsTrigger>
                            <TabsTrigger value="curl">cURL</TabsTrigger>
                          </TabsList>
                          
                          {Object.entries(codeExamples).map(([lang, code]) => (
                            <TabsContent key={lang} value={lang}>
                              <Card className="border-border">
                                <CardHeader className="flex flex-row items-center justify-between">
                                  <CardTitle className="text-lg capitalize">{lang} 示例</CardTitle>
                                  <Button 
                                    variant="outline" 
                                    size="sm"
                                    onClick={() => copyToClipboard(code)}
                                  >
                                    <Copy className="h-4 w-4 mr-2" />
                                    复制代码
                                  </Button>
                                </CardHeader>
                                <CardContent>
                                  <pre className="bg-muted p-4 rounded-lg overflow-x-auto">
                                    <code className="text-sm">{code}</code>
                                  </pre>
                                </CardContent>
                              </Card>
                            </TabsContent>
                          ))}
                        </Tabs>
                      </section>

                      {/* SDKs */}
                      <section id="sdks">
                        <h3 className="text-xl font-semibold mb-4 flex items-center gap-2">
                          <Download className="h-5 w-5 text-green-500" />
                          官方 SDK
                        </h3>

                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                          {sdks.map((sdk, index) => (
                            <Card key={index} className="border-border">
                              <CardHeader>
                                <CardTitle className="flex items-center justify-between">
                                  {sdk.language}
                                  <Badge variant={sdk.status === 'stable' ? 'default' : 'secondary'}>
                                    {sdk.status}
                                  </Badge>
                                </CardTitle>
                                <CardDescription>{sdk.description}</CardDescription>
                              </CardHeader>
                              <CardContent className="space-y-4">
                                <div>
                                  <p className="text-sm font-medium mb-2">安装命令：</p>
                                  <div className="bg-muted p-2 rounded flex items-center justify-between">
                                    <code className="text-sm">{sdk.install}</code>
                                    <Button 
                                      variant="ghost" 
                                      size="sm"
                                      onClick={() => copyToClipboard(sdk.install)}
                                    >
                                      <Copy className="h-4 w-4" />
                                    </Button>
                                  </div>
                                </div>
                                <div className="flex justify-between items-center">
                                  <span className="text-sm text-muted-foreground">版本: {sdk.version}</span>
                                  <Button variant="outline" size="sm" asChild>
                                    <a href={sdk.docs} target="_blank">
                                      <ExternalLink className="h-4 w-4 mr-2" />
                                      文档
                                    </a>
                                  </Button>
                                </div>
                              </CardContent>
                            </Card>
                          ))}
                        </div>
                      </section>

                      {/* Rate Limits */}
                      <section id="rate-limits">
                        <h3 className="text-xl font-semibold mb-4 flex items-center gap-2">
                          <AlertTriangle className="h-5 w-5 text-orange-500" />
                          速率限制
                        </h3>
                        
                        <Card className="border-border">
                          <CardContent className="p-6">
                            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                              <div className="text-center">
                                <div className="text-2xl font-bold text-primary mb-2">1,000</div>
                                <p className="text-sm text-muted-foreground">每分钟请求</p>
                              </div>
                              <div className="text-center">
                                <div className="text-2xl font-bold text-primary mb-2">10,000</div>
                                <p className="text-sm text-muted-foreground">每小时请求</p>
                              </div>
                              <div className="text-center">
                                <div className="text-2xl font-bold text-primary mb-2">100,000</div>
                                <p className="text-sm text-muted-foreground">每日请求</p>
                              </div>
                            </div>
                            <div className="mt-6 p-4 bg-blue-50 dark:bg-blue-950/20 border border-blue-200 dark:border-blue-900/50 rounded-lg">
                              <p className="text-sm text-blue-800 dark:text-blue-300">
                                <strong>提示：</strong> 如果您需要更高的速率限制，请联系我们的技术支持团队。
                              </p>
                            </div>
                          </CardContent>
                        </Card>
                      </section>
                    </div>

                    <div className="mt-12 pt-8 border-t border-border text-center">
                      <p className="text-sm text-muted-foreground mb-2">
                        本API文档于2024年12月1日更新，版本号：1.0
                      </p>
                      <p className="text-xs text-muted-foreground">
                        © 2024 LoomuAI. 保留所有权利。如有疑问请联系 <a href="mailto:<EMAIL>" className="text-primary hover:underline"><EMAIL></a>
                      </p>
                    </div>
                  </div>
                </div>
              </main>
            </div>
          </div>
        </div>
      </section>
    </main>
  );
} 