'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { FlickeringGrid } from '@/components/home/<USER>/flickering-grid';
import { useMediaQuery } from '@/hooks/use-media-query';
import { ArrowLeft } from 'lucide-react';
import { useRouter } from 'next/navigation';

export default function PrivacyPolicyPage() {
  const tablet = useMediaQuery('(max-width: 1024px)');
  const [mounted, setMounted] = useState(false);
  const [isScrolling, setIsScrolling] = useState(false);
  const router = useRouter();

  useEffect(() => {
    setMounted(true);
  }, []);

  return (
    <main className="flex flex-col items-center justify-center min-h-screen w-full">
      <section className="w-full relative overflow-hidden pb-20">
        <div className="relative flex flex-col items-center w-full px-6 pt-10">
          {/* Left side flickering grid with gradient fades */}
          <div className="absolute left-0 top-0 h-[600px] w-1/3 -z-10 overflow-hidden">
            <div className="absolute inset-0 bg-gradient-to-r from-transparent via-transparent to-background z-10" />
            <div className="absolute inset-x-0 top-0 h-32 bg-gradient-to-b from-background via-background/90 to-transparent z-10" />
            <div className="absolute inset-x-0 bottom-0 h-48 bg-gradient-to-t from-background via-background/90 to-transparent z-10" />

            <FlickeringGrid
              className="h-full w-full"
              squareSize={mounted && tablet ? 2 : 2.5}
              gridGap={mounted && tablet ? 2 : 2.5}
              color="var(--secondary)"
              maxOpacity={0.4}
              flickerChance={isScrolling ? 0.01 : 0.03}
            />
          </div>

          {/* Right side flickering grid with gradient fades */}
          <div className="absolute right-0 top-0 h-[600px] w-1/3 -z-10 overflow-hidden">
            <div className="absolute inset-0 bg-gradient-to-l from-transparent via-transparent to-background z-10" />
            <div className="absolute inset-x-0 top-0 h-32 bg-gradient-to-b from-background via-background/90 to-transparent z-10" />
            <div className="absolute inset-x-0 bottom-0 h-48 bg-gradient-to-t from-background via-background/90 to-transparent z-10" />

            <FlickeringGrid
              className="h-full w-full"
              squareSize={mounted && tablet ? 2 : 2.5}
              gridGap={mounted && tablet ? 2 : 2.5}
              color="var(--secondary)"
              maxOpacity={0.4}
              flickerChance={isScrolling ? 0.01 : 0.03}
            />
          </div>

          {/* Center content background with rounded bottom */}
          <div className="absolute inset-x-1/4 top-0 h-[600px] -z-20 bg-background rounded-b-xl"></div>

          <div className="max-w-4xl w-full mx-auto">
            <div className="flex items-center justify-center mb-10 relative">
              <button
                onClick={() => router.back()}
                className="absolute left-0 group border border-border/50 bg-background hover:bg-accent/20 hover:border-secondary/40 rounded-full text-sm h-8 px-3 flex items-center gap-2 transition-all duration-300 shadow-sm hover:shadow-md hover:scale-105"
              >
                <ArrowLeft size={14} className="text-muted-foreground" />
                <span className="font-medium text-muted-foreground text-xs tracking-wide">
                  返回
                </span>
              </button>

              <h1 className="text-3xl md:text-4xl font-medium tracking-tighter text-center">
                隐私 <span className="text-secondary">政策</span>
              </h1>
            </div>

            <div className="bg-background p-8">
              <div className="prose prose-lg max-w-none">
                <h1 className="text-2xl font-bold mb-2">太初（LoomuAI）隐私政策（中文）</h1>
                <p className="text-muted-foreground mb-8">最后更新日期：2025年06月01日</p>

                <p className="mb-6">
                  本隐私政策旨在说明思维涌动科技有限公司（以下简称"我们"或"公司"）在您使用太初（LoomuAI）web端软件服务（以下简称"本服务"）时，对信息的收集、使用和披露规则，并告知您的隐私权利及法律对您的保护。
                </p>
                
                <p className="mb-6">
                  使用本服务即表示您同意我们按照本隐私政策收集、使用和处理相关信息。
                </p>

                <h2 className="text-xl font-semibold mb-4 mt-8">一、术语定义</h2>
                
                <p className="mb-4">以下术语在本隐私政策中具有如下含义：</p>
                
                <ul className="list-disc pl-6 mb-4 space-y-2">
                  <li>太初/LoomuAI：指由思维涌动科技有限公司提供的web端软件服务及相关平台。</li>
                  <li>公司：指思维涌动科技有限公司，注册地址为[此处需补充公司实际注册地址]。</li>
                  <li>个人信息：指以电子或者其他方式记录的与已识别或者可识别的自然人有关的各种信息，不包括匿名化处理后的信息。</li>
                  <li>设备：指可访问本服务的终端设备，如电脑、手机、平板等。</li>
                  <li>用户：指使用本服务的自然人、法人或其他组织。</li>
                  <li>服务提供商：指受我们委托处理个人信息的第三方机构。</li>
                </ul>

                <h2 className="text-xl font-semibold mb-4 mt-8">二、个人信息的收集与使用</h2>
                
                <p className="mb-4">我们仅在必要、合理的范围内收集和使用您的个人信息，具体分为以下两类：</p>
                
                <h3 className="text-lg font-medium mb-3 mt-6">（一）主动提供的个人信息</h3>
                <p className="mb-4">当您使用本服务时，我们可能会收集以下与您相关的个人信息：</p>
                
                <ul className="list-disc pl-6 mb-4 space-y-2">
                  <li>注册信息：用于创建账户的手机号码、电子邮箱、用户名、密码等。</li>
                  <li>身份验证信息：在需要实名认证的场景下，可能收集姓名、身份证号码等。</li>
                  <li>联系信息：用于沟通服务需求的联系电话、收货地址等。</li>
                  <li>其他信息：您主动提交的反馈内容、咨询记录等。</li>
                </ul>
                
                <h3 className="text-lg font-medium mb-3 mt-6">（二）自动收集的使用信息</h3>
                <p className="mb-4">您使用本服务时，系统可能自动采集以下数据：</p>
                
                <ul className="list-disc pl-6 mb-4 space-y-2">
                  <li>设备信息：设备型号、操作系统、IP地址、唯一设备标识符（如IMEI/MEID）等。</li>
                  <li>日志信息：访问时间、浏览记录、点击操作、服务使用时长等。</li>
                  <li>位置信息：经您授权后，可能收集您的地理位置信息以优化服务体验。</li>
                </ul>
                
                <h3 className="text-lg font-medium mb-3 mt-6">（三）收集与使用的合法基础</h3>
                <p className="mb-4">我们收集和使用个人信息的法律依据包括：</p>
                
                <ul className="list-disc pl-6 mb-4 space-y-2">
                  <li>履行与您的服务合同（如账户注册、服务提供）；</li>
                  <li>遵守法律法规要求（如网络安全法、个人信息保护法）；</li>
                  <li>保障您的合法权益（如处理投诉、预防欺诈）；</li>
                  <li>基于您的明确同意（如获取位置信息、推送个性化内容）。</li>
                </ul>

                <h2 className="text-xl font-semibold mb-4 mt-8">三、Cookie及同类技术的使用</h2>
                
                <p className="mb-4">为提升服务体验，我们可能使用Cookie、像素标签等技术：</p>
                
                <h3 className="text-lg font-medium mb-3 mt-6">Cookie的类型与用途：</h3>
                <ul className="list-disc pl-6 mb-4 space-y-2">
                  <li>必需Cookie：用于保障服务基本功能（如账户登录、页面跳转），拒绝此类Cookie将导致部分服务无法使用。</li>
                  <li>功能Cookie：记住您的偏好设置（如语言选择、字体大小），提升个性化体验。</li>
                  <li>分析Cookie：统计用户行为数据（如页面访问量），用于优化服务性能。</li>
                </ul>
                
                <h3 className="text-lg font-medium mb-3 mt-6">管理Cookie的权利：</h3>
                <p className="mb-4">您可通过浏览器设置拒绝或删除Cookie，但可能影响服务的正常使用。具体操作可参考浏览器帮助文档。</p>

                <h2 className="text-xl font-semibold mb-4 mt-8">四、个人信息的共享、转让与披露</h2>
                
                <h3 className="text-lg font-medium mb-3 mt-6">（一）共享对象与情形</h3>
                <p className="mb-4">我们仅在以下情形下共享您的个人信息：</p>
                
                <ul className="list-disc pl-6 mb-4 space-y-2">
                  <li>服务提供商：委托第三方处理数据（如服务器维护、支付结算），我们将通过协议要求其严格保密并仅按授权使用。</li>
                  <li>关联公司：在集团内共享信息以提供协同服务，但需确保关联公司遵守同等保密义务。</li>
                  <li>法律要求：应司法机关、行政机关的合法要求，披露相关信息。</li>
                </ul>
                
                <h3 className="text-lg font-medium mb-3 mt-6">（二）转让与披露</h3>
                <ul className="list-disc pl-6 mb-4 space-y-2">
                  <li>业务转让：如发生合并、收购、资产转让等情形，我们将确保受让方继续履行本隐私政策项下的义务。</li>
                  <li>保护权益：为维护用户权益、平台安全或公共利益，在必要范围内披露相关信息。</li>
                </ul>

                <h2 className="text-xl font-semibold mb-4 mt-8">五、个人信息的存储与跨境传输</h2>
                
                <h3 className="text-lg font-medium mb-3 mt-6">（一）存储期限</h3>
                <p className="mb-4">我们仅在实现处理目的所必需的最短时间内保留您的个人信息，法律另有规定的除外。例如：</p>
                
                <ul className="list-disc pl-6 mb-4 space-y-2">
                  <li>账户信息在您注销账户后[X]年内删除；</li>
                  <li>日志信息保存[X]个月后匿名化处理。</li>
                </ul>
                
                <h3 className="text-lg font-medium mb-3 mt-6">（二）跨境传输</h3>
                <p className="mb-4">若因业务需要将您的个人信息传输至境外，我们将确保符合中国法律法规关于数据出境的规定（如通过安全评估、签订标准合同等），并保障您的信息安全。</p>

                <h2 className="text-xl font-semibold mb-4 mt-8">六、您的隐私权利</h2>
                
                <p className="mb-4">根据《个人信息保护法》，您享有以下权利：</p>
                
                <ul className="list-disc pl-6 mb-4 space-y-2">
                  <li>查询权：可随时查看您的个人信息收集、使用情况。</li>
                  <li>更正权：发现信息有误时，可要求我们更正或补充。</li>
                  <li>删除权：符合法定情形（如服务终止、撤回同意）时，可请求删除个人信息。</li>
                  <li>撤回同意权：可通过本服务设置或联系我们，撤回对特定信息收集的授权。</li>
                  <li>注销账户：您可通过"账户设置-注销"功能申请注销账户，我们将在[X]个工作日内处理并反馈。</li>
                </ul>

                <h2 className="text-xl font-semibold mb-4 mt-8">七、儿童个人信息保护</h2>
                
                <p className="mb-4">本服务不面向14周岁以下儿童。若您是14周岁以下儿童的监护人，请勿让儿童使用本服务。如我们发现收集了儿童个人信息，将立即删除并通知监护人。</p>

                <h2 className="text-xl font-semibold mb-4 mt-8">八、第三方链接声明</h2>
                
                <p className="mb-4">本服务可能包含第三方网站或服务的链接，我们对第三方的隐私政策和行为不承担责任。建议您在访问第三方链接时，仔细阅读其隐私条款。</p>

                <h2 className="text-xl font-semibold mb-4 mt-8">九、隐私政策的变更与通知</h2>
                
                <p className="mb-4">我们可能适时更新本政策，重大变更将通过APP通知、邮件或公告形式告知。更新后的政策自公布之日起生效，您继续使用服务即视为接受新条款。</p>

                <h2 className="text-xl font-semibold mb-4 mt-8">十、联系我们</h2>
                
                <p className="mb-4">如对本隐私政策有任何疑问或投诉，请通过以下方式联系我们：</p>
                
                <ul className="list-disc pl-6 mb-4 space-y-2">
                  <li>客服邮箱：<EMAIL></li>
                  <li>联系地址：[此处需补充公司实际办公地址]</li>
                  <li>处理时效：我们将在15个工作日内回复您的请求（需补充材料的情形除外）。</li>
                </ul>

                <div className="mt-12 pt-8 border-t border-border">
                  <p className="text-sm text-muted-foreground text-center">
                    思维涌动科技有限公司<br />
                    2025年06月01日
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </main>
  );
} 