'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/components/AuthProvider';
import { FlickeringGrid } from '@/components/home/<USER>/flickering-grid';
import { useMediaQuery } from '@/hooks/use-media-query';
import { 
  ArrowLeft,
  FileText, 
  Shield, 
  Gavel, 
  Code, 
  BookOpen, 
  ArrowRight,
  Search,
  Star,
  Users,
  Home,
  BarChart3
} from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';

const documentSections = [
  {
    title: '开发文档',
    description: 'API 接口文档、SDK 使用指南和技术集成说明',
    href: '/docs/api',
    icon: Code,
    badge: '技术',
    color: 'bg-blue-500',
    features: ['REST API', 'WebSocket', 'SDK', '认证授权']
  },
  {
    title: '使用指南',
    description: '快速开始、功能介绍和最佳实践指南',
    href: '/docs/guide',
    icon: BookOpen,
    badge: '指南',
    color: 'bg-green-500',
    features: ['快速开始', '功能介绍', '最佳实践', '常见问题']
  },
  {
    title: '用户协议',
    description: '用户注册和使用服务的协议条款',
    href: '/docs/agreement',
    icon: FileText,
    badge: '法律',
    color: 'bg-red-500',
    features: ['注册条款', '账户管理', '服务使用', '违规处理']
  }
];

const quickLinks = [
  { title: 'API 快速开始', href: '/docs/api#quick-start' },
  { title: '认证说明', href: '/docs/api#authentication' },
  { title: '功能概览', href: '/docs/guide#features' },
  { title: '常见问题', href: '/docs/guide#faq' },
  { title: '隐私政策', href: '/docs/privacy' },
  { title: '服务条款', href: '/docs/terms' }
];

const stats = [
  { label: '文档页面', value: '25+', icon: FileText },
  { label: 'API 接口', value: '50+', icon: Code },
  { label: '使用示例', value: '100+', icon: BookOpen }
];

export default function DocsHomePage() {
  const router = useRouter();
  const { user, isLoading } = useAuth();
  const tablet = useMediaQuery('(max-width: 1024px)');
  const [mounted, setMounted] = useState(false);
  const [isScrolling, setIsScrolling] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);


  return (
    <main className="flex flex-col items-center justify-center min-h-screen w-full">
      <section className="w-full relative overflow-hidden pb-20">
        <div className="relative flex flex-col items-center w-full px-6 pt-10">
          {/* Left side flickering grid with gradient fades */}
          <div className="absolute left-0 top-0 h-[600px] w-1/3 -z-10 overflow-hidden">
            <div className="absolute inset-0 bg-gradient-to-r from-transparent via-transparent to-background z-10" />
            <div className="absolute inset-x-0 top-0 h-32 bg-gradient-to-b from-background via-background/90 to-transparent z-10" />
            <div className="absolute inset-x-0 bottom-0 h-48 bg-gradient-to-t from-background via-background/90 to-transparent z-10" />

            <FlickeringGrid
              className="h-full w-full"
              squareSize={mounted && tablet ? 2 : 2.5}
              gridGap={mounted && tablet ? 2 : 2.5}
              color="var(--secondary)"
              maxOpacity={0.4}
              flickerChance={isScrolling ? 0.01 : 0.03}
            />
          </div>

          {/* Right side flickering grid with gradient fades */}
          <div className="absolute right-0 top-0 h-[600px] w-1/3 -z-10 overflow-hidden">
            <div className="absolute inset-0 bg-gradient-to-l from-transparent via-transparent to-background z-10" />
            <div className="absolute inset-x-0 top-0 h-32 bg-gradient-to-b from-background via-background/90 to-transparent z-10" />
            <div className="absolute inset-x-0 bottom-0 h-48 bg-gradient-to-t from-background via-background/90 to-transparent z-10" />

            <FlickeringGrid
              className="h-full w-full"
              squareSize={mounted && tablet ? 2 : 2.5}
              gridGap={mounted && tablet ? 2 : 2.5}
              color="var(--secondary)"
              maxOpacity={0.4}
              flickerChance={isScrolling ? 0.01 : 0.03}
            />
          </div>

          {/* Center content background with rounded bottom */}
          <div className="absolute inset-x-1/4 top-0 h-[600px] -z-20 bg-background rounded-b-xl"></div>

          <div className="max-w-6xl w-full mx-auto">
            <div className="flex items-center justify-center mb-10 relative">
              <button
                onClick={() => router.back()}
                className="absolute left-0 group border border-border/50 bg-background hover:bg-accent/20 hover:border-secondary/40 rounded-full text-sm h-8 px-3 flex items-center gap-2 transition-all duration-300 shadow-sm hover:shadow-md hover:scale-105"
              >
                <ArrowLeft size={14} className="text-muted-foreground" />
                <span className="font-medium text-muted-foreground text-xs tracking-wide">
                  返回
                </span>
              </button>

              <h1 className="text-3xl md:text-4xl font-medium tracking-tighter text-center">
                Loomu <span className="text-secondary">产品文档</span>
              </h1>
            </div>

            <div className="rounded-xl border border-border bg-[#F3F4F6] dark:bg-[#F9FAFB]/[0.02] p-8 shadow-sm">
              <div className="prose prose-sm max-w-none dark:prose-invert">
                <div className="flex items-center gap-3 mb-6">
                  <div className="p-2 bg-blue-500 rounded-lg">
                    <BookOpen className="h-6 w-6 text-white" />
                  </div>
                  <div>
                    <h2 className="text-2xl font-medium tracking-tight mb-1">文档中心</h2>
                    <p className="text-sm text-muted-foreground">
                      全面的产品文档，包含 API 接口、使用指南、法律条款等内容
                    </p>
                  </div>
                </div>

                <div className="bg-green-50 dark:bg-green-950/20 border border-green-200 dark:border-green-800 rounded-lg p-4 mb-6">
                  <div className="flex items-start gap-3">
                    <Star className="h-5 w-5 text-green-600 dark:text-green-400 mt-0.5 flex-shrink-0" />
                    <div>
                      <h3 className="font-semibold text-green-800 dark:text-green-300 mb-2">欢迎使用 Loomu 文档</h3>
                      <p className="text-sm text-green-700 dark:text-green-400">
                        这里提供了完整的产品使用指南，帮助您快速上手和集成 Loomu AI 助手。无论您是开发者还是普通用户，都能在这里找到需要的信息。
                      </p>
                    </div>
                  </div>
                </div>

                <div className="space-y-8">
                  {/* Search Bar */}
                  <section>
                    <div className="max-w-md mx-auto mb-8">
                      <div className="relative">
                        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                        <Input
                          placeholder="搜索文档内容..."
                          className="pl-10 pr-4 py-3 bg-background border-border rounded-lg"
                        />
                      </div>
                    </div>

                    {/* Stats */}
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                      {stats.map((stat, index) => {
                        const Icon = stat.icon;
                        return (
                          <div key={index} className="text-center">
                            <div className="flex items-center justify-center mb-2">
                              <Icon className="h-5 w-5 text-primary mr-2" />
                              <span className="text-2xl font-bold text-foreground">{stat.value}</span>
                            </div>
                            <p className="text-sm text-muted-foreground">{stat.label}</p>
                          </div>
                        );
                      })}
                    </div>
                  </section>

                  {/* Document Sections */}
                  <section>
                    <h3 className="text-xl font-semibold mb-6 text-foreground">文档分类</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
                      {documentSections.map((section, index) => {
                        const Icon = section.icon;
                        return (
                          <Link key={index} href={section.href}>
                            <Card className="h-full hover:shadow-lg transition-all duration-200 group cursor-pointer border-border">
                              <CardHeader className="pb-4">
                                <div className="flex items-start justify-between mb-3">
                                  <div className={`p-3 rounded-lg ${section.color}`}>
                                    <Icon className="h-6 w-6 text-white" />
                                  </div>
                                  <Badge variant="secondary" className="text-xs">
                                    {section.badge}
                                  </Badge>
                                </div>
                                <CardTitle className="text-lg group-hover:text-primary transition-colors">
                                  {section.title}
                                </CardTitle>
                                <CardDescription className="text-sm">
                                  {section.description}
                                </CardDescription>
                              </CardHeader>
                              <CardContent className="pt-0">
                                <div className="space-y-2 mb-4">
                                  {section.features.map((feature, idx) => (
                                    <div key={idx} className="flex items-center text-sm text-muted-foreground">
                                      <div className="w-1.5 h-1.5 rounded-full bg-primary mr-2" />
                                      {feature}
                                    </div>
                                  ))}
                                </div>
                                <div className="flex items-center text-sm text-primary group-hover:text-primary/80 transition-colors">
                                  查看详情
                                  <ArrowRight className="h-4 w-4 ml-1 group-hover:translate-x-1 transition-transform" />
                                </div>
                              </CardContent>
                            </Card>
                          </Link>
                        );
                      })}
                    </div>
                  </section>

                  {/* Quick Links */}
                  <section>
                    <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8">
                      <div className="lg:col-span-2">
                        <Card className="border-border">
                          <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                              <Star className="h-5 w-5 text-yellow-500" />
                              快速链接
                            </CardTitle>
                            <CardDescription>
                              常用文档页面的快捷访问
                            </CardDescription>
                          </CardHeader>
                          <CardContent>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                              {quickLinks.map((link, index) => (
                                <Link 
                                  key={index} 
                                  href={link.href}
                                  className="flex items-center justify-between p-3 rounded-lg border border-border hover:bg-accent transition-colors group"
                                >
                                  <span className="text-sm font-medium">{link.title}</span>
                                  <ArrowRight className="h-4 w-4 text-muted-foreground group-hover:text-foreground group-hover:translate-x-1 transition-all" />
                                </Link>
                              ))}
                            </div>
                          </CardContent>
                        </Card>
                      </div>

                      <div>
                        <Card className="border-border">
                          <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                              <Users className="h-5 w-5 text-blue-500" />
                              需要帮助？
                            </CardTitle>
                          </CardHeader>
                          <CardContent className="space-y-3">
                            <p className="text-sm text-muted-foreground">
                              如果您在使用过程中遇到问题，可以通过以下方式获取帮助：
                            </p>
                            <div className="space-y-2">
                              <Button variant="outline" size="sm" className="w-full justify-start" asChild>
                                <Link href="/docs/guide#faq">
                                  <BookOpen className="h-4 w-4 mr-2" />
                                  查看常见问题
                                </Link>
                              </Button>
                              <Button variant="outline" size="sm" className="w-full justify-start" asChild>
                                <Link href="mailto:<EMAIL>">
                                  <FileText className="h-4 w-4 mr-2" />
                                  联系技术支持
                                </Link>
                              </Button>
                            </div>
                          </CardContent>
                        </Card>
                      </div>
                    </div>
                  </section>
                </div>

                {/* Footer Info */}
                <div className="mt-12 pt-8 border-t border-border text-center">
                  <p className="text-sm text-muted-foreground mb-2">
                    文档持续更新中，最后更新时间：2024年12月
                  </p>
                  <p className="text-xs text-muted-foreground">
                    如有文档错误或建议，请通过 <Link href="mailto:<EMAIL>" className="text-primary hover:underline"><EMAIL></Link> 联系我们
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </main>
  );
} 