'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { cn } from '@/lib/utils';
import { 
  FileText, 
  Shield, 
  Gavel, 
  Code, 
  BookOpen, 
  Menu, 
  X,
  ChevronRight,
  Home
} from 'lucide-react';
import { Button } from '@/components/ui/button';

interface DocsLayoutProps {
  children: React.ReactNode;
}

interface NavItem {
  title: string;
  href: string;
  icon: React.ComponentType<{ className?: string }>;
  description?: string;
}

const navigation: NavItem[] = [
  {
    title: '文档首页',
    href: '/docs',
    icon: Home,
    description: '文档概览'
  },
  {
    title: '开发文档',
    href: '/docs/api',
    icon: Code,
    description: 'API 接口文档'
  },
  {
    title: '使用指南',
    href: '/docs/guide',
    icon: BookOpen,
    description: '快速开始指南'
  },
  {
    title: '用户协议',
    href: '/docs/agreement',
    icon: FileText,
    description: '用户服务协议'
  }
];

export default function DocsLayout({ children }: DocsLayoutProps) {
  return children;
} 