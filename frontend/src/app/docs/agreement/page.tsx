'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { FlickeringGrid } from '@/components/home/<USER>/flickering-grid';
import { useMediaQuery } from '@/hooks/use-media-query';
import { ArrowLeft } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { FileText, UserCheck, Shield, AlertCircle, Settings, Ban, Clock, Mail } from 'lucide-react';

// Force static generation for this page
export const dynamic = 'force-static';

export default function UserAgreementPage() {
  const router = useRouter();
  const tablet = useMediaQuery('(max-width: 1024px)');
  const [mounted, setMounted] = useState(false);
  const [isScrolling, setIsScrolling] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  return (
    <main className="flex flex-col items-center justify-center min-h-screen w-full">
      <section className="w-full relative overflow-hidden pb-20">
        <div className="relative flex flex-col items-center w-full px-6 pt-10">
          {/* Left side flickering grid with gradient fades */}
          <div className="absolute left-0 top-0 h-[600px] w-1/3 -z-10 overflow-hidden">
            <div className="absolute inset-0 bg-gradient-to-r from-transparent via-transparent to-background z-10" />
            <div className="absolute inset-x-0 top-0 h-32 bg-gradient-to-b from-background via-background/90 to-transparent z-10" />
            <div className="absolute inset-x-0 bottom-0 h-48 bg-gradient-to-t from-background via-background/90 to-transparent z-10" />

            <FlickeringGrid
              className="h-full w-full"
              squareSize={mounted && tablet ? 2 : 2.5}
              gridGap={mounted && tablet ? 2 : 2.5}
              color="var(--secondary)"
              maxOpacity={0.4}
              flickerChance={isScrolling ? 0.01 : 0.03}
            />
          </div>

          {/* Right side flickering grid with gradient fades */}
          <div className="absolute right-0 top-0 h-[600px] w-1/3 -z-10 overflow-hidden">
            <div className="absolute inset-0 bg-gradient-to-l from-transparent via-transparent to-background z-10" />
            <div className="absolute inset-x-0 top-0 h-32 bg-gradient-to-b from-background via-background/90 to-transparent z-10" />
            <div className="absolute inset-x-0 bottom-0 h-48 bg-gradient-to-t from-background via-background/90 to-transparent z-10" />

            <FlickeringGrid
              className="h-full w-full"
              squareSize={mounted && tablet ? 2 : 2.5}
              gridGap={mounted && tablet ? 2 : 2.5}
              color="var(--secondary)"
              maxOpacity={0.4}
              flickerChance={isScrolling ? 0.01 : 0.03}
            />
          </div>

          {/* Center content background with rounded bottom */}
          <div className="absolute inset-x-1/4 top-0 h-[600px] -z-20 bg-background rounded-b-xl"></div>

          <div className="max-w-4xl w-full mx-auto">
            <div className="flex items-center justify-center mb-10 relative">
              <button
                onClick={() => router.back()}
                className="absolute left-0 group border border-border/50 bg-background hover:bg-accent/20 hover:border-secondary/40 rounded-full text-sm h-8 px-3 flex items-center gap-2 transition-all duration-300 shadow-sm hover:shadow-md hover:scale-105"
              >
                <ArrowLeft size={14} className="text-muted-foreground" />
                <span className="font-medium text-muted-foreground text-xs tracking-wide">
                  返回
                </span>
              </button>

              <h1 className="text-3xl md:text-4xl font-medium tracking-tighter text-center">
                用户服务 <span className="text-secondary">协议</span>
              </h1>
            </div>

            <div className="rounded-xl border border-border bg-[#F3F4F6] dark:bg-[#F9FAFB]/[0.02] p-8 shadow-sm">
              <div className="prose prose-sm max-w-none dark:prose-invert">
                <div className="flex items-center gap-3 mb-6">
                  <div className="p-2 bg-red-500 rounded-lg">
                    <FileText className="h-6 w-6 text-white" />
                  </div>
                  <div>
                    <h2 className="text-2xl font-medium tracking-tight mb-1">Loomu 用户服务协议</h2>
                    <p className="text-sm text-muted-foreground">
                      版本 2.0 - 最后更新时间：2024年12月
                    </p>
                  </div>
                </div>

                {/* Agreement Highlight */}
                <div className="bg-green-50 dark:bg-green-950/20 border border-green-200 dark:border-green-800 rounded-lg p-4 mb-6">
                  <div className="flex items-start gap-3">
                    <UserCheck className="h-5 w-5 text-green-600 dark:text-green-400 mt-0.5 flex-shrink-0" />
                    <div>
                      <h3 className="font-semibold text-green-800 dark:text-green-300 mb-2">协议生效</h3>
                      <p className="text-sm text-green-700 dark:text-green-400">
                        当您点击"同意"、"注册"或以任何方式使用我们的服务时，即表示您已阅读、理解并同意受本协议约束。本协议是您与 Loomu 之间的法律合同。
                      </p>
                    </div>
                  </div>
                </div>

                <div className="space-y-8">
                  {/* Section 1: 协议范围 */}
                  <section>
                    <h3 className="text-xl font-semibold mb-4 flex items-center gap-2">
                      <FileText className="h-5 w-5 text-blue-500" />
                      1. 协议范围与定义
                    </h3>

                    <Card className="border-border">
                      <CardHeader>
                        <CardTitle className="text-lg">1.1 协议适用范围</CardTitle>
                        <CardDescription>本协议适用于所有 Loomu 服务的使用</CardDescription>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        <div>
                          <h4 className="font-semibold mb-2">涵盖的服务</h4>
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                            <div className="p-3 border border-border rounded-lg">
                              <h5 className="font-medium text-sm mb-1">核心服务</h5>
                              <ul className="text-xs text-muted-foreground space-y-1">
                                <li>• AI 对话助手</li>
                                <li>• 文档处理工具</li>
                                <li>• 数据分析功能</li>
                                <li>• 自动化工作流</li>
                              </ul>
                            </div>
                            <div className="p-3 border border-border rounded-lg">
                              <h5 className="font-medium text-sm mb-1">附加服务</h5>
                              <ul className="text-xs text-muted-foreground space-y-1">
                                <li>• API 接口服务</li>
                                <li>• 第三方集成</li>
                                <li>• 云存储服务</li>
                                <li>• 技术支持</li>
                              </ul>
                            </div>
                          </div>
                        </div>

                        <div>
                          <h4 className="font-semibold mb-2">重要定义</h4>
                          <div className="space-y-2">
                            <div className="p-2 bg-muted rounded">
                              <span className="font-medium text-sm">"用户"：</span>
                              <span className="text-sm text-muted-foreground">指注册并使用 Loomu 服务的个人或组织</span>
                            </div>
                            <div className="p-2 bg-muted rounded">
                              <span className="font-medium text-sm">"服务"：</span>
                              <span className="text-sm text-muted-foreground">指 Loomu 提供的所有产品、功能和相关服务</span>
                            </div>
                            <div className="p-2 bg-muted rounded">
                              <span className="font-medium text-sm">"内容"：</span>
                              <span className="text-sm text-muted-foreground">指用户上传、生成或通过服务创建的所有信息和数据</span>
                            </div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </section>

                  {/* Section 2: 注册条款 */}
                  <section>
                    <h3 className="text-xl font-semibold mb-4 flex items-center gap-2">
                      <UserCheck className="h-5 w-5 text-green-500" />
                      2. 用户注册与认证
                    </h3>

                    <div className="space-y-6">
                      <Card className="border-border">
                        <CardHeader>
                          <CardTitle className="text-lg">2.1 注册资格</CardTitle>
                        </CardHeader>
                        <CardContent>
                          <div className="space-y-4">
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                              <div className="p-4 border border-green-200 dark:border-green-800 bg-green-50 dark:bg-green-950/20 rounded-lg">
                                <h4 className="font-semibold text-green-800 dark:text-green-300 mb-2">个人用户</h4>
                                <ul className="text-sm text-green-700 dark:text-green-400 space-y-1">
                                  <li>• 年满18周岁的自然人</li>
                                  <li>• 具有完全民事行为能力</li>
                                  <li>• 提供真实有效的身份信息</li>
                                  <li>• 承诺合法使用服务</li>
                                </ul>
                              </div>
                              <div className="p-4 border border-blue-200 dark:border-blue-800 bg-blue-50 dark:bg-blue-950/20 rounded-lg">
                                <h4 className="font-semibold text-blue-800 dark:text-blue-300 mb-2">企业用户</h4>
                                <ul className="text-sm text-blue-700 dark:text-blue-400 space-y-1">
                                  <li>• 依法成立的企业或组织</li>
                                  <li>• 具有有效的营业执照</li>
                                  <li>• 由授权代表进行注册</li>
                                  <li>• 接受企业服务条款</li>
                                </ul>
                              </div>
                            </div>

                            <div className="p-4 border border-orange-200 dark:border-orange-800 bg-orange-50 dark:bg-orange-950/20 rounded-lg">
                              <div className="flex items-start gap-2">
                                <AlertCircle className="h-5 w-5 text-orange-600 dark:text-orange-400 mt-0.5 flex-shrink-0" />
                                <div>
                                  <h4 className="font-semibold text-orange-800 dark:text-orange-300 mb-1">特别说明</h4>
                                  <p className="text-sm text-orange-700 dark:text-orange-400">
                                    如果您所在地区的法定成年年龄高于18周岁，您必须达到该年龄才能注册。未成年人需要在监护人同意下使用服务。
                                  </p>
                                </div>
                              </div>
                            </div>
                          </div>
                        </CardContent>
                      </Card>

                      <Card className="border-border">
                        <CardHeader>
                          <CardTitle className="text-lg">2.2 注册流程与要求</CardTitle>
                        </CardHeader>
                        <CardContent>
                          <div className="space-y-4">
                            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                              <div className="text-center p-3 border border-border rounded-lg">
                                <div className="w-8 h-8 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center mx-auto mb-2">
                                  <span className="text-sm font-bold text-blue-600 dark:text-blue-400">1</span>
                                </div>
                                <h5 className="font-medium text-sm mb-1">填写信息</h5>
                                <p className="text-xs text-muted-foreground">提供真实准确的注册信息</p>
                              </div>
                              <div className="text-center p-3 border border-border rounded-lg">
                                <div className="w-8 h-8 rounded-full bg-green-100 dark:bg-green-900 flex items-center justify-center mx-auto mb-2">
                                  <span className="text-sm font-bold text-green-600 dark:text-green-400">2</span>
                                </div>
                                <h5 className="font-medium text-sm mb-1">邮箱验证</h5>
                                <p className="text-xs text-muted-foreground">验证邮箱地址的有效性</p>
                              </div>
                              <div className="text-center p-3 border border-border rounded-lg">
                                <div className="w-8 h-8 rounded-full bg-purple-100 dark:bg-purple-900 flex items-center justify-center mx-auto mb-2">
                                  <span className="text-sm font-bold text-purple-600 dark:text-purple-400">3</span>
                                </div>
                                <h5 className="font-medium text-sm mb-1">同意协议</h5>
                                <p className="text-xs text-muted-foreground">阅读并同意服务协议</p>
                              </div>
                              <div className="text-center p-3 border border-border rounded-lg">
                                <div className="w-8 h-8 rounded-full bg-orange-100 dark:bg-orange-900 flex items-center justify-center mx-auto mb-2">
                                  <span className="text-sm font-bold text-orange-600 dark:text-orange-400">4</span>
                                </div>
                                <h5 className="font-medium text-sm mb-1">账户激活</h5>
                                <p className="text-xs text-muted-foreground">完成注册并激活账户</p>
                              </div>
                            </div>

                            <div>
                              <h4 className="font-semibold mb-2">信息准确性承诺</h4>
                              <p className="text-sm text-muted-foreground mb-2">
                                您承诺提供的所有信息都是真实、准确、完整和最新的。如信息发生变化，您应及时更新。
                              </p>
                              <ul className="text-sm text-muted-foreground space-y-1 ml-4">
                                <li>• 提供虚假信息可能导致账户被暂停或终止</li>
                                <li>• 我们保留验证用户身份的权利</li>
                                <li>• 企业用户需要提供额外的认证文件</li>
                              </ul>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    </div>
                  </section>

                  {/* Section 3: 账户管理 */}
                  <section>
                    <h3 className="text-xl font-semibold mb-4 flex items-center gap-2">
                      <Settings className="h-5 w-5 text-purple-500" />
                      3. 账户管理
                    </h3>

                    <Card className="border-border">
                      <CardHeader>
                        <CardTitle className="text-lg">3.1 账户安全</CardTitle>
                        <CardDescription>保护您账户安全的重要措施</CardDescription>
                      </CardHeader>
                      <CardContent>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div className="p-3 bg-blue-50 dark:bg-blue-950/20 border border-blue-200 dark:border-blue-800 rounded">
                            <h5 className="font-medium text-blue-800 dark:text-blue-300 mb-1">密码要求</h5>
                            <p className="text-sm text-blue-700 dark:text-blue-400">至少8位字符，包含字母和数字</p>
                          </div>
                          <div className="p-3 bg-green-50 dark:bg-green-950/20 border border-green-200 dark:border-green-800 rounded">
                            <h5 className="font-medium text-green-800 dark:text-green-300 mb-1">两步验证</h5>
                            <p className="text-sm text-green-700 dark:text-green-400">建议启用双重身份验证</p>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </section>

                  {/* Section 4: 服务使用 */}
                  <section>
                    <h3 className="text-xl font-semibold mb-4 flex items-center gap-2">
                      <Shield className="h-5 w-5 text-green-500" />
                      4. 服务使用规范
                    </h3>

                    <Card className="border-border">
                      <CardHeader>
                        <CardTitle className="text-lg">4.1 使用限制</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div className="p-4 border border-red-200 dark:border-red-800 bg-red-50 dark:bg-red-950/20 rounded-lg">
                            <h5 className="font-semibold text-red-800 dark:text-red-300 mb-3">禁止行为</h5>
                            <ul className="text-sm text-red-700 dark:text-red-400 space-y-2">
                              <li>• 违反法律法规的内容生成</li>
                              <li>• 恶意攻击或试图破坏系统</li>
                              <li>• 侵犯他人知识产权</li>
                              <li>• 传播虚假或有害信息</li>
                              <li>• 滥用或过度使用服务资源</li>
                            </ul>
                          </div>
                          <div className="p-4 border border-green-200 dark:border-green-800 bg-green-50 dark:bg-green-950/20 rounded-lg">
                            <h5 className="font-semibold text-green-800 dark:text-green-300 mb-3">推荐做法</h5>
                            <ul className="text-sm text-green-700 dark:text-green-400 space-y-2">
                              <li>• 遵守使用量限制和时间间隔</li>
                              <li>• 尊重他人隐私和权益</li>
                              <li>• 提供准确真实的信息</li>
                              <li>• 合理使用共享资源</li>
                              <li>• 及时报告系统问题</li>
                            </ul>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </section>

                  {/* Section 5: 违规处理 */}
                  <section>
                    <h3 className="text-xl font-semibold mb-4 flex items-center gap-2">
                      <Ban className="h-5 w-5 text-red-500" />
                      5. 违规处理
                    </h3>

                    <Card className="border-border">
                      <CardHeader>
                        <CardTitle className="text-lg">5.1 处理措施</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-3">
                          <div className="flex items-center gap-4 p-3 border border-border rounded">
                            <div className="w-8 h-8 rounded-full bg-yellow-100 dark:bg-yellow-900 flex items-center justify-center">
                              <span className="text-sm font-bold text-yellow-600 dark:text-yellow-400">1</span>
                            </div>
                            <div className="flex-1">
                              <h5 className="font-medium text-sm">警告提醒</h5>
                              <p className="text-xs text-muted-foreground">首次轻微违规时的提醒</p>
                            </div>
                          </div>
                          <div className="flex items-center gap-4 p-3 border border-border rounded">
                            <div className="w-8 h-8 rounded-full bg-orange-100 dark:bg-orange-900 flex items-center justify-center">
                              <span className="text-sm font-bold text-orange-600 dark:text-orange-400">2</span>
                            </div>
                            <div className="flex-1">
                              <h5 className="font-medium text-sm">限制功能</h5>
                              <p className="text-xs text-muted-foreground">暂时限制部分服务功能</p>
                            </div>
                          </div>
                          <div className="flex items-center gap-4 p-3 border border-border rounded">
                            <div className="w-8 h-8 rounded-full bg-red-100 dark:bg-red-900 flex items-center justify-center">
                              <span className="text-sm font-bold text-red-600 dark:text-red-400">3</span>
                            </div>
                            <div className="flex-1">
                              <h5 className="font-medium text-sm">账户封禁</h5>
                              <p className="text-xs text-muted-foreground">严重违规时的最终处理</p>
                            </div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </section>

                  {/* Contact */}
                  <section>
                    <h3 className="text-xl font-semibold mb-4 flex items-center gap-2">
                      <Mail className="h-5 w-5 text-green-500" />
                      联系我们
                    </h3>
                    
                    <div className="border border-border rounded-lg p-6">
                      <p className="text-muted-foreground mb-4">
                        如果您对本用户协议有任何疑问或需要法律支持，请通过以下方式联系我们：
                      </p>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                          <h4 className="font-semibold mb-3">法务联系</h4>
                          <div className="space-y-2 text-sm">
                            <div className="flex items-center gap-2">
                              <Mail className="h-4 w-4 text-muted-foreground" />
                              <span><EMAIL></span>
                            </div>
                            <div className="flex items-center gap-2">
                              <FileText className="h-4 w-4 text-muted-foreground" />
                              <span>协议事务：<EMAIL></span>
                            </div>
                          </div>
                        </div>
                        <div>
                          <h4 className="font-semibold mb-3">响应承诺</h4>
                          <div className="space-y-2 text-sm text-muted-foreground">
                            <div>• 一般咨询：2个工作日内回复</div>
                            <div>• 协议争议：7个工作日内处理</div>
                            <div>• 紧急事项：24小时内响应</div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </section>
                </div>

                <div className="mt-12 pt-8 border-t border-border text-center">
                  <p className="text-sm text-muted-foreground mb-2">
                    本用户协议于2024年12月1日生效，版本号：2.0
                  </p>
                  <p className="text-xs text-muted-foreground">
                    © 2024 LoomuAI. 保留所有权利。本协议构成您与 Loomu 之间的完整法律协议。
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </main>
  );
} 