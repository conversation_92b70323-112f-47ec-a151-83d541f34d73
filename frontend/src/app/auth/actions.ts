'use server';

import { createClient } from '@/lib/supabase/server';
import { redirect } from 'next/navigation';

export async function signIn(prevState: any, formData: FormData) {
  const email = formData.get('email') as string;
  const password = formData.get('password') as string;
  const returnUrl = formData.get('returnUrl') as string | undefined;

  if (!email || !email.includes('@')) {
    return { message: 'auth.errors.invalidEmail' };
  }

  if (!password || password.length < 6) {
    return { message: 'auth.errors.passwordTooShort' };
  }

  const supabase = await createClient();

  const { error } = await supabase.auth.signInWithPassword({
    email,
    password,
  });

  if (error) {
    return { message: error.message || 'auth.errors.authenticationFailed' };
  }

  // Use client-side navigation instead of server-side redirect
  return { success: true, redirectTo: returnUrl || '/dashboard' };
}

export async function signUp(prevState: any, formData: FormData) {
  const origin = formData.get('origin') as string;
  const email = formData.get('email') as string;
  const password = formData.get('password') as string;
  const confirmPassword = formData.get('confirmPassword') as string;
  const returnUrl = formData.get('returnUrl') as string | undefined;

  if (!email || !email.includes('@')) {
    return { message: 'auth.errors.invalidEmail' };
  }

  if (!password || password.length < 6) {
    return { message: 'auth.errors.passwordTooShort' };
  }

  if (password !== confirmPassword) {
    return { message: 'auth.errors.passwordsDoNotMatch' };
  }

  const supabase = await createClient();

  const { error } = await supabase.auth.signUp({
    email,
    password,
    options: {
      emailRedirectTo: `${origin}/auth/callback?returnUrl=${returnUrl}`,
    },
  });

  if (error) {
    return { message: error.message || 'auth.errors.cannotCreateAccount' };
  }

  // Try to sign in immediately
  const { error: signInError } = await supabase.auth.signInWithPassword({
    email,
    password,
  });

  if (signInError) {
    return {
      message:
        'auth.errors.accountCreatedCheckEmail',
    };
  }

  // Use client-side navigation instead of server-side redirect
  return { success: true, redirectTo: returnUrl || '/dashboard' };
}

export async function forgotPassword(prevState: any, formData: FormData) {
  const email = formData.get('email') as string;
  const origin = formData.get('origin') as string;

  if (!email || !email.includes('@')) {
    return { message: 'auth.errors.invalidEmail' };
  }

  const supabase = await createClient();

  const { error } = await supabase.auth.resetPasswordForEmail(email, {
    redirectTo: `${origin}/auth/reset-password`,
  });

  if (error) {
    return { message: error.message || 'auth.errors.cannotSendResetEmail' };
  }

  return {
    success: true,
    message: 'auth.errors.checkEmailForResetLink',
  };
}

export async function resetPassword(prevState: any, formData: FormData) {
  const password = formData.get('password') as string;
  const confirmPassword = formData.get('confirmPassword') as string;

  if (!password || password.length < 6) {
    return { message: 'auth.errors.passwordTooShort' };
  }

  if (password !== confirmPassword) {
    return { message: 'auth.errors.passwordsDoNotMatch' };
  }

  const supabase = await createClient();

  const { error } = await supabase.auth.updateUser({
    password,
  });

  if (error) {
    return { message: error.message || 'auth.errors.cannotUpdatePassword' };
  }

  return {
    success: true,
    message: 'auth.errors.passwordUpdatedSuccessfully',
  };
}

export async function signOut() {
  const supabase = await createClient();
  const { error } = await supabase.auth.signOut();

  if (error) {
    return { message: error.message || 'auth.errors.cannotSignOut' };
  }

  return redirect('/');
}
