'use client';

import { useEffect, useState } from 'react';
import { Loader2, Server, RefreshCw, AlertCircle } from 'lucide-react';
import { checkApiHealth } from '@/lib/api';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { isLocalMode } from '@/lib/config';

export function MaintenancePage() {
  const [isCheckingHealth, setIsCheckingHealth] = useState(true);
  const [lastChecked, setLastChecked] = useState<Date | null>(null);

  const checkHealth = async () => {
    setIsCheckingHealth(true);
    try {
      await checkApiHealth();
      // If we get here, the API is healthy
      window.location.reload();
    } catch (error) {
      console.error('API health check failed:', error);
    } finally {
      setIsCheckingHealth(false);
      setLastChecked(new Date());
    }
  };

  useEffect(() => {
    checkHealth();
    // Check health every 30 seconds
    const interval = setInterval(checkHealth, 30000);
    return () => clearInterval(interval);
  }, []);

  return (
    <div className="flex flex-col items-center justify-center min-h-screen p-4 bg-background">
      <div className="w-full max-w-md space-y-8 text-center">
        <div className="space-y-4">
          <div className="flex justify-center">
            <div className="relative">
              <Server className="h-16 w-16 text-primary animate-pulse" />
              <div className="absolute inset-0 flex items-center justify-center"></div>
            </div>
          </div>

          <h1 className="text-3xl font-bold tracking-tight">
            系统维护中
          </h1>

          <p className="text-muted-foreground">
            {isLocalMode() ? (
              "后端服务器似乎已离线。请检查您的后端服务器是否正在运行。"
            ) : (
              "我们正在对系统进行维护。我们的团队正在努力使一切尽快恢复正常。"
            )}
          </p>

          <Alert className="mt-6">
            <AlertTitle>代理执行已停止</AlertTitle>
            <AlertDescription>
              {isLocalMode() ? (
                "后端服务器需要运行才能执行代理。请启动后端服务器并重试。"
              ) : (
                "维护期间所有正在运行的代理执行已停止。系统恢复在线后，您需要手动继续这些执行。"
              )}
            </AlertDescription>
          </Alert>
        </div>

        <div className="space-y-4">
          <Button
            onClick={checkHealth}
            disabled={isCheckingHealth}
            className="w-full"
          >
            <RefreshCw
              className={cn('mr-2 h-4 w-4', isCheckingHealth && 'animate-spin')}
            />
            {isCheckingHealth ? '检查中...' : '重新检查'}
          </Button>

          {lastChecked && (
            <p className="text-sm text-muted-foreground">
              上次检查时间：{lastChecked.toLocaleTimeString()}
            </p>
          )}
        </div>
      </div>
    </div>
  );
}
