import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

const items = [
    { id: 1, content: "初始化神经网络..." },
    { id: 2, content: "分析查询复杂度..." },
    { id: 3, content: "构建认知框架..." },
    { id: 4, content: "编排思维过程..." },
    { id: 5, content: "合成上下文理解..." },
    { id: 6, content: "校准响应参数..." },
    { id: 7, content: "启动推理算法..." },
    { id: 8, content: "处理语义结构..." },
    { id: 9, content: "制定策略方案..." },
    { id: 10, content: "优化解决路径..." },
    { id: 11, content: "协调数据流..." },
    { id: 12, content: "构建智能响应..." },
    { id: 13, content: "微调认知模型..." },
    { id: 14, content: "编织叙述线索..." },
    { id: 15, content: "凝聚洞察力..." },
    { id: 16, content: "准备综合分析..." }
  ];

export const AgentLoader = () => {
  const [index, setIndex] = useState(0);
  useEffect(() => {
    const id = setInterval(() => {
      setIndex((state) => {
        if (state >= items.length - 1) return 0;
        return state + 1;
      });
    }, 1500);
    return () => clearInterval(id);
  }, []);

  return (
    <div className="flex-1 space-y-2 w-full h-16 bg-background">
    <div className="max-w-[90%] animate-shimmer bg-transparent h-full p-0.5 text-sm border rounded-xl shadow-sm relative overflow-hidden">
        <div className="rounded-md bg-background flex px-5 items-start justify-start h-full relative z-10">
        <div className="flex flex-col py-5 items-start w-full space-y-3">
            <AnimatePresence>
            <motion.div
                key={items[index].id}
                initial={{ y: 20, opacity: 0, filter: "blur(8px)" }}
                animate={{ y: 0, opacity: 1, filter: "blur(0px)" }}
                exit={{ y: -20, opacity: 0, filter: "blur(8px)" }}
                transition={{ ease: "easeInOut" }}
                style={{ position: "absolute" }}
            >
                {items[index].content}
            </motion.div>
            </AnimatePresence>
        </div>
        </div>
    </div>
    </div>
  );
};
