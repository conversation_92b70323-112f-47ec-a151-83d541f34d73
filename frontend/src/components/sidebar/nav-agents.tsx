'use client';

import { useEffect, useState, useRef, useMemo, useCallback } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import {
  ArrowUpRight,
  Link as LinkIcon,
  MoreHorizontal,
  Trash2,
  Plus,
  MessagesSquare,
  Loader2,
  Share2,
  X,
  Check,
  Home, 
  Compass, 
  Briefcase, 
  Store, 
  Settings2, 
  History,
  ChevronDown,
  ChevronRight
} from "lucide-react"
import { toast } from "sonner"
import { usePathname, useRouter } from "next/navigation"
import { cn } from "@/lib/utils"

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  SidebarGroup,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuAction,
  SidebarMenuButton,
  SidebarMenuItem,
  useSidebar,
} from '@/components/ui/sidebar';
import {
  Toolt<PERSON>,
  TooltipContent,
  TooltipTrigger
} from "@/components/ui/tooltip"
import Link from "next/link"
import { ShareModal } from "./share-modal"
import { DeleteConfirmationDialog } from "@/components/thread/DeleteConfirmationDialog"
import { useDeleteOperation } from '@/contexts/DeleteOperationContext'
import { Button } from "@/components/ui/button"
import { Checkbox } from "@/components/ui/checkbox"
import { ThreadWithProject } from '@/hooks/react-query/sidebar/use-sidebar';
import { processThreadsWithProjects, useDeleteMultipleThreads, useDeleteThread, useProjects, useThreads } from '@/hooks/react-query/sidebar/use-sidebar';
import { projectKeys, threadKeys } from '@/hooks/react-query/sidebar/keys';
import { Skeleton } from "@/components/ui/skeleton";

export function NavAgents({ enabled = true }: { enabled?: boolean }) {
  const { isMobile, state } = useSidebar()
  const [loadingThreadId, setLoadingThreadId] = useState<string | null>(null)
  const [showShareModal, setShowShareModal] = useState(false)
  const [selectedItem, setSelectedItem] = useState<{ threadId: string, projectId: string } | null>(null)
  const pathname = usePathname()
  const router = useRouter()
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [threadToDelete, setThreadToDelete] = useState<{ id: string; name: string } | null>(null)
  const isNavigatingRef = useRef(false)
  const { performDelete } = useDeleteOperation();
  const isPerformingActionRef = useRef(false);
  const queryClient = useQueryClient();
  
  const [selectedThreads, setSelectedThreads] = useState<Set<string>>(new Set());
  const [deleteProgress, setDeleteProgress] = useState(0);
  const [totalToDelete, setTotalToDelete] = useState(0);
  const [isHistoryExpanded, setIsHistoryExpanded] = useState(false);

  // 懒加载：首次渲染时才请求数据
  const [shouldLoad, setShouldLoad] = useState(false);
  const hasRequested = useRef(false);

  useEffect(() => {
    if (!hasRequested.current) {
      setShouldLoad(true);
      hasRequested.current = true;
    }
  }, []);

  const {
    data: projects = [],
    isLoading: isProjectsLoading,
    error: projectsError
  } = useProjects({ enabled: shouldLoad });
  const {
    data: threads = [],
    isLoading: isThreadsLoading,
    error: threadsError
  } = useThreads({ enabled: shouldLoad });

  const { mutate: deleteThreadMutation, isPending: isDeletingSingle } = useDeleteThread();
  const { 
    mutate: deleteMultipleThreadsMutation, 
    isPending: isDeletingMultiple 
  } = useDeleteMultipleThreads();

  const combinedThreads: ThreadWithProject[] = useMemo(() => {
    if (isProjectsLoading || isThreadsLoading) return [];
    return processThreadsWithProjects(threads, projects);
  }, [threads, projects, isProjectsLoading, isThreadsLoading]);

  // 只保留最近聊天记录（前5条）
  const recentHistoryThreads = useMemo(() => 
    combinedThreads.slice(0, 5), 
    [combinedThreads]
  );

  // Memoize callbacks
  const handleHistoryToggle = useCallback((e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsHistoryExpanded(!isHistoryExpanded);
  }, [isHistoryExpanded]);

  const handleThreadClick = useCallback((e: React.MouseEvent<HTMLAnchorElement>, threadId: string, url: string) => {
    if (selectedThreads.has(threadId)) {
      e.preventDefault();
      return;
    }
    
    e.preventDefault()
    setLoadingThreadId(threadId)
    router.push(url)
  }, [selectedThreads, router]);

  const toggleThreadSelection = useCallback((threadId: string, e?: React.MouseEvent) => {
    if (e) {
      e.preventDefault();
      e.stopPropagation();
    }
    
    setSelectedThreads(prev => {
      const newSelection = new Set(prev);
      if (newSelection.has(threadId)) {
        newSelection.delete(threadId);
      } else {
        newSelection.add(threadId);
      }
      return newSelection;
    });
  }, []);

  const handleDeletionProgress = (completed: number, total: number) => {
    const percentage = (completed / total) * 100;
    setDeleteProgress(percentage);
  };
  
  useEffect(() => {
    const handleProjectUpdate = (event: Event) => {
      const customEvent = event as CustomEvent;
      if (customEvent.detail) {
        const { projectId, updatedData } = customEvent.detail;
        queryClient.invalidateQueries({ queryKey: projectKeys.detail(projectId) });
        queryClient.invalidateQueries({ queryKey: projectKeys.lists() });
      }
    };

    window.addEventListener('project-updated', handleProjectUpdate as EventListener);

    return () => {
      window.removeEventListener(
        'project-updated',
        handleProjectUpdate as EventListener,
      );
    };
  }, [queryClient]);

  useEffect(() => {
    setLoadingThreadId(null);
  }, [pathname]);

  useEffect(() => {
    const handleNavigationComplete = () => {
      console.log('NAVIGATION - Navigation event completed');
      document.body.style.pointerEvents = 'auto';
      isNavigatingRef.current = false;
    };

    window.addEventListener("popstate", handleNavigationComplete);

    return () => {
      window.removeEventListener('popstate', handleNavigationComplete);
      document.body.style.pointerEvents = "auto";
    };
  }, []);

  useEffect(() => {
    isNavigatingRef.current = false;
    document.body.style.pointerEvents = 'auto';
  }, [pathname]);

  const selectAllThreads = () => {
    const allThreadIds = combinedThreads.map(thread => thread.threadId);
    setSelectedThreads(new Set(allThreadIds));
  };

  const deselectAllThreads = () => {
    setSelectedThreads(new Set());
  };

  const handleDeleteThread = async (threadId: string, threadName: string) => {
    setThreadToDelete({ id: threadId, name: threadName });
    setIsDeleteDialogOpen(true);
  };

  const handleMultiDelete = () => {
    if (selectedThreads.size === 0) return;
    
    const threadsToDelete = combinedThreads.filter(t => selectedThreads.has(t.threadId));
    const threadNames = threadsToDelete.map(t => t.projectName).join(", ");
    
    setThreadToDelete({ 
      id: "multiple", 
      name: selectedThreads.size > 3 
        ? `${selectedThreads.size} conversations` 
        : threadNames 
    });
    
    setTotalToDelete(selectedThreads.size);
    setDeleteProgress(0);
    setIsDeleteDialogOpen(true);
  };

  const confirmDelete = async () => {
    if (!threadToDelete || isPerformingActionRef.current) return;

    isPerformingActionRef.current = true;
    setIsDeleteDialogOpen(false);

    if (threadToDelete.id !== "multiple") {
      const threadId = threadToDelete.id;
      const isActive = pathname?.includes(threadId);
      const deletedThread = { ...threadToDelete };

      console.log('DELETION - Starting thread deletion process', {
        threadId: deletedThread.id,
        isCurrentThread: isActive,
      });

      await performDelete(
        threadId,
        isActive,
        async () => {
          deleteThreadMutation(
            { threadId },
            {
              onSuccess: () => {
                queryClient.invalidateQueries({ queryKey: threadKeys.lists() });
                toast.success('Conversation deleted successfully');
              },
              onSettled: () => {
                setThreadToDelete(null);
                isPerformingActionRef.current = false;
              }
            }
          );
        },
        () => {
          setThreadToDelete(null);
          isPerformingActionRef.current = false;
        },
      );
    } else {
      const threadIdsToDelete = Array.from(selectedThreads);
      const isActiveThreadIncluded = threadIdsToDelete.some(id => pathname?.includes(id));
      
      toast.info(`Deleting ${threadIdsToDelete.length} conversations...`);
      
      try {
        if (isActiveThreadIncluded) {
          isNavigatingRef.current = true;
          document.body.style.pointerEvents = 'none';
          router.push('/dashboard');
          await new Promise(resolve => setTimeout(resolve, 100));
        }
        
        deleteMultipleThreadsMutation(
          { 
            threadIds: threadIdsToDelete,
            onProgress: handleDeletionProgress
          },
          {
            onSuccess: (data) => {
              queryClient.invalidateQueries({ queryKey: threadKeys.lists() });
              toast.success(`Successfully deleted ${data.successful.length} conversations`);
              if (data.failed.length > 0) {
                toast.warning(`Failed to delete ${data.failed.length} conversations`);
              }
              setSelectedThreads(new Set());
              setDeleteProgress(0);
              setTotalToDelete(0);
            },
            onError: (error) => {
              console.error('Error in bulk deletion:', error);
              toast.error('Error deleting conversations');
            },
            onSettled: () => {
              setThreadToDelete(null);
              isPerformingActionRef.current = false;
              setDeleteProgress(0);
              setTotalToDelete(0);
            }
          }
        );
      } catch (err) {
        console.error('Error initiating bulk deletion:', err);
        toast.error('Error initiating deletion process');
        setSelectedThreads(new Set());
        setThreadToDelete(null);
        isPerformingActionRef.current = false;
        setDeleteProgress(0);
        setTotalToDelete(0);
      }
    }
  };

  const overallIsLoading = isProjectsLoading || isThreadsLoading;
  const hasError = projectsError || threadsError;
  
  if (hasError) {
    console.error('Error loading data:', { projectsError, threadsError });
    // 可选：渲染错误提示
  }

  if (overallIsLoading) {
    return (
      <SidebarMenu>
        {Array.from({ length: 5 }).map((_, index) => (
          <SidebarMenuItem key={`skeleton-${index}`}>
            <SidebarMenuButton className="w-full">
              <Skeleton className="h-4 w-4 mr-2 rounded animate-pulse" />
              <Skeleton className="h-3 w-3/4 rounded animate-pulse" />
            </SidebarMenuButton>
          </SidebarMenuItem>
        ))}
      </SidebarMenu>
    );
  }

  if (recentHistoryThreads.length === 0) {
    return (
      <SidebarMenu>
        <SidebarMenuItem>
          <SidebarMenuButton className="text-sidebar-foreground/70 w-full justify-start pl-7">
            <MessagesSquare className="h-4 w-4 mr-2 flex-shrink-0" />
            <span>暂无对话</span>
          </SidebarMenuButton>
        </SidebarMenuItem>
      </SidebarMenu>
    );
  }

  return (
    <SidebarMenu className="overflow-y-auto max-h-[calc(100vh-160px)] [&::-webkit-scrollbar]:hidden [-ms-overflow-style:'none'] [scrollbar-width:'none']">
      <SidebarGroup className='pb-0'>
        {recentHistoryThreads.map((thread) => {
          const isActive = pathname?.includes(thread.threadId) || false;
          const isThreadLoading = loadingThreadId === thread.threadId;
          const isSelected = selectedThreads.has(thread.threadId);

          return (
            <SidebarMenuItem key={`thread-${thread.threadId}`} className="group relative chat-history-item">
              <SidebarMenuButton
                key={thread.threadId}
                asChild
                isActive={isActive}
                className={cn(
                  "w-full justify-start pl-7 pr-12",
                  loadingThreadId === thread.threadId ? 'opacity-50 pointer-events-none' : ''
                )}
                onClick={(e: React.MouseEvent<HTMLButtonElement, MouseEvent>) => handleThreadClick(e as any, thread.threadId, `/agents/${thread.threadId}`)}
              >
                <Link 
                  href={`/agents/${thread.threadId}`}
                  className="flex items-center w-full"
                >
                  {loadingThreadId === thread.threadId ? (
                    <Loader2 className="h-4 w-4 mr-2 animate-spin flex-shrink-0" />
                  ) : (
                    <MessagesSquare className="h-4 w-4 mr-2 flex-shrink-0" />
                  )}
                  <span className="truncate flex-1 min-w-0" title={thread.projectName}> 
                    {thread.projectName}
                  </span>
                </Link>
              </SidebarMenuButton>
              {/* 可选：保留分享、删除等操作 */}
            </SidebarMenuItem>
          );
        })}
      </SidebarGroup>
    </SidebarMenu>
  );
}