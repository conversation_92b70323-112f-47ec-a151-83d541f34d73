import { useThreads, useProjects, processThreadsWithProjects } from '@/hooks/react-query/sidebar/use-sidebar';
import { useMemo } from 'react';
import { usePathname, useRouter } from 'next/navigation';
import { SidebarMenu, SidebarMenuItem, SidebarMenuButton, SidebarGroup } from '@/components/ui/sidebar';
import { Skeleton } from '@/components/ui/skeleton';
import { MessagesSquare, Loader2 } from 'lucide-react';
import { cn } from '@/lib/utils';
import Link from 'next/link';

export function NavHistory() {
  const router = useRouter();
  const pathname = usePathname();
  const { data: projects = [], isLoading: isProjectsLoading } = useProjects();
  const { data: threads = [], isLoading: isThreadsLoading } = useThreads();

  const combinedThreads = useMemo(() => {
    if (isProjectsLoading || isThreadsLoading) return [];
    return processThreadsWithProjects(threads, projects);
  }, [threads, projects, isProjectsLoading, isThreadsLoading]);

  // 历史记录：第6条及以后
  const historyThreads = useMemo(() => combinedThreads.slice(5), [combinedThreads]);

  if (isProjectsLoading || isThreadsLoading) {
    return (
      <SidebarMenu>
        {Array.from({ length: 5 }).map((_, index) => (
          <SidebarMenuItem key={`history-skeleton-${index}`}>
            <SidebarMenuButton className="w-full">
              <Skeleton className="h-4 w-4 mr-2 rounded animate-pulse" />
              <Skeleton className="h-3 w-3/4 rounded animate-pulse" />
            </SidebarMenuButton>
          </SidebarMenuItem>
        ))}
      </SidebarMenu>
    );
  }

  if (historyThreads.length === 0) {
    return (
      <SidebarMenu>
        <SidebarMenuItem>
          <SidebarMenuButton className="text-sidebar-foreground/70 w-full justify-start pl-7">
            <MessagesSquare className="h-4 w-4 mr-2 flex-shrink-0" />
            <span>暂无历史记录</span>
          </SidebarMenuButton>
        </SidebarMenuItem>
      </SidebarMenu>
    );
  }

  return (
    <SidebarMenu>
      <SidebarGroup>
        {historyThreads.map((thread) => {
          const isActive = pathname?.includes(thread.threadId) || false;
          return (
            <SidebarMenuItem key={`history-thread-${thread.threadId}`}>
              <SidebarMenuButton
                asChild
                isActive={isActive}
                className={cn('w-full justify-start pl-7')}
                onClick={() => router.push(`/agents/${thread.threadId}`)}
              >
                <Link href={`/agents/${thread.threadId}`} className="flex items-center w-full">
                  <MessagesSquare className="h-4 w-4 mr-2 flex-shrink-0" />
                  <span className="truncate flex-1 min-w-0" title={thread.projectName}>
                    {thread.projectName}
                  </span>
                </Link>
              </SidebarMenuButton>
            </SidebarMenuItem>
          );
        })}
      </SidebarGroup>
    </SidebarMenu>
  );
} 