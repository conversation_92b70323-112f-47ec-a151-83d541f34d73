'use client';

import * as React from 'react';
import Link from 'next/link';
import { Menu } from 'lucide-react';
import { useRouter } from 'next/navigation';

import { NavAgents } from '@/components/sidebar/nav-agents';
import { NavUserWithTeams } from '@/components/sidebar/nav-user-with-teams';
import { LoomuLogo } from '@/components/sidebar/LoomuAI-logo';
import { CTACard } from '@/components/sidebar/cta';
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarRail,
  SidebarTrigger,
  useSidebar,
} from '@/components/ui/sidebar';
import { useEffect, useState } from 'react';
import { createClient } from '@/lib/supabase/client';
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { useIsMobile } from '@/hooks/use-mobile';
import { MessagesSquare, Compass, Briefcase, Store, Settings2, ChevronDown, ChevronRight, History, Plus } from 'lucide-react';
import { SidebarMenu, SidebarMenuItem, SidebarMenuButton } from '@/components/ui/sidebar';
import { NavHistory } from '@/components/sidebar/nav-history';
import { cn } from '@/lib/utils';
import { useLanguage } from '@/contexts/LanguageContext';

export function SidebarLeft({
  ...props
}: React.ComponentProps<typeof Sidebar>) {
  const { t } = useLanguage();
  const { state, setOpen, setOpenMobile } = useSidebar();
  const isMobile = useIsMobile();
  const router = useRouter();
  const [user, setUser] = useState<{
    name: string;
    email: string;
    avatar: string;
  }>({
    name: t('sidebar.loading'),
    email: '<EMAIL>',
    avatar: '',
  });
  const [isChatExpanded, setIsChatExpanded] = useState(false);
  const [isHistoryExpanded, setIsHistoryExpanded] = useState(false);
  const isCollapsed = state === 'collapsed';

  // Fetch user data
  useEffect(() => {
    const fetchUserData = async () => {
      const supabase = createClient();
      const { data } = await supabase.auth.getUser();

      if (data.user) {
        setUser({
          name:
            data.user.user_metadata?.name ||
            data.user.email?.split('@')[0] ||
            t('sidebar.defaultUser'),
          email: data.user.email || '',
          avatar: data.user.user_metadata?.avatar_url || '',
        });
      }
    };

    fetchUserData();
  }, [t]);

  // Handle keyboard shortcuts (CMD+B) for consistency
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if ((event.metaKey || event.ctrlKey) && event.key === 'b') {
        event.preventDefault();
        // We'll handle this in the parent page component
        // to ensure proper coordination between panels
        setOpen(!state.startsWith('expanded'));

        // Broadcast a custom event to notify other components
        window.dispatchEvent(
          new CustomEvent('sidebar-left-toggled', {
            detail: { expanded: !state.startsWith('expanded') },
          }),
        );
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [state, setOpen]);

  return (
    <Sidebar
      collapsible="icon"
      className="border-r-0 bg-background/95 backdrop-blur-sm [&::-webkit-scrollbar]:hidden [-ms-overflow-style:'none'] [scrollbar-width:'none']"
      {...props}
    >
      <SidebarHeader className="px-2 py-2">
        <div className="flex h-[40px] items-center px-1 relative">
          <Link href="/dashboard">
            <LoomuLogo />
          </Link>
          {state !== 'collapsed' && (
            <div className="ml-2 transition-all duration-200 ease-in-out whitespace-nowrap">
              {/* <span className="font-semibold"> Loomu</span> */}
            </div>
          )}
          <div className="ml-auto flex items-center gap-2">
            {state !== 'collapsed' && (
              <Tooltip>
                <TooltipTrigger asChild>
                  <SidebarTrigger className="h-8 w-8" />
                </TooltipTrigger>
                <TooltipContent>{t('sidebar.toggleSidebar')}</TooltipContent>
              </Tooltip>
            )}
            {isMobile && (
              <Tooltip>
                <TooltipTrigger asChild>
                  <button
                    onClick={() => setOpenMobile(true)}
                    className="h-8 w-8 flex items-center justify-center rounded-md hover:bg-accent"
                  >
                    <Menu className="h-4 w-4" />
                  </button>
                </TooltipTrigger>
                <TooltipContent>{t('sidebar.openMenu')}</TooltipContent>
              </Tooltip>
            )}
          </div>
        </div>
      </SidebarHeader>
      <SidebarContent className="[&::-webkit-scrollbar]:hidden [-ms-overflow-style:'none'] [scrollbar-width:'none']">
        <SidebarMenu>
          {/* 聊天一级菜单 */}
          <SidebarMenuItem className="flex items-center transition-all duration-200 ease-in-out">
            <SidebarMenuButton
              className={cn(
                "flex items-center flex-1 w-full transition-all duration-200 ease-in-out",
                isCollapsed && "justify-center px-0 py-0 h-12 min-h-0"
              )}
              style={isCollapsed ? { minWidth: 0, minHeight: 0, height: '48px', padding: 0, transition: 'all 0.2s ease-in-out' } : { transition: 'all 0.2s ease-in-out' }}
              onClick={() => router.push('/dashboard')}
            >
              <MessagesSquare className="h-5 w-5 transition-all duration-200 ease-in-out" />
              {!isCollapsed && <span className="ml-2 transition-all duration-200 ease-in-out">{t('sidebar.chat')}</span>}
              {!isCollapsed && (
                <button
                  className="ml-auto flex items-center justify-center rounded hover:bg-accent p-1 transition-all duration-200 ease-in-out"
                  title={t('sidebar.newChat')}
                  onClick={e => {
                    e.stopPropagation();
                    router.push('/dashboard');
                  }}
                >
                  <Plus className="h-4 w-4" />
                </button>
              )}
            </SidebarMenuButton>
            {!isCollapsed && (
              <button
                className="ml-1 flex items-center justify-center rounded hover:bg-accent p-1 transition-all duration-200 ease-in-out"
                title={isChatExpanded ? t('sidebar.collapse') : t('sidebar.expand')}
                onClick={e => {
                  e.stopPropagation();
                  setIsChatExpanded(v => !v);
                }}
              >
                {isChatExpanded ? <ChevronDown className="h-4 w-4" /> : <ChevronRight className="h-4 w-4" />}
              </button>
            )}
          </SidebarMenuItem>
          {/* 聊天二级菜单（懒加载，缩进减少） */}
          {!isCollapsed && isChatExpanded && (
            <div className="pl-2 transition-all duration-200 ease-in-out">
              <NavAgents />
            </div>
          )}
          {/* 发现一级菜单 */}
          <SidebarMenuItem className="flex items-center transition-all duration-200 ease-in-out">
            <SidebarMenuButton
              className={cn(
                "flex items-center flex-1 w-full transition-all duration-200 ease-in-out",
                isCollapsed && "justify-center px-0 py-0 h-12 min-h-0"
              )}
              style={isCollapsed ? { minWidth: 0, minHeight: 0, height: '48px', padding: 0, transition: 'all 0.2s ease-in-out' } : { transition: 'all 0.2s ease-in-out' }}
              onClick={() => router.push('/discover')}
            >
              <Compass className="h-5 w-5 transition-all duration-200 ease-in-out" />
              {!isCollapsed && <span className="ml-2 transition-all duration-200 ease-in-out">{t('sidebar.discover')}</span>}
            </SidebarMenuButton>
          </SidebarMenuItem>
          {/* 我的项目一级菜单 */}
          <SidebarMenuItem className="flex items-center transition-all duration-200 ease-in-out">
            <SidebarMenuButton
              className={cn(
                "flex items-center flex-1 w-full transition-all duration-200 ease-in-out",
                isCollapsed && "justify-center px-0 py-0 h-12 min-h-0"
              )}
              style={isCollapsed ? { minWidth: 0, minHeight: 0, height: '48px', padding: 0, transition: 'all 0.2s ease-in-out' } : { transition: 'all 0.2s ease-in-out' }}
              onClick={() => router.push('/projects')}
            >
              <Briefcase className="h-5 w-5 transition-all duration-200 ease-in-out" />
              {!isCollapsed && <span className="ml-2 transition-all duration-200 ease-in-out">{t('sidebar.projects')}</span>}
            </SidebarMenuButton>
          </SidebarMenuItem>
          {/* MCP市场一级菜单 */}
          <SidebarMenuItem className="flex items-center transition-all duration-200 ease-in-out">
            <SidebarMenuButton
              className={cn(
                "flex items-center flex-1 w-full transition-all duration-200 ease-in-out",
                isCollapsed && "justify-center px-0 py-0 h-12 min-h-0"
              )}
              style={isCollapsed ? { minWidth: 0, minHeight: 0, height: '48px', padding: 0, transition: 'all 0.2s ease-in-out' } : { transition: 'all 0.2s ease-in-out' }}
              onClick={() => router.push('/mcp-market')}
            >
              <Store className="h-5 w-5 transition-all duration-200 ease-in-out" />
              {!isCollapsed && <span className="ml-2 transition-all duration-200 ease-in-out">{t('sidebar.mcpMarket')}</span>}
            </SidebarMenuButton>
          </SidebarMenuItem>
          {/* 连接器一级菜单 */}
          <SidebarMenuItem className="flex items-center transition-all duration-200 ease-in-out">
            <SidebarMenuButton
              className={cn(
                "flex items-center flex-1 w-full transition-all duration-200 ease-in-out",
                isCollapsed && "justify-center px-0 py-0 h-12 min-h-0"
              )}
              style={isCollapsed ? { minWidth: 0, minHeight: 0, height: '48px', padding: 0, transition: 'all 0.2s ease-in-out' } : { transition: 'all 0.2s ease-in-out' }}
              onClick={() => router.push('/connectors')}
            >
              <Settings2 className="h-5 w-5 transition-all duration-200 ease-in-out" />
              {!isCollapsed && <span className="ml-2 transition-all duration-200 ease-in-out">{t('sidebar.connectors')}</span>}
            </SidebarMenuButton>
          </SidebarMenuItem>
          {/* 历史记录一级菜单 */}
          <SidebarMenuItem className="flex items-center transition-all duration-200 ease-in-out">
            <SidebarMenuButton
              className={cn(
                "flex items-center flex-1 w-full transition-all duration-200 ease-in-out",
                isCollapsed && "justify-center px-0 py-0 h-12 min-h-0"
              )}
              style={isCollapsed ? { minWidth: 0, minHeight: 0, height: '48px', padding: 0, transition: 'all 0.2s ease-in-out' } : { transition: 'all 0.2s ease-in-out' }}
              onClick={() => !isCollapsed && setIsHistoryExpanded(v => !v)}
              disabled={isCollapsed}
            >
              <History className="h-5 w-5 transition-all duration-200 ease-in-out" />
              {!isCollapsed && <span className="ml-2 transition-all duration-200 ease-in-out">{t('sidebar.history')}</span>}
            </SidebarMenuButton>
            {!isCollapsed && (
              <button
                className="ml-1 flex items-center justify-center rounded hover:bg-accent p-1 transition-all duration-200 ease-in-out"
                title={isHistoryExpanded ? t('sidebar.collapse') : t('sidebar.expand')}
                onClick={e => {
                  e.stopPropagation();
                  setIsHistoryExpanded(v => !v);
                }}
              >
                {isHistoryExpanded ? <ChevronDown className="h-4 w-4" /> : <ChevronRight className="h-4 w-4" />}
              </button>
            )}
          </SidebarMenuItem>
          {/* 历史记录二级菜单（懒加载，缩进减少） */}
          {!isCollapsed && isHistoryExpanded && (
            <div className="pl-2 transition-all duration-200 ease-in-out">
              <NavHistory />
            </div>
          )}
        </SidebarMenu>
      </SidebarContent>
      {/* {state !== 'collapsed' && (
        <div className="px-3 py-2">
          <CTACard />
        </div>
      )} */}
      <SidebarFooter>
        {state === 'collapsed' && (
          <div className="mt-2 flex justify-center">
            <Tooltip>
              <TooltipTrigger asChild>
                <SidebarTrigger className="h-8 w-8" />
              </TooltipTrigger>
              <TooltipContent>{t('sidebar.expandSidebar')}</TooltipContent>
            </Tooltip>
          </div>
        )}
        <NavUserWithTeams user={user} />
      </SidebarFooter>
      <SidebarRail />
    </Sidebar>
  );
}
