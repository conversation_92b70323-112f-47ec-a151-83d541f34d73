'use client';

import React from 'react';
import { useLanguage } from '@/contexts/LanguageContext';
import { locales } from '@/lib/i18n';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Button } from '@/components/ui/button';
import { Globe, Check } from 'lucide-react';
import { cn } from '@/lib/utils';

const languageNames = {
  en: 'English',
  zh: '中文',
};

const languageFlags = {
  en: '🇺🇸',
  zh: '🇨🇳',
};

export function LanguageSwitcher() {
  const { locale, setLocale, t } = useLanguage();

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="outline"
          size="icon"
          className="cursor-pointer rounded-full h-8 w-8"
          aria-label={t('common.language')}
        >
          <Globe className="h-4 w-4 text-primary"  />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent
        align="end"
        className="w-32 rounded-md border border-border bg-background dark:bg-background shadow-lg p-1"
      >
        {locales.map((lang) => (
          <DropdownMenuItem
            key={lang}
            onClick={() => setLocale(lang)}
            className={cn(
              'flex items-center justify-between cursor-pointer rounded px-2 py-1.5 text-sm transition',
              locale === lang
                ? 'bg-accent text-primary'
                : 'hover:bg-muted dark:hover:bg-muted/70 text-muted-foreground'
            )}
          >
            <div className="flex items-center gap-2">
              <span className="text-base">{languageFlags[lang]}</span>
              <span>{languageNames[lang]}</span>
            </div>
            {locale === lang && <Check className="h-3 w-3" />}
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

// 简化版本的语言切换器，只显示当前语言
export function SimpleLanguageSwitcher() {
  const { locale, setLocale } = useLanguage();

  const toggleLanguage = () => {
    setLocale(locale === 'en' ? 'zh' : 'en');
  };

  return (
    <Button
      variant="ghost"
      size="sm"
      onClick={toggleLanguage}
      className="flex items-center gap-2"
    >
      <span className="text-sm">{languageFlags[locale]}</span>
      <span className="text-sm">{languageNames[locale]}</span>
    </Button>
  );
} 