import React, { useState, useEffect, useRef } from 'react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import rehypeRaw from 'rehype-raw';
import { motion, AnimatePresence } from 'framer-motion';
import { ChevronDown, ChevronRight, Search, ZoomIn } from 'lucide-react';

interface MarkdownRendererProps {
  content: string;
  showToc?: boolean;
  showProgress?: boolean;
  highlightKeywords?: string[];
  className?: string;
}

interface TocItem {
  id: string;
  text: string;
  level: number;
}

export const MarkdownRenderer: React.FC<MarkdownRendererProps> = ({
  content,
  showToc = true,
  showProgress = true,
  highlightKeywords = [],
  className = ''
}) => {
  const [tocItems, setTocItems] = useState<TocItem[]>([]);
  const [activeSection, setActiveSection] = useState<string>('');
  const [readingProgress, setReadingProgress] = useState(0);
  const [collapsedSections, setCollapsedSections] = useState<Set<string>>(new Set());
  const [selectedImage, setSelectedImage] = useState<string | null>(null);
  const contentRef = useRef<HTMLDivElement>(null);
  const progressRef = useRef<HTMLDivElement>(null);

  // Extract table of contents from markdown
  useEffect(() => {
    const headingRegex = /^(#{2,4})\s+(.+)$/gm;
    const toc: TocItem[] = [];
    let match;

    while ((match = headingRegex.exec(content)) !== null) {
      const level = match[1].length;
      const text = match[2].trim();
      const id = text.toLowerCase().replace(/[^\w\u4e00-\u9fa5]+/g, '-');
      toc.push({ id, text, level });
    }

    setTocItems(toc);
  }, [content]);

  // Handle scroll for reading progress and active section
  useEffect(() => {
    const handleScroll = () => {
      if (!contentRef.current || !progressRef.current) return;

      const container = contentRef.current;
      const scrollTop = container.scrollTop;
      const scrollHeight = container.scrollHeight - container.clientHeight;
      const progress = scrollHeight > 0 ? (scrollTop / scrollHeight) * 100 : 0;
      
      setReadingProgress(Math.min(100, Math.max(0, progress)));

      // Find active section
      const headings = container.querySelectorAll('h2, h3, h4');
      let currentActive = '';

      headings.forEach((heading) => {
        const rect = heading.getBoundingClientRect();
        const containerRect = container.getBoundingClientRect();
        
        if (rect.top <= containerRect.top + 100) {
          currentActive = heading.id;
        }
      });

      setActiveSection(currentActive);
    };

    const container = contentRef.current;
    if (container) {
      container.addEventListener('scroll', handleScroll);
      return () => container.removeEventListener('scroll', handleScroll);
    }
  }, []);

  const scrollToSection = (id: string) => {
    const element = document.getElementById(id);
    if (element && contentRef.current) {
      const container = contentRef.current;
      const elementTop = element.offsetTop;
      container.scrollTo({
        top: elementTop - 20,
        behavior: 'smooth'
      });
    }
  };

  const toggleSection = (sectionId: string) => {
    const newCollapsed = new Set(collapsedSections);
    if (newCollapsed.has(sectionId)) {
      newCollapsed.delete(sectionId);
    } else {
      newCollapsed.add(sectionId);
    }
    setCollapsedSections(newCollapsed);
  };

  const highlightText = (text: string) => {
    if (highlightKeywords.length === 0) return text;
    
    let highlightedText = text;
    highlightKeywords.forEach(keyword => {
      const regex = new RegExp(`(${keyword})`, 'gi');
      highlightedText = highlightedText.replace(regex, '<mark class="bg-yellow-200 px-1 rounded">$1</mark>');
    });
    
    return highlightedText;
  };

  const customComponents = {
    h1: ({ children, ...props }: any) => (
      <h1 className="text-2xl font-bold text-gray-900 mb-4 leading-tight" {...props}>
        {children}
      </h1>
    ),
    h2: ({ children, ...props }: any) => {
      const id = children?.toString().toLowerCase().replace(/[^\w\u4e00-\u9fa5]+/g, '-') || '';
      const isCollapsed = collapsedSections.has(id);
      
      return (
        <div className="mt-8 mb-4">
          <motion.h2
            className="text-xl font-bold text-gray-900 mb-3 leading-tight flex items-center cursor-pointer group"
            onClick={() => toggleSection(id)}
            whileHover={{ x: 2 }}
            id={id}
            {...props}
          >
            <motion.div
              animate={{ rotate: isCollapsed ? 0 : 90 }}
              className="mr-2 text-gray-400 group-hover:text-gray-600"
            >
              <ChevronRight size={16} />
            </motion.div>
            {children}
          </motion.h2>
          <AnimatePresence>
            {!isCollapsed && (
              <motion.div
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: 'auto' }}
                exit={{ opacity: 0, height: 0 }}
                className="overflow-hidden"
              >
                <div className="border-l-2 border-blue-200 pl-4 ml-4">
                  {/* Content will be rendered here by markdown */}
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      );
    },
    h3: ({ children, ...props }: any) => {
      const id = children?.toString().toLowerCase().replace(/[^\w\u4e00-\u9fa5]+/g, '-') || '';
      return (
        <h3 className="text-lg font-semibold text-gray-900 mb-3 mt-6 leading-tight" id={id} {...props}>
          {children}
        </h3>
      );
    },
    h4: ({ children, ...props }: any) => {
      const id = children?.toString().toLowerCase().replace(/[^\w\u4e00-\u9fa5]+/g, '-') || '';
      return (
        <h4 className="text-base font-semibold text-gray-900 mb-2 mt-4 leading-tight" id={id} {...props}>
          {children}
        </h4>
      );
    },
    p: ({ children, ...props }: any) => (
      <p className="text-sm text-gray-700 mb-4 leading-relaxed" {...props}>
        <span dangerouslySetInnerHTML={{ __html: highlightText(children?.toString() || '') }} />
      </p>
    ),
    blockquote: ({ children, ...props }: any) => (
      <blockquote className="border-l-4 border-blue-500 pl-4 py-2 my-4 bg-blue-50 rounded-r-lg" {...props}>
        <div className="text-sm text-blue-800 italic">{children}</div>
      </blockquote>
    ),
    ul: ({ children, ...props }: any) => (
      <ul className="list-disc list-inside mb-4 space-y-1 text-sm text-gray-700" {...props}>
        {children}
      </ul>
    ),
    ol: ({ children, ...props }: any) => (
      <ol className="list-decimal list-inside mb-4 space-y-1 text-sm text-gray-700" {...props}>
        {children}
      </ol>
    ),
    li: ({ children, ...props }: any) => (
      <li className="leading-relaxed" {...props}>{children}</li>
    ),
    code: ({ inline, children, ...props }: any) => {
      if (inline) {
        return (
          <code className="bg-gray-100 text-gray-800 px-1 py-0.5 rounded text-xs font-mono" {...props}>
            {children}
          </code>
        );
      }
      return (
        <div className="bg-gray-100 rounded-lg p-4 mb-4 overflow-x-auto">
          <code className="text-xs font-mono text-gray-800 block whitespace-pre" {...props}>
            {children}
          </code>
        </div>
      );
    },
    img: ({ src, alt, ...props }: any) => (
      <div className="text-center my-6">
        <motion.img
          src={src}
          alt={alt}
          className="max-w-full h-auto rounded-lg shadow-md cursor-pointer hover:shadow-lg transition-shadow mx-auto"
          onClick={() => setSelectedImage(src)}
          whileHover={{ scale: 1.02 }}
          {...props}
        />
        {alt && <p className="text-xs text-gray-500 mt-2 italic">{alt}</p>}
      </div>
    ),
    table: ({ children, ...props }: any) => (
      <div className="overflow-x-auto my-4">
        <table className="min-w-full border border-gray-200 rounded-lg" {...props}>
          {children}
        </table>
      </div>
    ),
    thead: ({ children, ...props }: any) => (
      <thead className="bg-gray-50" {...props}>{children}</thead>
    ),
    th: ({ children, ...props }: any) => (
      <th className="px-4 py-2 text-left text-xs font-bold text-gray-700 uppercase tracking-wider border-b border-gray-200" {...props}>
        {children}
      </th>
    ),
    td: ({ children, ...props }: any) => (
      <td className="px-4 py-2 text-sm text-gray-700 border-b border-gray-200" {...props}>
        {children}
      </td>
    ),
    a: ({ href, children, ...props }: any) => (
      <a
        href={href}
        className="text-blue-600 hover:text-blue-800 hover:underline transition-colors"
        target="_blank"
        rel="noopener noreferrer"
        {...props}
      >
        {children}
      </a>
    ),
  };

  return (
    <div className={`relative ${className}`}>
      {/* Reading Progress Bar */}
      {showProgress && (
        <div className="sticky top-0 z-10 bg-white border-b border-gray-200">
          <div
            ref={progressRef}
            className="h-1 bg-blue-600 transition-all duration-300 ease-out"
            style={{ width: `${readingProgress}%` }}
          />
        </div>
      )}

      <div className="flex gap-6">
        {/* Table of Contents */}
        {showToc && tocItems.length > 0 && (
          <div className="w-64 flex-shrink-0">
            <div className="sticky top-8">
              <div className="bg-gray-50 rounded-lg p-4">
                <h4 className="font-semibold text-gray-900 mb-3 flex items-center">
                  <Search size={16} className="mr-2" />
                  目录导航
                </h4>
                <nav className="space-y-1">
                  {tocItems.map((item) => (
                    <motion.button
                      key={item.id}
                      onClick={() => scrollToSection(item.id)}
                      className={`
                        block w-full text-left text-sm py-1 px-2 rounded transition-colors
                        ${item.level === 2 ? 'font-medium' : ''}
                        ${item.level === 3 ? 'ml-3 text-gray-600' : ''}
                        ${item.level === 4 ? 'ml-6 text-gray-500' : ''}
                        ${activeSection === item.id 
                          ? 'bg-blue-100 text-blue-700' 
                          : 'hover:bg-gray-100 text-gray-700'
                        }
                      `}
                      whileHover={{ x: 2 }}
                    >
                      {item.text}
                    </motion.button>
                  ))}
                </nav>
              </div>
            </div>
          </div>
        )}

        {/* Main Content */}
        <div className="flex-1 max-w-3xl">
          <div
            ref={contentRef}
            className="prose prose-sm max-w-none overflow-y-auto max-h-96 pr-4"
            style={{ scrollbarWidth: 'thin' }}
          >
            <ReactMarkdown
              remarkPlugins={[remarkGfm]}
              rehypePlugins={[rehypeRaw]}
              components={customComponents}
            >
              {content}
            </ReactMarkdown>
          </div>
        </div>
      </div>

      {/* Image Modal */}
      <AnimatePresence>
        {selectedImage && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 z-50 bg-black bg-opacity-75 flex items-center justify-center p-4"
            onClick={() => setSelectedImage(null)}
          >
            <motion.div
              initial={{ scale: 0.8 }}
              animate={{ scale: 1 }}
              exit={{ scale: 0.8 }}
              className="relative max-w-4xl max-h-full"
              onClick={(e) => e.stopPropagation()}
            >
              <img
                src={selectedImage}
                alt="放大查看"
                className="max-w-full max-h-full object-contain rounded-lg"
              />
              <button
                onClick={() => setSelectedImage(null)}
                className="absolute top-4 right-4 bg-white bg-opacity-90 hover:bg-opacity-100 rounded-full p-2 transition-colors"
              >
                <ZoomIn size={20} className="text-gray-700" />
              </button>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};