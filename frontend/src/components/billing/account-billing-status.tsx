'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { isLocalMode } from '@/lib/config';
import { useAuth } from '@/components/AuthProvider';
import { Skeleton } from '@/components/ui/skeleton';
import { useCredits } from '@/contexts/CreditsContext';
import { useSearchParams } from 'next/navigation'; // Next.js 13+
type SubscriptionTier = 'basic' | 'plus' | 'pro';

// 扩展 SubscriptionPlan 类型，增加 price_id 字段
interface SubscriptionPlanWithPriceId {
  tier: string;
  name: string;
  price: number;
  monthlyCredits: number;
  features: string[];
  price_id?: string;
}

// 本地定义 SUBSCRIPTION_PLANS，避免导入错误
const SUBSCRIPTION_PLANS = [
  {
    tier: 'basic' as SubscriptionTier,
    name: 'Basic',
    price: 19.9,
    monthlyCredits: 1000,
    features: [
      '每月 1000 积分',
      '基础 AI 模型访问',
      '标准客服支持'
    ]
  },
  {
    tier: 'plus' as SubscriptionTier,
    name: 'Plus',
    price: 36.9,
    monthlyCredits: 2500,
    features: [
      '每月 2500 积分',
      '高级 AI 模型访问',
      '优先客服支持',
      '更多工具访问权限'
    ]
  },
  {
    tier: 'pro' as SubscriptionTier,
    name: 'Pro',
    price: 199,
    monthlyCredits: 10000,
    features: [
      '每月 10000 积分',
      '最新 AI 模型访问',
      '24/7 专属客服支持',
      '所有工具访问权限',
      'API 访问权限'
    ]
  }
];
import { CreditsPurchaseModal } from './CreditsPurchaseModal';
import { SubscriptionConfirmModal } from './SubscriptionConfirmModal';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Coins, Crown, Zap, Star, Check } from 'lucide-react';
import { Progress } from '@/components/ui/progress';
import { toast } from 'sonner';
import { getSubscriptionPlans, type SubscriptionPlan, getSubscription, createCheckoutSession } from '@/lib/api';
import { UsageStatisticsCard } from './UsageStatisticsCard';
import { TransactionHistoryCard } from './TransactionHistoryCard';

interface AccountBillingStatusProps {
  accountId?: string;
  returnUrl?: string;
}

export default function AccountBillingStatus({ accountId, returnUrl }: AccountBillingStatusProps) {
  const { session, isLoading: authLoading } = useAuth();
  // 只用 credits, isLoading, error
  const { credits, isLoading, error } = useCredits();
  const [showPurchaseModal, setShowPurchaseModal] = useState(false);
  const [showConfirmModal, setShowConfirmModal] = useState(false);
  const [selectedTier, setSelectedTier] = useState<SubscriptionTier>('basic');
  const [isUpgrading, setIsUpgrading] = useState(false)

  // 新增本地 state 管理
  const [subscriptionPlans, setSubscriptionPlans] = useState<SubscriptionPlanWithPriceId[]>([]);
  const [plansLoading, setPlansLoading] = useState(true);
  const [plansError, setPlansError] = useState<string | null>(null);
  const [subscriptionTier, setSubscriptionTier] = useState<SubscriptionTier>('basic');
  const [monthlyCredits, setMonthlyCredits] = useState<number>(0);
  const [usedCredits, setUsedCredits] = useState<number>(0);
  const [subStatusLoading, setSubStatusLoading] = useState(true);
  const [subStatusError, setSubStatusError] = useState<string | null>(null);
  const [subscriptionStatus, setSubscriptionStatus] = useState<any>(null); // SubscriptionStatus 类型
  


  // 先获取当前用户订阅状态，再拉取套餐
  useEffect(() => {
    setSubStatusLoading(true);
    getSubscription()
      .then((data) => {
        setSubscriptionStatus(data);
        // 这里假设后端返回的 plan_name 就是 tier
        if (data.plan_name) {
          setSubscriptionTier(data.plan_name as SubscriptionTier);
        }
        
        setSubStatusError(null);
      })
      .catch(() => {
        setSubStatusError('获取订阅状态失败');
      })
      .finally(() => setSubStatusLoading(false));
  }, []);

  const searchParams = useSearchParams();

  useEffect(() => {
    const payment = searchParams.get('payment');
    if (payment === 'success' || payment === 'cancel') {
      // 刷新订阅状态
      setSubStatusLoading(true);
      getSubscription()
        .then((data) => {
          setSubscriptionStatus(data);
          if (data.plan_name) {
            setSubscriptionTier(data.plan_name as SubscriptionTier);
          }
          setSubStatusError(null);
        })
        .catch(() => {
          setSubStatusError('获取订阅状态失败');
        })
        .finally(() => setSubStatusLoading(false));

      // 弹窗提示
      if (payment === 'success') {
        toast.success('支付成功', { description: '您的订阅已激活！' });
      } else if (payment === 'cancel') {
        toast.error('支付未完成', { description: '支付未完成或已取消。' });
      }

      // 移除 URL 参数，避免重复弹窗
      if (typeof window !== 'undefined') {
        const url = new URL(window.location.href);
        url.searchParams.delete('payment');
        window.history.replaceState({}, '', url.toString());
      }
    }
  }, [searchParams]);

  // 拉取订阅计划（依赖 subscriptionTier）
  useEffect(() => {
    if (!subscriptionTier) return;
    setPlansLoading(true);
    getSubscriptionPlans()
      .then((plans) => {
        // 用 SUBSCRIPTION_PLANS 的 features 字段覆盖接口返回的 features
        const plansWithDescriptions = plans.map((plan: any) => {
          const localPlan = SUBSCRIPTION_PLANS.find(p => p.tier === plan.tier);
          return {
            ...plan,
            features: localPlan?.features || plan.features || [],
            price: localPlan?.price || plan.price,
            monthlyCredits: localPlan?.monthlyCredits || plan.monthlyCredits,
            price_id: plan.price_id, // 合并 price_id 字段
          };
        });
        setSubscriptionPlans(plansWithDescriptions);
        setPlansError(null);
        // 默认选当前订阅等级为当前订阅
        const current = plansWithDescriptions.find(p => p.tier === subscriptionTier) || plansWithDescriptions[0];
        if (current) {
          setMonthlyCredits(current.monthlyCredits);
          setUsedCredits(subscriptionStatus?.current_usage || 0);
        }
      })
      .catch((err) => {
        setPlansError('订阅计划获取失败');
        setSubscriptionPlans([]);
      })
      .finally(() => setPlansLoading(false));
  }, [subscriptionTier, subscriptionStatus]);

  // 当前订阅计划
  let currentPlan: any = null;
  if (subscriptionStatus) {
    // 用订阅状态接口返回的为主
    currentPlan = {
      ...subscriptionPlans.find(plan => plan.tier === subscriptionTier),
      ...subscriptionStatus,
    };
  } else {
    currentPlan = subscriptionPlans.find(plan => plan.tier === subscriptionTier);
  }
  const usagePercentage = monthlyCredits > 0 ? (usedCredits / monthlyCredits) * 100 : 0;

  // 升级订阅（模拟）
  const upgradeSubscription = async (tier: SubscriptionTier) => {
    setIsUpgrading(true);
    try {
      // 这里应调用后端接口
      await new Promise(resolve => setTimeout(resolve, 1000));
      setSubscriptionTier(tier);
      const plan = subscriptionPlans.find(p => p.tier === tier);
      if (plan) {
        setMonthlyCredits(plan.monthlyCredits);
        setUsedCredits(0);
      }
      toast.success('订阅升级成功', { description: `已升级到 ${plan?.name}` });
    } catch (err) {
      toast.error('订阅升级失败', { description: '请稍后重试' });
    } finally {
      setIsUpgrading(false);
    }
  };

  const handleUpgradeClick = (tier: SubscriptionTier) => {
    setSelectedTier(tier);
    setShowConfirmModal(true);
  };

  const handleConfirmSubscription = async () => {
    try {
      setIsUpgrading(true);

      // 获取当前选中套餐
      const selectedPlan = subscriptionPlans.find(p => p.tier === selectedTier);
      if (!selectedPlan || !selectedPlan.price_id) {
        toast.error('无法获取套餐信息，请重试');
        return;
      }

      // 构造参数
      const price_id = selectedPlan.price_id;
      const success_url = `${window.location.origin}/settings/billing?payment=success`;
      const cancel_url = `${window.location.origin}/settings/billing?payment=cancel`;

      // 使用 api.ts 封装的 createCheckoutSession
      const data = await createCheckoutSession({ price_id, success_url, cancel_url });

      // 跳转到 Stripe Checkout
      if (data.url) {
        window.location.href = data.url;
      } else {
        throw new Error('未获取到支付链接');
      }
    } catch (error) {
      toast.error('支付处理失败', { description: '请稍后重试或联系客服' });
      throw error;
    } finally {
      setIsUpgrading(false);
    }
  };



  if (isLoading || authLoading || plansLoading || subStatusLoading) {
    return (
      <div className="max-w-8xl mx-auto px-10 py-8">
        <Card>
          <CardHeader>
            <CardTitle>订阅状态</CardTitle>
            <CardDescription>加载中...</CardDescription>
          </CardHeader>
        </Card>
      </div>
    );
  }

  if (error || plansError || subStatusError) {
    return (
      <div className="max-w-8xl mx-auto px-10 py-8">
        <Card>
          <CardHeader>
            <CardTitle>订阅状态</CardTitle>
            <CardDescription>加载失败，请稍后重试</CardDescription>
          </CardHeader>
        </Card>
      </div>
    );
  }

  return (
    <div className=" max-w-8xl mx-auto px-10 py-8 space-y-8">
      {/* Current Subscription Card */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Crown className="h-5 w-5" />
            当前订阅
          </CardTitle>
          <CardDescription>您的订阅计划和积分使用情况</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div className="space-y-2">
              <span className="text-sm font-medium text-muted-foreground">订阅等级</span>
              <div className="text-2xl font-bold text-primary">{currentPlan?.plan_name || currentPlan?.name}</div>
            </div>
            <div className="space-y-2">
              <span className="text-sm font-medium text-muted-foreground">月费</span>
              <div className="text-2xl font-bold">¥{currentPlan?.price_usd || currentPlan?.price}</div>
            </div>
            <div className="space-y-2">
              <span className="text-sm font-medium text-muted-foreground">剩余积分</span>
              <div className="text-2xl font-bold text-primary">{currentPlan?.remaining_credits ?? credits}</div>
            </div>
            <div className="space-y-2">
              <span className="text-sm font-medium text-muted-foreground">使用进度</span>
              <div className="space-y-1">
                <div className="flex justify-between text-sm">
                  <span>{usedCredits} / {monthlyCredits}</span>
                  <span>{usagePercentage.toFixed(1)}%</span>
                </div>
                <Progress value={usagePercentage} className="h-2" />
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Subscription Plans Card */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Zap className="h-5 w-5" />
            选择订阅计划
          </CardTitle>
          <CardDescription>选择适合您的订阅计划，享受更多功能和积分</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-20">
            {subscriptionPlans.map((plan) => (
              <div
                key={plan.tier}
                className={`relative p-6 rounded-xl border transition-all hover:shadow-lg flex flex-col ${
                  subscriptionTier === plan.tier
                    ? 'border-primary bg-primary/5 shadow-lg'
                    : 'border-border hover:border-primary/50'
                }`}
              >
                {subscriptionTier === plan.tier && (
                  <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                    <span className="bg-primary text-primary-foreground px-3 py-1 rounded-full text-xs font-medium">
                      当前计划
                    </span>
                  </div>
                )}
                
                <div className="text-center mb-6">
                  <h3 className="text-xl font-bold mb-2">{plan.name}</h3>
                  <div className="text-3xl font-bold text-primary mb-1">¥{plan.price}</div>
                  <div className="text-sm text-muted-foreground">每月</div>
                </div>

                <div className="space-y-4 mb-6 flex-grow">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-primary">{plan.monthlyCredits}</div>
                    <div className="text-sm text-muted-foreground">积分/月</div>
                  </div>
                  
                  <ul className="space-y-2">
                    {plan.features.map((feature, index) => (
                      <li key={index} className="flex items-center gap-2 text-sm">
                        <Check className="h-4 w-4 text-green-500 flex-shrink-0" />
                        <span>{feature}</span>
                      </li>
                    ))}
                  </ul>
                </div>

                <div className="mt-auto">
                  {subscriptionTier !== plan.tier && (
                    <Button
                      className="w-full"
                      onClick={() => handleUpgradeClick(plan.tier as SubscriptionTier)}
                      disabled={isUpgrading}
                    >
                      {isUpgrading ? '处理中...' : '选择此计划'}
                    </Button>
                  )}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Usage Statistics Card */}
      <UsageStatisticsCard />

      {/* Transaction History Card */}
      <TransactionHistoryCard />

      <CreditsPurchaseModal
        isOpen={showPurchaseModal}
        onClose={() => setShowPurchaseModal(false)}
      />

      <SubscriptionConfirmModal
        isOpen={showConfirmModal}
        onClose={() => setShowConfirmModal(false)}
        selectedTier={selectedTier}
        onConfirm={handleConfirmSubscription}
      />
    </div>
  );
}
