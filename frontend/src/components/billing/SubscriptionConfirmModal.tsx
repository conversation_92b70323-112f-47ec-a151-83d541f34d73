import { useState } from 'react';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Check, CreditCard, Crown } from 'lucide-react';
type SubscriptionTier = 'basic' | 'plus' | 'pro';

// 本地定义 SUBSCRIPTION_PLANS，避免导入错误
const SUBSCRIPTION_PLANS = [
  {
    tier: 'basic' as SubscriptionTier,
    name: 'Basic',
    price: 19.9,
    monthlyCredits: 1000,
    features: [
      '每月 1000 积分',
      '基础 AI 模型访问',
      '标准客服支持'
    ]
  },
  {
    tier: 'plus' as SubscriptionTier,
    name: 'Plus',
    price: 36.9,
    monthlyCredits: 2500,
    features: [
      '每月 2500 积分',
      '高级 AI 模型访问',
      '优先客服支持',
      '更多工具访问权限'
    ]
  },
  {
    tier: 'pro' as SubscriptionTier,
    name: 'Pro',
    price: 199,
    monthlyCredits: 10000,
    features: [
      '每月 10000 积分',
      '最新 AI 模型访问',
      '24/7 专属客服支持',
      '所有工具访问权限',
      'API 访问权限'
    ]
  }
];

interface SubscriptionConfirmModalProps {
  isOpen: boolean;
  onClose: () => void;
  selectedTier: SubscriptionTier;
  onConfirm: () => Promise<void>;
}

export function SubscriptionConfirmModal({
  isOpen,
  onClose,
  selectedTier,
  onConfirm,
}: SubscriptionConfirmModalProps) {
  const [isProcessing, setIsProcessing] = useState(false);
  const selectedPlan = SUBSCRIPTION_PLANS.find(plan => plan.tier === selectedTier);

  const handleConfirm = async () => {
    try {
      setIsProcessing(true);
      await onConfirm();
      onClose();
    } catch (error) {
      console.error('Subscription confirmation failed:', error);
    } finally {
      setIsProcessing(false);
    }
  };

  if (!selectedPlan) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Crown className="h-5 w-5" />
            确认订阅
          </DialogTitle>
          <DialogDescription>
            请确认您的订阅选择，确认后将跳转到支付页面
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Selected Plan Card */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Crown className="h-5 w-5" />
                {selectedPlan.name} 计划
              </CardTitle>
              <CardDescription>您选择的订阅计划详情</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">月费</span>
                  <span className="text-2xl font-bold text-primary">¥{selectedPlan.price}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">每月积分</span>
                  <span className="text-lg font-semibold">{selectedPlan.monthlyCredits} 积分</span>
                </div>
                <div className="space-y-2">
                  <p className="text-sm font-medium">包含功能：</p>
                  <ul className="text-sm text-muted-foreground space-y-1">
                    {selectedPlan.features.map((feature, index) => (
                      <li key={index} className="flex items-center gap-2">
                        <Check className="h-4 w-4 text-green-500" />
                        {feature}
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Payment Info */}
          <div className="bg-muted/30 rounded-lg p-4">
            <div className="flex items-center gap-2 mb-2">
              <CreditCard className="h-4 w-4" />
              <span className="text-sm font-medium">支付信息</span>
            </div>
            <p className="text-sm text-muted-foreground">
              确认后将跳转到 Stripe 安全支付页面完成订阅
            </p>
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end gap-4">
            <Button variant="outline" onClick={onClose} disabled={isProcessing}>
              取消
            </Button>
            <Button onClick={handleConfirm} disabled={isProcessing}>
              {isProcessing ? '处理中...' : '确认订阅'}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
} 