import { useState } from 'react';
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, DialogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { useCredits } from '@/contexts/CreditsContext';
import { Coins } from 'lucide-react';

interface CreditsPurchaseModalProps {
  isOpen: boolean;
  onClose: () => void;
  requiredCredits?: number;
}

const CREDIT_PACKAGES = [
  {
    id: "basic",
    name: "基础套餐",
    credits: 100,
    price: 10,
    bonus: 0,
  },
  {
    id: "standard",
    name: "标准套餐",
    credits: 500,
    price: 45,
    bonus: 50,
  },
  {
    id: "premium",
    name: "高级套餐",
    credits: 1000,
    price: 80,
    bonus: 150,
  },
];

export function CreditsPurchaseModal({
  isOpen,
  onClose,
  requiredCredits = 0,
}: CreditsPurchaseModalProps) {
  const { addCredits } = useCredits();
  const [selectedPackage, setSelectedPackage] = useState(CREDIT_PACKAGES[0]);
  const [isProcessing, setIsProcessing] = useState(false);

  const handlePurchase = async () => {
    try {
      setIsProcessing(true);
      await addCredits(selectedPackage.credits + selectedPackage.bonus);
      onClose();
    } catch (error) {
      console.error("Failed to purchase credits:", error);
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Coins className="h-5 w-5" />
            购买积分
          </DialogTitle>
          <DialogDescription>
            {requiredCredits > 0
              ? `您需要 ${requiredCredits} 积分才能继续。请选择以下套餐：`
              : "选择您想要购买的积分套餐"}
          </DialogDescription>
        </DialogHeader>

        <div className="grid gap-4 py-4">
          {CREDIT_PACKAGES.map((pkg) => (
            <div
              key={pkg.id}
              className={`flex items-center justify-between p-4 rounded-lg border cursor-pointer transition-colors ${
                selectedPackage.id === pkg.id
                  ? "border-primary bg-primary/5"
                  : "border-border hover:border-primary/50"
              }`}
              onClick={() => setSelectedPackage(pkg)}
            >
              <div>
                <div className="font-medium">{pkg.name}</div>
                <div className="text-sm text-muted-foreground">
                  {pkg.credits} 积分
                  {pkg.bonus > 0 && (
                    <span className="text-primary ml-1">
                      + {pkg.bonus} 赠送积分
                    </span>
                  )}
                </div>
              </div>
              <div className="text-lg font-semibold">¥{pkg.price}</div>
            </div>
          ))}
        </div>

        <div className="flex justify-end gap-4">
          <Button variant="outline" onClick={onClose}>
            取消
          </Button>
          <Button onClick={handlePurchase} disabled={isProcessing}>
            {isProcessing ? "处理中..." : "确认购买"}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
} 