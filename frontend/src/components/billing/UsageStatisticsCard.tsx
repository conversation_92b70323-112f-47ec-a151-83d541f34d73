'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { TrendingUp } from 'lucide-react';
import { getCreditUsageStats, type CreditUsageStats } from '@/lib/api';

export function UsageStatisticsCard() {
  const [usageStats, setUsageStats] = useState<CreditUsageStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    setLoading(true);
    getCreditUsageStats()
      .then((stats) => {
        setUsageStats(stats);
        setError(null);
      })
      .catch((err) => {
        setError('获取使用统计失败');
        setUsageStats(null);
      })
      .finally(() => setLoading(false));
  }, []);

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5" />
            使用统计
          </CardTitle>
          <CardDescription>近期的积分使用情况</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="text-center">
                <div className="h-8 bg-muted rounded animate-pulse mb-2"></div>
                <div className="h-4 bg-muted rounded animate-pulse"></div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5" />
            使用统计
          </CardTitle>
          <CardDescription>近期的积分使用情况</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-sm text-muted-foreground text-center py-8">
            加载失败，请稍后重试
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <TrendingUp className="h-5 w-5" />
          使用统计
        </CardTitle>
        <CardDescription>近期的积分使用情况</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div className="text-center">
            <div className="text-2xl font-bold text-primary">{usageStats?.today || 0}</div>
            <div className="text-sm text-muted-foreground">今日使用</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-primary">{usageStats?.this_week || 0}</div>
            <div className="text-sm text-muted-foreground">本周使用</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-primary">{usageStats?.this_month || 0}</div>
            <div className="text-sm text-muted-foreground">本月使用</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-primary">{usageStats?.total_usage || 0}</div>
            <div className="text-sm text-muted-foreground">总使用量</div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
} 