'use client';

import { FlickeringGrid } from '@/components/home/<USER>/flickering-grid';
import { useMediaQuery } from '@/hooks/use-media-query';
import { siteConfig } from '@/lib/home';
import { ChevronRightIcon } from '@radix-ui/react-icons';
import Link from 'next/link';
import Image from 'next/image';
import { useTheme } from 'next-themes';
import { useEffect, useState } from 'react';
import { useLanguage } from '@/contexts/LanguageContext';

export function FooterSection() {
  const { t } = useLanguage();
  const tablet = useMediaQuery('(max-width: 1024px)');
  const { theme, resolvedTheme } = useTheme();
  const [mounted, setMounted] = useState(false);

  // After mount, we can access the theme
  useEffect(() => {
    setMounted(true);
  }, []);

  const logoSrc = !mounted
    ? '/LoomuAI-logo.svg'
    : resolvedTheme === 'dark'
      ? '/LoomuAI-logo-white.svg'
      : '/LoomuAI-logo.svg';

  return (
    <footer id="footer" className="w-full pb-0">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between p-10 max-w-6xl mx-auto">
        <div className="flex flex-col items-start justify-start gap-y-5 max-w-xs mx-0">
          <Link href="/" className="flex items-center gap-2">
            <Image
              src={logoSrc}
              alt="Loomu Logo"
              width={122}
              height={22}
              priority
            />
          </Link>
          <p className="tracking-tight text-muted-foreground font-medium">
            {t('footer.description')}
          </p>

          {/* 保留社交媒体链接，但注释掉跳转功能 */}
          <div className="flex items-center gap-4">
            {/* <a
              // href="https://github.com/Loomu-ai/Loomu"
              // target="_blank"
              // rel="noopener noreferrer"
              aria-label="GitHub"
              className="cursor-pointer"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 24 24"
                className="size-5 text-muted-foreground hover:text-primary transition-colors"
              >
                <path
                  fill="currentColor"
                  d="M12 2C6.477 2 2 6.484 2 12.017c0 4.425 2.865 8.18 6.839 9.504.5.092.682-.217.682-.483 0-.237-.008-.868-.013-1.703-2.782.605-3.369-1.343-3.369-1.343-.454-1.158-1.11-1.466-1.11-1.466-.908-.62.069-.608.069-.608 1.003.07 1.531 1.032 1.531 1.032.892 1.53 2.341 1.088 2.91.832.092-.647.35-1.088.636-1.338-2.22-.253-4.555-1.113-4.555-4.951 0-1.093.39-1.988 1.029-2.688-.103-.253-.446-1.272.098-2.65 0 0 .84-.27 2.75 1.026A9.564 9.564 0 0 1 12 6.844a9.59 9.59 0 0 1 2.504.337c1.909-1.296 2.747-1.027 2.747-1.027.546 1.379.202 2.398.1 2.651.64.7 1.028 1.595 1.028 2.688 0 3.848-2.339 4.695-4.566 4.943.359.309.678.92.678 1.855 0 1.338-.012 2.419-.012 2.747 0 .268.18.58.688.482A10.02 10.02 0 0 0 22 12.017C22 6.484 17.522 2 12 2z"
                />
              </svg>
            </a> */}
            <a
              href="https://x.com/Loomu_AI"
              target="_blank"
              rel="noopener noreferrer"
              aria-label="X (Twitter)"
              className="cursor-pointer"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 24 24"
                className="size-5 text-muted-foreground hover:text-primary transition-colors"
              >
                <path
                  fill="currentColor"
                  d="M18.901 1.153h3.68l-8.04 9.19L24 22.846h-7.406l-5.8-7.584-6.638 7.584H.474l8.6-9.83L0 1.154h7.594l5.243 6.932ZM17.61 20.644h2.039L6.486 3.24H4.298Z"
                />
              </svg>
            </a>
            {/* <a
              // href="https://www.xiaohongshu.com"
              // target="_blank"
              // rel="noopener noreferrer"
              aria-label="小红书"
              className="cursor-pointer"
            >
              <svg 
              className="size-5 text-muted-foreground hover:text-primary transition-colors" viewBox="0 0 1024 1024" 
              version="1.1" xmlns="http://www.w3.org/2000/svg" >
                  <path d="M0 0m256 0l512 0q256 0 256 256l0 512q0 256-256 256l-512 0q-256 0-256-256l0-512q0-256 256-256Z" fill="currentColor"></path><path d="M445.824 766.293333c4.181333-9.130667 7.68-17.066667 11.477333-24.789333a512 512 0 0 0 23.253334-49.237333 29.141333 29.141333 0 0 1 34.773333-21.717334c21.973333 1.578667 44.032 0.426667 66.986667 0.426667V384.853333c-15.445333 0-30.677333-0.554667-45.781334 0-10.538667 0.554667-14.250667-2.901333-13.909333-14.165333 0.725333-27.093333 0-54.272 0-82.602667h214.229333v65.706667c0 30.890667 0 30.890667-29.952 30.890667h-30.122666v285.952h65.024c26.197333 0 26.197333 0 26.197333 27.52v57.642666c0 7.978667-1.962667 12.032-10.624 12.032-101.674667-0.170667-203.392-0.298667-305.066667-0.213333a37.717333 37.717333 0 0 1-6.485333-1.365333" fill="#FFFFFF"></path><path d="M486.357333 559.146667c-13.397333 27.605333-25.173333 52.266667-37.546666 76.501333a11.605333 11.605333 0 0 1-8.96 4.522667c-29.781333 0-59.733333 1.152-89.429334-1.066667-29.738667-2.218667-41.642667-21.333333-29.781333-50.517333 13.482667-33.664 29.738667-66.218667 44.8-99.2l3.413333-8.832c-12.032 0-22.570667 0.298667-33.109333 0-8.576 0.128-17.152-0.682667-25.514667-2.346667a30.037333 30.037333 0 0 1-25.728-33.536 30.72 30.72 0 0 1 2.901334-10.112c18.048-43.349333 38.272-85.845333 57.770666-128.554667 6.357333-13.994667 13.013333-27.776 20.181334-41.386666 1.834667-3.541333 6.101333-8.064 9.386666-8.192 27.861333-0.682667 55.808-0.341333 86.186667-0.341334-2.645333 6.784-4.138667 11.392-6.144 15.701334-17.066667 35.712-34.176 71.381333-51.370667 106.965333-3.456 7.210667-7.68 14.72 5.290667 20.224 3.413333-18.56 17.408-15.189333 29.610667-15.189333h70.4c-2.944 7.04-4.864 11.946667-6.997334 16.597333-21.76 45.44-43.861333 90.538667-65.28 135.936-8.789333 18.474667-5.845333 22.997333 14.634667 23.168 10.538667-0.298667 21.205333-0.341333 35.285333-0.341333m-38.314666 111.872c-16.512 33.109333-31.274667 62.890667-46.378667 92.501333a10.24 10.24 0 0 1-7.68 4.266667c-40.490667-0.426667-81.066667-1.**********.685333-2.261334a79.317333 79.317333 0 0 1-16.298667-4.266666l22.698667-45.909334c7.381333-15.189333 14.592-30.293333 22.570666-44.714666a13.653333 13.653333 0 0 1 9.728-6.4c37.205333 1.834667 74.410667 4.**********.658667 6.698666 7.424 0.384 14.506667 0.085333 25.386667 0.085334" fill="#FFFFFF"></path></svg>
            </a> */}
            {/* LinkedIn部分暂时隐藏 */}
            {/* <a
              // href="https://www.linkedin.com/company/Loomu/"
              // target="_blank"
              // rel="noopener noreferrer"
              aria-label="LinkedIn"
              className="cursor-pointer"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 24 24"
                className="size-5 text-muted-foreground hover:text-primary transition-colors"
              >
                <path
                  fill="currentColor"
                  d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433a2.062 2.062 0 01-2.063-2.065 2.064 2.064 0 112.063 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"
                />
              </svg>
            </a> */}
          </div>

          {/* 合规图标部分暂时隐藏 */}
          {/* <div className="flex items-center gap-2 dark:hidden">
            <Icons.soc2 className="size-12" />
            <Icons.hipaa className="size-12" />
            <Icons.gdpr className="size-12" />
          </div>
          <div className="dark:flex items-center gap-2 hidden">
            <Icons.soc2Dark className="size-12" />
            <Icons.hipaaDark className="size-12" />
            <Icons.gdprDark className="size-12" />
          </div> */}
        </div>

        {/* 保留开发文档部分，但注释掉跳转功能 */}
        <div className="pt-5 md:w-1/2">
          <div className="flex flex-col items-start justify-start md:flex-row md:items-center md:justify-between gap-y-5 lg:pl-10">
            {/* 只显示开发文档相关链接，其他暂时隐藏 */}
            <ul className="flex flex-col gap-y-2">
              <li className="mb-2 text-sm font-semibold text-primary">
                开发文档
              </li>
              <li className="group inline-flex cursor-pointer items-center justify-start gap-1 text-[15px]/snug text-muted-foreground">
                <span>API 文档</span>
                <div className="flex size-4 items-center justify-center border border-border rounded translate-x-0 transform opacity-0 transition-all duration-300 ease-out group-hover:translate-x-1 group-hover:opacity-100">
                  <ChevronRightIcon className="h-4 w-4 " />
                </div>
              </li>
              <li className="group inline-flex cursor-pointer items-center justify-start gap-1 text-[15px]/snug text-muted-foreground">
                <span>快速开始</span>
                <div className="flex size-4 items-center justify-center border border-border rounded translate-x-0 transform opacity-0 transition-all duration-300 ease-out group-hover:translate-x-1 group-hover:opacity-100">
                  <ChevronRightIcon className="h-4 w-4 " />
                </div>
              </li>
              <li className="group inline-flex cursor-pointer items-center justify-start gap-1 text-[15px]/snug text-muted-foreground">
                <span>示例代码</span>
                <div className="flex size-4 items-center justify-center border border-border rounded translate-x-0 transform opacity-0 transition-all duration-300 ease-out group-hover:translate-x-1 group-hover:opacity-100">
                  <ChevronRightIcon className="h-4 w-4 " />
                </div>
              </li>
            </ul>

            {/* 原有的footerLinks暂时隐藏 */}
            {/* {siteConfig.footerLinks.map((column, columnIndex) => (
              <ul key={columnIndex} className="flex flex-col gap-y-2">
                <li className="mb-2 text-sm font-semibold text-primary">
                  {column.title}
                </li>
                {column.links.map((link) => (
                  <li
                    key={link.id}
                    className="group inline-flex cursor-pointer items-center justify-start gap-1 text-[15px]/snug text-muted-foreground"
                  >
                    <Link href={link.url}>{link.title}</Link>
                    <div className="flex size-4 items-center justify-center border border-border rounded translate-x-0 transform opacity-0 transition-all duration-300 ease-out group-hover:translate-x-1 group-hover:opacity-100">
                      <ChevronRightIcon className="h-4 w-4 " />
                    </div>
                  </li>
                ))}
              </ul>
            ))} */}
          </div>
        </div>
      </div>

      {/* 底部FlickeringGrid部分暂时隐藏 */}
      <Link
        // href="https://www.youtube.com/watch?v=nuf5BF1jvjQ"
        href="/"
        target="_blank"
        rel="noopener noreferrer"
        className="block w-full h-48 md:h-64 relative mt-24 z-0 cursor-pointer"
      >
        <div className="absolute inset-0 bg-gradient-to-t from-transparent to-background z-10 from-40%" />
        <div className="absolute inset-0 mx-6">
          <FlickeringGrid
            text={tablet ? 'Loomu 太初 Loomu' : 'Loomu 太初 Loomu'}
            fontSize={tablet ? 70 : 90}
            className="h-full w-full"
            squareSize={2}
            gridGap={tablet ? 2 : 3}
            color="#6B7280"
            maxOpacity={0.3}
            flickerChance={0.1}
          />
        </div>
      </Link>
    </footer>
  );
}
