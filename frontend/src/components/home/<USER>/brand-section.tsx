'use client'

import { motion } from 'framer-motion'
import { CreativeFlywheelVisualization } from './creative-flywheel-visualization'
import { useAuth } from '@/components/AuthProvider'
import { useRouter } from 'next/navigation'
import { useLanguage } from '@/contexts/LanguageContext';

export function BrandSection() {
  const { user, isLoading } = useAuth();
  const router = useRouter();
  const { t } = useLanguage();  
  const handleGetStarted = () => {
    if (isLoading) return;
    if (user) {
      router.push('/dashboard');
    } else {
      router.push('/auth');
    }
  };
  return (
    <section className="min-h-screen flex items-center justify-center px-4 py-20 relative overflow-hidden">
      <div className="max-w-7xl mx-auto w-full relative z-10">
        <div className="grid lg:grid-cols-2 gap-12 lg:gap-20 items-center">
          {/* Left Side: Brand Message */}
          <motion.div 
            className="space-y-10"
            initial={{ opacity: 0, x: -50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
          >
            <div className="space-y-7">
              <motion.h1 
                className="text-5xl md:text-6xl lg:text-7xl font-extrabold bg-gradient-to-r from-primary to-blue-400 bg-clip-text text-transparent drop-shadow-lg"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.2 }}
              >
                Loomu AI
              </motion.h1>
              <motion.p 
                className="text-xl md:text-2xl lg:text-3xl text-primary font-semibold"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.4 }}
              >
                {t('home.brand.subtitle')}
              </motion.p>
              <motion.div 
                className="prose prose-lg text-muted-foreground max-w-none"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.6 }}
              >
                <p className="text-lg leading-relaxed">
                  {t('home.brand.description')}
                </p>
              </motion.div>
            </div>
            <motion.div 
              className="flex flex-wrap gap-4"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.8 }}
            >
              <button
                className="bg-gradient-to-r from-primary to-blue-500 px-8 py-4 rounded-xl font-semibold text-white shadow-lg hover:scale-105 transition-all duration-300"
                onClick={handleGetStarted}
                disabled={isLoading}
              >
                {t('home.brand.cta')}
              </button>
              <button className="border border-primary px-8 py-4 rounded-xl font-semibold text-primary bg-background hover:bg-primary hover:text-white transition-all duration-300">
                {t('home.brand.secondaryCta')}
              </button>
            </motion.div>
          </motion.div>
          {/* Right Side: Creative Flywheel Visualization */}
          <motion.div 
            className="flex justify-center"
            initial={{ opacity: 0, x: 50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
          >
            <CreativeFlywheelVisualization />
          </motion.div>
        </div>
      </div>
    </section>
  )
}
