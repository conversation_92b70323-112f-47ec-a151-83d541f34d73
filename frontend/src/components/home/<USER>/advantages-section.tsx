'use client';

import { motion } from 'framer-motion';
import { TrendingUp, PenTool, Sparkles, Check } from 'lucide-react';
import { SectionHeader } from '@/components/home/<USER>';

const advantages = [
  {
    icon: TrendingUp,
    iconColor: 'text-blue-600',
    iconBg: 'bg-blue-50 dark:bg-blue-950/20',
    title: '智能热点发现',
    description: '多平台热点实时监控，智能分析趋势走向，让您第一时间把握市场动态，抢占内容先机。',
    features: ['多平台数据整合', '实时热点追踪', '趋势预测分析', '定制化监控']
  },
  {
    icon: PenTool,
    iconColor: 'text-purple-600',
    iconBg: 'bg-purple-50 dark:bg-purple-950/20',
    title: '智能创作助手',
    description: '基于热点趋势一键生成高质量文章，AI辅助创作让内容生产更高效，让创意更有灵感。',
    features: ['一键生成文章', 'AI智能改写', '多场景模板', '创意优化建议']
  },
  {
    icon: Sparkles,
    iconColor: 'text-amber-500',
    iconBg: 'bg-amber-50 dark:bg-amber-950/20',
    title: '内容优化专家',
    description: '全方位的内容优化服务，从排版到SEO，让您的文章更专业，更容易被发现和传播。',
    features: ['智能排版优化', 'SEO深度分析', '多样化展现', '阅读体验提升']
  }
];

export function AdvantagesSection() {
  return (
    <section id="advantages" className="flex flex-col items-center justify-center gap-10 pb-10 w-full relative">
      <SectionHeader>
            <h2 className="text-3xl md:text-4xl font-medium tracking-tighter text-center text-balance">
            内容创作的智能助手
            </h2>
            <p className="text-muted-foreground text-center text-balance font-medium">
            让内容创作更简单，让传播更有效率
            </p>
        </SectionHeader>
      <div className="mx-auto max-w-7xl px-6 lg:px-8">
        <div className="grid min-[650px]:grid-cols-2 min-[900px]:grid-cols-3 gap-4 w-full max-w-6xl mx-auto">
          {advantages.map((advantage, index) => (
            <motion.div
              key={advantage.title}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: index * 0.2 }}
              className="rounded-xl overflow-hidden relative h-full flex flex-col md:shadow-[0px_61px_24px_-10px_rgba(0,0,0,0.01),0px_34px_20px_-8px_rgba(0,0,0,0.05),0px_15px_15px_-6px_rgba(0,0,0,0.09),0px_4px_8px_-2px_rgba(0,0,0,0.10),0px_0px_0px_1px_rgba(0,0,0,0.08)] bg-card hover:bg-card/80 transition-all duration-300 group"
            >
              <div className="flex flex-col gap-4 p-6 h-full">
                <div className="flex items-center justify-center gap-3">
                  <div className={`rounded-full ${advantage.iconBg} p-2 group-hover:scale-110 transition-transform duration-300`}>
                    <advantage.icon className={`w-4 h-4 ${advantage.iconColor}`} />
                  </div>
                  <h3 className="text-lg font-medium line-clamp-1 group-hover:text-foreground transition-colors duration-300">
                    {advantage.title}
                  </h3>
                </div>
                
                <p className="text-sm text-muted-foreground leading-relaxed flex-1">
                  {advantage.description}
                </p>
                
                <div className="mt-auto pt-4 border-t border-border/50">
                  <div className="grid grid-cols-2 gap-2">
                    {advantage.features.map((feature, featureIndex) => (
                      <motion.div 
                        key={feature} 
                        initial={{ opacity: 0, x: -10 }}
                        whileInView={{ opacity: 1, x: 0 }}
                        viewport={{ once: true }}
                        transition={{ duration: 0.3, delay: index * 0.1 + featureIndex * 0.05 }}
                        className="flex items-center justify-center gap-2 text-xs text-muted-foreground group-hover:text-foreground/80 transition-colors duration-300"
                      >
                        <div className={`w-1 h-1 rounded-full ${advantage.iconColor.replace('text-', 'bg-')} group-hover:scale-125 transition-transform duration-300`}></div>
                        <span className="leading-relaxed">{feature}</span>
                      </motion.div>
                    ))}
                  </div>
                </div>
              </div>
              
              {/* 悬浮时的微妙光晕效果 */}
              <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500 pointer-events-none"></div>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
} 