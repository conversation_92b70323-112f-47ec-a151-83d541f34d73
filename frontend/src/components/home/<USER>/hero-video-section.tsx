import { HeroVideoDialog } from '@/components/home/<USER>/hero-video-dialog';

export function HeroVideoSection() {
  return (
    <div className="relative px-6 mt-10">
      <div className="relative w-full max-w-3xl mx-auto shadow-xl rounded-2xl overflow-hidden">
        <HeroVideoDialog
          className="block dark:hidden z-999"
          animationStyle="from-center"
          videoSrc="/Loomu-introduce.mp4"
          thumbnailSrc="/video-light.png"
          thumbnailAlt="Loomu AI 介绍视频"
        />
        <HeroVideoDialog
          className="hidden dark:block z-999"
          animationStyle="from-center"
          videoSrc="/Loomu-introduce.mp4"
          thumbnailSrc="/video-dark.png"
          thumbnailAlt="Loomu AI 介绍视频"
        />
      </div>
    </div>
  );
}
