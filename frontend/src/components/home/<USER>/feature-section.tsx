import { SectionHeader } from '@/components/home/<USER>';
import { Feature as FeatureComponent } from '@/components/home/<USER>/feature-slideshow';
import { siteConfig } from '@/lib/home';

export function FeatureSection() {
  const { title, description, steps } = siteConfig.features;

  // Transform the steps data to match FeatureItem interface
  const featureItems = steps.map(step => ({
    id: step.id,
    title: step.title,
    content: step.description, // Map description to content
  }));

  return (
    <section
      id="features"
      className="flex flex-col items-center justify-center gap-5 w-full relative"
    >
      <SectionHeader>
        <h2 className="text-3xl md:text-4xl font-medium tracking-tighter text-center text-balance">
          {title}
        </h2>
        <p className="text-muted-foreground text-center text-balance font-medium">
          {description}
        </p>
      </SectionHeader>
      <div className="w-full h-full lg:h-[450px] flex items-center justify-center">
        <FeatureComponent
          collapseDelay={5000}
          linePosition="bottom"
          featureItems={featureItems}
          lineColor="bg-secondary"
        />
      </div>
    </section>
  );
}
