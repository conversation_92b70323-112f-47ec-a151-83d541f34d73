import React, { useEffect, useState } from "react";
import { motion } from 'framer-motion';
import { 
  Radar, 
  Sparkles, 
  Users, 
  Calendar,
  TrendingUp,
  Image,
  Video,
  FileText,
  Mic,
  Zap,
  Target,
  Clock,
  CheckCircle
} from 'lucide-react';
import { Card } from '@/components/home/<USER>/card';
import { useLanguage } from '@/contexts/LanguageContext';

export function FeaturesSection() {
  const { t } = useLanguage();
  const [activeFeature, setActiveFeature] = useState(0);
  const [isPaused, setIsPaused] = useState(false);

  useEffect(() => {
    if (isPaused) return; // Don't start interval if paused
    
    const interval = setInterval(() => {
      setActiveFeature((prev) => (prev + 1) % 4);
    }, 4000);
    
    return () => clearInterval(interval);
  }, [isPaused]);

  const handleMouseEnter = () => {
    setIsPaused(true);
  };

  const handleMouseLeave = () => {
    setIsPaused(false);
  };

  const features = [
    {
      id: 1,
      title: t('features.radar.title'),
      subtitle: t('features.radar.subtitle'),
      icon: Radar,
      bgColor: "bg-purple-50 dark:bg-purple-950/20",
      iconColor: "text-purple-600",
      description: t('features.radar.description'),
      visual: "radar"
    },
    {
      id: 2,
      title: t('features.contentEngine.title'),
      subtitle: t('features.contentEngine.subtitle'),
      icon: Sparkles,
      bgColor: "bg-blue-50 dark:bg-blue-950/20",
      iconColor: "text-blue-600",
      description: t('features.contentEngine.description'),
      visual: "content"
    },
    {
      id: 3,
      title: t('features.automation.title'),
      subtitle: t('features.automation.subtitle'),
      icon: Users,
      bgColor: "bg-green-50 dark:bg-green-950/20",
      iconColor: "text-green-600",
      description: t('features.automation.description'),
      visual: "automation"
    },
    {
      id: 4,
      title: t('features.calendar.title'),
      subtitle: t('features.calendar.subtitle'),
      icon: Calendar,
      bgColor: "bg-orange-50 dark:bg-orange-950/20",
      iconColor: "text-orange-600",
      description: t('features.calendar.description'),
      visual: "calendar"
    }
  ];

  const currentFeature = features[activeFeature];
  const IconComponent = currentFeature.icon;

  const RadarVisual = () => (
    <div className="relative w-64 h-64 mx-auto">
      {/* Main radar circles */}
      <div className="absolute inset-0 rounded-full border-2 border-purple-400/60 animate-pulse">
        <div className="absolute inset-4 rounded-full border border-purple-300/50">
          <div className="absolute inset-4 rounded-full border border-purple-200/40">
            <div className="absolute inset-4 rounded-full border border-purple-100/30">
              {/* Radar sweep line */}
              <motion.div 
                className="absolute top-1/2 left-1/2 w-1 h-32 bg-gradient-to-t from-purple-500 via-pink-400 to-transparent origin-bottom transform -translate-x-1/2 -translate-y-full"
                animate={{ rotate: 360 }}
                transition={{ 
                  duration: 3, 
                  repeat: Infinity, 
                  ease: "linear" 
                }}
              />
              {/* Center dot */}
              <div className="absolute top-1/2 left-1/2 w-3 h-3 bg-purple-500 rounded-full transform -translate-x-1/2 -translate-y-1/2 shadow-lg shadow-purple-500/50" />
            </div>
          </div>
        </div>
      </div>
      
      {/* Grid lines */}
      <div className="absolute inset-0">
        <div className="absolute top-1/2 left-0 w-full h-px bg-gradient-to-r from-transparent via-purple-300/30 to-transparent" />
        <div className="absolute left-1/2 top-0 w-px h-full bg-gradient-to-b from-transparent via-purple-300/30 to-transparent" />
      </div>
      
      {/* Inspiration bubbles with enhanced animations */}
      <motion.div 
        className="absolute top-8 right-12 w-10 h-10 bg-gradient-to-br from-purple-500 to-pink-500 rounded-full flex items-center justify-center shadow-lg shadow-purple-500/30"
        animate={{ 
          y: [-8, 8, -8],
          scale: [1, 1.1, 1],
          rotate: [0, 5, -5, 0]
        }}
        transition={{ 
          duration: 3, 
          repeat: Infinity,
          ease: "easeInOut"
        }}
      >
        <TrendingUp size={18} className="text-white" />
      </motion.div>
      
      <motion.div 
        className="absolute bottom-16 left-12 w-8 h-8 bg-gradient-to-br from-pink-500 to-red-500 rounded-full flex items-center justify-center shadow-lg shadow-pink-500/30"
        animate={{ 
          y: [-6, 6, -6],
          scale: [1, 1.15, 1],
          rotate: [0, -5, 5, 0]
        }}
        transition={{ 
          duration: 3.5, 
          repeat: Infinity, 
          delay: 0.5,
          ease: "easeInOut"
        }}
      >
        <FileText size={14} className="text-white" />
      </motion.div>
      
      <motion.div 
        className="absolute top-16 left-16 w-9 h-9 bg-gradient-to-br from-blue-500 to-cyan-500 rounded-full flex items-center justify-center shadow-lg shadow-blue-500/30"
        animate={{ 
          y: [-7, 7, -7],
          scale: [1, 1.12, 1],
          rotate: [0, 3, -3, 0]
        }}
        transition={{ 
          duration: 4, 
          repeat: Infinity, 
          delay: 1,
          ease: "easeInOut"
        }}
      >
        <FileText size={16} className="text-white" />
      </motion.div>

      {/* Additional floating elements */}
      {/* <motion.div 
        className="absolute top-1/4 right-1/4 w-6 h-6 bg-gradient-to-br from-yellow-500 to-orange-500 rounded-full flex items-center justify-center shadow-lg shadow-yellow-500/30"
        animate={{ 
          y: [-4, 4, -4],
          scale: [1, 1.08, 1],
          opacity: [0.7, 1, 0.7]
        }}
        transition={{ 
          duration: 2.5, 
          repeat: Infinity, 
          delay: 1.5,
          ease: "easeInOut"
        }}
      >
        <Image size={12} className="text-white" />
      </motion.div> */}

      {/* <motion.div 
        className="absolute bottom-1/4 left-1/4 w-5 h-5 bg-gradient-to-br from-green-500 to-emerald-500 rounded-full flex items-center justify-center shadow-lg shadow-green-500/30"
        animate={{ 
          y: [-5, 5, -5],
          scale: [1, 1.1, 1],
          opacity: [0.6, 1, 0.6]
        }}
        transition={{ 
          duration: 3.2, 
          repeat: Infinity, 
          delay: 2,
          ease: "easeInOut"
        }}
      >
        <Video size={1} className="text-white" />
      </motion.div> */}

      {/* Pulse rings */}
      <motion.div 
        className="absolute top-1/2 left-1/2 w-4 h-4 border-2 border-purple-400 rounded-full transform -translate-x-1/2 -translate-y-1/2"
        animate={{ 
          scale: [1, 2, 1],
          opacity: [1, 0, 1]
        }}
        transition={{ 
          duration: 2, 
          repeat: Infinity,
          ease: "easeOut"
        }}
      />
      
      <motion.div 
        className="absolute top-1/2 left-1/2 w-6 h-6 border border-purple-300 rounded-full transform -translate-x-1/2 -translate-y-1/2"
        animate={{ 
          scale: [1, 1.5, 1],
          opacity: [0.5, 0, 0.5]
        }}
        transition={{ 
          duration: 2.5, 
          repeat: Infinity,
          delay: 0.5,
          ease: "easeOut"
        }}
      />

      {/* Connection lines */}
      <svg className="absolute inset-0 w-full h-full" style={{ zIndex: -1 }}>
        <motion.line
          x1="50%"
          y1="50%"
          x2="75%"
          y2="25%"
          stroke="url(#purpleGradient)"
          strokeWidth="1"
          strokeDasharray="5,5"
          initial={{ pathLength: 0 }}
          animate={{ pathLength: 1 }}
          transition={{ duration: 2, repeat: Infinity, repeatDelay: 1 }}
        />
        <motion.line
          x1="50%"
          y1="50%"
          x2="25%"
          y2="75%"
          stroke="url(#pinkGradient)"
          strokeWidth="1"
          strokeDasharray="5,5"
          initial={{ pathLength: 0 }}
          animate={{ pathLength: 1 }}
          transition={{ duration: 2, repeat: Infinity, repeatDelay: 1, delay: 0.5 }}
        />
        <motion.line
          x1="50%"
          y1="50%"
          x2="35%"
          y2="35%"
          stroke="url(#blueGradient)"
          strokeWidth="1"
          strokeDasharray="5,5"
          initial={{ pathLength: 0 }}
          animate={{ pathLength: 1 }}
          transition={{ duration: 2, repeat: Infinity, repeatDelay: 1, delay: 1 }}
        />
        
        {/* Gradients */}
        <defs>
          <linearGradient id="purpleGradient" x1="0%" y1="0%" x2="100%" y2="0%">
            <stop offset="0%" stopColor="rgba(168, 85, 247, 0.3)" />
            <stop offset="100%" stopColor="rgba(236, 72, 153, 0.3)" />
          </linearGradient>
          <linearGradient id="pinkGradient" x1="0%" y1="0%" x2="100%" y2="0%">
            <stop offset="0%" stopColor="rgba(236, 72, 153, 0.3)" />
            <stop offset="100%" stopColor="rgba(239, 68, 68, 0.3)" />
          </linearGradient>
          <linearGradient id="blueGradient" x1="0%" y1="0%" x2="100%" y2="0%">
            <stop offset="0%" stopColor="rgba(59, 130, 246, 0.3)" />
            <stop offset="100%" stopColor="rgba(6, 182, 212, 0.3)" />
          </linearGradient>
        </defs>
      </svg>
    </div>
  );

  const ContentEngineVisual = () => (
    <div className="relative w-64 h-64 mx-auto">
      <div className="absolute top-1/2 left-8 transform -translate-y-1/2">
        <motion.div 
          className="w-16 h-16 bg-gradient-to-br from-blue-500 to-cyan-500 rounded-lg flex items-center justify-center"
          animate={{ scale: [1, 1.05, 1] }}
          transition={{ duration: 2, repeat: Infinity }}
        >
          <Sparkles className="text-white" size={24} />
        </motion.div>
        <p className="text-sm text-center mt-2 text-blue-600 font-medium">{t('features.aiEngine')}</p>
      </div>
      
      <div className="absolute top-1/2 right-8 transform -translate-y-1/2 space-y-3">
        {[
          { icon: FileText, color: "bg-green-500", delay: 0 },
          { icon: Image, color: "bg-yellow-500", delay: 0.5 },
          { icon: Mic, color: "bg-red-500", delay: 1 },
          { icon: Video, color: "bg-purple-500", delay: 1.5 }
        ].map((item, index) => (
          <motion.div 
            key={index}
            className={`w-12 h-12 ${item.color} rounded-lg flex items-center justify-center`}
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: item.delay, duration: 0.5 }}
          >
            <item.icon className="text-white" size={20} />
          </motion.div>
        ))}
      </div>
      
      {/* Connecting lines - adjusted to center */}
      <div className="absolute top-1/2 left-24 right-24 h-0.5 bg-gradient-to-r from-blue-500 to-purple-500 transform -translate-y-1/2">
        <motion.div 
          className="absolute -top-1.5 left-0 w-3 h-3 bg-blue-500 rounded-full shadow-lg shadow-blue-500/50"
          animate={{ x: [0, 100, 0] }}
          transition={{ duration: 3, repeat: Infinity }}
        />
      </div>
    </div>
  );

  const AutomationVisual = () => (
    <div className="relative w-64 h-64 mx-auto">
      <div className="flex flex-col space-y-4">
        {[
          { text: t('features.automationSteps.layout'), icon: Target },
          { text: t('features.automationSteps.rewrite'), icon: Zap },
          { text: t('features.automationSteps.seo'), icon: TrendingUp },
          { text: t('features.automationSteps.publish'), icon: CheckCircle }
        ].map((step, index) => (
          <motion.div 
            key={index}
            className="flex items-center space-x-4"
            initial={{ opacity: 0, y: 20 }}
            animate={{ 
              opacity: activeFeature === 2 ? 1 : 0, 
              y: activeFeature === 2 ? 0 : 20 
            }}
            transition={{ 
              delay: activeFeature === 2 ? index * 0.3 : 0, 
              duration: 0.5 
            }}
          >
            <div className="w-12 h-12 bg-gradient-to-br from-green-500 to-emerald-500 rounded-lg flex items-center justify-center">
              <step.icon className="text-white" size={20} />
            </div>
            <div className="flex-1 h-2 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden">
              <motion.div 
                className="h-full bg-gradient-to-r from-green-500 to-emerald-500"
                initial={{ width: 0 }}
                animate={{ width: activeFeature === 2 ? '100%' : '0%' }}
                transition={{ 
                  duration: 1, 
                  delay: activeFeature === 2 ? index * 0.5 : 0 
                }}
              />
            </div>
            <span className="text-sm font-medium text-green-600 dark:text-green-400">{step.text}</span>
          </motion.div>
        ))}
      </div>
    </div>
  );

  const CalendarVisual = () => (
    <div className="relative w-64 h-64 mx-auto">
      <div className="bg-card border border-border rounded-lg shadow-lg p-4">
        <div className="grid grid-cols-7 gap-1 mb-4">
          {['S', 'M', 'T', 'W', 'T', 'F', 'S'].map((day, i) => (
            <div key={i} className="text-xs text-muted-foreground text-center font-medium">{day}</div>
          ))}
        </div>
        <div className="grid grid-cols-7 gap-1">
          {Array.from({length: 35}, (_, i) => {
            const hasContent = [7, 12, 15, 23, 28].includes(i);
            return (
              <div 
                key={i} 
                className={`aspect-square text-xs flex items-center justify-center rounded ${
                  hasContent 
                    ? 'bg-gradient-to-br from-orange-500 to-red-500 text-white' 
                    : 'text-muted-foreground hover:bg-muted'
                }`}
              >
                {i < 31 ? i + 1 : ''}
              </div>
            );
          })}
        </div>
      </div>
      
      {/* Floating task indicators */}
      <motion.div 
        className="absolute -top-2 -right-2 w-6 h-6 bg-orange-500 rounded-full flex items-center justify-center"
        animate={{ scale: [1, 1.2, 1] }}
        transition={{ duration: 2, repeat: Infinity }}
      >
        <Clock size={12} className="text-white" />
      </motion.div>
    </div>
  );

  const getVisualComponent = (visual: string) => {
    switch(visual) {
      case 'radar': return <RadarVisual />;
      case 'content': return <ContentEngineVisual />;
      case 'automation': return <AutomationVisual />;
      case 'calendar': return <CalendarVisual />;
      default: return <RadarVisual />;
    }
  };

  return (
    <section 
      id="features" 
      className="relative py-20 overflow-hidden bg-background"
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      <div className="absolute inset-0 -z-10">
        <div className="absolute inset-0 bg-[radial-gradient(ellipse_80%_50%_at_50%_50%,rgba(var(--primary),0.05),transparent)]" />
      </div>

      <div className="max-w-7xl mx-auto px-6 lg:px-8 relative z-10">
        {/* Debug info */}
        {/* <div className="text-center mb-4 text-sm text-muted-foreground">
          Active Feature: {activeFeature + 1} - {currentFeature.title}
        </div> */}
        
        <div className="flex flex-wrap justify-center gap-4 mb-16">
          {features.map((feature, index) => {
            const FeatureIcon = feature.icon;
            return (
              <motion.button
                key={feature.id}
                onClick={() => setActiveFeature(index)}
                className={`group px-6 py-3 rounded-full font-medium transition-all duration-300 border ${
                  activeFeature === index
                    ? 'bg-card text-foreground shadow-lg scale-105 border-border'
                    : 'bg-card/50 text-muted-foreground hover:bg-card hover:text-foreground border-border/50'
                }`}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <FeatureIcon className="inline mr-2" size={20} />
                {feature.title}
              </motion.button>
            );
          })}
        </div>

        <motion.div 
          className="grid lg:grid-cols-2 gap-16 items-center"
          key={activeFeature}
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.6 }}
          onMouseEnter={handleMouseEnter}
          onMouseLeave={handleMouseLeave}
        >
          <div className="text-center lg:text-left">
            <motion.div 
              className={`inline-flex items-center justify-center w-20 h-20 rounded-2xl ${currentFeature.bgColor} mb-6`}
              whileHover={{ scale: 1.1 }}
              transition={{ duration: 0.3 }}
            >
              <IconComponent className={`w-10 h-10 ${currentFeature.iconColor}`} />
            </motion.div>
            
            <h3 className="text-3xl md:text-4xl font-bold text-foreground mb-4">
              {currentFeature.title}
            </h3>
            
            <p className="text-xl text-primary mb-6 font-medium">
              {currentFeature.subtitle}
            </p>
            
            <p className="text-lg text-muted-foreground mb-8 leading-relaxed">
              {currentFeature.description}
            </p>
          </div>

          <div className="flex justify-center">
            <Card className="p-8 bg-card/50 backdrop-blur-sm border-border shadow-xl">
              {getVisualComponent(currentFeature.visual)}
            </Card>
          </div>
        </motion.div>
      </div>
    </section>
  );
} 