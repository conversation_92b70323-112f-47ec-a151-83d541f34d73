'use client';
import { HeroVideoSection } from '@/components/home/<USER>/hero-video-section';
import { siteConfig } from '@/lib/home';
import { ArrowRight, Github, X, AlertCircle } from 'lucide-react';
import { FlickeringGrid } from '@/components/home/<USER>/flickering-grid';
import { useMediaQuery } from '@/hooks/use-media-query';
import { useState, useEffect, useRef, FormEvent } from 'react';
import { useScroll } from 'motion/react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/components/AuthProvider';
import {
  createProject,
  createThread,
  addUserMessage,
  startAgent,
  BillingError,
} from '@/lib/api';
import { generateThreadName } from '@/lib/actions/threads';
import GoogleSignIn from '@/components/GoogleSignIn';
import { Input } from '@/components/ui/input';
import { SubmitButton } from '@/components/ui/submit-button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogOverlay,
} from '@/components/ui/dialog';
import { BillingErrorAlert } from '@/components/billing/usage-limit-alert';
import { useBillingError } from '@/hooks/useBillingError';
import { useAccounts } from '@/hooks/use-accounts';
import { isLocalMode, config } from '@/lib/config';
import { toast } from 'sonner';
import { useLanguage } from '@/contexts/LanguageContext';

// Custom dialog overlay with blur effect
const BlurredDialogOverlay = () => (
  <DialogOverlay className="bg-background/40 backdrop-blur-md" />
);

// Constant for localStorage key to ensure consistency
const PENDING_PROMPT_KEY = 'pendingAgentPrompt';

export function HeroSection() {
  const { hero } = siteConfig;
  const { t } = useLanguage();
  const tablet = useMediaQuery('(max-width: 1024px)');
  const [mounted, setMounted] = useState(false);
  const [isScrolling, setIsScrolling] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const scrollTimeout = useRef<NodeJS.Timeout | null>(null);
  const { scrollY } = useScroll();
  const [inputValue, setInputValue] = useState('');
  const router = useRouter();
  const { user, isLoading } = useAuth();
  const { billingError, handleBillingError, clearBillingError } =
    useBillingError();
  const { data: accounts } = useAccounts();
  const personalAccount = accounts?.find((account) => account.personal_account);

  // Auth dialog state
  const [authDialogOpen, setAuthDialogOpen] = useState(false);
  const [authError, setAuthError] = useState<string | null>(null);

  useEffect(() => {
    setMounted(true);
  }, []);

  // Detect when scrolling is active to reduce animation complexity
  useEffect(() => {
    const unsubscribe = scrollY.on('change', () => {
      setIsScrolling(true);

      // Clear any existing timeout
      if (scrollTimeout.current) {
        clearTimeout(scrollTimeout.current);
      }

      // Set a new timeout
      scrollTimeout.current = setTimeout(() => {
        setIsScrolling(false);
      }, 300); // Wait 300ms after scroll stops
    });

    return () => {
      unsubscribe();
      if (scrollTimeout.current) {
        clearTimeout(scrollTimeout.current);
      }
    };
  }, [scrollY]);

  // Store the input value when auth dialog opens
  useEffect(() => {
    if (authDialogOpen && inputValue.trim()) {
      localStorage.setItem(PENDING_PROMPT_KEY, inputValue.trim());
    }
  }, [authDialogOpen, inputValue]);

  // Close dialog and redirect when user authenticates
  useEffect(() => {
    if (authDialogOpen && user && !isLoading) {
      setAuthDialogOpen(false);
      router.push('/dashboard');
    }
  }, [user, isLoading, authDialogOpen, router]);

  // Create an agent with the provided prompt
  const createAgentWithPrompt = async () => {
    if (!inputValue.trim() || isSubmitting) return;

    setIsSubmitting(true);

    try {
      // Generate a name for the project using GPT
      const projectName = await generateThreadName(inputValue);

      // 1. Create a new project with the GPT-generated name
      const newAgent = await createProject({
        name: projectName,
        description: '',
      });

      // 2. Create a new thread for this project
      const thread = await createThread(newAgent.id);

      // 3. Add the user message to the thread
      await addUserMessage(thread.thread_id, inputValue.trim());

      // 4. Start the agent with the thread ID
      await startAgent(thread.thread_id, {
        stream: true,
      });

      // 5. Navigate to the new agent's thread page
      router.push(`/agents/${thread.thread_id}`);
      // Clear input on success
      setInputValue('');
    } catch (error: any) {
      console.error('创建代理时出错:', error);

      // Check specifically for BillingError (402)
      if (error instanceof BillingError) {
        console.log('Handling BillingError from hero section:', error.detail);
        handleBillingError({
          message:
            error.detail.message ||
            '已达到本月使用限额。请升级您的计划以继续使用。',
          currentUsage: error.detail.currentUsage as number | undefined,
          limit: error.detail.limit as number | undefined,
          subscription: error.detail.subscription || {
            price_id: config.SUBSCRIPTION_TIERS.FREE.priceId,
            plan_name: '免费版',
          },
        });
        // Don't show toast for billing errors
      } else {
        // Handle other errors (e.g., network, other API errors)
        const isConnectionError =
          error instanceof TypeError &&
          error.message.includes('Failed to fetch');
        if (!isLocalMode() || isConnectionError) {
          toast.error(
            error.message || '创建代理失败。请重试。',
          );
        }
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle form submission
  const handleSubmit = async (e?: FormEvent) => {
    if (e) {
      e.preventDefault();
      e.stopPropagation(); // Stop event propagation to prevent dialog closing
    }

    if (!inputValue.trim() || isSubmitting) return;

    // If user is not logged in, save prompt and show auth dialog
    if (!user && !isLoading) {
      // Save prompt to localStorage BEFORE showing the dialog
      localStorage.setItem(PENDING_PROMPT_KEY, inputValue.trim());
      setAuthDialogOpen(true);
      return;
    }

    // User is logged in, create the agent
    createAgentWithPrompt();
  };

  // Handle Enter key press
  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      e.preventDefault(); // Prevent default form submission
      e.stopPropagation(); 
      return // Stop event propagation
      handleSubmit();
    }
  };

  // Handle auth form submission
  const handleSignIn = async (prevState: any, formData: FormData) => {
    setAuthError(null);
    try {
      // Implement sign in logic here
      const email = formData.get('email') as string;
      const password = formData.get('password') as string;

      // Add the returnUrl to the form data for proper redirection
      formData.append('returnUrl', '/dashboard');

      return { message: t('home.hero.invalidCredentials') };
    } catch (error) {
      console.error('登录错误:', error);
      setAuthError(
        error instanceof Error ? error.message : t('home.hero.loginError')
      );
      return { message: t('home.hero.loginError') };
    }
  };

  return (
    <section id="hero" className="w-full relative overflow-hidden">
      {/* Background Pattern Layer */}
      <div className="absolute inset-0 -z-20">
        {/* Curved Lines and Dots */}
        <svg className="absolute w-full h-full" viewBox="0 0 1200 800" fill="none" xmlns="http://www.w3.org/2000/svg"
          style={{ 
            mask: 'linear-gradient(to bottom, rgba(0,0,0,1) 50%, rgba(0,0,0,0))',
            WebkitMask: 'linear-gradient(to bottom, rgba(0,0,0,1) 50%, rgba(0,0,0,0))'
          }}
        >
          {/* Main flowing curves */}
          <path
            d="M-100 200C50 150 300 500 600 450C900 400 1100 200 1300 250"
            className="stroke-secondary/45 dark:stroke-secondary/35"
            strokeWidth="4"
            fill="none"
          />
          <path
            d="M-100 400C200 350 400 650 600 600C800 550 1000 400 1300 450"
            className="stroke-secondary/40 dark:stroke-secondary/30"
            strokeWidth="3.5"
            fill="none"
          />
          <path
            d="M-100 600C300 550 500 800 700 750C900 700 1100 600 1300 650"
            className="stroke-secondary/35 dark:stroke-secondary/25"
            strokeWidth="3"
            fill="none"
          />

          {/* Scattered dots */}
          <circle cx="150" cy="150" r="2" className="fill-secondary/25" />
          <circle cx="450" cy="180" r="1.5" className="fill-secondary/20" />
          <circle cx="750" cy="120" r="2" className="fill-secondary/30" />
          <circle cx="1050" cy="200" r="1.5" className="fill-secondary/25" />
          
          <circle cx="250" cy="350" r="2" className="fill-secondary/20" />
          <circle cx="650" cy="380" r="1.5" className="fill-secondary/30" />
          <circle cx="950" cy="320" r="2" className="fill-secondary/25" />
          
          <circle cx="150" cy="550" r="1.5" className="fill-secondary/30" />
          <circle cx="550" cy="580" r="2" className="fill-secondary/25" />
          <circle cx="850" cy="520" r="1.5" className="fill-secondary/20" />
          <circle cx="1100" cy="600" r="2" className="fill-secondary/30" />
          
          <circle cx="350" cy="700" r="1.5" className="fill-secondary/25" />
          <circle cx="750" cy="680" r="2" className="fill-secondary/20" />
          <circle cx="1000" cy="720" r="1.5" className="fill-secondary/30" />

          {/* Animated dots with pulse effect */}
          <circle cx="300" cy="250" r="2" className="fill-secondary/40">
            <animate
              attributeName="opacity"
              values="0.4;0.2;0.4"
              dur="3s"
              repeatCount="indefinite"
            />
          </circle>
          <circle cx="800" cy="300" r="2" className="fill-secondary/40">
            <animate
              attributeName="opacity"
              values="0.4;0.2;0.4"
              dur="4s"
              repeatCount="indefinite"
            />
          </circle>
          <circle cx="600" cy="500" r="2" className="fill-secondary/40">
            <animate
              attributeName="opacity"
              values="0.4;0.2;0.4"
              dur="5s"
              repeatCount="indefinite"
            />
          </circle>
        </svg>

        {/* Animated gradient line */}
        <div className="absolute inset-0">
          <div className="absolute top-2/4 left-0 w-full h-[2px] bg-gradient-to-r from-transparent via-secondary/35 to-transparent animate-flow-left"></div>
        </div>

        {/* Enhanced radial gradient overlay */}
        <div className="absolute inset-0 bg-[radial-gradient(ellipse_80%_80%_at_50%_-20%,rgba(var(--secondary),0.35),rgba(255,255,255,0))]"></div>
      </div>

      <div className="relative flex flex-col items-center w-full px-6">
        {/* Left side decoration */}
        <div className="absolute left-0 top-0 h-[600px] md:h-[800px] w-1/3 -z-10 overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-r from-transparent via-transparent to-background z-10" />
          <div className="absolute inset-x-0 top-0 h-32 bg-gradient-to-b from-background via-background/90 to-transparent z-10" />
          <div className="absolute inset-x-0 bottom-0 h-48 bg-gradient-to-t from-background via-background/90 to-transparent z-10" />
        </div>

        {/* Right side decoration */}
        <div className="absolute right-0 top-0 h-[600px] md:h-[800px] w-1/3 -z-10 overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-l from-transparent via-transparent to-background z-10" />
          <div className="absolute inset-x-0 top-0 h-32 bg-gradient-to-b from-background via-background/90 to-transparent z-10" />
          <div className="absolute inset-x-0 bottom-0 h-48 bg-gradient-to-t from-background via-background/90 to-transparent z-10" />
        </div>

        {/* Content area */}
        {/* <div className="relative z-10 pt-32 max-w-3xl mx-auto h-full w-full flex flex-col gap-10 items-center justify-center">
          <div className="flex flex-col items-center justify-center gap-5">
            <h1 className="text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-medium tracking-tighter text-balance text-center">
              <span className="text-secondary">Loomu</span>
              <span className="text-primary">，您的 AI 助手。</span>
            </h1>
            <p className="text-base md:text-lg text-center text-muted-foreground font-medium text-balance leading-relaxed tracking-tight">
              {hero.description}
            </p>
          </div>

  
          <div className="w-full max-w-xl relative group">
            <div className="absolute -inset-1 bg-gradient-to-r from-secondary/40 via-secondary/60 to-secondary/40 rounded-full opacity-0 group-hover:opacity-100 blur-2xl transition-all duration-500 group-hover:duration-200 -z-10"></div>
            <div className="absolute -inset-2 bg-gradient-to-r from-secondary/30 via-secondary/40 to-secondary/30 rounded-full blur-2xl opacity-40 -z-10"></div>
            
            <form className="w-full relative" onSubmit={handleSubmit}>
              <div className="relative">
                <div className="flex items-center rounded-full border border-border bg-background/80 backdrop-blur px-4 shadow-lg transition-all duration-300 group-hover:border-secondary/50 group-hover:shadow-secondary/30 group-hover:shadow-xl">
                  <input
                    type="text"
                    value={inputValue}
                    onChange={(e) => setInputValue(e.target.value)}
                    onKeyDown={handleKeyDown}
                    placeholder={t('home.hero.placeholder')}
                    className="flex-1 h-12 md:h-14 rounded-full px-2 bg-transparent focus:outline-none text-sm md:text-base py-2"
                    disabled={isSubmitting}
                  />
                  <button
                    type="submit"
                    className={`rounded-full p-2 md:p-3 transition-all duration-300 ${
                      inputValue.trim()
                        ? 'bg-secondary text-secondary-foreground hover:bg-secondary/90 hover:scale-105'
                        : 'bg-muted text-muted-foreground'
                    }`}
                    disabled={!inputValue.trim() || isSubmitting}
                    aria-label="Submit"
                  >
                    {isSubmitting ? (
                      <div className="h-4 md:h-5 w-4 md:w-5 border-2 border-secondary-foreground border-t-transparent rounded-full animate-spin" />
                    ) : (
                      <ArrowRight className="size-4 md:size-5" />
                    )}
                  </button>
                </div>
              </div>
            </form>
          </div>
        </div> */}
      </div>
      {/* Video section with higher z-index to create layering effect */}
      <div className="relative z-10 mb-10 max-w-4xl mx-auto">
        <HeroVideoSection />
      </div>

      {/* Auth Dialog */}
      <Dialog open={authDialogOpen} onOpenChange={setAuthDialogOpen}>
        <BlurredDialogOverlay />
        <DialogContent className="sm:max-w-md rounded-xl bg-[#F3F4F6] dark:bg-[#F9FAFB]/[0.02] border border-border">
          <DialogHeader>
            <div className="flex items-center justify-between">
              <DialogTitle className="text-xl font-medium">
                {t('home.hero.authRequired')}
              </DialogTitle>
            </div>
            <DialogDescription className="text-muted-foreground">
              {t('home.hero.authDescription')}
            </DialogDescription>
          </DialogHeader>

          {/* Auth error message */}
          {authError && (
            <div className="mb-4 p-3 rounded-lg flex items-center gap-3 bg-secondary/10 border border-secondary/20 text-secondary">
              <AlertCircle className="h-5 w-5 flex-shrink-0 text-secondary" />
              <span className="text-sm font-medium">{authError}</span>
            </div>
          )}

          {/* Google Sign In */}
          <div className="w-full">
            <GoogleSignIn returnUrl="/dashboard" />
          </div>

          {/* Divider */}
          <div className="relative my-6">
            <div className="absolute inset-0 flex items-center">
              <div className="w-full border-t border-border"></div>
            </div>
            <div className="relative flex justify-center text-sm">
              <span className="px-2 bg-[#F3F4F6] dark:bg-[#F9FAFB]/[0.02] text-muted-foreground">
                {t('home.hero.orContinueWith')}
              </span>
            </div>
          </div>

          {/* Sign in form */}
          <form className="space-y-4">
            <div>
              <Input
                id="email"
                name="email"
                type="email"
                placeholder={t('home.hero.emailPlaceholder')}
                className="h-12 rounded-full bg-background border-border"
                required
              />
            </div>

            <div>
              <Input
                id="password"
                name="password"
                type="password"
                placeholder={t('home.hero.passwordPlaceholder')}
                className="h-12 rounded-full bg-background border-border"
                required
              />
            </div>

            <div className="space-y-4 pt-4">
              <SubmitButton
                formAction={handleSignIn}
                className="w-full h-12 rounded-full bg-primary text-primary-foreground hover:bg-primary/90 transition-all shadow-md"
                pendingText="登录中..."
              >
                登录
              </SubmitButton>

              <Link
                href={`/auth?mode=signup&returnUrl=${encodeURIComponent('/dashboard')}`}
                className="flex h-12 items-center justify-center w-full text-center rounded-full border border-border bg-background hover:bg-accent/20 transition-all"
                onClick={() => setAuthDialogOpen(false)}
              >
                创建新账号
              </Link>
            </div>

            <div className="text-center pt-2">
              <Link
                href={`/auth?returnUrl=${encodeURIComponent('/dashboard')}`}
                className="text-sm text-primary hover:underline"
                onClick={() => setAuthDialogOpen(false)}
              >
                更多登录选项
              </Link>
            </div>
          </form>

          <div className="mt-4 text-center text-xs text-muted-foreground">
            继续使用即表示您同意我们的{' '}
            <Link href="/terms" className="text-primary hover:underline">
              服务条款
            </Link>{' '}
            和{' '}
            <Link href="/privacy" className="text-primary hover:underline">
              隐私政策
            </Link>
          </div>
        </DialogContent>
      </Dialog>

      {/* Add Billing Error Alert here */}
      <BillingErrorAlert
        message={billingError?.message}
        currentUsage={billingError?.currentUsage}
        limit={billingError?.limit}
        accountId={personalAccount?.account_id}
        onDismiss={clearBillingError}
        isOpen={!!billingError}
      />
    </section>
  );
}
