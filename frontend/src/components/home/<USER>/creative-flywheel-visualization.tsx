'use client'

import { motion } from 'framer-motion'
import { useTheme } from 'next-themes'
import { 
  Radar, 
  FileText, 
  Wand2, 
  TrendingUp, 
  Network, 
  DollarSign,
  Sparkles,
  Zap
} from 'lucide-react'

const flywheelSteps = [
  {
    id: 1,
    title: 'Inspiration Capture',
    icon: Radar,
    angle: 0,
    description: 'Discover trending topics'
  },
  {
    id: 2,
    title: 'Content Generation',
    icon: FileText,
    angle: 60,
    description: 'Create multi-format content'
  },
  {
    id: 3,
    title: 'Smart Optimization',
    icon: Wand2,
    angle: 120,
    description: 'AI-powered enhancement'
  },
  {
    id: 4,
    title: 'Data Growth',
    icon: TrendingUp,
    angle: 180,
    description: 'Analytics & insights'
  },
  {
    id: 5,
    title: 'Scalable Expansion',
    icon: Network,
    angle: 240,
    description: 'Audience growth'
  },
  {
    id: 6,
    title: 'Value Monetization',
    icon: DollarSign,
    angle: 300,
    description: 'Revenue generation'
  }
]

const getHexagonVertex = (angle: number, radius: number) => {
  const radian = (angle - 90) * (Math.PI / 180)
  return {
    x: Math.cos(radian) * radius,
    y: Math.sin(radian) * radius
  }
}

export function CreativeFlywheelVisualization() {
  const radius = 210
  const containerSize = 576
  const center = containerSize / 2
  
  const hexagonVertices = flywheelSteps.map(step => 
    getHexagonVertex(step.angle, radius)
  )
  
  const svgPoints = hexagonVertices.map(vertex => 
    `${center + vertex.x},${center + vertex.y}`
  ).join(' ')

  const { resolvedTheme } = useTheme?.() || {}
  const isDark = resolvedTheme === 'dark'
  const green = isDark ? '#00ff88' : '#22c55e' // 深色荧光绿，浅色柔和绿

  return (
    <div className="relative" style={{ width: '576px', height: '576px' }}>
      {/* 外圈 */}
      <motion.div 
        className="absolute rounded-full border border-gray-600/30"
        style={{
          width: '480px',
          height: '480px',
          left: `${center - 240}px`,
          top: `${center - 240}px`,
        }}
        animate={{ rotate: 360 }}
        transition={{ duration: 30, repeat: Infinity, ease: "linear" }}
      />

      {/* 精确的六边形 */}
      <svg 
        className="absolute top-0 left-0 w-full h-full" 
        viewBox={`0 0 ${containerSize} ${containerSize}`}
        style={{ width: `${containerSize}px`, height: `${containerSize}px` }}
      >
        <motion.polygon
          points={svgPoints}
          fill="none"
          stroke="url(#connectionGradient)"
          strokeWidth="3.5"
          strokeDasharray="12,6"
          initial={{ pathLength: 0 }}
          animate={{ pathLength: 1 }}
          transition={{ duration: 2, delay: 1 }}
        />
        
        <defs>
          <linearGradient id="connectionGradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" stopColor="#00ff88" stopOpacity="0.9" />
            <stop offset="50%" stopColor="#8b5cf6" stopOpacity="1" />
            <stop offset="100%" stopColor="#00d4ff" stopOpacity="0.9" />
          </linearGradient>
        </defs>
      </svg>

      {/* 流动粒子 */}
      {[...Array(4)].map((_, i) => {
        return (
          <motion.div
            key={i}
            className="absolute w-4 h-4 rounded-full shadow-lg z-10"
            style={{
              left: '0px',
              top: '0px',
              boxShadow: '0 0 15px #00ff88'
            }}
            animate={{
              x: [
                ...hexagonVertices.map(vertex => center + vertex.x),
                center + hexagonVertices[0].x
              ],
              y: [
                ...hexagonVertices.map(vertex => center + vertex.y),
                center + hexagonVertices[0].y
              ],
            }}
            transition={{
              duration: 10,
              repeat: Infinity,
              ease: "linear",
              delay: i * 2.5
            }}
          />
        )
      })}

      {/* 中心 Loomu AI */}
      <motion.div 
        className="absolute rounded-full bg-gradient-to-br from-purple-500 via-blue-500 to-green-400 flex flex-col items-center justify-center z-20 border-4 border-white/10"
        style={{ 
          width: '144px',
          height: '144px',
          left: `${center - 72}px`,
          top: `${center - 72}px`,
          boxShadow: '0 0 60px rgba(139, 92, 246, 0.6)' 
        }}
        animate={{ 
          scale: [1, 1.02, 1],
          boxShadow: [
            '0 0 60px rgba(139, 92, 246, 0.6)',
            '0 0 90px rgba(139, 92, 246, 0.8)',
            '0 0 60px rgba(139, 92, 246, 0.6)'
          ]
        }}
        transition={{ 
          duration: 3,
          repeat: Infinity,
          ease: "easeInOut"
        }}
      >
        {/* AI符号 */}
        <motion.div 
          className="relative flex flex-col items-center"
          animate={{ 
            rotate: [0, 5, -5, 0]
          }}
          transition={{ 
            duration: 4,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        >
          <div className="relative mb-2">
            <Sparkles className="w-10 h-10 text-white" />
            <motion.div
              className="absolute -top-1 -right-4"
              animate={{ 
                scale: [1, 1.3, 1],
                opacity: [0.7, 1, 0.7]
              }}
              transition={{ 
                duration: 2,
                repeat: Infinity,
                ease: "easeInOut"
              }}
            >
              <Zap className="w-4 h-4" style={{ color: green }} />
            </motion.div>
          </div>
          
          <span className="text-white font-bold text-sm text-center leading-tight">
            Loomu<br/>AI
          </span>
        </motion.div>

        {/* 环绕光粒子 */}
        {[...Array(6)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-2 h-2 bg-white rounded-full opacity-60"
            style={{
              left: '50%',
              top: '50%',
            }}
            animate={{
              x: [0, Math.cos((i * 60) * Math.PI / 180) * 50],
              y: [0, Math.sin((i * 60) * Math.PI / 180) * 50],
              opacity: [0, 1, 0],
              scale: [0, 1, 0]
            }}
            transition={{
              duration: 3,
              repeat: Infinity,
              ease: "easeInOut",
              delay: i * 0.5
            }}
          />
        ))}
      </motion.div>

      {/* 步骤节点 */}
      {flywheelSteps.map((step, index) => {
        const IconComponent = step.icon
        const vertex = hexagonVertices[index]
        const nodeX = center + vertex.x - 48
        const nodeY = center + vertex.y - 48
        
        return (
          <motion.div
            key={step.id}
            className="absolute z-30"
            style={{
              left: `${nodeX}px`,
              top: `${nodeY}px`,
            }}
            initial={{ scale: 0, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{
              delay: index * 0.2,
              duration: 0.5,
              type: "spring" as const,
              stiffness: 100
            }}
            whileHover={{ scale: 1.15 }}
          >
            <div className="relative group">
              <motion.div 
                className="w-24 h-24 rounded-full bg-gray-800 border-2 border-purple-400/60 flex items-center justify-center"
                animate={{ 
                  borderColor: [
                    'rgba(168, 85, 247, 0.6)',
                    'rgba(0, 255, 136, 0.8)',
                    'rgba(168, 85, 247, 0.6)'
                  ]
                }}
                transition={{ 
                  duration: 3,
                  repeat: Infinity,
                  delay: index * 0.5
                }}
              >
                <IconComponent className="w-9 h-9 text-purple-300" />
              </motion.div>

              <div className="absolute -top-3 -right-3 w-8 h-8 rounded-full bg-gradient-to-br from-blue-400 via-primary to-cyan-300 dark:from-blue-500 dark:via-blue-400 dark:to-cyan-400 border-2 border-white dark:border-gray-900 text-white font-bold flex items-center justify-center shadow-lg">
                {step.id}
              </div>

              <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-6 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none z-50">
                <div className="bg-gray-900/95 backdrop-blur-sm text-white p-4 rounded-xl border border-purple-400/30 text-center whitespace-nowrap">
                  <div className="font-semibold text-base text-green-500">{step.title}</div>
                  <div className="text-sm text-gray-300 mt-2">{step.description}</div>
                </div>
              </div>
            </div>
          </motion.div>
        )
      })}
    </div>
  )
}
