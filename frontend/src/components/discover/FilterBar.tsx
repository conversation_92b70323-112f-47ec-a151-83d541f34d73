import React from 'react';
import { Search, Filter, SlidersHorizontal, Grid3X3, List } from 'lucide-react';
import { FilterOptions, ContentType, LayoutMode } from '@/types/content';
import { Button } from '@/components/ui/button';
import { useLanguage } from '@/contexts/LanguageContext';

interface FilterBarProps {
  filters: FilterOptions;
  onFiltersChange: (filters: FilterOptions) => void;
  searchQuery: string;
  onSearchChange: (query: string) => void;
}

export const FilterBar: React.FC<FilterBarProps> = ({
  filters,
  onFiltersChange,
  searchQuery,
  onSearchChange,
}) => {
  const { t } = useLanguage();
  
  const contentTypes: { value: ContentType | 'all'; label: string }[] = [
    { value: 'all', label: t('discover.filters.contentTypes.all') },
    { value: 'video', label: t('discover.filters.contentTypes.video') },
    { value: 'text', label: t('discover.filters.contentTypes.text') },
    { value: 'news', label: t('discover.filters.contentTypes.news') },
    { value: 'analysis', label: t('discover.filters.contentTypes.analysis') },
    { value: 'hybrid', label: t('discover.filters.contentTypes.hybrid') },
  ];

  const dateRanges = [
    { value: 'all', label: t('discover.filters.allTime') },
    { value: 'week', label: t('discover.filters.lastWeek') },
    { value: 'month', label: t('discover.filters.lastMonth') },
    { value: 'year', label: t('discover.filters.lastYear') },
  ];

  const sortOptions = [
    { value: 'popular', label: t('discover.filters.mostPopular') },
    { value: 'recent', label: t('discover.filters.latest') },
    { value: 'favorites', label: t('discover.filters.mostFavorited') },
  ];

  return (
    <div className="bg-card border border-border rounded-lg shadow-sm p-4 mb-6">
      <div className="flex flex-col lg:flex-row gap-4">
        {/* Search */}
        <div className="flex-1">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground" size={20} />
            <input
              type="text"
              placeholder={t('discover.searchPlaceholder')}
              value={searchQuery}
              onChange={(e) => onSearchChange(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-input bg-background rounded-md focus:ring-2 focus:ring-ring focus:border-transparent text-foreground placeholder:text-muted-foreground"
            />
          </div>
        </div>

        {/* Filters */}
        <div className="flex flex-wrap gap-3">
          {/* Content Type */}
          <div className="flex items-center space-x-2">
            <Filter size={16} className="text-muted-foreground" />
            <select
              value={filters.type}
              onChange={(e) =>
                onFiltersChange({
                  ...filters,
                  type: e.target.value as ContentType | 'all',
                })
              }
              className="border border-input bg-background rounded-md px-3 py-2 text-sm focus:ring-2 focus:ring-ring focus:border-transparent text-foreground"
            >
              {contentTypes.map((type) => (
                <option key={type.value} value={type.value}>
                  {type.label}
                </option>
              ))}
            </select>
          </div>

          {/* Date Range */}
          <select
            value={filters.dateRange}
            onChange={(e) =>
              onFiltersChange({
                ...filters,
                dateRange: e.target.value as FilterOptions['dateRange'],
              })
            }
            className="border border-input bg-background rounded-md px-3 py-2 text-sm focus:ring-2 focus:ring-ring focus:border-transparent text-foreground"
          >
            {dateRanges.map((range) => (
              <option key={range.value} value={range.value}>
                {range.label}
              </option>
            ))}
          </select>

          {/* Sort */}
          <select
            value={filters.sortBy}
            onChange={(e) =>
              onFiltersChange({
                ...filters,
                sortBy: e.target.value as FilterOptions['sortBy'],
              })
            }
            className="border border-input bg-background rounded-md px-3 py-2 text-sm focus:ring-2 focus:ring-ring focus:border-transparent text-foreground"
          >
            {sortOptions.map((option) => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>

          {/* <Button variant="ghost" size="sm">
            <SlidersHorizontal size={16} className="mr-2" />
            高级筛选
          </Button> */}

          {/* Layout Toggle */}
          <div className="flex items-center  bg-background rounded-md">
            <Button
              variant={filters.layout === 'grid' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => onFiltersChange({ ...filters, layout: 'grid' })}
              className="rounded-r-none border-r border-input"
            >
              <Grid3X3 size={16} />
            </Button>
            <Button
              variant={filters.layout === 'list' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => onFiltersChange({ ...filters, layout: 'list' })}
              className="rounded-l-none"
            >
              <List size={16} />
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};