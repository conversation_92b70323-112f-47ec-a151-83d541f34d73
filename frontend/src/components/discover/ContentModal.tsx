import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Heart, Eye, Share2, Co<PERSON>, Edit, ExternalLink, TrendingUp, BarChart3, Play, FileText } from 'lucide-react';
import { Modal } from '@/components/ui/modal';
import { Button } from '@/components/ui/button';
import { MarkdownRenderer } from '@/components/ui/markdownRenderer';
import { ContentCase } from '@/types/content';
import { getContentTypeConfig } from '@/data/mockData';
import { formatDistanceToNow } from 'date-fns';
import { zhCN, enUS } from 'date-fns/locale';
import { useLanguage } from '@/contexts/LanguageContext';

interface ContentModalProps {
  content: ContentCase | null;
  isOpen: boolean;
  onClose: () => void;
  onToggleFavorite: (id: string) => void;
}

// Mock real-time trending data
const mockTrendingData = [
  {
    id: 1,
    keyword: '冬日暖阳',
    trend: '+156%',
    volume: '2.3M',
    platform: '抖音',
    category: '生活方式',
    relatedBrands: ['美妆', '服装', '家居'],
    opportunity: '建议美妆品牌结合暖色系产品推广'
  },
  {
    id: 2,
    keyword: '年货节',
    trend: '+89%',
    volume: '1.8M',
    platform: '小红书',
    category: '购物消费',
    relatedBrands: ['食品', '礼品', '家电'],
    opportunity: '食品品牌可借势进行促销活动'
  },
  {
    id: 3,
    keyword: '健康生活',
    trend: '+67%',
    volume: '1.2M',
    platform: '微博',
    category: '健康养生',
    relatedBrands: ['运动', '保健', '有机食品'],
    opportunity: '运动品牌适合切入健康话题'
  },
  {
    id: 4,
    keyword: '新年计划',
    trend: '+45%',
    volume: '980K',
    platform: 'B站',
    category: '个人成长',
    relatedBrands: ['教育', '健身', '理财'],
    opportunity: '教育类产品可结合目标设定'
  },
  {
    id: 5,
    keyword: '居家办公',
    trend: '+34%',
    volume: '756K',
    platform: '知乎',
    category: '工作效率',
    relatedBrands: ['办公用品', '家具', '数码'],
    opportunity: '办公设备品牌可推出居家解决方案'
  }
];

// Mock rich markdown content for text cases
const mockRichContent = `# 宝妈必看！这款奶瓶让我家宝宝从此爱上喝奶💕

## 产品介绍

作为一个二胎妈妈，我试过无数奶瓶，终于找到了这个宝藏！**防胀气设计**真的太棒了，宝宝喝奶再也不哭闹了。

### 核心卖点

> 这款奶瓶采用了独特的防胀气阀门设计，有效减少宝宝吞咽空气，告别肠绞痛困扰！

#### 主要特点：

1. **防胀气阀门** - 专利技术，减少90%空气吞咽
2. **仿母乳奶嘴** - 硅胶材质，接近真实触感
3. **宽口径设计** - 方便清洗和冲泡奶粉
4. **刻度清晰** - 精准掌握喂奶量

### 使用体验

我家大宝用了3个月，效果真的很明显：

- ✅ 喝奶时间从30分钟缩短到15分钟
- ✅ 夜奶次数明显减少
- ✅ 再也不会因为胀气哭闹
- ✅ 奶嘴接受度100%

## 对比测试

| 品牌 | 防胀气效果 | 奶嘴柔软度 | 清洗便利性 | 价格 |
|------|------------|------------|------------|------|
| 这款奶瓶 | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | 89元 |
| 品牌A | ⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐ | 128元 |
| 品牌B | ⭐⭐ | ⭐⭐⭐ | ⭐⭐⭐⭐ | 156元 |

### 购买建议

\`\`\`
推荐购买套装：
- 奶瓶 x2 (120ml + 240ml)
- 替换奶嘴 x4
- 奶瓶刷 x1
- 总价：168元（原价208元）
\`\`\`

## 真实反馈

**@新手妈妈小李**：用了一周，宝宝真的不胀气了！强烈推荐！

**@二胎妈妈王女士**：比之前用的几个品牌都好，性价比很高。

**@奶爸张先生**：老婆买的，确实好用，宝宝喝奶很顺畅。

---

*温馨提示：每个宝宝情况不同，建议先购买单个试用。如有任何问题，7天无理由退换货。*

[立即购买](https://example.com) | [查看更多评价](https://example.com)`;

export const ContentModal: React.FC<ContentModalProps> = ({
  content,
  isOpen,
  onClose,
  onToggleFavorite
}) => {
  const { t, locale } = useLanguage();
  const [selectedTrendItem, setSelectedTrendItem] = useState<number | null>(null);

  if (!content) return null;

  const typeConfig = getContentTypeConfig(content.type, t);

  const formatNumber = (num: number) => {
    if (num >= 1000000) return `${(num / 1000000).toFixed(1)}M`;
    if (num >= 1000) return `${(num / 1000).toFixed(1)}K`;
    return num.toString();
  };

  const handleTrendItemClick = (itemId: number) => {
    setSelectedTrendItem(selectedTrendItem === itemId ? null : itemId);
  };

  const renderVideoCase = () => (
    <div className="space-y-6">
      {/* Video Player */}
      <div className="relative bg-black rounded-lg overflow-hidden">
        <img
          src={content.coverImage}
          alt={content.title}
          className="w-full h-80 object-cover"
        />
        <div className="absolute inset-0 flex items-center justify-center">
          <motion.button
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
            className="w-16 h-16 bg-white bg-opacity-90 rounded-full flex items-center justify-center shadow-lg"
          >
            <Play size={24} className="text-gray-800 ml-1" />
          </motion.button>
        </div>
        {content.duration && (
          <div className="absolute bottom-4 right-4 px-2 py-1 bg-black bg-opacity-70 text-white text-sm rounded">
            {Math.floor(content.duration / 60)}:{String(content.duration % 60).padStart(2, '0')}
          </div>
        )}
      </div>

      {/* Video Details */}
      <div className="bg-gray-50 rounded-lg p-4">
        <h4 className="font-semibold mb-3 text-gray-900">{t('discover.modal.creationDetails')}</h4>
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div>
            <span className="text-gray-600">{t('discover.modal.targetAudience')}：</span>
            <span className="text-gray-900">{content.content?.targetAudience || '25-40岁有车一族'}</span>
          </div>
          <div>
            <span className="text-gray-600">{t('discover.modal.marketingGoal')}：</span>
            <span className="text-gray-900">{content.content?.campaignGoal || '品牌情感连接'}</span>
          </div>
        </div>
      </div>

      {/* Performance Metrics */}
      {content.content?.performance && (
        <div className="bg-white border rounded-lg p-4">
          <h4 className="font-semibold mb-3 text-gray-900">{t('discover.modal.performanceData')}</h4>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">{formatNumber(content.content.performance.views)}</div>
              <div className="text-sm text-gray-600">{t('discover.modal.views')}</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">{formatNumber(content.content.performance.likes)}</div>
              <div className="text-sm text-gray-600">{t('discover.modal.likes')}</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">{formatNumber(content.content.performance.shares)}</div>
              <div className="text-sm text-gray-600">{t('discover.modal.shares')}</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-orange-600">{content.content.performance.conversionRate}</div>
              <div className="text-sm text-gray-600">{t('discover.modal.conversionRate')}</div>
            </div>
          </div>
        </div>
      )}

      <div className="prose max-w-none">
        <p className="text-gray-700 leading-relaxed">{content.description}</p>
      </div>
    </div>
  );

  const renderTextCase = () => (
    <div className="space-y-6">
      {/* Rich Markdown Content */}
      <div className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg p-6">
        <div className="flex items-center mb-4">
          <FileText className="text-blue-600 mr-2" size={20} />
          <h4 className="font-semibold text-gray-900">营销文案内容</h4>
        </div>
        
        {/* Enhanced Markdown Renderer */}
        <MarkdownRenderer
          content={mockRichContent}
          showToc={true}
          showProgress={true}
          highlightKeywords={['防胀气', '宝宝', '奶瓶', '推荐']}
          className="bg-white rounded-lg border"
        />
      </div>

      {/* Platform & Keywords */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="bg-white border rounded-lg p-4">
          <h5 className="font-medium text-gray-900 mb-2">投放平台</h5>
          <div className="inline-block px-3 py-1 bg-pink-100 text-pink-800 rounded-full text-sm">
            {content.content?.platform || '小红书'}
          </div>
        </div>
        <div className="bg-white border rounded-lg p-4">
          <h5 className="font-medium text-gray-900 mb-2">目标关键词</h5>
          <div className="flex flex-wrap gap-2">
            {(content.content?.targetKeywords || ['婴儿奶瓶', '防胀气', '宝宝用品']).map((keyword: string, index: number) => (
              <span key={index} className="px-2 py-1 bg-gray-100 text-gray-700 rounded text-sm">
                {keyword}
              </span>
            ))}
          </div>
        </div>
      </div>

      {/* Performance Data */}
      {content.content?.performance && (
        <div className="bg-white border rounded-lg p-4">
          <h4 className="font-semibold mb-3 text-gray-900">投放效果</h4>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">{formatNumber(content.content.performance.reads)}</div>
              <div className="text-sm text-gray-600">阅读量</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-red-600">{formatNumber(content.content.performance.likes)}</div>
              <div className="text-sm text-gray-600">点赞数</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-yellow-600">{formatNumber(content.content.performance.collections)}</div>
              <div className="text-sm text-gray-600">收藏数</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">{content.content.performance.conversionRate}</div>
              <div className="text-sm text-gray-600">转化率</div>
            </div>
          </div>
        </div>
      )}
    </div>
  );

  const renderNewsCase = () => (
    <div className="space-y-6">
      {/* Trending Header */}
      <div className="flex items-center space-x-3 pb-4 border-b border-gray-200">
        <div className="w-8 h-8 bg-green-600 rounded-lg flex items-center justify-center">
          <TrendingUp size={16} className="text-white" />
        </div>
        <div>
          <h4 className="text-base font-bold text-gray-900 leading-tight">实时热点监测</h4>
          <p className="text-sm text-gray-600 leading-tight">
            数据更新时间：{formatDistanceToNow(new Date(content.publishedAt), { 
              addSuffix: true,
              locale: zhCN 
            })}
          </p>
        </div>
      </div>

      {/* Trending Topics */}
      <div className="bg-gray-50 rounded-lg p-4">
        <h4 className="font-semibold mb-3 text-gray-900 flex items-center">
          <BarChart3 size={16} className="mr-2" />
          热点话题排行
        </h4>
        <div className="space-y-2">
          {mockTrendingData.map((item, index) => {
            const isSelected = selectedTrendItem === item.id;
            
            return (
              <div key={item.id} className="space-y-2">
                <motion.div
                  whileHover={{ backgroundColor: '#EFEFEF' }}
                  onClick={() => handleTrendItemClick(item.id)}
                  className="p-3 rounded-md cursor-pointer transition-colors bg-white hover:bg-gray-100"
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <span className="text-lg font-bold text-gray-700 w-6">
                        {index + 1}
                      </span>
                      <div>
                        <h5 className="font-medium text-gray-900">{item.keyword}</h5>
                        <div className="flex items-center space-x-4 text-sm text-gray-600 mt-1">
                          <span>{item.platform}</span>
                          <span>{item.category}</span>
                        </div>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-lg font-bold text-green-600">{item.trend}</div>
                      <div className="text-sm text-gray-600">{item.volume}</div>
                    </div>
                  </div>
                </motion.div>

                {/* Expanded Details */}
                {isSelected && (
                  <motion.div
                    initial={{ opacity: 0, height: 0 }}
                    animate={{ opacity: 1, height: 'auto' }}
                    exit={{ opacity: 0, height: 0 }}
                    className="ml-8 p-4 bg-blue-50 rounded-md border-l-4 border-blue-500"
                  >
                    <div className="space-y-3">
                      <div>
                        <span className="text-sm font-medium text-gray-700">相关品类：</span>
                        <div className="flex flex-wrap gap-2 mt-1">
                          {item.relatedBrands.map((brand, idx) => (
                            <span key={idx} className="px-2 py-1 bg-white text-gray-700 rounded text-sm">
                              {brand}
                            </span>
                          ))}
                        </div>
                      </div>
                      <div>
                        <span className="text-sm font-medium text-gray-700">营销建议：</span>
                        <p className="text-sm text-gray-600 mt-1">{item.opportunity}</p>
                      </div>
                    </div>
                  </motion.div>
                )}
              </div>
            );
          })}
        </div>
      </div>

      {/* Analysis Summary */}
      <div className="bg-white border rounded-lg p-4">
        <h4 className="font-semibold mb-3 text-gray-900">洞察总结</h4>
        <div className="space-y-2 text-sm text-gray-700">
          <p>• 生活方式类话题热度持续上升，"冬日暖阳"相关内容增长156%</p>
          <p>• 购物消费类话题受年货节影响，预计热度将持续到春节前</p>
          <p>• 健康养生话题在新年期间表现活跃，适合相关品牌布局</p>
          <p>• 建议品牌结合热点话题制定内容策略，提升传播效果</p>
        </div>
      </div>

      <div className="prose max-w-none">
        <p className="text-gray-700 leading-relaxed">{content.description}</p>
      </div>
    </div>
  );

  const renderAnalysisCase = () => (
    <div className="space-y-6">
      {/* Key Metrics */}
      <div className="bg-gray-50 rounded-lg p-6">
        <h4 className="font-semibold mb-4 flex items-center">
          <BarChart3 size={16} className="mr-2" />
          核心数据指标
        </h4>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="bg-white p-4 rounded-lg text-center">
            <div className="text-2xl font-bold text-blue-600">{content.content?.keyMetrics?.totalReach || '2.3M'}</div>
            <div className="text-sm text-gray-600">总触达</div>
          </div>
          <div className="bg-white p-4 rounded-lg text-center">
            <div className="text-2xl font-bold text-green-600">{content.content?.keyMetrics?.engagement || '4.8%'}</div>
            <div className="text-sm text-gray-600">互动率</div>
          </div>
          <div className="bg-white p-4 rounded-lg text-center">
            <div className="text-2xl font-bold text-purple-600">{content.content?.keyMetrics?.conversion || '2.1%'}</div>
            <div className="text-sm text-gray-600">转化率</div>
          </div>
          <div className="bg-white p-4 rounded-lg text-center">
            <div className="text-2xl font-bold text-orange-600">{content.content?.keyMetrics?.roi || '3.2:1'}</div>
            <div className="text-sm text-gray-600">投资回报</div>
          </div>
        </div>
      </div>

      {/* Platform Performance */}
      <div className="bg-white border rounded-lg p-4">
        <h4 className="font-semibold mb-3 text-gray-900">平台表现分析</h4>
        <div className="grid grid-cols-2 gap-4">
          <div className="bg-red-50 p-4 rounded-lg">
            <h5 className="font-medium text-red-800 mb-2">📱 小红书</h5>
            <p className="text-sm text-gray-700">种草内容互动率最高(6.2%)</p>
            <div className="mt-2 text-xs text-red-600">优势平台 ⭐</div>
          </div>
          <div className="bg-purple-50 p-4 rounded-lg">
            <h5 className="font-medium text-purple-800 mb-2">🎵 抖音</h5>
            <p className="text-sm text-gray-700">视频曝光量最大(1.8M)</p>
            <div className="mt-2 text-xs text-purple-600">流量担当 🚀</div>
          </div>
          <div className="bg-blue-50 p-4 rounded-lg">
            <h5 className="font-medium text-blue-800 mb-2">🐦 微博</h5>
            <p className="text-sm text-gray-700">话题讨论促进品牌认知</p>
            <div className="mt-2 text-xs text-blue-600">口碑传播 💬</div>
          </div>
          <div className="bg-orange-50 p-4 rounded-lg">
            <h5 className="font-medium text-orange-800 mb-2">🛒 淘宝</h5>
            <p className="text-sm text-gray-700">直播带货实现最终转化</p>
            <div className="mt-2 text-xs text-orange-600">转化利器 💰</div>
          </div>
        </div>
      </div>

      {/* Key Insights */}
      <div className="bg-white border rounded-lg p-4">
        <h4 className="font-semibold mb-3 text-gray-900">关键洞察</h4>
        <div className="space-y-2">
          {(content.content?.insights || [
            '小红书种草内容获得最高互动率(6.2%)',
            '抖音视频带来最大曝光量(1.8M)',
            '微博话题讨论促进品牌认知',
            '淘宝直播实现最终转化'
          ]).map((insight: string, index: number) => (
            <div key={index} className="flex items-start space-x-2">
              <span className="text-blue-500 mt-1">•</span>
              <span className="text-sm text-gray-700">{insight}</span>
            </div>
          ))}
        </div>
      </div>

      <div className="prose max-w-none">
        <p className="text-gray-700 leading-relaxed">{content.description}</p>
      </div>
    </div>
  );

  const renderHybridCase = () => (
    <div className="space-y-6">
      {/* Campaign Overview */}
      <div className="bg-muted/50 rounded-lg p-6">
        <h4 className="font-semibold mb-3 text-card-foreground">营销活动概览</h4>
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div>
            <span className="text-muted-foreground">活动名称：</span>
            <span className="text-card-foreground font-medium">{content.content?.campaignName || '春节团圆季'}</span>
          </div>
          <div>
            <span className="text-muted-foreground">预算投入：</span>
            <span className="text-card-foreground font-medium">{content.content?.budget || '50万'}</span>
          </div>
          <div>
            <span className="text-muted-foreground">执行周期：</span>
            <span className="text-card-foreground font-medium">{content.content?.timeline || '2024-01-15 至 2024-02-15'}</span>
          </div>
          <div>
            <span className="text-muted-foreground">预期ROI：</span>
            <span className="text-card-foreground font-medium">{content.content?.expectedROI || '4.5:1'}</span>
          </div>
        </div>
      </div>

      {/* Platform Strategy */}
      <div className="grid grid-cols-2 gap-4">
        <div className="bg-muted/30 p-4 rounded-lg border border-border">
          <h5 className="font-semibold text-card-foreground mb-2">🐦 微博</h5>
          <p className="text-sm text-muted-foreground">{content.content?.platforms?.weibo || '话题讨论 + KOL合作'}</p>
          <div className="mt-2 text-xs text-primary">预期曝光：500万</div>
        </div>
        <div className="bg-muted/30 p-4 rounded-lg border border-border">
          <h5 className="font-semibold text-card-foreground mb-2">📱 小红书</h5>
          <p className="text-sm text-muted-foreground">{content.content?.platforms?.xiaohongshu || '种草笔记 + 用户UGC'}</p>
          <div className="mt-2 text-xs text-secondary">预期互动：8万</div>
        </div>
        <div className="bg-muted/30 p-4 rounded-lg border border-border">
          <h5 className="font-semibold text-card-foreground mb-2">🎵 抖音</h5>
          <p className="text-sm text-muted-foreground">{content.content?.platforms?.douyin || '短视频挑战 + 直播带货'}</p>
          <div className="mt-2 text-xs text-accent">预期播放：1000万</div>
        </div>
        <div className="bg-muted/30 p-4 rounded-lg border border-border">
          <h5 className="font-semibold text-card-foreground mb-2">📺 B站</h5>
          <p className="text-sm text-muted-foreground">{content.content?.platforms?.bilibili || '品牌故事 + 互动活动'}</p>
          <div className="mt-2 text-xs text-primary">预期观看：200万</div>
        </div>
      </div>

      {/* Timeline */}
      <div className="bg-card border border-border rounded-lg p-4">
        <h4 className="font-semibold mb-3 text-card-foreground">执行时间线</h4>
        <div className="space-y-3">
          <div className="flex items-center space-x-3">
            <div className="w-3 h-3 bg-primary rounded-full"></div>
            <div className="text-sm">
              <span className="font-medium text-card-foreground">第1周：</span>
              <span className="text-muted-foreground">预热阶段 - 微博话题发起，小红书种草内容投放</span>
            </div>
          </div>
          <div className="flex items-center space-x-3">
            <div className="w-3 h-3 bg-secondary rounded-full"></div>
            <div className="text-sm">
              <span className="font-medium text-card-foreground">第2-3周：</span>
              <span className="text-muted-foreground">爆发阶段 - 抖音挑战赛启动，B站品牌故事上线</span>
            </div>
          </div>
          <div className="flex items-center space-x-3">
            <div className="w-3 h-3 bg-accent rounded-full"></div>
            <div className="text-sm">
              <span className="font-medium text-card-foreground">第4周：</span>
              <span className="text-muted-foreground">收割阶段 - 全平台直播带货，用户UGC征集</span>
            </div>
          </div>
        </div>
      </div>

      <div className="prose max-w-none">
        <p className="text-card-foreground leading-relaxed">{content.description}</p>
      </div>
    </div>
  );

  const renderContentByType = () => {
    switch (content.type) {
      case 'video':
        return renderVideoCase();
      case 'text':
        return renderTextCase();
      case 'news':
        return renderNewsCase();
      case 'analysis':
        return renderAnalysisCase();
      case 'hybrid':
        return renderHybridCase();
      default:
        return <p className="text-card-foreground">{content.description}</p>;
    }
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose} maxWidth="2xl">
      <div className="p-6">
        {/* Header */}
        <div className="mb-6">
          <div className="flex items-start justify-between mb-4">
            <div className="flex-1 mr-4">
              <div className={`inline-block px-3 py-1 rounded-full text-sm font-medium ${typeConfig.color} mb-3`}>
                {typeConfig.label}
              </div>
              <h1 className="text-2xl font-bold text-card-foreground mb-3">{content.title}</h1>
            </div>
          </div>
          
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <img
                src={content.author.avatar}
                alt={content.author.name}
                className="w-10 h-10 rounded-full"
              />
              <div>
                <p className="font-medium text-card-foreground">{content.author.name}</p>
                <p className="text-sm text-muted-foreground">
                  {formatDistanceToNow(new Date(content.publishedAt), { 
                    addSuffix: true,
                    locale: zhCN 
                  })}
                </p>
              </div>
            </div>
            
            <div className="flex items-center space-x-6">
              <motion.button
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
                onClick={() => onToggleFavorite(content.id)}
                className={`flex items-center space-x-2 ${
                  content.isFavorited 
                    ? 'text-destructive' 
                    : 'text-muted-foreground hover:text-destructive'
                }`}
              >
                <Heart 
                  size={18} 
                  fill={content.isFavorited ? 'currentColor' : 'none'}
                />
                <span>{formatNumber(content.favorites)}</span>
              </motion.button>
              
              <div className="flex items-center space-x-2 text-muted-foreground">
                <Eye size={18} />
                <span>{formatNumber(content.views)}</span>
              </div>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="mb-8">
          {renderContentByType()}
        </div>

        {/* Tags */}
        {content.tags && content.tags.length > 0 && (
          <div className="mb-6">
            <div className="flex flex-wrap gap-2">
              {content.tags.map((tag, index) => (
                <span
                  key={index}
                  className="px-3 py-1 bg-muted text-muted-foreground text-sm rounded-full"
                >
                  #{tag}
                </span>
              ))}
            </div>
          </div>
        )}

        {/* Actions */}
        <div className="flex items-center justify-between pt-6 border-t border-border">
          <div className="flex space-x-3">
            <Button variant="default">
              <Copy size={16} className="mr-2" />
              复制模板
            </Button>
            <Button variant="outline">
              <Edit size={16} className="mr-2" />
              二次编辑
            </Button>
          </div>
          
          <Button variant="ghost">
            <Share2 size={16} className="mr-2" />
            分享
          </Button>
        </div>
      </div>
    </Modal>
  );
};