import React from 'react';
import { motion } from 'framer-motion';
import { Heart, Eye } from 'lucide-react';
import { ContentCase } from '../../types/content';
import { getContentTypeConfig } from '../../data/mockData';
import { formatDistanceToNow } from 'date-fns';
import { zhCN, enUS } from 'date-fns/locale';
import { useLanguage } from '@/contexts/LanguageContext';

interface ContentListItemProps {
  content: ContentCase;
  onClick: (content: ContentCase) => void;
  onToggleFavorite: (id: string) => void;
}

export const ContentListItem: React.FC<ContentListItemProps> = ({
  content,
  onClick,
  onToggleFavorite
}) => {
  const { t, locale } = useLanguage();
  const typeConfig = getContentTypeConfig(content.type, t);
  
  const handleFavoriteClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    onToggleFavorite(content.id);
  };

  const formatNumber = (num: number) => {
    if (num >= 1000000) return `${(num / 1000000).toFixed(1)}M`;
    if (num >= 1000) return `${(num / 1000).toFixed(1)}K`;
    return num.toString();
  };

  return (
    <motion.div
      whileHover={{ scale: 1.01, y: -1 }}
      transition={{ duration: 0.2 }}
      className="bg-card border border-border rounded-lg shadow-sm hover:shadow-md cursor-pointer overflow-hidden group"
      onClick={() => onClick(content)}
    >
      <div className="flex">
        {/* Image */}
        <div className="relative w-48 h-32 flex-shrink-0">
          <img
            src={content.coverImage}
            alt={content.title}
            className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
          />
          <div className={`absolute top-2 left-2 px-2 py-1 rounded-md text-xs font-medium bg-white/90 backdrop-blur-sm ${typeConfig.color}`}>
            {typeConfig.label}
          </div>
          {content.duration && (
            <div className="absolute bottom-2 right-2 px-2 py-1 bg-black/70 text-white text-xs rounded">
              {Math.floor(content.duration / 60)}:{String(content.duration % 60).padStart(2, '0')}
            </div>
          )}
        </div>

        {/* Content */}
        <div className="flex-1 p-4 flex flex-col justify-between">
          <div>
            <h3 className="font-semibold text-lg text-card-foreground mb-2 leading-6">
              {content.title}
            </h3>
            
            {/* {content.description && (
              <p className="text-sm text-muted-foreground mb-3 line-clamp-2" style={{ 
                display: '-webkit-box', 
                WebkitLineClamp: 2, 
                WebkitBoxOrient: 'vertical' 
              }}>
                {content.description}
              </p>
            )} */}
            
            <div className="flex items-center mb-3">
              <img
                src={content.author.avatar}
                alt={content.author.name}
                className="w-6 h-6 rounded-full mr-2"
              />
              <span className="text-sm text-muted-foreground mr-2">{content.author.name}</span>
              <span className="text-xs text-muted-foreground">
                {formatDistanceToNow(new Date(content.publishedAt), { 
                  addSuffix: true,
                  locale: locale === 'zh' ? zhCN : enUS
                })}
              </span>
            </div>
          </div>

          {/* Stats and Actions */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <motion.button
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
                onClick={handleFavoriteClick}
                className={`flex items-center space-x-1 text-sm transition-colors ${
                  content.isFavorited 
                    ? 'text-destructive' 
                    : 'text-muted-foreground hover:text-destructive'
                }`}
              >
                <Heart 
                  size={14} 
                  fill={content.isFavorited ? 'currentColor' : 'none'}
                />
                <span>{formatNumber(content.favorites)}</span>
              </motion.button>
              
              <div className="flex items-center space-x-1 text-sm text-muted-foreground">
                <Eye size={14} />
                <span>{formatNumber(content.views)}</span>
              </div>
            </div>

            {/* Tags */}
            {content.tags && content.tags.length > 0 && (
              <div className="flex flex-wrap gap-1">
                {content.tags.slice(0, 3).map((tag, index) => (
                  <span
                    key={index}
                    className="px-2 py-1 bg-muted text-muted-foreground text-xs rounded-full"
                  >
                    #{tag}
                  </span>
                ))}
                {content.tags.length > 3 && (
                  <span className="px-2 py-1 bg-muted text-muted-foreground text-xs rounded-full">
                    +{content.tags.length - 3}
                  </span>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    </motion.div>
  );
}; 