import { useCredits } from '@/contexts/CreditsContext';
import { Coins } from 'lucide-react';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';

export function CreditsDisplay() {
  const { credits, isLoading } = useCredits();

  if (isLoading) {
    return null;
  }

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <div className="flex items-center gap-2">
            <div className="flex items-center gap-1 px-2 py-1 rounded-full bg-primary/10 text-primary">
              <Coins className="w-4 h-4" />
              <span className="text-sm font-medium">{credits}</span>
            </div>
          </div>
        </TooltipTrigger>
        <TooltipContent>
          <div className="space-y-1">
            <p>剩余积分: {credits}</p>
          </div>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
} 