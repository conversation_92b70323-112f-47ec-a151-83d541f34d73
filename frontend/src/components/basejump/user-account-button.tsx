import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import Link from 'next/link';
import { UserIcon } from 'lucide-react';
import { createClient } from '@/lib/supabase/server';
import { redirect } from 'next/navigation';

export default async function UserAccountButton() {
  const supabaseClient = await createClient();
  
  // Check if Supabase is available
  if (!supabaseClient) {
    return (
      <Button variant="ghost" disabled>
        <UserIcon />
      </Button>
    );
  }

  let personalAccount = null;
  let error = null;

  try {
    const { data, error: rpcError } = await supabaseClient.rpc('get_personal_account');
    if (rpcError) {
      throw rpcError;
    }
    personalAccount = data;
  } catch (err) {
    error = err;
    console.error('Failed to load personal account:', err);
  }

  // If no account data, show disabled button
  if (error || !personalAccount) {
    return (
      <Button variant="ghost" disabled>
        <UserIcon />
      </Button>
    );
  }

  const signOut = async () => {
    'use server';

    const supabase = await createClient();
    if (supabase) {
      await supabase.auth.signOut();
    }
    return redirect('/');
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost">
          <UserIcon />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="w-56" align="end" forceMount>
        <DropdownMenuLabel className="font-normal">
          <div className="flex flex-col space-y-1">
            <p className="text-sm font-medium leading-none">
              {personalAccount.name}
            </p>
            <p className="text-xs leading-none text-muted-foreground">
              {personalAccount.email}
            </p>
          </div>
        </DropdownMenuLabel>
        <DropdownMenuSeparator />
        <DropdownMenuGroup>
          <DropdownMenuItem asChild>
            <Link href="/dashboard">我的账户</Link>
          </DropdownMenuItem>
          <DropdownMenuItem asChild>
            <Link href="/settings">设置</Link>
          </DropdownMenuItem>
          <DropdownMenuItem asChild>
            <Link href="/settings/teams">团队</Link>
          </DropdownMenuItem>
        </DropdownMenuGroup>
        <DropdownMenuSeparator />
        <DropdownMenuItem>
          <form action={signOut}>
            <button>退出登录</button>
          </form>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
