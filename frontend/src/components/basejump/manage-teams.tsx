import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '../ui/card';
import { createClient } from '@/lib/supabase/server';
import { Table, TableRow, TableBody, TableCell } from '../ui/table';
import { But<PERSON> } from '../ui/button';
import Link from 'next/link';
import { Badge } from '../ui/badge';
import { Alert, AlertTitle, AlertDescription } from '../ui/alert';

export default async function ManageTeams() {
  const supabaseClient = await createClient();

  // Check if Supabase is available
  if (!supabaseClient) {
    return (
      <Card className="border-subtle dark:border-white/10 bg-white dark:bg-background-secondary shadow-none rounded-xl">
        <CardHeader className="pb-3">
          <CardTitle className="text-base text-card-title">您的团队</CardTitle>
          <CardDescription className="text-foreground/70">
            您所属或拥有的团队
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Alert className="border-orange-300 dark:border-orange-800">
            <AlertTitle>服务不可用</AlertTitle>
            <AlertDescription>
              团队管理功能当前不可用。请联系管理员配置 Supabase 服务。
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    );
  }

  let teams: any[] = [];
  let error = null;

  try {
    const { data, error: rpcError } = await supabaseClient.rpc('get_accounts');
    if (rpcError) {
      throw rpcError;
    }
    teams = data?.filter((team: any) => team.personal_account === false) || [];
  } catch (err) {
    error = err;
    console.error('Failed to load teams:', err);
  }

  if (error) {
    return (
      <Card className="border-subtle dark:border-white/10 bg-white dark:bg-background-secondary shadow-none rounded-xl">
        <CardHeader className="pb-3">
          <CardTitle className="text-base text-card-title">您的团队</CardTitle>
          <CardDescription className="text-foreground/70">
            您所属或拥有的团队
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Alert variant="destructive" className="border-red-300 dark:border-red-800">
            <AlertTitle>加载失败</AlertTitle>
            <AlertDescription>
              无法加载团队信息。请稍后重试或联系客服。
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="border-subtle dark:border-white/10 bg-white dark:bg-background-secondary shadow-none rounded-xl">
      <CardHeader className="pb-3">
        <CardTitle className="text-base text-card-title">您的团队</CardTitle>
        <CardDescription className="text-foreground/70">
          您所属或拥有的团队
        </CardDescription>
      </CardHeader>
      <CardContent>
        {teams.length === 0 ? (
          <div className="text-center py-6">
            <p className="text-muted-foreground text-sm">您还没有加入任何团队</p>
          </div>
        ) : (
          <Table>
            <TableBody>
              {teams.map((team) => (
                <TableRow
                  key={team.account_id}
                  className="hover:bg-hover-bg border-subtle dark:border-white/10"
                >
                  <TableCell>
                    <div className="flex items-center gap-x-2">
                      <span className="font-medium text-card-title">
                        {team.name}
                      </span>
                      <Badge
                        variant={
                          team.account_role === 'owner' ? 'default' : 'outline'
                        }
                        className={
                          team.account_role === 'owner'
                            ? 'bg-primary hover:bg-primary/90'
                            : 'text-foreground/70 border-subtle dark:border-white/10'
                        }
                      >
                        {team.is_primary_owner
                          ? '主要所有者'
                          : team.account_role === 'owner' 
                            ? '所有者'
                            : team.account_role === 'member'
                            ? '成员'
                            : team.account_role}
                      </Badge>
                    </div>
                  </TableCell>
                  <TableCell className="text-right">
                    <Button
                      variant="outline"
                      asChild
                      className="rounded-lg h-9 border-subtle dark:border-white/10 hover:bg-hover-bg dark:hover:bg-hover-bg-dark"
                    >
                      <Link href={`/${team.slug}`}>查看</Link>
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        )}
      </CardContent>
    </Card>
  );
}
