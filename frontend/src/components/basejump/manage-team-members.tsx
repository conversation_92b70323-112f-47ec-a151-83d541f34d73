import { createClient } from '@/lib/supabase/server';
import { Table, TableRow, TableBody, TableCell } from '../ui/table';
import { Badge } from '../ui/badge';
import TeamMemberOptions from './team-member-options';
import { Alert, AlertTitle, AlertDescription } from '../ui/alert';

type Props = {
  accountId: string;
};

export default async function ManageTeamMembers({ accountId }: Props) {
  const supabaseClient = await createClient();

  // Check if Supabase is available
  if (!supabaseClient) {
    return (
      <Alert className="border-orange-300 dark:border-orange-800">
        <AlertTitle>服务不可用</AlertTitle>
        <AlertDescription>
          团队成员管理功能当前不可用。请联系管理员配置 Supabase 服务。
        </AlertDescription>
      </Alert>
    );
  }

  let members: any[] = [];
  let currentUser = null;
  let error = null;

  try {
    const [membersResult, userResult] = await Promise.all([
      supabaseClient.rpc('get_account_members', { account_id: accountId }),
      supabaseClient.auth.getUser()
    ]);

    if (membersResult.error) {
      throw membersResult.error;
    }
    if (userResult.error) {
      throw userResult.error;
    }

    members = membersResult.data || [];
    currentUser = userResult.data?.user;
  } catch (err) {
    error = err;
    console.error('Failed to load team members:', err);
  }

  if (error) {
    return (
      <Alert variant="destructive" className="border-red-300 dark:border-red-800">
        <AlertTitle>加载失败</AlertTitle>
        <AlertDescription>
          无法加载团队成员信息。请稍后重试或联系客服。
        </AlertDescription>
      </Alert>
    );
  }

  const isPrimaryOwner = members?.find(
    (member: any) => member.user_id === currentUser?.id,
  )?.is_primary_owner;

  if (members.length === 0) {
    return (
      <div className="text-center py-6">
        <p className="text-muted-foreground text-sm">暂无团队成员</p>
      </div>
    );
  }

  return (
    <div>
      <Table>
        <TableBody>
          {members.map((member: any) => (
            <TableRow
              key={member.user_id}
              className="hover:bg-hover-bg border-subtle dark:border-white/10"
            >
              <TableCell>
                <div className="flex items-center gap-x-2">
                  <span className="font-medium text-card-title">
                    {member.name}
                  </span>
                  <Badge
                    variant={
                      member.account_role === 'owner' ? 'default' : 'outline'
                    }
                    className={
                      member.account_role === 'owner'
                        ? 'bg-primary hover:bg-primary/90'
                        : 'text-foreground/70 border-subtle dark:border-white/10'
                    }
                  >
                    {member.is_primary_owner
                      ? '主要所有者'
                      : member.account_role === 'owner'
                      ? '所有者'
                      : member.account_role === 'member'
                      ? '成员'
                      : member.account_role}
                  </Badge>
                </div>
              </TableCell>
              <TableCell>
                <span className="text-sm text-foreground/70">
                  {member.email}
                </span>
              </TableCell>
              <TableCell className="text-right">
                {!Boolean(member.is_primary_owner) && (
                  <TeamMemberOptions
                    teamMember={member}
                    accountId={accountId}
                    isPrimaryOwner={isPrimaryOwner}
                  />
                )}
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
}
