import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '../ui/card';
import { createClient } from '@/lib/supabase/server';
import { Table, TableRow, TableBody, TableCell } from '../ui/table';
import { Badge } from '../ui/badge';
import CreateTeamInvitationButton from './create-team-invitation-button';
import { formatDistanceToNow } from 'date-fns';
import DeleteTeamInvitationButton from './delete-team-invitation-button';
import { Alert, AlertTitle, AlertDescription } from '../ui/alert';

type Props = {
  accountId: string;
};

export default async function ManageTeamInvitations({ accountId }: Props) {
  const supabaseClient = await createClient();

  // Check if Supabase is available
  if (!supabaseClient) {
    return (
      <Card>
        <CardHeader>
          <div className="flex justify-between">
            <div>
              <CardTitle>待处理邀请</CardTitle>
              <CardDescription>
                您的团队待处理邀请列表
              </CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <Alert className="border-orange-300 dark:border-orange-800">
            <AlertTitle>服务不可用</AlertTitle>
            <AlertDescription>
              团队邀请管理功能当前不可用。请联系管理员配置 Supabase 服务。
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    );
  }

  let invitations: any[] = [];
  let error = null;

  try {
    const { data, error: rpcError } = await supabaseClient.rpc('get_account_invitations', {
      account_id: accountId,
    });
    
    if (rpcError) {
      throw rpcError;
    }
    
    invitations = data || [];
  } catch (err) {
    error = err;
    console.error('Failed to load team invitations:', err);
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <div className="flex justify-between">
            <div>
              <CardTitle>待处理邀请</CardTitle>
              <CardDescription>
                您的团队待处理邀请列表
              </CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <Alert variant="destructive" className="border-red-300 dark:border-red-800">
            <AlertTitle>加载失败</AlertTitle>
            <AlertDescription>
              无法加载团队邀请信息。请稍后重试或联系客服。
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex justify-between">
          <div>
            <CardTitle>待处理邀请</CardTitle>
            <CardDescription>
              您的团队待处理邀请列表
            </CardDescription>
          </div>
          <CreateTeamInvitationButton accountId={accountId} />
        </div>
      </CardHeader>
      {Boolean(invitations?.length) ? (
        <CardContent>
          <Table>
            <TableBody>
              {invitations.map((invitation: any) => (
                <TableRow key={invitation.invitation_id}>
                  <TableCell>
                    <div className="flex gap-x-2">
                      {formatDistanceToNow(invitation.created_at, {
                        addSuffix: true,
                      })}
                      <Badge
                        variant={
                          invitation.invitation_type === '24_hour'
                            ? 'default'
                            : 'outline'
                        }
                      >
                        {invitation.invitation_type === '24_hour' ? '24小时' : invitation.invitation_type}
                      </Badge>
                      <Badge
                        variant={
                          invitation.account_role === 'owner'
                            ? 'default'
                            : 'outline'
                        }
                      >
                        {invitation.account_role === 'owner' ? '所有者' : invitation.account_role === 'member' ? '成员' : invitation.account_role}
                      </Badge>
                    </div>
                  </TableCell>
                  <TableCell className="text-right">
                    <DeleteTeamInvitationButton
                      invitationId={invitation.invitation_id}
                    />
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      ) : (
        <CardContent>
          <div className="text-center py-6">
            <p className="text-muted-foreground text-sm">暂无待处理邀请</p>
          </div>
        </CardContent>
      )}
    </Card>
  );
}
