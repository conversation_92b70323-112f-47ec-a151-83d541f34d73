import { useState } from 'react';
import { useCredits } from '@/contexts/CreditsContext';
import { CreditsPurchaseModal } from '@/components/billing/CreditsPurchaseModal';
import { FC } from 'react';

interface UseModelCreditsOptions {
  requiredCredits: number;
  onSuccess?: () => void;
  onError?: (error: Error) => void;
}

export function useModelCredits({ requiredCredits, onSuccess, onError }: UseModelCreditsOptions) {
  const { credits, deductCredits } = useCredits();
  const [isProcessing, setIsProcessing] = useState(false);
  const [showPurchaseModal, setShowPurchaseModal] = useState(false);

  const checkAndDeductCredits = async (): Promise<boolean> => {
    setIsProcessing(true);
    try {
      if (credits < requiredCredits) {
        setShowPurchaseModal(true);
        return false;
      }

      const success = await deductCredits(requiredCredits);
      if (success) {
        onSuccess?.();
      }
      return success;
    } catch (error) {
      onError?.(error instanceof Error ? error : new Error('Failed to process credits'));
      return false;
    } finally {
      setIsProcessing(false);
    }
  };

  const CreditsModal: FC = () => {
    return (
      <CreditsPurchaseModal
        isOpen={showPurchaseModal}
        onClose={() => setShowPurchaseModal(false)}
        requiredCredits={requiredCredits}
      />
    );
  };

  return {
    credits,
    isProcessing,
    checkAndDeductCredits,
    CreditsModal,
  };
} 