import { useState } from 'react';
import { useCredits } from '@/contexts/CreditsContext';

interface UseModelCreditsOptions {
  requiredCredits: number;
  onSuccess?: () => void;
  onError?: (error: Error) => void;
}

export function useModelCredits({ requiredCredits, onSuccess, onError }: UseModelCreditsOptions) {
  const { credits, checkCredits, deductCredits } = useCredits();
  const [isProcessing, setIsProcessing] = useState(false);
  const [showPurchaseModal, setShowPurchaseModal] = useState(false);

  const checkAndDeductCredits = async (): Promise<boolean> => {
    setIsProcessing(true);
    try {
      if (!checkCredits(requiredCredits)) {
        setShowPurchaseModal(true);
        return false;
      }

      const success = await deductCredits(requiredCredits);
      if (success) {
        onSuccess?.();
      }
      return success;
    } catch (error) {
      onError?.(error instanceof Error ? error : new Error('Failed to process credits'));
      return false;
    } finally {
      setIsProcessing(false);
    }
  };

  return {
    credits,
    isProcessing,
    showPurchaseModal,
    setShowPurchaseModal,
    checkAndDeductCredits,
  };
} 