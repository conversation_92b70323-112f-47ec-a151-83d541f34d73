import { useState } from 'react';
import { useCredits } from '@/contexts/CreditsContext';

interface UseCreditCheckOptions {
  requiredCredits: number;
  onInsufficientCredits?: () => void;
}

export function useCreditCheck({ requiredCredits, onInsufficientCredits }: UseCreditCheckOptions) {
  const { credits, checkCredits, deductCredits } = useCredits();
  const [isChecking, setIsChecking] = useState(false);

  const checkAndDeductCredits = async (): Promise<boolean> => {
    setIsChecking(true);
    try {
      if (!checkCredits(requiredCredits)) {
        onInsufficientCredits?.();
        return false;
      }

      const success = await deductCredits(requiredCredits);
      return success;
    } finally {
      setIsChecking(false);
    }
  };

  return {
    credits,
    isChecking,
    checkAndDeductCredits,
  };
} 