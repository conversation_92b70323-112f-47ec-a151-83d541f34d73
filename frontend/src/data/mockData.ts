import { ContentCase, ContentType } from '../types/content';

// 注意：这个文件中的硬编码文本在实际应用中应该通过翻译系统处理
// 这里保持原样是为了演示目的，实际使用时应该从翻译文件中获取

const authors = [
  { id: '1', name: '小红书博主@美妆达人', avatar: 'https://images.pexels.com/photos/774909/pexels-photo-774909.jpeg?auto=compress&cs=tinysrgb&w=64&h=64&fit=crop' },
  { id: '2', name: '抖音创作者@生活记录', avatar: 'https://images.pexels.com/photos/1222271/pexels-photo-1222271.jpeg?auto=compress&cs=tinysrgb&w=64&h=64&fit=crop' },
  { id: '3', name: '微博营销@品牌推广', avatar: 'https://images.pexels.com/photos/1239291/pexels-photo-1239291.jpeg?auto=compress&cs=tinysrgb&w=64&h=64&fit=crop' },
  { id: '4', name: 'B站UP主@科技评测', avatar: 'https://images.pexels.com/photos/1043471/pexels-photo-1043471.jpeg?auto=compress&cs=tinysrgb&w=64&h=64&fit=crop' },
  { id: '5', name: '电商运营@母婴专家', avatar: 'https://images.pexels.com/photos/1181244/pexels-photo-1181244.jpeg?auto=compress&cs=tinysrgb&w=64&h=64&fit=crop' },
  { id: '6', name: '内容创作@美食博主', avatar: 'https://images.pexels.com/photos/1029757/pexels-photo-1029757.jpeg?auto=compress&cs=tinysrgb&w=64&h=64&fit=crop' },
];

const coverImages = [
  'https://images.pexels.com/photos/267389/pexels-photo-267389.jpeg?auto=compress&cs=tinysrgb&w=400&h=250&fit=crop',
  'https://images.pexels.com/photos/1591056/pexels-photo-1591056.jpeg?auto=compress&cs=tinysrgb&w=400&h=250&fit=crop',
  'https://images.pexels.com/photos/265087/pexels-photo-265087.jpeg?auto=compress&cs=tinysrgb&w=400&h=250&fit=crop',
  'https://images.pexels.com/photos/943096/pexels-photo-943096.jpeg?auto=compress&cs=tinysrgb&w=400&h=250&fit=crop',
  'https://images.pexels.com/photos/1029757/pexels-photo-1029757.jpeg?auto=compress&cs=tinysrgb&w=400&h=250&fit=crop',
  'https://images.pexels.com/photos/1181244/pexels-photo-1181244.jpeg?auto=compress&cs=tinysrgb&w=400&h=250&fit=crop',
  'https://images.pexels.com/photos/1640777/pexels-photo-1640777.jpeg?auto=compress&cs=tinysrgb&w=400&h=250&fit=crop',
  'https://images.pexels.com/photos/1640774/pexels-photo-1640774.jpeg?auto=compress&cs=tinysrgb&w=400&h=250&fit=crop',
];

export const mockContentCases: ContentCase[] = [
  {
    id: '1',
    title: '长安汽车荔枝故事短视频 - 情感营销爆款',
    type: 'video',
    coverImage: coverImages[0],
    author: authors[1],
    publishedAt: '2024-01-15T10:30:00Z',
    favorites: 2847,
    views: 156789,
    isFavorited: false,
    description: '使用LoomuAI智能创作体生成的长安汽车品牌故事视频，以荔枝的甜美寓意家的温暖，播放量破15万，转化率提升45%。',
    tags: ['汽车营销', '情感故事', '品牌传播', '短视频'],
    status: 'published',
    duration: 45,
    content: {
      videoScript: '夏日午后，一颗荔枝的香甜，唤起了回家的渴望...',
      targetAudience: '25-40岁有车一族',
      campaignGoal: '品牌情感连接',
      performance: {
        views: 156789,
        likes: 12456,
        shares: 3421,
        comments: 892,
        conversionRate: '4.5%'
      }
    }
  },
  {
    id: '2',
    title: '母婴奶瓶营销文案 - 小红书种草爆文',
    type: 'text',
    coverImage: coverImages[1],
    author: authors[4],
    publishedAt: '2024-01-14T15:45:00Z',
    favorites: 3421,
    views: 89234,
    isFavorited: true,
    description: '通过LoomuAI生成的母婴奶瓶种草文案，结合用户痛点和产品卖点，获得8.9万阅读量，带货转化率达12.3%。',
    tags: ['母婴用品', '小红书', '种草文案', '电商转化'],
    status: 'published',
    content: {
      copyText: '宝妈必看！这款奶瓶让我家宝宝从此爱上喝奶💕\n\n作为一个二胎妈妈，我试过无数奶瓶...',
      platform: '小红书',
      targetKeywords: ['婴儿奶瓶', '防胀气', '宝宝用品'],
      performance: {
        reads: 89234,
        likes: 5678,
        collections: 2341,
        comments: 456,
        conversionRate: '12.3%'
      }
    }
  },
  {
    id: '3',
    title: '抖音今日热点话题挖掘 - 实时热搜分析',
    type: 'news',
    coverImage: coverImages[2],
    author: authors[2],
    publishedAt: '2024-01-16T09:20:00Z',
    favorites: 1567,
    views: 34521,
    isFavorited: false,
    description: '使用LoomuAI灵感雷达实时抓取抖音热点话题，发现"冬日暖阳"相关内容热度上升156%，为品牌营销提供及时洞察。',
    tags: ['热点分析', '抖音', '趋势洞察', '营销机会'],
    status: 'published',
    content: {
      hotTopics: [
        { keyword: '冬日暖阳', trend: '+156%', volume: '2.3M' },
        { keyword: '年货节', trend: '+89%', volume: '1.8M' },
        { keyword: '健康生活', trend: '+67%', volume: '1.2M' }
      ],
      analysisTime: '2024-01-16 09:00',
      platform: '抖音',
      recommendations: [
        '建议美妆品牌结合"冬日暖阳"话题推出暖色系产品',
        '食品品牌可借势"年货节"进行促销活动',
        '运动品牌适合切入"健康生活"话题'
      ]
    }
  },
  {
    id: '4',
    title: '美妆品牌传播路径数据分析 - 全平台监测',
    type: 'analysis',
    coverImage: coverImages[3],
    author: authors[0],
    publishedAt: '2024-01-12T14:15:00Z',
    favorites: 892,
    views: 23456,
    isFavorited: true,
    description: '通过LoomuAI分析某美妆品牌在全平台的传播效果，发现小红书种草→抖音展示→淘宝转化的最优路径，ROI提升78%。',
    tags: ['数据分析', '美妆营销', '全平台', 'ROI优化'],
    status: 'completed',
    scheduledDate: '2024-01-20T10:00:00Z',
    content: {
      analysisType: '传播路径分析',
      timeRange: '2024-01-01 至 2024-01-12',
      platforms: ['小红书', '抖音', '微博', '淘宝'],
      keyMetrics: {
        totalReach: '2.3M',
        engagement: '4.8%',
        conversion: '2.1%',
        roi: '3.2:1'
      },
      insights: [
        '小红书种草内容获得最高互动率(6.2%)',
        '抖音视频带来最大曝光量(1.8M)',
        '微博话题讨论促进品牌认知',
        '淘宝直播实现最终转化'
      ]
    }
  },
  {
    id: '5',
    title: '春节营销全平台联动方案 - 多渠道整合',
    type: 'hybrid',
    coverImage: coverImages[4],
    author: authors[2],
    publishedAt: '2024-01-11T11:30:00Z',
    favorites: 2134,
    views: 67890,
    isFavorited: false,
    description: '使用LoomuAI制定的春节营销全平台联动方案，覆盖微博、小红书、抖音、B站四大平台，整体曝光量达6.8万，转化率提升89%。',
    tags: ['春节营销', '全平台', '联动方案', '节日营销'],
    status: 'published',
    content: {
      campaignName: '春节团圆季',
      platforms: {
        weibo: '话题讨论 + KOL合作',
        xiaohongshu: '种草笔记 + 用户UGC',
        douyin: '短视频挑战 + 直播带货',
        bilibili: '品牌故事 + 互动活动'
      },
      timeline: '2024-01-15 至 2024-02-15',
      budget: '50万',
      expectedROI: '4.5:1'
    }
  },
  {
    id: '6',
    title: '美食探店Vlog脚本 - B站爆款内容',
    type: 'video',
    coverImage: coverImages[5],
    author: authors[5],
    publishedAt: '2024-01-10T16:00:00Z',
    favorites: 1678,
    views: 45623,
    isFavorited: true,
    description: '通过LoomuAI生成的美食探店Vlog脚本，结合当地特色和个人风格，B站播放量4.5万，粉丝增长2000+。',
    tags: ['美食Vlog', 'B站', '探店', '内容创作'],
    status: 'published',
    duration: 480,
    content: {
      storeInfo: '成都宽窄巷子老字号火锅',
      videoStructure: [
        '开场：街景+背景音乐',
        '进店：环境介绍',
        '点餐：菜品推荐',
        '品尝：真实反应',
        '总结：评分推荐'
      ],
      targetAudience: '美食爱好者',
      expectedDuration: '8分钟'
    }
  },
  {
    id: '7',
    title: '科技产品评测文案 - 专业测评内容',
    type: 'text',
    coverImage: coverImages[6],
    author: authors[3],
    publishedAt: '2024-01-09T13:20:00Z',
    favorites: 1234,
    views: 28901,
    isFavorited: false,
    description: '使用LoomuAI生成的iPhone 15 Pro深度评测文案，从性能、拍照、续航等维度全面分析，获得2.9万阅读量。',
    tags: ['科技评测', '手机测评', '专业内容', '产品分析'],
    status: 'published',
    content: {
      productName: 'iPhone 15 Pro',
      testDimensions: ['性能跑分', '拍照对比', '续航测试', '发热控制'],
      conclusion: '综合评分8.5/10',
      targetAudience: '科技爱好者、潜在购买者'
    }
  },
  {
    id: '8',
    title: '双11购物节热点监测 - 电商趋势分析',
    type: 'news',
    coverImage: coverImages[7],
    author: authors[2],
    publishedAt: '2024-01-08T10:45:00Z',
    favorites: 987,
    views: 19876,
    isFavorited: true,
    description: '通过LoomuAI实时监测双11期间各平台热点变化，发现"直播带货"话题热度激增234%，为电商策略调整提供数据支持。',
    tags: ['双11', '电商分析', '热点监测', '购物节'],
    status: 'published',
    content: {
      monitoringPeriod: '2023-11-01 至 2023-11-12',
      keyFindings: [
        '直播带货话题热度+234%',
        '美妆个护类目增长最快',
        '下沉市场消费力显著提升',
        '社交电商成为新增长点'
      ],
      platforms: ['淘宝', '京东', '拼多多', '抖音电商'],
      recommendations: '建议品牌加大直播投入，重点关注下沉市场'
    }
  }
];

export const getContentTypeConfig = (type: ContentType, t?: (key: string) => string) => {
  const configs = {
    video: { 
      label: t ? t('discover.contentTypeLabels.video') : '视频 Video', 
      color: 'bg-destructive/10 text-destructive', 
      icon: 'video' 
    },
    text: { 
      label: t ? t('discover.contentTypeLabels.text') : '文案 Copy', 
      color: 'bg-primary/10 text-primary', 
      icon: 'file-text' 
    },
    news: { 
      label: t ? t('discover.contentTypeLabels.news') : '热点 Trend', 
      color: 'bg-secondary/10 text-secondary', 
      icon: 'trending-up' 
    },
    analysis: { 
      label: t ? t('discover.contentTypeLabels.analysis') : '分析 Analysis', 
      color: 'bg-accent/10 text-accent', 
      icon: 'bar-chart' 
    },
    hybrid: { 
      label: t ? t('discover.contentTypeLabels.hybrid') : '整合 Campaign', 
      color: 'bg-muted text-muted-foreground', 
      icon: 'layers' 
    },
  };
  return configs[type];
};

export const getStatusConfig = (status: string) => {
  const configs = {
    inspiration: { label: '待灵感', color: 'bg-muted text-muted-foreground' },
    drafting: { label: '制作中', color: 'bg-accent/10 text-accent' },
    completed: { label: '已完成', color: 'bg-primary/10 text-primary' },
    published: { label: '已发布', color: 'bg-secondary/10 text-secondary' },
  };
  return configs[status] || configs.inspiration;
};