'use server';
import { createServerClient, type CookieOptions } from '@supabase/ssr';
import { cookies } from 'next/headers';

export const createClient = async () => {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

  // Check if environment variables are available
  if (!supabaseUrl || !supabaseAnonKey) {
    // Return null during build time or when environment variables are not set
    return null;
  }

  const cookieStore = await cookies();

  // Ensure the URL is in the proper format with http/https protocol
  let processedUrl = supabaseUrl;
  if (processedUrl && !processedUrl.startsWith('http')) {
    // If it's just a hostname without protocol, add http://
    processedUrl = `http://${processedUrl}`;
  }

  // console.log('[SERVER] Supabase URL:', processedUrl);
  // console.log('[SERVER] Supabase Anon Key:', supabase<PERSON>nonKey);

  return createServerClient(processedUrl, supabaseAnonKey, {
    cookies: {
      getAll() {
        return cookieStore.getAll();
      },
      setAll(cookiesToSet) {
        try {
          cookiesToSet.forEach(({ name, value, options }) =>
            cookieStore.set({ name, value, ...options }),
          );
        } catch (error) {
          // The `set` method was called from a Server Component.
          // This can be ignored if you have middleware refreshing
          // user sessions.
        }
      },
    },
  });
};
