import { createBrowserClient } from '@supabase/ssr';

export const createClient = () => {
  // Get URL and key from environment variables
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

  // Check if environment variables are available
  if (!supabaseUrl || !supabaseAnonKey) {
    // During build time or in environments without Supabase, return null
    if (typeof window === 'undefined') {
      // Server-side: return null for build-time rendering
      return null;
    }
    // Client-side: throw error only in browser
    throw new Error(
      'Missing Supabase environment variables. Please set NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY in your environment variables.'
    );
  }

  // Ensure the URL is in the proper format with http/https protocol
  let processedUrl = supabaseUrl;
  if (processedUrl && !processedUrl.startsWith('http')) {
    // If it's just a hostname without protocol, add http://
    processedUrl = `http://${processedUrl}`;
  }

  // console.log('Supabase URL:', processedUrl);
  // console.log('Supabase Anon Key:', supabaseAnonKey);

  return createBrowserClient(processedUrl, supabaseAnonKey);
};
