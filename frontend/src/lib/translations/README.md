# 翻译文件说明

## 文件结构

```
src/lib/translations/
├── en.json          # 英文翻译文件
├── zh.json          # 中文翻译文件
├── index.ts         # 导出索引文件
└── README.md        # 说明文档
```

## 使用方法

### 1. 添加新的翻译键

1. 在 `src/lib/i18n.ts` 中的 `Translations` 接口中添加新的键类型定义
2. 在 `en.json` 中添加英文翻译
3. 在 `zh.json` 中添加中文翻译

### 2. 在组件中使用

```typescript
import { useLanguage } from '@/contexts/LanguageContext';

function MyComponent() {
  const { t } = useLanguage();
  
  return (
    <div>
      <h1>{t('home.hero.title')}</h1>
      <p>{t('home.hero.description')}</p>
    </div>
  );
}
```

### 3. 嵌套键的使用

支持使用点号分隔的嵌套键：

```typescript
// 访问嵌套对象
t('sidebar.userMenu.settings')  // 设置
t('dashboard.exampleItems.quantumPhysics')  // 简单解释一下量子物理
```

## 翻译键命名规范

- 使用小驼峰命名法
- 按功能模块分组（如：`home`、`dashboard`、`sidebar`）
- 嵌套对象使用点号分隔
- 保持键名简洁明了

## 示例

### 英文翻译 (en.json)
```json
{
  "home": {
    "hero": {
      "title": "The Generalist AI Agent that can act on your behalf",
      "subtitle": "Loomu is a fully open source AI assistant"
    }
  }
}
```

### 中文翻译 (zh.json)
```json
{
  "home": {
    "hero": {
      "title": "代表您行动的通用型 AI 代理",
      "subtitle": "Loomu 是一个完全开源的 AI 助手"
    }
  }
}
```

## 注意事项

1. 确保英文和中文翻译文件中的键结构完全一致
2. 翻译内容要准确、自然，符合目标语言的习惯
3. 定期检查和更新翻译内容
4. 新增翻译键时，记得同时更新 TypeScript 类型定义 