{"common": {"loading": "加载中...", "error": "错误", "success": "成功", "cancel": "取消", "confirm": "确认", "save": "保存", "delete": "删除", "edit": "编辑", "close": "关闭", "back": "返回", "next": "下一步", "previous": "上一步", "search": "搜索", "filter": "筛选", "sort": "排序", "refresh": "刷新", "settings": "设置", "profile": "个人资料", "logout": "退出登录", "login": "登录", "signup": "注册", "language": "语言", "theme": "主题", "dark": "深色", "light": "浅色", "system": "系统"}, "navigation": {"home": "首页", "dashboard": "仪表板", "agents": "智能助手", "projects": "项目", "discover": "发现", "settings": "设置", "billing": "账单", "help": "帮助", "about": "关于", "contact": "联系"}, "home": {"hero": {"title": "代表您行动的通用型 AI 代理", "subtitle": "Loomu 是一个完全开源的 AI 助手，帮助您轻松完成现实世界的任务。", "description": "通过自然对话，Loomu 成为您的研究、数据分析和日常挑战的数字伙伴。", "cta": "免费开始", "secondaryCta": "了解更多", "placeholder": "告诉 Loomu 您想要完成什么...", "authRequired": "需要身份验证", "authDescription": "请登录或创建账号以开始与 Loomu 对话", "signIn": "登录", "signUp": "注册", "orContinueWith": "或使用邮箱继续", "emailPlaceholder": "请输入邮箱", "passwordPlaceholder": "请输入密码", "invalidCredentials": "登录凭证无效", "loginError": "登录过程中发生错误"}, "brand": {"title": "Loomu AI", "subtitle": "您的所有社交媒体创建的 AI 助手", "description": "从捕捉灵感到内容变现，Loomu AI 赋能创作者打造智能、高效且可扩展的内容创作工作流。体验流畅的灵感管理、智能自动化与创意变现——尽在一个平台。", "cta": "开始使用", "secondaryCta": "了解更多"}, "features": {"title": "功能特性", "subtitle": "开始使用所需的一切"}, "pricing": {"title": "定价", "subtitle": "选择适合您的计划"}, "faq": {"title": "常见问题", "subtitle": "关于 Loomu 您需要知道的一切"}}, "dashboard": {"title": "仪表板", "welcome": "欢迎回来！", "recentConversations": "最近对话", "quickActions": "快速操作", "examples": "试试这些示例", "startNewChat": "开始新对话", "placeholder": "问我任何问题...", "capabilities": "能力", "discover": "发现", "openMenu": "打开菜单", "exampleItems": {"quantumPhysics": "简单解释一下量子物理", "marketingPlan": "为新产品制定营销计划", "techSummary": "总结最新技术会议的要点"}, "capabilityItems": {"memory": "记住用户在对话中早些时候说过的话", "corrections": "允许用户提供后续更正", "safety": "学会拒绝不当请求"}, "discoverItems": {"aiTrends": "探索最新AI趋势", "hotTopics": "发现热门话题", "recommendations": "查看推荐内容"}}, "chat": {"sendMessage": "发送消息", "placeholder": "输入您的消息...", "thinking": "思考中...", "error": "出现错误，请重试。", "retry": "重试", "stop": "停止", "clear": "清除", "export": "导出", "share": "分享"}, "settings": {"title": "设置", "general": "常规", "account": "账户", "billing": "账单", "notifications": "通知", "privacy": "隐私", "language": "语言", "theme": "主题"}, "features": {"radar": {"title": "灵感雷达", "subtitle": "抢先发现流行趋势", "description": "AI 驱动的趋势检测系统，覆盖书籍、关键词、新闻和社交平台，助你把握热点先机。通过智能算法实时监控全网动态，精准捕捉新兴趋势，让你始终走在时代前沿。"}, "contentEngine": {"title": "智能内容引擎", "subtitle": "一键生成多格式内容", "description": "将灵感自动转化为文本、音频、图片和视频，内容创作高效便捷。基于先进的AI技术，智能理解你的创意意图，快速生成专业级别的多媒体内容，让创作变得简单而强大。"}, "automation": {"title": "人性化自动化", "subtitle": "不仅智能，更像人类助手", "description": "自动排版、去AI痕迹重写、SEO优化、智能发布，一站式内容管理。融合人工智能与人性化设计理念，让每个自动化环节都体现人文关怀，既追求效率最大化，又保持人性化温度，真正实现智能与温情的完美结合。"}, "calendar": {"title": "时间大师日历", "subtitle": "计划、追踪、创作一体化", "description": "可视化创作日程管理，全面掌控每日创作安排。智能优化工作流程，自动分配时间资源，提升创作效率。提供秘书级日程服务，无缝对接各大社交平台，实现创作到发布的一体化管理。"}, "automationSteps": {"layout": "自动排版", "rewrite": "去AI痕迹重写", "seo": "SEO优化", "publish": "智能发布"}, "aiEngine": "AI引擎"}, "sidebar": {"chat": "聊天", "discover": "发现", "projects": "我的项目", "mcpMarket": "MCP市场", "connectors": "连接器", "history": "历史记录", "newChat": "新建对话", "expand": "展开", "collapse": "收起", "toggleSidebar": "切换侧边栏 (CMD+B)", "openMenu": "打开菜单", "expandSidebar": "展开侧边栏 (CMD+B)", "loading": "加载中...", "defaultUser": "用户", "userMenu": {"personalAccount": "个人账户", "teamAccount": "团队账户", "personalPlan": "个人版", "teamPlan": "团队版", "settings": "设置", "billing": "账单", "help": "帮助", "logout": "退出登录", "credits": "积分", "newTeam": "新建团队", "switchAccount": "切换账户", "teams": "团队", "inviteFriends": "邀请好友", "docs": "开发文档", "helpCenter": "帮助中心", "lightMode": "浅色模式", "darkMode": "深色模式", "createNewTeam": "创建新团队", "createTeamDescription": "创建团队以与他人协作。"}}, "footer": {"description": "Loomu 是一个完全开源的 AI 助手，帮助您轻松完成现实世界的任务。", "copyright": "© 2024 Loomu AI. 保留所有权利。", "allRightsReserved": "保留所有权利。"}, "discover": {"title": "内容发现", "subtitle": "探索优质案例，激发创作灵感", "searchPlaceholder": "搜索内容案例...", "resultsSummary": "找到 {count} 个相关案例", "pageInfo": "第 {current} 页，共 {total} 页", "noResults": {"title": "未找到相关内容", "description": "尝试调整搜索条件或筛选器"}, "filters": {"all": "全部", "allTime": "全部时间", "lastWeek": "近一周", "lastMonth": "近一月", "lastYear": "近一年", "mostPopular": "最热门", "latest": "最新", "mostFavorited": "最多收藏", "contentTypes": {"all": "全部", "video": "视频", "text": "文字", "news": "资讯", "analysis": "分析", "hybrid": "混合"}}, "pagination": {"previous": "上一页", "next": "下一页"}, "contentTypeLabels": {"video": "视频", "text": "文案", "news": "热点", "analysis": "分析", "hybrid": "整合"}, "statusLabels": {"inspiration": "待灵感", "drafting": "制作中", "completed": "已完成", "published": "已发布"}, "modal": {"creationDetails": "创作详情", "targetAudience": "目标受众", "marketingGoal": "营销目标", "performanceData": "效果数据", "views": "播放量", "likes": "点赞数", "shares": "分享数", "conversionRate": "转化率", "playCount": "播放量", "likeCount": "点赞数", "shareCount": "分享数", "conversionRateLabel": "转化率"}}, "mcpMarket": {"title": "MCP 市场", "submitMCP": "提交你的 MCP", "searchPlaceholder": "搜索 MCPs...", "noResults": "未找到符合您条件的 MCPs。", "categories": {"all": "所有类别", "analytics": "数据分析", "infrastructure": "基础设施", "security": "安全", "aiMl": "AI/ML", "automation": "自动化", "communication": "通信", "development": "开发", "marketing": "营销", "productivity": "生产力"}, "popularityFilters": {"all": "所有热度", "popular": "热门 (4+)", "veryPopular": "非常热门 (5)", "extremelyPopular": "极其热门 (5)"}, "dateFilters": {"all": "任意日期", "newest": "最新添加", "oldest": "最早发布"}, "pagination": {"previous": "上一页", "next": "下一页"}, "developer": "由"}, "projects": {"title": "我的项目", "createWorkflow": "创建新工作流", "searchPlaceholder": "搜索工作流...", "allProjects": "所有项目", "noWorkflows": "未找到工作流。", "tableHeaders": {"name": "名称", "description": "描述", "date": "日期", "tags": "标签", "status": "状态", "actions": "操作"}, "status": {"public": "公开", "private": "私有"}, "actions": {"run": "运行", "edit": "编辑", "share": "分享", "delete": "删除"}}, "connectors": {"title": "连接器", "subtitle": "连接您喜爱的平台和应用程序，解锁强大的集成功能并简化您的工作流程。", "searchPlaceholder": "搜索平台或应用程序...", "noResults": "未找到 \"{searchTerm}\" 相关的连接器。", "status": {"connected": "已连接", "notConnected": "未连接"}, "actionText": {"connect": "连接", "manage": "管理"}, "boundAccount": "已绑定账号", "clickToConnect": "点击连接按钮配置此平台的授权信息", "configDialog": {"title": "配置 {name}", "description": "请填写以下信息以连接您的 {name} 账号。所有信息将被安全加密存储。", "cancel": "取消", "saving": "保存中...", "updateConfig": "更新配置", "connectAccount": "连接账号"}}, "auth": {"backToHome": "返回首页", "welcomeBack": "欢迎回来", "joinLoomu": "加入 Loomu", "createAccount": "创建您的账号，开始使用 AI", "signInAccount": "登录您的账号，开始使用 AI", "useEmailSignIn": "使用邮箱登录", "useEmailContinue": "或使用邮箱继续", "emailPlaceholder": "请输入邮箱", "passwordPlaceholder": "请输入密码", "confirmPasswordPlaceholder": "请确认密码", "inviteCodePlaceholder": "请输入邀请码（可选）", "signIn": "登录", "signUp": "注册", "createNewAccount": "创建新账号", "backToSignIn": "返回登录", "signingIn": "登录中...", "creatingAccount": "创建账号中...", "forgotPassword": "忘记密码？", "termsAndPrivacy": "继续使用即表示您同意我们的", "termsOfService": "服务条款", "privacyPolicy": "隐私政策", "and": "和", "checkEmail": "请查看您的邮箱", "emailSentTo": "我们已发送确认链接至：", "yourEmail": "您的邮箱地址", "emailInstructions": "请点击邮件中的链接以激活您的账号。如果没有收到邮件，请检查垃圾邮件文件夹。", "backToHomepage": "返回首页", "backToSignInPage": "返回登录", "resetPassword": "重置密码", "resetPasswordDescription": "输入您的邮箱地址，我们将向您发送密码重置链接。", "resetPasswordEmailPlaceholder": "请输入邮箱地址", "cancel": "取消", "sendResetLink": "发送重置链接", "passwordResetComplete": "密码重置完成", "passwordUpdatedSuccessfully": "您的密码已成功更新。现在可以使用新密码登录了。", "goToSignIn": "前往登录", "createNewPassword": "为您的账号创建新密码", "newPassword": "新密码", "confirmNewPassword": "确认新密码", "updatingPassword": "更新密码中...", "resetPasswordButton": "重置密码", "invalidResetCode": "重置码无效或缺失。请重新申请密码重置链接。", "invalidCode": "重置码无效", "errors": {"invalidEmail": "请输入有效的邮箱地址", "passwordTooShort": "密码长度至少为6位", "passwordsDoNotMatch": "两次输入的密码不一致", "authenticationFailed": "用户认证失败", "cannotCreateAccount": "无法创建账号", "accountCreatedCheckEmail": "账号创建成功！请检查您的邮箱以确认注册。", "cannotSendResetEmail": "无法发送密码重置邮件", "checkEmailForResetLink": "请检查您的邮箱获取密码重置链接", "cannotUpdatePassword": "无法更新密码", "passwordUpdatedSuccessfully": "密码更新成功", "cannotSignOut": "无法退出登录"}}, "errors": {"notFound": {"title": "页面未找到", "description": "您要查找的页面不存在。", "backHome": "返回首页"}, "serverError": {"title": "服务器错误", "description": "我们这边出现了问题，请重试。", "retry": "重试"}}}