{"common": {"loading": "Loading...", "error": "Error", "success": "Success", "cancel": "Cancel", "confirm": "Confirm", "save": "Save", "delete": "Delete", "edit": "Edit", "close": "Close", "back": "Back", "next": "Next", "previous": "Previous", "search": "Search", "filter": "Filter", "sort": "Sort", "refresh": "Refresh", "settings": "Settings", "profile": "Profile", "logout": "Logout", "login": "<PERSON><PERSON>", "signup": "Sign Up", "language": "Language", "theme": "Theme", "dark": "Dark", "light": "Light", "system": "System"}, "navigation": {"home": "Home", "dashboard": "Dashboard", "agents": "Agents", "projects": "Projects", "discover": "Discover", "settings": "Settings", "billing": "Billing", "help": "Help", "about": "About", "contact": "Contact"}, "home": {"hero": {"title": "The Generalist AI Agent that can act on your behalf", "subtitle": "<PERSON><PERSON><PERSON> is a fully open source AI assistant that helps you accomplish real-world tasks with ease.", "description": "Through natural conversation, <PERSON><PERSON><PERSON> becomes your digital companion for research, data analysis, and everyday challenges.", "cta": "Start Free", "secondaryCta": "Learn More", "placeholder": "Tell <PERSON><PERSON><PERSON> what you want to accomplish...", "authRequired": "Authentication required", "authDescription": "Please sign in or create an account to start chatting with <PERSON><PERSON><PERSON>", "signIn": "Sign In", "signUp": "Sign Up", "orContinueWith": "Or continue with email", "emailPlaceholder": "Enter your email", "passwordPlaceholder": "Enter your password", "invalidCredentials": "Invalid credentials", "loginError": "An error occurred during login"}, "brand": {"title": "Loomu AI", "subtitle": "Your All-in-One AI Assistant for Social Media Creation", "description": "From capturing inspiration to monetizing your content, Loomu AI empowers creators to build an intelligent, efficient, and scalable content creation workflow. Experience seamless idea management, smart automation, and creative monetization—all in one platform.", "cta": "Get Started", "secondaryCta": "Learn More"}, "features": {"title": "Features", "subtitle": "Everything you need to get started"}, "pricing": {"title": "Pricing", "subtitle": "Choose the plan that works for you"}, "faq": {"title": "Frequently Asked Questions", "subtitle": "Everything you need to know about <PERSON><PERSON><PERSON>"}}, "dashboard": {"title": "Dashboard", "welcome": "Welcome back!", "recentConversations": "Recent Conversations", "quickActions": "Quick Actions", "examples": "Try these examples", "startNewChat": "Start a new conversation", "placeholder": "Ask me anything...", "capabilities": "Capabilities", "discover": "Discover", "openMenu": "Open menu", "exampleItems": {"quantumPhysics": "Explain quantum physics simply", "marketingPlan": "Create a marketing plan for new product", "techSummary": "Summarize latest tech conference highlights"}, "capabilityItems": {"memory": "Remember what you said earlier in conversation", "corrections": "Allow you to provide follow-up corrections", "safety": "Learn to decline inappropriate requests"}, "discoverItems": {"aiTrends": "Explore latest AI trends", "hotTopics": "Discover trending topics", "recommendations": "View recommended content"}}, "chat": {"sendMessage": "Send message", "placeholder": "Type your message...", "thinking": "Thinking...", "error": "Something went wrong. Please try again.", "retry": "Retry", "stop": "Stop", "clear": "Clear", "export": "Export", "share": "Share"}, "settings": {"title": "Settings", "general": "General", "account": "Account", "billing": "Billing", "notifications": "Notifications", "privacy": "Privacy", "language": "Language", "theme": "Theme"}, "features": {"radar": {"title": "Inspiration Radar", "subtitle": "Discover trends first", "description": "AI-powered trend detection system covering books, keywords, news and social platforms, helping you seize hot opportunities. Through intelligent algorithms, it monitors global network dynamics in real-time, precisely capturing emerging trends, keeping you always at the forefront of the times."}, "contentEngine": {"title": "Smart Content Engine", "subtitle": "Generate multi-format content with one click", "description": "Automatically transform inspiration into text, audio, images and videos for efficient content creation. Based on advanced AI technology, it intelligently understands your creative intent and rapidly generates professional-level multimedia content, making creation simple yet powerful."}, "automation": {"title": "Humanized Automation", "subtitle": "Not just smart, more like a human assistant", "description": "Automatic layout, AI trace removal rewriting, SEO optimization, intelligent publishing, one-stop content management. Integrating artificial intelligence with humanized design concepts, every automation aspect reflects human care, pursuing maximum efficiency while maintaining human warmth, truly achieving the perfect combination of intelligence and compassion."}, "calendar": {"title": "Time Master Calendar", "subtitle": "Plan, track, create all in one", "description": "Visualized creative schedule management, comprehensively controlling daily creative arrangements. Intelligently optimizes workflows, automatically allocates time resources, and enhances creative efficiency. Provides secretary-level scheduling services, seamlessly integrates with major social platforms, achieving integrated management from creation to publication."}, "automationSteps": {"layout": "Auto Layout", "rewrite": "De-AI Rewrite", "seo": "SEO Optimize", "publish": "Auto Publish"}, "aiEngine": "AI Engine"}, "sidebar": {"chat": "Cha<PERSON>", "discover": "Discover", "projects": "My Projects", "mcpMarket": "MCP Market", "connectors": "Connectors", "history": "History", "newChat": "New Chat", "expand": "Expand", "collapse": "Collapse", "toggleSidebar": "Toggle Sidebar (CMD+B)", "openMenu": "Open Menu", "expandSidebar": "Expand Sidebar (CMD+B)", "loading": "Loading...", "defaultUser": "User", "userMenu": {"personalAccount": "Personal Account", "teamAccount": "Team Account", "personalPlan": "Personal Plan", "teamPlan": "Team Plan", "settings": "Settings", "billing": "Billing", "help": "Help", "logout": "Logout", "credits": "Credits", "newTeam": "New Team", "switchAccount": "Switch Account", "teams": "Teams", "inviteFriends": "Invite Friends", "docs": "Documentation", "helpCenter": "Help Center", "lightMode": "Light Mode", "darkMode": "Dark Mode", "createNewTeam": "Create a new team", "createTeamDescription": "Create a team to collaborate with others."}}, "footer": {"description": "<PERSON><PERSON><PERSON> is a fully open source AI assistant that helps you accomplish real-world tasks with ease.", "copyright": "© 2024 Loomu AI. All rights reserved.", "allRightsReserved": "All rights reserved."}, "discover": {"title": "Content Discovery", "subtitle": "Explore quality cases and inspire creative ideas", "searchPlaceholder": "Search content cases...", "resultsSummary": "Found {count} related cases", "pageInfo": "Page {current} of {total}", "noResults": {"title": "No content found", "description": "Try adjusting your search criteria or filters"}, "filters": {"all": "All", "allTime": "All Time", "lastWeek": "Last Week", "lastMonth": "Last Month", "lastYear": "Last Year", "mostPopular": "Most Popular", "latest": "Latest", "mostFavorited": "Most Favorited", "contentTypes": {"all": "All", "video": "Video", "text": "Text", "news": "News", "analysis": "Analysis", "hybrid": "Hybrid"}}, "pagination": {"previous": "Previous", "next": "Next"}, "contentTypeLabels": {"video": "Video", "text": "Copy", "news": "Trend", "analysis": "Analysis", "hybrid": "Campaign"}, "statusLabels": {"inspiration": "Inspiration", "drafting": "Drafting", "completed": "Completed", "published": "Published"}, "modal": {"creationDetails": "Creation Details", "targetAudience": "Target Audience", "marketingGoal": "Marketing Goal", "performanceData": "Performance Data", "views": "Views", "likes": "<PERSON>s", "shares": "Shares", "conversionRate": "Conversion Rate", "playCount": "Play Count", "likeCount": "Like Count", "shareCount": "Share Count", "conversionRateLabel": "Conversion Rate"}}, "mcpMarket": {"title": "MCP Market", "submitMCP": "Submit your MCP", "searchPlaceholder": "Search MCPs...", "noResults": "No MCPs found matching your criteria.", "categories": {"all": "All Categories", "analytics": "Analytics", "infrastructure": "Infrastructure", "security": "Security", "aiMl": "AI/ML", "automation": "Automation", "communication": "Communication", "development": "Development", "marketing": "Marketing", "productivity": "Productivity"}, "popularityFilters": {"all": "All Popularity", "popular": "Popular (4+)", "veryPopular": "Very Popular (5)", "extremelyPopular": "Extremely Popular (5)"}, "dateFilters": {"all": "Any Date", "newest": "Newest Added", "oldest": "Oldest Published"}, "pagination": {"previous": "Previous", "next": "Next"}, "developer": "Developed by"}, "projects": {"title": "My Projects", "createWorkflow": "Create New Workflow", "searchPlaceholder": "Search workflows...", "allProjects": "All Projects", "noWorkflows": "No workflows found.", "tableHeaders": {"name": "Name", "description": "Description", "date": "Date", "tags": "Tags", "status": "Status", "actions": "Actions"}, "status": {"public": "Public", "private": "Private"}, "actions": {"run": "Run", "edit": "Edit", "share": "Share", "delete": "Delete"}}, "connectors": {"title": "Connectors", "subtitle": "Connect your favorite platforms and applications to unlock powerful integration features and simplify your workflow.", "searchPlaceholder": "Search platforms or applications...", "noResults": "No connectors found for \"{searchTerm}\".", "status": {"connected": "Connected", "notConnected": "Not Connected"}, "actionText": {"connect": "Connect", "manage": "Manage"}, "boundAccount": "Bound Account", "clickToConnect": "Click the connect button to configure authorization information for this platform", "configDialog": {"title": "Configure {name}", "description": "Please fill in the following information to connect your {name} account. All information will be securely encrypted and stored.", "cancel": "Cancel", "saving": "Saving...", "updateConfig": "Update Configuration", "connectAccount": "Connect Account"}}, "auth": {"backToHome": "Back to Home", "welcomeBack": "Welcome Back", "joinLoomu": "Join <PERSON>", "createAccount": "Create your account and start using AI", "signInAccount": "Sign in to your account and start using AI", "useEmailSignIn": "Sign in with email", "useEmailContinue": "Or continue with email", "emailPlaceholder": "Enter your email", "passwordPlaceholder": "Enter your password", "confirmPasswordPlaceholder": "Confirm your password", "inviteCodePlaceholder": "Enter invite code (optional)", "signIn": "Sign In", "signUp": "Sign Up", "createNewAccount": "Create New Account", "backToSignIn": "Back to Sign In", "signingIn": "Signing in...", "creatingAccount": "Creating account...", "forgotPassword": "Forgot Password?", "termsAndPrivacy": "By continuing, you agree to our", "termsOfService": "Terms of Service", "privacyPolicy": "Privacy Policy", "and": "and", "checkEmail": "Please check your email", "emailSentTo": "We have sent a confirmation link to:", "yourEmail": "Your email address", "emailInstructions": "Please click the link in the email to activate your account. If you don't receive the email, please check your spam folder.", "backToHomepage": "Back to Homepage", "backToSignInPage": "Back to Sign In", "resetPassword": "Reset Password", "resetPasswordDescription": "Enter your email address and we will send you a password reset link.", "resetPasswordEmailPlaceholder": "Enter your email address", "cancel": "Cancel", "sendResetLink": "Send Reset Link", "passwordResetComplete": "Password Reset Complete", "passwordUpdatedSuccessfully": "Your password has been successfully updated. You can now sign in with your new password.", "goToSignIn": "Go to Sign In", "createNewPassword": "Create a new password for your account", "newPassword": "New Password", "confirmNewPassword": "Confirm New Password", "updatingPassword": "Updating password...", "resetPasswordButton": "Reset Password", "invalidResetCode": "Invalid or missing reset code. Please request a new password reset link.", "invalidCode": "Invalid code", "errors": {"invalidEmail": "Please enter a valid email address", "passwordTooShort": "Password must be at least 6 characters long", "passwordsDoNotMatch": "Passwords do not match", "authenticationFailed": "User authentication failed", "cannotCreateAccount": "Unable to create account", "accountCreatedCheckEmail": "Account created successfully! Please check your email to confirm registration.", "cannotSendResetEmail": "Unable to send password reset email", "checkEmailForResetLink": "Please check your email for password reset link", "cannotUpdatePassword": "Unable to update password", "passwordUpdatedSuccessfully": "Password updated successfully", "cannotSignOut": "Unable to sign out"}}, "errors": {"notFound": {"title": "Page Not Found", "description": "The page you are looking for does not exist.", "backHome": "Back to Home"}, "serverError": {"title": "Server Error", "description": "Something went wrong on our end. Please try again.", "retry": "Try Again"}}}