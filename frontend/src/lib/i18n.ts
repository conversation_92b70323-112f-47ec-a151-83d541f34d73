export const locales = ['en', 'zh'] as const;
export type Locale = typeof locales[number];

export const defaultLocale: Locale = 'en';

// 翻译键类型定义
export interface Translations {
  // 通用
  common: {
    loading: string;
    error: string;
    success: string;
    cancel: string;
    confirm: string;
    save: string;
    delete: string;
    edit: string;
    close: string;
    back: string;
    next: string;
    previous: string;
    search: string;
    filter: string;
    sort: string;
    refresh: string;
    settings: string;
    profile: string;
    logout: string;
    login: string;
    signup: string;
    language: string;
    theme: string;
    dark: string;
    light: string;
    system: string;
  };
  
  // 导航
  navigation: {
    home: string;
    dashboard: string;
    agents: string;
    projects: string;
    discover: string;
    settings: string;
    billing: string;
    help: string;
    about: string;
    contact: string;
  };
  
  // 首页
  home: {
    hero: {
      title: string;
      subtitle: string;
      description: string;
      cta: string;
      secondaryCta: string;
      placeholder: string;
      authRequired: string;
      authDescription: string;
      signIn: string;
      signUp: string;
      orContinueWith: string;
      emailPlaceholder: string;
      passwordPlaceholder: string;
      invalidCredentials: string;
      loginError: string;
    };
    brand: {
      title: string;
      subtitle: string;
      description: string;
      cta: string;
      secondaryCta: string;
    };
    features: {
      title: string;
      subtitle: string;
    };
    pricing: {
      title: string;
      subtitle: string;
    };
    faq: {
      title: string;
      subtitle: string;
    };
  };
  
  // 仪表板
  dashboard: {
    title: string;
    welcome: string;
    recentConversations: string;
    quickActions: string;
    examples: string;
    startNewChat: string;
    placeholder: string;
    capabilities: string;
    discover: string;
    openMenu: string;
    exampleItems: {
      quantumPhysics: string;
      marketingPlan: string;
      techSummary: string;
    };
    capabilityItems: {
      memory: string;
      corrections: string;
      safety: string;
    };
    discoverItems: {
      aiTrends: string;
      hotTopics: string;
      recommendations: string;
    };
  };
  
  // 聊天
  chat: {
    sendMessage: string;
    placeholder: string;
    thinking: string;
    error: string;
    retry: string;
    stop: string;
    clear: string;
    export: string;
    share: string;
  };
  
  // 设置
  settings: {
    title: string;
    general: string;
    account: string;
    billing: string;
    notifications: string;
    privacy: string;
    language: string;
    theme: string;
  };
  
  // 功能特性
  features: {
    radar: {
      title: string;
      subtitle: string;
      description: string;
    };
    contentEngine: {
      title: string;
      subtitle: string;
      description: string;
    };
    automation: {
      title: string;
      subtitle: string;
      description: string;
    };
    calendar: {
      title: string;
      subtitle: string;
      description: string;
    };
    automationSteps: {
      layout: string;
      rewrite: string;
      seo: string;
      publish: string;
    };
    aiEngine: string;
  };
  
  // 侧边栏
  sidebar: {
    chat: string;
    discover: string;
    projects: string;
    mcpMarket: string;
    connectors: string;
    history: string;
    newChat: string;
    expand: string;
    collapse: string;
    toggleSidebar: string;
    openMenu: string;
    expandSidebar: string;
    loading: string;
    defaultUser: string;
    userMenu: {
      personalAccount: string;
      teamAccount: string;
      personalPlan: string;
      teamPlan: string;
      settings: string;
      billing: string;
      help: string;
      logout: string;
      credits: string;
      newTeam: string;
      switchAccount: string;
      teams: string;
      inviteFriends: string;
      docs: string;
      helpCenter: string;
      lightMode: string;
      darkMode: string;
      createNewTeam: string;
      createTeamDescription: string;
    };
  };
  
  // 页脚
  footer: {
    description: string;
    copyright: string;
    allRightsReserved: string;
  };
  
  // 发现页面
  discover: {
    title: string;
    subtitle: string;
    searchPlaceholder: string;
    resultsSummary: string;
    pageInfo: string;
    noResults: {
      title: string;
      description: string;
    };
    filters: {
      all: string;
      allTime: string;
      lastWeek: string;
      lastMonth: string;
      lastYear: string;
      mostPopular: string;
      latest: string;
      mostFavorited: string;
      contentTypes: {
        all: string;
        video: string;
        text: string;
        news: string;
        analysis: string;
        hybrid: string;
      };
    };
    pagination: {
      previous: string;
      next: string;
    };
    contentTypeLabels: {
      video: string;
      text: string;
      news: string;
      analysis: string;
      hybrid: string;
    };
    statusLabels: {
      inspiration: string;
      drafting: string;
      completed: string;
      published: string;
    };
    modal: {
      creationDetails: string;
      targetAudience: string;
      marketingGoal: string;
      performanceData: string;
      views: string;
      likes: string;
      shares: string;
      conversionRate: string;
      playCount: string;
      likeCount: string;
      shareCount: string;
      conversionRateLabel: string;
    };
  };
  
  // MCP 市场
  mcpMarket: {
    title: string;
    submitMCP: string;
    searchPlaceholder: string;
    noResults: string;
    categories: {
      all: string;
      analytics: string;
      infrastructure: string;
      security: string;
      aiMl: string;
      automation: string;
      communication: string;
      development: string;
      marketing: string;
      productivity: string;
    };
    popularityFilters: {
      all: string;
      popular: string;
      veryPopular: string;
      extremelyPopular: string;
    };
    dateFilters: {
      all: string;
      newest: string;
      oldest: string;
    };
    pagination: {
      previous: string;
      next: string;
    };
    developer: string;
  };
  
  // 项目页面
  projects: {
    title: string;
    createWorkflow: string;
    searchPlaceholder: string;
    allProjects: string;
    noWorkflows: string;
    tableHeaders: {
      name: string;
      description: string;
      date: string;
      tags: string;
      status: string;
      actions: string;
    };
    status: {
      public: string;
      private: string;
    };
    actions: {
      run: string;
      edit: string;
      share: string;
      delete: string;
    };
  };
  
  // 连接器页面
  connectors: {
    title: string;
    subtitle: string;
    searchPlaceholder: string;
    noResults: string;
    status: {
      connected: string;
      notConnected: string;
    };
    actionText: {
      connect: string;
      manage: string;
    };
    boundAccount: string;
    clickToConnect: string;
    configDialog: {
      title: string;
      description: string;
      cancel: string;
      saving: string;
      updateConfig: string;
      connectAccount: string;
    };
  };
  
  // 认证页面
  auth: {
    backToHome: string;
    welcomeBack: string;
    joinLoomu: string;
    createAccount: string;
    signInAccount: string;
    useEmailSignIn: string;
    useEmailContinue: string;
    emailPlaceholder: string;
    passwordPlaceholder: string;
    confirmPasswordPlaceholder: string;
    inviteCodePlaceholder: string;
    signIn: string;
    signUp: string;
    createNewAccount: string;
    backToSignIn: string;
    signingIn: string;
    creatingAccount: string;
    forgotPassword: string;
    termsAndPrivacy: string;
    termsOfService: string;
    privacyPolicy: string;
    and: string;
    checkEmail: string;
    emailSentTo: string;
    yourEmail: string;
    emailInstructions: string;
    backToHomepage: string;
    backToSignInPage: string;
    resetPassword: string;
    resetPasswordDescription: string;
    resetPasswordEmailPlaceholder: string;
    cancel: string;
    sendResetLink: string;
    passwordResetComplete: string;
    passwordUpdatedSuccessfully: string;
    goToSignIn: string;
    createNewPassword: string;
    newPassword: string;
    confirmNewPassword: string;
    updatingPassword: string;
    resetPasswordButton: string;
    invalidResetCode: string;
    invalidCode: string;
    errors: {
      invalidEmail: string;
      passwordTooShort: string;
      passwordsDoNotMatch: string;
      authenticationFailed: string;
      cannotCreateAccount: string;
      accountCreatedCheckEmail: string;
      cannotSendResetEmail: string;
      checkEmailForResetLink: string;
      cannotUpdatePassword: string;
      passwordUpdatedSuccessfully: string;
      cannotSignOut: string;
    };
  };
  
  // 错误页面
  errors: {
    notFound: {
      title: string;
      description: string;
      backHome: string;
    };
    serverError: {
      title: string;
      description: string;
      retry: string;
    };
  };
}

// 导入翻译文件
import enTranslations from './translations/en.json';
import zhTranslations from './translations/zh.json';

// 翻译数据
const translations: Record<Locale, Translations> = {
  en: enTranslations as Translations,
  zh: zhTranslations as Translations,
};

// 获取翻译函数
export function getTranslation(locale: Locale): Translations {
  return translations[locale] || translations[defaultLocale];
}

// 获取嵌套翻译值
export function getNestedTranslation(
  locale: Locale,
  path: string
): string {
  const translation = getTranslation(locale);
  const keys = path.split('.');
  let value: any = translation;
  
  for (const key of keys) {
    if (value && typeof value === 'object' && key in value) {
      value = value[key];
    } else {
      // 如果找不到翻译，返回英文默认值
      const defaultTranslation = getTranslation(defaultLocale);
      let defaultValue: any = defaultTranslation;
      for (const defaultKey of keys) {
        if (defaultValue && typeof defaultValue === 'object' && defaultKey in defaultValue) {
          defaultValue = defaultValue[defaultKey];
        } else {
          return path; // 如果连默认值都找不到，返回路径
        }
      }
      return defaultValue;
    }
  }
  
  return typeof value === 'string' ? value : path;
}

// 初始化翻译（在应用启动时调用）
export async function initializeTranslations(): Promise<void> {
  // 翻译文件已经通过 import 同步加载，无需异步初始化
  return Promise.resolve();
} 