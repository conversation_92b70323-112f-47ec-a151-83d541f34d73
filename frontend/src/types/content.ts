export type ContentType = 'video' | 'text' | 'news' | 'analysis' | 'hybrid';

export type ContentStatus = 'inspiration' | 'drafting' | 'completed' | 'published';

export interface Author {
  id: string;
  name: string;
  avatar: string;
}

export interface ContentCase {
  id: string;
  title: string;
  type: ContentType;
  coverImage: string;
  author: Author;
  publishedAt: string;
  favorites: number;
  views: number;
  isFavorited: boolean;
  description?: string;
  content?: any; // Content-specific data
  tags?: string[];
  status?: ContentStatus;
  scheduledDate?: string;
  duration?: number; // For videos
}

export type LayoutMode = 'grid' | 'list';

export interface FilterOptions {
  type: ContentType | 'all';
  dateRange: 'all' | 'week' | 'month' | 'year';
  sortBy: 'popular' | 'recent' | 'favorites';
  tags: string[];
  layout: LayoutMode;
}