declare module '@/contexts/CreditsContext' {
  export interface CreditsContextType {
    credits: number;
    isLoading: boolean;
    error: string | null;
    deductCredits: (amount: number) => Promise<boolean>;
    addCredits: (amount: number) => Promise<void>;
    checkCredits: (requiredAmount: number) => boolean;
  }

  export function useCredits(): CreditsContextType;
  export function CreditsProvider({ children }: { children: React.ReactNode }): JSX.Element;
}

declare module '@/components/billing/CreditsPurchaseModal' {
  interface CreditsPurchaseModalProps {
    isOpen: boolean;
    onClose: () => void;
    requiredCredits: number;
  }

  export function CreditsPurchaseModal(props: CreditsPurchaseModalProps): JSX.Element;
} 