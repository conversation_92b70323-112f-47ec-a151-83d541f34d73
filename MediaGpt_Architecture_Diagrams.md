# MediaGpt项目架构图和时序执行图

## 1. 整体服务架构图

```mermaid
graph TB
    %% User Layer
    User[👤 User]
    Browser[🌐 Browser]

    %% Frontend Layer
    subgraph "Frontend Service (Port: 3000)"
        Frontend[Next.js Frontend]
        ReactQuery[React Query Cache]
        Zustand[Zustand State Management]
        SSE[SSE Client]
    end

    %% Backend API Layer
    subgraph "Backend API Service (Port: 8000)"
        API[FastAPI Server]
        AuthMiddleware[Auth Middleware]
        CORS[CORS Middleware]
        HealthCheck[Health Check]
    end

    %% Worker Layer
    subgraph "Backend Worker Service"
        Worker[Dramatiq Worker]
        AgentRunner[Agent Runner]
        ToolSystem[Tool System]
    end

    %% Message Queue Layer
    subgraph "Message Queue (Port: 5672)"
        RabbitMQ[RabbitMQ]
        TaskQueue[Task Queue]
    end

    %% Cache Layer
    subgraph "Cache Service (Port: 6379)"
        Redis[Redis]
        PubSub[Pub/Sub Channel]
        ResponseCache[Response Cache]
        TTLManager[TTL Manager]
    end

    %% Database Layer
    subgraph "Database Service"
        Supabase[Supabase PostgreSQL]
        RLS[Row Level Security]
        Auth[User Authentication]
    end

    %% Sandbox Environment
    subgraph "Sandbox Environment"
        Sandbox[Daytona Sandbox]
        CodeExecution[Code Execution Environment]
        FileSystem[Isolated File System]
    end

    %% External Services
    subgraph "External Services"
        LLM["LLM API (OpenAI/Gemini)"]
        Langfuse[Langfuse Monitoring]
        Stripe[Stripe Billing]
    end

    %% Connections
    User --> Browser
    Browser --> Frontend
    Frontend --> ReactQuery
    Frontend --> Zustand
    Frontend --> SSE
    Frontend --> Supabase

    Frontend --> API
    API --> AuthMiddleware
    API --> CORS
    API --> HealthCheck

    API --> RabbitMQ
    RabbitMQ --> TaskQueue
    TaskQueue --> Worker
    Worker --> AgentRunner
    AgentRunner --> ToolSystem

    API --> Redis
    Worker --> Redis
    Redis --> PubSub
    Redis --> ResponseCache
    Redis --> TTLManager

    API --> Supabase
    Worker --> Supabase
    Supabase --> RLS
    Supabase --> Auth

    Worker --> Sandbox
    Sandbox --> CodeExecution
    Sandbox --> FileSystem

    Worker --> LLM
    Worker --> Langfuse
    API --> Stripe

    %% SSE Connections
    SSE -.-> API
    PubSub -.-> SSE
```

## 2. 用户请求时序执行图

```mermaid
sequenceDiagram
    participant U as 👤 User
    participant F as 🌐 Frontend
    participant A as 🔧 API Service
    participant R as 📦 Redis
    participant Q as 🐰 RabbitMQ
    participant W as ⚙️ Worker
    participant D as 🗄️ Supabase
    participant L as 🤖 LLM API
    participant S as 📦 Sandbox

    %% 1. User sends message
    U->>F: Input and send message
    F->>F: Optimistic UI update
    F->>D: Insert message into messages table (Supabase)
    D-->>F: Confirm save success

    %% 2. Start Agent
    F->>A: POST /api/thread/{id}/agent/start
    A->>D: Create agent_run record
    D-->>A: Return agent_run_id
    A->>Q: Send task to queue (run_agent_background)
    A-->>F: Return agent_run_id

    %% 3. Establish SSE connection
    F->>A: GET /api/agent-run/{id}/stream (SSE)
    A->>R: Check agent running status
    R-->>A: Return status info
    A-->>F: Establish SSE connection

    %% 4. Worker processes task
    Q->>W: Distribute task to Worker
    W->>R: Set instance active status (TTL)
    W->>R: Subscribe to control channel (Pub/Sub)
    W->>D: Get thread message history
    D-->>W: Return message history

    %% 5. Agent execution loop
    loop Agent Execution Process
        W->>L: Call LLM API
        L-->>W: Return LLM response

        alt Tool call needed
            W->>S: Execute tool in sandbox
            S-->>W: Return tool execution result
            W->>R: Store tool call result
            R->>R: Publish new response notification
            R-->>F: Push tool call via SSE
        else Plain text response
            W->>R: Store text response
            R->>R: Publish new response notification
            R-->>F: Push text content via SSE
        end

        F->>F: Real-time UI update

        %% Check stop signal
        W->>R: Check stop signal
        opt Stop signal received
            Note over W,R: Stop execution
        end
    end

    %% 6. Complete processing
    W->>D: Update agent_run status to completed
    W->>R: Publish completion signal
    R-->>F: Push completion status via SSE
    W->>R: Cleanup Redis keys (set TTL)
    F->>F: Update UI to completed state
    F->>A: Close SSE connection
```

## 3. 实时通信机制详图

```mermaid
graph LR
    subgraph "Frontend Real-time Communication"
        EventSource[EventSource]
        StreamHook[useAgentStream Hook]
        MessageHandler[Message Handler]
        UIUpdate[UI Update]
    end

    subgraph "Backend SSE Service"
        SSEEndpoint[SSE Endpoint]
        StreamManager[Stream Manager]
        AuthCheck[Auth Check]
    end

    subgraph "Redis Pub/Sub"
        ResponseChannel[Response Channel]
        ControlChannel[Control Channel]
        Publisher[Publisher]
        Subscriber[Subscriber]
    end

    subgraph "Worker Process"
        AgentLoop[Agent Loop]
        ResponseGenerator[Response Generator]
        ControlListener[Control Listener]
    end

    %% Connections
    EventSource --> SSEEndpoint
    SSEEndpoint --> AuthCheck
    SSEEndpoint --> StreamManager
    StreamManager --> Subscriber
    Subscriber --> ResponseChannel
    Subscriber --> ControlChannel

    AgentLoop --> ResponseGenerator
    ResponseGenerator --> Publisher
    Publisher --> ResponseChannel
    ControlListener --> ControlChannel

    StreamManager --> MessageHandler
    MessageHandler --> StreamHook
    StreamHook --> UIUpdate
```

## 4. 数据流架构图

```mermaid
flowchart TD
    %% User Interaction Layer
    subgraph "User Interaction Layer"
        UI[User Interface]
        ChatInput[Chat Input]
        MessageList[Message List]
        ToolPanel[Tool Panel]
    end

    %% State Management Layer
    subgraph "Frontend State Management"
        ReactQuery[React Query]
        Zustand[Zustand Store]
        LocalState[Component Local State]
        SSEState[SSE Connection State]
    end

    %% API Communication Layer
    subgraph "API Communication Layer"
        RESTClient[REST API Client]
        SSEClient[SSE Client]
        AuthToken[Auth Token]
        ErrorHandler[Error Handler]
    end

    %% Backend Routing Layer
    subgraph "Backend Routing Layer"
        ThreadAPI[Thread API]
        MessageAPI[Message API]
        AgentAPI[Agent API]
        SandboxAPI[Sandbox API]
        BillingAPI[Billing API]
    end

    %% Business Logic Layer
    subgraph "Business Logic Layer"
        ThreadManager[Thread Manager]
        AgentRunner[Agent Runner]
        ToolRegistry[Tool Registry]
        ContextManager[Context Manager]
    end

    %% Data Persistence Layer
    subgraph "Data Persistence Layer"
        ProjectTable[Project Table]
        ThreadTable[Thread Table]
        MessageTable[Message Table]
        AgentRunTable[Agent Run Table]
        UserTable[User Table]
    end

    %% Connections
    UI --> ChatInput
    UI --> MessageList
    UI --> ToolPanel

    ChatInput --> ReactQuery
    MessageList --> ReactQuery
    ToolPanel --> Zustand

    ReactQuery --> RESTClient
    SSEState --> SSEClient

    RESTClient --> ThreadAPI
    RESTClient --> MessageAPI
    RESTClient --> AgentAPI
    SSEClient --> AgentAPI

    ThreadAPI --> ThreadManager
    MessageAPI --> ThreadManager
    AgentAPI --> AgentRunner

    ThreadManager --> ThreadTable
    ThreadManager --> MessageTable
    AgentRunner --> AgentRunTable
```

## 5. 微服务部署架构图

```mermaid
graph TB
    %% Load Balancer Layer
    subgraph "Load Balancer Layer"
        LB[Load Balancer]
        SSL[SSL Termination]
    end

    %% Frontend Service Cluster
    subgraph "Frontend Service Cluster"
        FE1[Frontend-1:3000]
        FE2[Frontend-2:3000]
        FE3[Frontend-3:3000]
    end

    %% API Service Cluster
    subgraph "API Service Cluster"
        API1[API-1:8000]
        API2[API-2:8000]
        API3[API-3:8000]
    end

    %% Worker Service Cluster
    subgraph "Worker Service Cluster"
        W1[Worker-1]
        W2[Worker-2]
        W3[Worker-3]
        W4[Worker-4]
    end

    %% Middleware Services
    subgraph "Middleware Services"
        Redis1[Redis Master]
        Redis2[Redis Replica]
        RMQ1[RabbitMQ-1]
        RMQ2[RabbitMQ-2]
    end

    %% Database Services
    subgraph "Database Services"
        DB1[Supabase Primary]
        DB2[Supabase Replica]
    end

    %% Sandbox Service Cluster
    subgraph "Sandbox Service Cluster"
        SB1[Sandbox-1]
        SB2[Sandbox-2]
        SB3[Sandbox-3]
    end

    %% Monitoring Services
    subgraph "Monitoring Services"
        Prometheus[Prometheus]
        Grafana[Grafana]
        Langfuse[Langfuse]
        Logs[Log Aggregation]
    end

    %% Connections
    LB --> SSL
    SSL --> FE1
    SSL --> FE2
    SSL --> FE3

    FE1 --> API1
    FE2 --> API2
    FE3 --> API3

    API1 --> RMQ1
    API2 --> RMQ1
    API3 --> RMQ2

    RMQ1 --> W1
    RMQ1 --> W2
    RMQ2 --> W3
    RMQ2 --> W4

    API1 --> Redis1
    API2 --> Redis1
    API3 --> Redis2

    W1 --> Redis1
    W2 --> Redis1
    W3 --> Redis2
    W4 --> Redis2

    API1 --> DB1
    API2 --> DB1
    API3 --> DB2

    W1 --> DB1
    W2 --> DB1
    W3 --> DB2
    W4 --> DB2

    W1 --> SB1
    W2 --> SB2
    W3 --> SB3
    W4 --> SB1

    %% Monitoring Connections
    API1 -.-> Prometheus
    API2 -.-> Prometheus
    API3 -.-> Prometheus
    W1 -.-> Langfuse
    W2 -.-> Langfuse
    W3 -.-> Langfuse
    W4 -.-> Langfuse
```

## 6. Agent工具执行时序图

```mermaid
sequenceDiagram
    participant A as Agent
    participant T as Tool Registry
    participant S as Sandbox
    participant F as File System
    participant N as Network Service
    participant R as Redis Cache
    participant U as Frontend UI

    %% Tool call flow
    A->>T: Query available tools
    T-->>A: Return tool list

    A->>A: LLM decides tool selection
    A->>T: Call specific tool
    T->>S: Execute in sandbox

    alt File operation tool
        S->>F: Read/write file
        F-->>S: Return file content/confirmation
    else Code execution tool
        S->>S: Execute Python/JS code
        S-->>S: Get execution result
    else Network request tool
        S->>N: Make HTTP request
        N-->>S: Return response data
    end

    S-->>T: Return tool execution result
    T-->>A: Return result to Agent
    A->>R: Cache tool call result
    R->>U: Real-time push tool execution status
    U->>U: Update tool panel display
```

## 7. 错误处理和恢复机制图

```mermaid
flowchart TD
    %% Error Detection Layer
    subgraph "Error Detection"
        HealthCheck[Health Check]
        HeartBeat[Heartbeat Monitor]
        TTLMonitor[TTL Monitor]
        ErrorBoundary[Error Boundary]
    end

    %% Error Types
    subgraph "Error Types"
        NetworkError[Network Error]
        ServiceError[Service Error]
        DatabaseError[Database Error]
        AuthError[Auth Error]
        TimeoutError[Timeout Error]
    end

    %% Recovery Strategies
    subgraph "Recovery Strategies"
        Retry[Retry Mechanism]
        Fallback[Fallback Service]
        CircuitBreaker[Circuit Breaker]
        Rollback[Rollback Operation]
    end

    %% Notification Mechanisms
    subgraph "Notification Mechanisms"
        UserNotify[User Notification]
        LogAlert[Log Alert]
        MetricAlert[Metric Alert]
        AdminNotify[Admin Notification]
    end

    %% Connections
    HealthCheck --> NetworkError
    HeartBeat --> ServiceError
    TTLMonitor --> TimeoutError
    ErrorBoundary --> AuthError

    NetworkError --> Retry
    ServiceError --> CircuitBreaker
    DatabaseError --> Fallback
    TimeoutError --> Rollback

    Retry --> UserNotify
    CircuitBreaker --> LogAlert
    Fallback --> MetricAlert
    Rollback --> AdminNotify
```

## 8. 安全架构图

```mermaid
graph TB
    %% Network Security Layer
    subgraph "Network Security Layer"
        WAF[Web Application Firewall]
        DDoS[DDoS Protection]
        RateLimit[Rate Limiting]
        IPFilter[IP Filtering]
    end

    %% Authentication & Authorization Layer
    subgraph "Auth & Authorization Layer"
        JWT[JWT Tokens]
        OAuth[OAuth Integration]
        RLS[Row Level Security]
        RBAC[Role-Based Access Control]
    end

    %% Data Security Layer
    subgraph "Data Security Layer"
        Encryption[Data Encryption]
        KeyManagement[Key Management]
        DataMasking[Data Masking]
        Backup[Secure Backup]
    end

    %% Runtime Security Layer
    subgraph "Runtime Security Layer"
        SandboxIsolation[Sandbox Isolation]
        ContainerSecurity[Container Security]
        NetworkIsolation[Network Isolation]
        ResourceLimit[Resource Limits]
    end

    %% Audit & Monitoring Layer
    subgraph "Audit & Monitoring Layer"
        AccessLog[Access Logs]
        SecurityLog[Security Logs]
        AuditTrail[Audit Trail]
        ThreatDetection[Threat Detection]
    end

    %% Connections
    WAF --> JWT
    DDoS --> OAuth
    RateLimit --> RLS
    IPFilter --> RBAC

    JWT --> Encryption
    OAuth --> KeyManagement
    RLS --> DataMasking
    RBAC --> Backup

    Encryption --> SandboxIsolation
    KeyManagement --> ContainerSecurity
    DataMasking --> NetworkIsolation
    Backup --> ResourceLimit

    SandboxIsolation --> AccessLog
    ContainerSecurity --> SecurityLog
    NetworkIsolation --> AuditTrail
    ResourceLimit --> ThreatDetection
```

## 9. 关键技术组件说明

### 9.1 前端技术栈
- **Next.js 15**: 全栈React框架，支持SSR和API路由
- **React Query**: 强大的数据获取、缓存和同步库
- **Zustand**: 轻量级状态管理库
- **Server-Sent Events**: 实时数据流通信
- **TypeScript**: 类型安全的JavaScript超集

### 9.2 后端技术栈
- **FastAPI**: 高性能异步Python Web框架
- **Dramatiq**: 简单可靠的Python任务队列库
- **Redis**: 高性能内存数据库，用于缓存和Pub/Sub
- **RabbitMQ**: 可靠的消息队列中间件
- **Supabase**: 开源的Firebase替代品，提供PostgreSQL数据库

### 9.3 核心设计模式

#### 微服务架构
- **API服务**: 处理HTTP请求和用户认证
- **Worker服务**: 异步执行AI Agent任务
- **前端服务**: 用户界面和交互逻辑

#### 事件驱动架构
- **Redis Pub/Sub**: 服务间异步消息传递
- **SSE**: 前端实时数据推送
- **事件溯源**: 完整的操作历史记录

#### 沙盒隔离
- **Docker容器**: 安全的代码执行环境
- **网络隔离**: 限制网络访问权限
- **资源限制**: CPU和内存使用控制

### 9.4 数据流特点

#### 实时性
- **毫秒级响应**: SSE实时推送Agent执行结果
- **状态同步**: 多层次的状态管理机制
- **乐观更新**: 提升用户体验的即时反馈

#### 可靠性
- **消息持久化**: RabbitMQ确保任务不丢失
- **重试机制**: 自动重试失败的操作
- **故障恢复**: 自动检测和恢复异常状态

#### 可扩展性
- **水平扩展**: 支持多实例负载均衡
- **无状态设计**: 服务实例可随意增减
- **插件化**: 工具系统支持动态扩展

## 10. 架构优势总结

### 10.1 技术优势
1. **高并发处理**: 异步架构支持大量并发用户
2. **实时交互**: SSE提供流畅的实时体验
3. **安全隔离**: 沙盒环境确保代码执行安全
4. **故障隔离**: 微服务架构提高系统稳定性

### 10.2 业务优势
1. **快速响应**: 毫秒级的用户交互反馈
2. **可靠执行**: 任务队列确保操作完整性
3. **灵活扩展**: 支持多种LLM模型和工具
4. **成本优化**: 按需扩展资源使用

### 10.3 运维优势
1. **容器化部署**: Docker简化部署和管理
2. **监控完善**: 全方位的系统监控和告警
3. **日志聚合**: 集中化的日志管理和分析
4. **自动恢复**: 智能的故障检测和恢复机制

这个架构设计为AI Agent应用提供了坚实的技术基础，支持复杂的多用户、多项目、实时交互的场景，是现代AI应用的最佳实践示例。
